{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Attach to Process",
      "type": "go",
      "request": "attach",
      "mode": "remote",
      "remotePath": "${workspaceFolder}",
      "port": 49202,
      "host": "127.0.0.1"
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "CMS",
      "url": "http://localhost:9200",
      "webRoot": "${workspaceFolder}/web/cms"
    },
    {
      "name": "Debug Server",
      "type": "go",
      "request": "launch",
      "mode": "auto",
      "program": "${workspaceFolder}/cmd/server/main.go",
      "cwd": "${workspaceFolder}",

      "env": {},
      "args": []
    },
    
    {
      "name": "[<PERSON><PERSON>] Tenant Server",
      "type": "go",
      "request": "attach",
      "mode": "remote",

      "port": 49202,
      "host": "127.0.0.1",
      "showLog": true,
      "apiVersion": 2,
      "trace": "verbose",
   
    }
  ]
}
