package plugins

import (
	"context"
	"fmt"
	"sync"
)

// Registry quản lý các plugin trong ứng dụng
type Registry struct {
	mu        sync.RWMutex
	factories map[string]PluginFactory
	plugins   map[string]Plugin
}

// GlobalRegistry là registry toàn cục cho plugins
var GlobalRegistry = NewRegistry()

// NewRegistry tạo registry mới
func NewRegistry() *Registry {
	return &Registry{
		factories: make(map[string]PluginFactory),
		plugins:   make(map[string]Plugin),
	}
}

// Register đăng ký plugin factory
func (r *Registry) Register(name string, factory PluginFactory) {
	if r == nil || factory == nil {
		return
	}

	r.mu.Lock()
	defer r.mu.Unlock()
	r.factories[name] = factory
}

// Get lấy plugin factory theo tên
func (r *Registry) Get(name string) (PluginFactory, bool) {
	if r == nil || name == "" {
		return nil, false
	}

	r.mu.RLock()
	defer r.mu.RUnlock()
	factory, ok := r.factories[name]
	return factory, ok
}

// RegisterPlugin đăng ký plugin instance
func (r *Registry) RegisterPlugin(plugin Plugin) {
	if r == nil || plugin == nil {
		return
	}

	r.mu.Lock()
	defer r.mu.Unlock()
	r.plugins[plugin.Name()] = plugin
}

// GetPlugin lấy plugin theo tên
func (r *Registry) GetPlugin(name string) (Plugin, bool) {
	if r == nil || name == "" {
		return nil, false
	}

	r.mu.RLock()
	defer r.mu.RUnlock()
	plugin, ok := r.plugins[name]
	return plugin, ok
}

// RegisterPluginFactory đăng ký plugin factory với registry toàn cục
func RegisterPluginFactory(name string, factory PluginFactory) {
	if name == "" || factory == nil {
		return
	}
	GlobalRegistry.Register(name, factory)
}

// GetPlugins trả về danh sách tất cả các plugin đã đăng ký
func (r *Registry) GetPlugins() map[string]Plugin {
	if r == nil {
		return nil
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	plugins := make(map[string]Plugin, len(r.plugins))
	for k, v := range r.plugins {
		plugins[k] = v
	}
	return plugins
}

// CreateAndInitialize tạo và khởi tạo plugin
func (r *Registry) CreateAndInitialize(ctx context.Context, name string, config map[string]interface{}) (Plugin, error) {
	factory, ok := r.Get(name)
	if !ok {
		return nil, fmt.Errorf("plugin factory not found: %s", name)
	}

	plugin, err := factory(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create plugin %s: %w", name, err)
	}

	if err := plugin.Init(ctx); err != nil {
		return nil, fmt.Errorf("failed to initialize plugin %s: %w", name, err)
	}

	r.RegisterPlugin(plugin)
	return plugin, nil
}

// GetFactories trả về map factories đã đăng ký
func (r *Registry) GetFactories() map[string]PluginFactory {
	if r == nil {
		return make(map[string]PluginFactory)
	}

	r.mu.RLock()
	defer r.mu.RUnlock()

	result := make(map[string]PluginFactory, len(r.factories))
	for k, v := range r.factories {
		result[k] = v
	}
	return result
}
