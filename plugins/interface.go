package plugins

import (
	"context"
)

// Plugin định nghĩa interface cho các plugin
type Plugin interface {
	// Name trả về tên của plugin
	Name() string

	// Init khởi tạo plugin
	Init(ctx context.Context) error

	// Shutdown dọn dẹp tài nguyên của plugin
	Shutdown(ctx context.Context) error
}

// PluginFactory là một hàm tạo plugin
type PluginFactory func(config map[string]interface{}) (Plugin, error)

// PluginOption chứa thông tin cấu hình cho plugin
type PluginOption struct {
	// Enabled xác định plugin có được kích hoạt hay không
	Enabled bool

	// Config chứa cấu hình cho plugin
	Config map[string]interface{}
}
