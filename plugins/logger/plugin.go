package logger

import (
	"context"
	"io"
	"os"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/plugins"
)

func init() {
	// Đăng ký plugin factory
	plugins.RegisterPluginFactory("logger", NewLoggerPlugin)
}

// LoggerPlugin triển khai plugin logger
type LoggerPlugin struct {
	name          string
	config        map[string]interface{}
	logLevel      logger.Level
	logFile       string
	fileWriter    io.WriteCloser
	rotationSize  int64
	rotationCount int
}

// NewLoggerPlugin tạo một plugin logger mới
func NewLoggerPlugin(config map[string]interface{}) (plugins.Plugin, error) {
	plugin := &LoggerPlugin{
		name:          "logger",
		config:        config,
		logLevel:      logger.LevelInfo,
		rotationSize:  10 * 1024 * 1024, // 10MB mặc định
		rotationCount: 5,                // 5 file backup mặc định
	}

	// Đ<PERSON><PERSON> c<PERSON>u hình
	if level, ok := config["level"].(string); ok {
		switch level {
		case "debug":
			plugin.logLevel = logger.LevelDebug
		case "info":
			plugin.logLevel = logger.LevelInfo
		case "warn":
			plugin.logLevel = logger.LevelWarn
		case "error":
			plugin.logLevel = logger.LevelError
		}
	}

	if file, ok := config["file"].(string); ok {
		plugin.logFile = file
	}

	if size, ok := config["rotation_size"].(int64); ok {
		plugin.rotationSize = size
	}

	if count, ok := config["rotation_count"].(int); ok {
		plugin.rotationCount = count
	}

	return plugin, nil
}

// Name trả về tên của plugin
func (p *LoggerPlugin) Name() string {
	return p.name
}

// Init khởi tạo plugin
func (p *LoggerPlugin) Init(ctx context.Context) error {
	// Nếu logFile được cấu hình, mở file để ghi log
	if p.logFile != "" {
		file, err := os.OpenFile(p.logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
		if err != nil {
			return err
		}
		p.fileWriter = file

		// Tạo file logger
		fileLogger := logger.NewConsoleLoggerWithWriter("file", p.logLevel, file)

		// Ghi thông tin khởi động
		fileLogger.Info("Logger plugin initialized",
			"time", time.Now().Format(time.RFC3339),
			"level", p.logLevel.String(),
			"file", p.logFile,
		)
	}

	// Ghi log khởi tạo plugin
	consoleLogger := logger.NewConsoleLogger("plugin", logger.LevelInfo)
	consoleLogger.Info("Logger plugin initialized",
		"level", p.logLevel.String(),
		"file", p.logFile,
	)

	return nil
}

// Shutdown dọn dẹp tài nguyên của plugin
func (p *LoggerPlugin) Shutdown(ctx context.Context) error {
	// Đóng file nếu đã mở
	if p.fileWriter != nil {
		if err := p.fileWriter.Close(); err != nil {
			return err
		}
		p.fileWriter = nil
	}
	return nil
}
