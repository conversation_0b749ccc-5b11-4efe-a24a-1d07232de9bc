name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: wnapi_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
        
    - name: Check out code
      uses: actions/checkout@v3
      
    - name: Get dependencies
      run: go mod download
      
    - name: Run tests
      run: go test -v ./...
      
    - name: Run tests with coverage
      run: go test -v -coverprofile=coverage.out ./...
      
    - name: Upload coverage report
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: coverage.out
        
  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'
        
    - name: Check out code
      uses: actions/checkout@v3
      
    - name: Get dependencies
      run: go mod download
      
    - name: Build
      run: go build -v -o build/wnapi ./cmd/server/main.go
      
    - name: Upload binary
      uses: actions/upload-artifact@v3
      with:
        name: wnapi-binary
        path: build/wnapi
        
  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: build
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Check out code
      uses: actions/checkout@v3
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
      
    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        push: false
        tags: wnapi:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max 