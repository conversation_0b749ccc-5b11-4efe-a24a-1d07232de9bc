package configs

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"

	"github.com/spf13/viper"
)

// Config đại diện cho cấu hình của module SEO
type Config struct {
	Server  ServerConfig   `yaml:"server"`
	DB      DatabaseConfig `yaml:"db"`
	Tracing TracingConfig  `yaml:"tracing"`
}

// ServerConfig đại diện cho cấu hình server
type ServerConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

// DatabaseConfig đại diện cho cấu hình database
type DatabaseConfig struct {
	Host     string `yaml:"host"`
	Port     int    `yaml:"port"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Database string `yaml:"database"`
}

// TracingConfig đại diện cho cấu hình OpenTelemetry tracing
type TracingConfig struct {
	Enabled      bool         `yaml:"enabled"`
	ServiceName  string       `yaml:"service_name"`
	ExporterType string       `yaml:"exporter_type"` // "signoz" hoặc "jaeger"
	SigNoz       SigNozConfig `yaml:"signoz"`
	Jaeger       JaegerConfig `yaml:"jaeger"`
	SampleRatio  float64      `yaml:"sample_ratio"`
}

// SigNozConfig đại diện cho cấu hình SigNoz exporter
type SigNozConfig struct {
	Endpoint string `yaml:"endpoint"`
}

// JaegerConfig đại diện cho cấu hình Jaeger exporter
type JaegerConfig struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
}

// NewDefaultConfig trả về một cấu hình mặc định
func NewDefaultConfig() *Config {
	return &Config{
		Server: ServerConfig{
			Host: "localhost",
			Port: 8080,
		},
		DB: DatabaseConfig{
			Host:     "localhost",
			Port:     3306,
			Username: "root",
			Password: "password",
			Database: "wn_seo",
		},
		Tracing: TracingConfig{
			Enabled:      true,
			ServiceName:  "seo-service",
			ExporterType: "signoz",
			SigNoz: SigNozConfig{
				Endpoint: "localhost:4317",
			},
			Jaeger: JaegerConfig{
				Host: "localhost",
				Port: "6831",
			},
			SampleRatio: 1.0,
		},
	}
}

// LoadConfig loads the configuration from config files
func LoadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("../configs")
	viper.AddConfigPath("../../configs")

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Override with environment variables if set
	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.DB.Host = dbHost
	}

	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		if port, err := strconv.Atoi(dbPort); err == nil {
			config.DB.Port = port
		}
	}

	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		config.DB.Username = dbUser
	}

	if dbPass := os.Getenv("DB_PASSWORD"); dbPass != "" {
		config.DB.Password = dbPass
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.DB.Database = dbName
	}

	if httpPort := os.Getenv("HTTP_PORT"); httpPort != "" {
		if port, err := strconv.Atoi(httpPort); err == nil {
			config.Server.Port = port
		}
	}

	// Handle tracing endpoint from jaeger agent host and port
	jaegerHost := os.Getenv("JAEGER_AGENT_HOST")
	jaegerPort := os.Getenv("JAEGER_AGENT_PORT")
	if jaegerHost != "" && jaegerPort != "" {
		config.Tracing.Jaeger.Host = jaegerHost
		config.Tracing.Jaeger.Port = jaegerPort
		config.Tracing.Enabled = true
	}

	// Set default values if they are not set
	if config.DB.Host == "" {
		config.DB.Host = "mysql"
	}
	if config.DB.Port == 0 {
		config.DB.Port = 3306
	}
	if config.DB.Username == "" {
		config.DB.Username = "root"
	}
	if config.DB.Database == "" {
		config.DB.Database = "blog_v4"
	}
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 9047
	}

	// Print config in Docker environment
	if os.Getenv("DOCKER_ENV") == "true" {
		PrintConfig(&config)
	}

	return &config, nil
}

// PrintConfig prints all configuration values in a formatted JSON
func PrintConfig(config *Config) {
	// Create a copy of the config with the password masked for security
	configCopy := *config
	configCopy.DB.Password = "********"

	// Marshal config to JSON for pretty printing
	configJSON, err := json.MarshalIndent(configCopy, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling config: %v\n", err)
		return
	}

	fmt.Printf("=== SEO MODULE CONFIGURATION ===\n%s\n==============================\n", string(configJSON))
}
