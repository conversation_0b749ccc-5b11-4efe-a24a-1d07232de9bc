package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// SeoMeta đại diện cho bảng seo_meta trong database
type SeoMeta struct {
	MetaID             uint       `json:"meta_id" gorm:"primaryKey;column:meta_id"`
	ObjectID           uint       `json:"object_id" gorm:"column:object_id"`
	ObjectType         string     `json:"object_type" gorm:"column:object_type;default:post"`
	SeoTitle           string     `json:"seo_title" gorm:"column:seo_title"`
	MetaDescription    string     `json:"meta_description" gorm:"column:meta_description"`
	FocusKeyphrase     string     `json:"focus_keyphrase" gorm:"column:focus_keyphrase"`
	CanonicalURL       string     `json:"canonical_url" gorm:"column:canonical_url"`
	IsCornerstone      uint8      `json:"is_cornerstone" gorm:"column:is_cornerstone;default:0"`
	OgTitle            string     `json:"og_title" gorm:"column:og_title"`
	OgDescription      string     `json:"og_description" gorm:"column:og_description"`
	OgImage            string     `json:"og_image" gorm:"column:og_image"`
	TwitterTitle       string     `json:"twitter_title" gorm:"column:twitter_title"`
	TwitterDescription string     `json:"twitter_description" gorm:"column:twitter_description"`
	TwitterImage       string     `json:"twitter_image" gorm:"column:twitter_image"`
	RobotsIndex        string     `json:"robots_index" gorm:"column:robots_index;default:default"`
	RobotsFollow       string     `json:"robots_follow" gorm:"column:robots_follow;default:default"`
	RobotsAdvanced     string     `json:"robots_advanced" gorm:"column:robots_advanced"`
	SeoScore           string     `json:"seo_score" gorm:"column:seo_score"`
	ReadabilityScore   string     `json:"readability_score" gorm:"column:readability_score"`
	SchemaData         SchemaData `json:"schema_data" gorm:"column:schema_data"`
	LastUpdated        time.Time  `json:"last_updated" gorm:"column:last_updated"`
}

// SchemaData đại diện cho dữ liệu schema JSON
type SchemaData map[string]interface{}

// Value cài đặt method Value cho kiểu SchemaData để sử dụng với trường JSON
func (s SchemaData) Value() (driver.Value, error) {
	return json.Marshal(s)
}

// Scan cài đặt method Scan cho kiểu SchemaData để sử dụng với trường JSON
func (s *SchemaData) Scan(value interface{}) error {
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("kiểu dữ liệu không được hỗ trợ cho SchemaData")
	}

	if len(bytes) == 0 {
		*s = make(SchemaData)
		return nil
	}

	return json.Unmarshal(bytes, &s)
}

// TableName trả về tên bảng cho model SeoMeta
func (SeoMeta) TableName() string {
	return "seo_meta"
}
