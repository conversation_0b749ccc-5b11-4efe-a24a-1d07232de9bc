package service

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel/attribute"

	"github.com/webnew/wn-backend-v2/modules/seo/models"
	"github.com/webnew/wn-backend-v2/modules/seo/repository"
	"github.com/webnew/wn-backend-v2/modules/seo/tracing"
)

// SeoMetaService đại diện cho service xử lý nghiệp vụ liên quan đến SeoMeta
type SeoMetaService struct {
	seoMetaRepo repository.SeoMetaRepository
}

// NewSeoMetaService tạo một instance mới của SeoMetaService
func NewSeoMetaService(seoMetaRepo repository.SeoMetaRepository) *SeoMetaService {
	return &SeoMetaService{
		seoMetaRepo: seoMetaRepo,
	}
}

// CreateSeoMeta tạo một bản ghi SeoMeta mới
func (s *SeoMetaService) CreateSeoMeta(ctx context.Context, meta *models.SeoMeta) error {
	ctx, span := tracing.StartSeoMetaSpan(ctx, "CreateSeoMeta", 0, meta.ObjectID, meta.ObjectType)
	defer span.End()

	startTime := time.Now()
	err := s.seoMetaRepo.Create(ctx, meta)
	if err != nil {
		tracing.RecordError(ctx, span, err, "Không thể tạo SeoMeta")
		return err
	}

	// Record performance metrics
	duration := float64(time.Since(startTime).Milliseconds())
	tagCount := countMetaTags(meta)
	tracing.RecordMetaPerformance(span, duration, tagCount)

	return nil
}

// GetSeoMetaByID lấy thông tin SeoMeta theo ID
func (s *SeoMetaService) GetSeoMetaByID(ctx context.Context, id uint) (*models.SeoMeta, error) {
	ctx, span := tracing.StartSeoMetaSpan(ctx, "GetSeoMetaByID", id, 0, "")
	defer span.End()

	meta, err := s.seoMetaRepo.GetByID(ctx, id)
	if err != nil {
		tracing.RecordError(ctx, span, err, fmt.Sprintf("Không tìm thấy SeoMeta với ID %d", id))
		return nil, err
	}

	// Thêm thuộc tính cho span sau khi lấy được dữ liệu
	span.SetAttributes(
		attribute.String("seo.object_type", meta.ObjectType),
		attribute.Int64("seo.object_id", int64(meta.ObjectID)),
	)

	return meta, nil
}

// GetSeoMetaByObject lấy thông tin SeoMeta theo object_id và object_type
func (s *SeoMetaService) GetSeoMetaByObject(ctx context.Context, objectID uint, objectType string) (*models.SeoMeta, error) {
	ctx, span := tracing.StartSeoMetaSpan(ctx, "GetSeoMetaByObject", 0, objectID, objectType)
	defer span.End()

	meta, err := s.seoMetaRepo.GetByObject(ctx, objectID, objectType)
	if err != nil {
		tracing.RecordError(ctx, span, err, fmt.Sprintf("Không tìm thấy SeoMeta cho object_id=%d, object_type=%s", objectID, objectType))
		return nil, err
	}

	// Thêm thuộc tính meta_id sau khi lấy được dữ liệu
	span.SetAttributes(attribute.Int64("seo.meta_id", int64(meta.MetaID)))

	return meta, nil
}

// UpdateSeoMeta cập nhật thông tin SeoMeta
func (s *SeoMetaService) UpdateSeoMeta(ctx context.Context, meta *models.SeoMeta) error {
	ctx, span := tracing.StartSeoMetaSpan(ctx, "UpdateSeoMeta", meta.MetaID, meta.ObjectID, meta.ObjectType)
	defer span.End()

	// Kiểm tra xem bản ghi tồn tại không
	_, err := s.seoMetaRepo.GetByID(ctx, meta.MetaID)
	if err != nil {
		errMsg := fmt.Sprintf("không tìm thấy bản ghi SEO với ID %d", meta.MetaID)
		tracing.RecordError(ctx, span, err, errMsg)
		return fmt.Errorf("%s: %w", errMsg, err)
	}

	startTime := time.Now()
	// Cập nhật bản ghi
	err = s.seoMetaRepo.Update(ctx, meta)
	if err != nil {
		tracing.RecordError(ctx, span, err, "Lỗi khi cập nhật SeoMeta")
		return err
	}

	// Record performance metrics
	duration := float64(time.Since(startTime).Milliseconds())
	tagCount := countMetaTags(meta)
	tracing.RecordMetaPerformance(span, duration, tagCount)

	return nil
}

// DeleteSeoMeta xóa SeoMeta theo ID
func (s *SeoMetaService) DeleteSeoMeta(ctx context.Context, id uint) error {
	ctx, span := tracing.StartSeoMetaSpan(ctx, "DeleteSeoMeta", id, 0, "")
	defer span.End()

	// Trước khi xóa, lấy thông tin để ghi log
	meta, err := s.seoMetaRepo.GetByID(ctx, id)
	if err == nil && meta != nil {
		span.SetAttributes(
			attribute.Int64("seo.object_id", int64(meta.ObjectID)),
			attribute.String("seo.object_type", meta.ObjectType),
		)
	}

	err = s.seoMetaRepo.Delete(ctx, id)
	if err != nil {
		tracing.RecordError(ctx, span, err, fmt.Sprintf("Không thể xóa SeoMeta với ID %d", id))
		return err
	}

	return nil
}

// DeleteSeoMetaByObject xóa SeoMeta theo object_id và object_type
func (s *SeoMetaService) DeleteSeoMetaByObject(ctx context.Context, objectID uint, objectType string) error {
	ctx, span := tracing.StartSeoMetaSpan(ctx, "DeleteSeoMetaByObject", 0, objectID, objectType)
	defer span.End()

	err := s.seoMetaRepo.DeleteByObject(ctx, objectID, objectType)
	if err != nil {
		tracing.RecordError(ctx, span, err, fmt.Sprintf("Không thể xóa SeoMeta với object_id=%d, object_type=%s", objectID, objectType))
		return err
	}

	return nil
}

// CreateOrUpdateSeoMeta tạo mới hoặc cập nhật SeoMeta cho một đối tượng
func (s *SeoMetaService) CreateOrUpdateSeoMeta(ctx context.Context, meta *models.SeoMeta) error {
	ctx, span := tracing.StartSeoMetaSpan(ctx, "CreateOrUpdateSeoMeta", 0, meta.ObjectID, meta.ObjectType)
	defer span.End()

	startTime := time.Now()

	// Kiểm tra xem đã tồn tại SeoMeta cho object này chưa
	existingMeta, err := s.seoMetaRepo.GetByObject(ctx, meta.ObjectID, meta.ObjectType)

	var opType string
	if err == nil && existingMeta != nil {
		// Đã tồn tại, cập nhật
		opType = "update"
		span.SetAttributes(attribute.String("seo.operation", opType))
		span.SetAttributes(attribute.Int64("seo.meta_id", int64(existingMeta.MetaID)))

		meta.MetaID = existingMeta.MetaID
		err = s.seoMetaRepo.Update(ctx, meta)
		if err != nil {
			tracing.RecordError(ctx, span, err, "Lỗi khi cập nhật SeoMeta")
			return err
		}
	} else {
		// Chưa tồn tại, tạo mới
		opType = "create"
		span.SetAttributes(attribute.String("seo.operation", opType))

		err = s.seoMetaRepo.Create(ctx, meta)
		if err != nil {
			tracing.RecordError(ctx, span, err, "Lỗi khi tạo mới SeoMeta")
			return err
		}
	}

	// Record performance metrics
	duration := float64(time.Since(startTime).Milliseconds())
	tagCount := countMetaTags(meta)
	tracing.RecordMetaPerformance(span, duration, tagCount)

	return nil
}

// countMetaTags đếm số lượng meta tag được sử dụng trong SeoMeta
func countMetaTags(meta *models.SeoMeta) int {
	count := 0

	if meta.SeoTitle != "" {
		count++
	}
	if meta.MetaDescription != "" {
		count++
	}
	if meta.CanonicalURL != "" {
		count++
	}
	if meta.OgTitle != "" {
		count++
	}
	if meta.OgDescription != "" {
		count++
	}
	if meta.OgImage != "" {
		count++
	}
	if meta.TwitterTitle != "" {
		count++
	}
	if meta.TwitterDescription != "" {
		count++
	}
	if meta.TwitterImage != "" {
		count++
	}
	if meta.RobotsIndex != "" && meta.RobotsIndex != "default" {
		count++
	}
	if meta.RobotsFollow != "" && meta.RobotsFollow != "default" {
		count++
	}
	if meta.RobotsAdvanced != "" {
		count++
	}
	if len(meta.SchemaData) > 0 {
		count++
	}

	return count
}
