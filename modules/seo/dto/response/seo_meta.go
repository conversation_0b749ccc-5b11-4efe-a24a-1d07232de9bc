package response

import (
	"time"

	"github.com/webnew/wn-backend-v2/modules/seo/models"
)

// SeoMetaResponse đại diện cho response từ API trả về thông tin SeoMeta
type SeoMetaResponse struct {
	MetaID             uint                   `json:"meta_id"`
	ObjectID           uint                   `json:"object_id"`
	ObjectType         string                 `json:"object_type"`
	SeoTitle           string                 `json:"seo_title"`
	MetaDescription    string                 `json:"meta_description"`
	FocusKeyphrase     string                 `json:"focus_keyphrase"`
	CanonicalURL       string                 `json:"canonical_url"`
	IsCornerstone      uint8                  `json:"is_cornerstone"`
	OgTitle            string                 `json:"og_title"`
	OgDescription      string                 `json:"og_description"`
	OgImage            string                 `json:"og_image"`
	TwitterTitle       string                 `json:"twitter_title"`
	TwitterDescription string                 `json:"twitter_description"`
	TwitterImage       string                 `json:"twitter_image"`
	RobotsIndex        string                 `json:"robots_index"`
	RobotsFollow       string                 `json:"robots_follow"`
	RobotsAdvanced     string                 `json:"robots_advanced"`
	SeoScore           string                 `json:"seo_score"`
	ReadabilityScore   string                 `json:"readability_score"`
	SchemaData         map[string]interface{} `json:"schema_data"`
	LastUpdated        time.Time              `json:"last_updated"`
}

// FromModel chuyển đổi model SeoMeta thành SeoMetaResponse
func FromModel(meta models.SeoMeta) SeoMetaResponse {
	return SeoMetaResponse{
		MetaID:             meta.MetaID,
		ObjectID:           meta.ObjectID,
		ObjectType:         meta.ObjectType,
		SeoTitle:           meta.SeoTitle,
		MetaDescription:    meta.MetaDescription,
		FocusKeyphrase:     meta.FocusKeyphrase,
		CanonicalURL:       meta.CanonicalURL,
		IsCornerstone:      meta.IsCornerstone,
		OgTitle:            meta.OgTitle,
		OgDescription:      meta.OgDescription,
		OgImage:            meta.OgImage,
		TwitterTitle:       meta.TwitterTitle,
		TwitterDescription: meta.TwitterDescription,
		TwitterImage:       meta.TwitterImage,
		RobotsIndex:        meta.RobotsIndex,
		RobotsFollow:       meta.RobotsFollow,
		RobotsAdvanced:     meta.RobotsAdvanced,
		SeoScore:           meta.SeoScore,
		ReadabilityScore:   meta.ReadabilityScore,
		SchemaData:         meta.SchemaData,
		LastUpdated:        meta.LastUpdated,
	}
}

// FromModelList chuyển đổi danh sách model SeoMeta thành danh sách SeoMetaResponse
func FromModelList(metas []models.SeoMeta) []SeoMetaResponse {
	result := make([]SeoMetaResponse, len(metas))
	for i, meta := range metas {
		result[i] = FromModel(meta)
	}
	return result
}
