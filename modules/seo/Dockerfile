FROM golang:1.23-alpine AS development

# Cài đặt công cụ hot-reload Air
RUN go install github.com/air-verse/air@latest
RUN go install github.com/go-delve/delve/cmd/dlv@latest
RUN apk add --no-cache git gcc musl-dev


# Thiết lập thư mục làm việc
WORKDIR /app/modules/site

# Copy file go.mod và go.sum
COPY modules/site/go.mod .
COPY modules/site/go.sum* .
COPY pkg/go.mod /app/pkg/
COPY pkg/go.sum /app/pkg/

# Tải các dependency
RUN go mod download

# Copy mã nguồn của module
COPY modules/site/ .
COPY pkg/ /app/pkg/

# Để Air hoạt động chính xác, chúng ta cần có quyền ghi vào thư mục tmp
RUN mkdir -p tmp && chmod 777 tmp

# Command mặc định cho môi trường phát triển
CMD ["air", "-c", ".air.toml"]

# Stage build cho production
FROM golang:1.23-alpine AS builder

WORKDIR /app
COPY modules/site/go.mod modules/site/go.sum* ./modules/site/
COPY pkg/go.mod pkg/go.sum ./pkg/

WORKDIR /app/modules/site
RUN go mod download

COPY modules/site/ ./
COPY pkg/ /app/pkg/

# Build ứng dụng
RUN go mod tidy && CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/main.go


# Image cuối cùng nhẹ hơn cho production
FROM alpine:latest AS production

RUN apk --no-cache add ca-certificates
WORKDIR /app

COPY --from=builder /app/modules/site/main .
COPY --from=builder /app/modules/site/configs/config.yaml ./configs/

EXPOSE 9047 49047

CMD ["./main"] 