package seo

import (
	"context"
	"log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/webnew/wn-backend-v2/modules/seo/api"
	"github.com/webnew/wn-backend-v2/modules/seo/api/middleware"
	"github.com/webnew/wn-backend-v2/modules/seo/configs"
	"github.com/webnew/wn-backend-v2/modules/seo/repository"
	"github.com/webnew/wn-backend-v2/modules/seo/service"
	"github.com/webnew/wn-backend-v2/modules/seo/tracing"
)

// ModuleSEO đại diện cho module SEO
type ModuleSEO struct {
	config         *configs.Config
	db             *gorm.DB
	seoMetaHandler *api.SeoMetaHandler
	tracerShutdown tracing.ShutdownFunc
}

// NewSEOModule tạo một instance mới của ModuleSEO
func NewSEOModule(db *gorm.DB) (*ModuleSEO, error) {
	config, err := configs.LoadConfig()
	if err != nil {
		return nil, err
	}

	// Khởi tạo tracing
	tracerShutdown, err := tracing.InitTracer(config)
	if err != nil {
		log.Printf("WARN: Không thể khởi tạo tracer cho SEO module: %v", err)
		// Tiếp tục mà không dùng tracing
		tracerShutdown = func(ctx context.Context) error { return nil }
	}

	// Khởi tạo repository
	seoMetaRepo := repository.NewGormSeoMetaRepository(db)

	// Khởi tạo service
	seoMetaService := service.NewSeoMetaService(seoMetaRepo)

	// Khởi tạo handler
	seoMetaHandler := api.NewSeoMetaHandler(seoMetaService)

	return &ModuleSEO{
		config:         config,
		db:             db,
		seoMetaHandler: seoMetaHandler,
		tracerShutdown: tracerShutdown,
	}, nil
}

// RegisterRoutes đăng ký các routes của module SEO
func (m *ModuleSEO) RegisterRoutes(router *gin.Engine) {
	// Áp dụng middleware cho tất cả các routes của module SEO
	seoGroup := router.Group("/api/v1/seo", middleware.TracingMiddleware())
	{
		// Các routes cho SeoMeta
		metaGroup := seoGroup.Group("/meta")
		{
			metaGroup.GET("/:id", m.seoMetaHandler.GetSeoMeta)
			metaGroup.GET("/object", m.seoMetaHandler.GetSeoMetaByObject)
			metaGroup.POST("/", m.seoMetaHandler.CreateSeoMeta)
			metaGroup.PUT("/:id", m.seoMetaHandler.UpdateSeoMeta)
			metaGroup.DELETE("/:id", m.seoMetaHandler.DeleteSeoMeta)
			metaGroup.DELETE("/object", m.seoMetaHandler.DeleteSeoMetaByObject)
			metaGroup.POST("/upsert", m.seoMetaHandler.CreateOrUpdateSeoMeta)
		}
	}
}

// Cleanup dọn dẹp tài nguyên của module
func (m *ModuleSEO) Cleanup(ctx context.Context) error {
	log.Println("Shutting down SEO module...")

	// Đóng tracer
	if err := m.tracerShutdown(ctx); err != nil {
		log.Printf("Error shutting down SEO module tracer: %v", err)
		return err
	}

	log.Println("SEO module shutdown completed successfully")
	return nil
}
