openapi: 3.1.0
info:
  title: SEO Module API
  description: API for managing SEO metadata for various content types
  version: 1.0.0
servers:
  - url: http://wn-api.local
    description: Local development server
paths:
  /api/v1/seo/meta/{id}:
    get:
      summary: <PERSON><PERSON><PERSON> thông tin SEO metadata theo ID
      operationId: getSeoMeta
      parameters:
        - name: id
          in: path
          description: ID của SEO metadata
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SeoMetaResponse'
        '404':
          description: Không tìm thấy SEO metadata
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Cập nhật SEO metadata
      operationId: updateSeoMeta
      parameters:
        - name: id
          in: path
          description: ID của SEO metadata
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSeoMetaRequest'
      responses:
        '200':
          description: SEO metadata đã được cập nhật
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SeoMetaResponse'
        '404':
          description: Không tìm thấy SEO metadata
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Xóa SEO metadata
      operationId: deleteSeoMeta
      parameters:
        - name: id
          in: path
          description: ID của SEO metadata
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: SEO metadata đã được xóa
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          description: Không tìm thấy SEO metadata
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/seo/meta:
    get:
      summary: Lấy thông tin SEO metadata theo object_id và object_type
      operationId: getSeoMetaByObject
      parameters:
        - name: object_id
          in: query
          description: ID của đối tượng liên kết
          required: true
          schema:
            type: integer
        - name: object_type
          in: query
          description: Loại đối tượng liên kết
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Thành công
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SeoMetaResponse'
        '404':
          description: Không tìm thấy SEO metadata
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      summary: Tạo SEO metadata mới
      operationId: createSeoMeta
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSeoMetaRequest'
      responses:
        '200':
          description: SEO metadata đã được tạo
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SeoMetaResponse'
        '400':
          description: Dữ liệu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Xóa SEO metadata theo object_id và object_type
      operationId: deleteSeoMetaByObject
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSeoMetaRequest'
      responses:
        '200':
          description: SEO metadata đã được xóa
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Dữ liệu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/seo/meta/upsert:
    post:
      summary: Tạo mới hoặc cập nhật SEO metadata
      operationId: createOrUpdateSeoMeta
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSeoMetaRequest'
      responses:
        '200':
          description: SEO metadata đã được tạo hoặc cập nhật
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SeoMetaResponse'
        '400':
          description: Dữ liệu không hợp lệ
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    Status:
      type: object
      properties:
        code:
          type: integer
          example: 200
        message:
          type: string
          example: "Operation completed successfully"
        success:
          type: boolean
          example: true
        error_code:
          type: string
          nullable: true
          example: null
        path:
          type: string
          example: "/api/v1/seo/meta/1"
        timestamp:
          type: string
          format: date-time
          example: "2025-03-15T14:35:22Z"
        details:
          type: array
          nullable: true
          items:
            type: object
            properties:
              field:
                type: string
              message:
                type: string
          example: null

    SeoMeta:
      type: object
      properties:
        meta_id:
          type: integer
          description: ID của SEO metadata
        object_id:
          type: integer
          description: ID của đối tượng liên kết
        object_type:
          type: string
          description: Loại đối tượng liên kết
        seo_title:
          type: string
          description: Tiêu đề SEO
          nullable: true
        meta_description:
          type: string
          description: Mô tả meta
          nullable: true
        focus_keyphrase:
          type: string
          description: Từ khóa tập trung
          nullable: true
        canonical_url:
          type: string
          description: URL chuẩn
          nullable: true
        is_cornerstone:
          type: integer
          description: Đánh dấu nội dung cornerstone (0 = No, 1 = Yes)
        og_title:
          type: string
          description: Tiêu đề Open Graph
          nullable: true
        og_description:
          type: string
          description: Mô tả Open Graph
          nullable: true
        og_image:
          type: string
          description: URL hình ảnh Open Graph
          nullable: true
        twitter_title:
          type: string
          description: Tiêu đề Twitter Card
          nullable: true
        twitter_description:
          type: string
          description: Mô tả Twitter Card
          nullable: true
        twitter_image:
          type: string
          description: URL hình ảnh Twitter Card
          nullable: true
        robots_index:
          type: string
          description: Chỉ thị robots index
          nullable: true
          enum: [index, noindex, default]
        robots_follow:
          type: string
          description: Chỉ thị robots follow
          nullable: true
          enum: [follow, nofollow, default]
        robots_advanced:
          type: string
          description: Chỉ thị robots nâng cao
          nullable: true
        seo_score:
          type: string
          description: Điểm SEO
          nullable: true
        readability_score:
          type: string
          description: Điểm đọc hiểu
          nullable: true
        schema_data:
          type: object
          description: Dữ liệu Schema.org
          additionalProperties: true
          nullable: true
        last_updated:
          type: string
          format: date-time
          description: Thời gian cập nhật cuối cùng

    GetSeoMetaRequest:
      type: object
      required:
        - object_id
        - object_type
      properties:
        object_id:
          type: integer
          description: ID của đối tượng liên kết
        object_type:
          type: string
          description: Loại đối tượng liên kết

    CreateSeoMetaRequest:
      type: object
      required:
        - object_id
        - object_type
      properties:
        object_id:
          type: integer
          description: ID của đối tượng liên kết
        object_type:
          type: string
          description: Loại đối tượng liên kết
        seo_title:
          type: string
          description: Tiêu đề SEO
        meta_description:
          type: string
          description: Mô tả meta
        focus_keyphrase:
          type: string
          description: Từ khóa tập trung
        canonical_url:
          type: string
          description: URL chuẩn
        is_cornerstone:
          type: integer
          description: Đánh dấu nội dung cornerstone (0 = No, 1 = Yes)
          default: 0
        og_title:
          type: string
          description: Tiêu đề Open Graph
        og_description:
          type: string
          description: Mô tả Open Graph
        og_image:
          type: string
          description: URL hình ảnh Open Graph
        twitter_title:
          type: string
          description: Tiêu đề Twitter Card
        twitter_description:
          type: string
          description: Mô tả Twitter Card
        twitter_image:
          type: string
          description: URL hình ảnh Twitter Card
        robots_index:
          type: string
          description: Chỉ thị robots index
          enum: [index, noindex, default]
          default: default
        robots_follow:
          type: string
          description: Chỉ thị robots follow
          enum: [follow, nofollow, default]
          default: default
        robots_advanced:
          type: string
          description: Chỉ thị robots nâng cao
        seo_score:
          type: string
          description: Điểm SEO
        readability_score:
          type: string
          description: Điểm đọc hiểu
        schema_data:
          type: object
          description: Dữ liệu Schema.org
          additionalProperties: true

    UpdateSeoMetaRequest:
      type: object
      properties:
        seo_title:
          type: string
          description: Tiêu đề SEO
        meta_description:
          type: string
          description: Mô tả meta
        focus_keyphrase:
          type: string
          description: Từ khóa tập trung
        canonical_url:
          type: string
          description: URL chuẩn
        is_cornerstone:
          type: integer
          description: Đánh dấu nội dung cornerstone (0 = No, 1 = Yes)
        og_title:
          type: string
          description: Tiêu đề Open Graph
        og_description:
          type: string
          description: Mô tả Open Graph
        og_image:
          type: string
          description: URL hình ảnh Open Graph
        twitter_title:
          type: string
          description: Tiêu đề Twitter Card
        twitter_description:
          type: string
          description: Mô tả Twitter Card
        twitter_image:
          type: string
          description: URL hình ảnh Twitter Card
        robots_index:
          type: string
          description: Chỉ thị robots index
          enum: [index, noindex, default]
        robots_follow:
          type: string
          description: Chỉ thị robots follow
          enum: [follow, nofollow, default]
        robots_advanced:
          type: string
          description: Chỉ thị robots nâng cao
        seo_score:
          type: string
          description: Điểm SEO
        readability_score:
          type: string
          description: Điểm đọc hiểu
        schema_data:
          type: object
          description: Dữ liệu Schema.org
          additionalProperties: true

    SeoMetaResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          $ref: '#/components/schemas/SeoMeta'

    SuccessResponse:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/Status'
        data:
          type: object
          nullable: true

    ErrorResponse:
      type: object
      properties:
        status:
          type: object
          properties:
            code:
              type: integer
              example: 404
            message:
              type: string
              example: "Resource not found"
            success:
              type: boolean
              example: false
            error_code:
              type: string
              example: "SEO_META_NOT_FOUND"
            path:
              type: string
              example: "/api/v1/seo/meta/999"
            timestamp:
              type: string
              format: date-time
              example: "2025-03-15T14:30:45Z"
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                  message:
                    type: string
