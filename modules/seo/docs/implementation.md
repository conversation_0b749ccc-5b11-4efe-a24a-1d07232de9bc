# SEO Module Technical Implementation

This document provides technical details about the implementation of the SEO module.

## Database Schema

The SEO module is built around a central `seo_meta` table which stores metadata for any content type.

### Table Structure

```sql
CREATE TABLE `seo_meta` (
  `meta_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,             -- Primary key
  `object_id` INT UNSIGNED NOT NULL,                          -- Foreign key to content
  `object_type` VARCHAR(50) NOT NULL DEFAULT 'post',          -- Content type identifier
  `seo_title` TEXT DEFAULT NULL,                              -- SEO title
  `meta_description` TEXT DEFAULT NULL,                       -- Meta description
  `focus_keyphrase` VARCHAR(255) DEFAULT NULL,                -- Focus keyword
  `canonical_url` VARCHAR(2083) DEFAULT NULL,                 -- Canonical URL
  `is_cornerstone` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,    -- Cornerstone content flag
  
  -- Open Graph data
  `og_title` VARCHAR(255) DEFAULT NULL,                       -- OG title
  `og_description` TEXT DEFAULT NULL,                         -- OG description
  `og_image` VARCHAR(2083) DEFAULT NULL,                      -- OG image URL
  
  -- Twitter Card data
  `twitter_title` VARCHAR(255) DEFAULT NULL,                  -- Twitter title
  `twitter_description` TEXT DEFAULT NULL,                    -- Twitter description
  `twitter_image` VARCHAR(2083) DEFAULT NULL,                 -- Twitter image URL
  
  -- Robots directives
  `robots_index` ENUM('index', 'noindex', 'default') DEFAULT 'default',
  `robots_follow` ENUM('follow', 'nofollow', 'default') DEFAULT 'default',
  `robots_advanced` VARCHAR(255) DEFAULT NULL,                -- Additional directives
  
  -- Performance metrics
  `seo_score` VARCHAR(50) DEFAULT NULL,                       -- SEO score
  `readability_score` VARCHAR(50) DEFAULT NULL,               -- Readability score
  
  -- Structured data
  `schema_data` JSON DEFAULT NULL,                            -- Schema.org data
  
  `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  PRIMARY KEY (`meta_id`),
  UNIQUE KEY `idx_object_id_type` (`object_id`, `object_type`),
  KEY `idx_object_id` (`object_id`),
  KEY `idx_object_type` (`object_type`),
  KEY `idx_focus_keyphrase` (`focus_keyphrase`(191))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Component Architecture

The SEO module follows a layered architecture:

```
API Controller → Service → Repository → Model → Database
```

### Models

`SeoMeta` is the main model representing a row in the `seo_meta` table. It includes a custom type `SchemaData` for handling JSON schema data.

```go
// SeoMeta represents a row in the seo_meta table
type SeoMeta struct {
    MetaID             uint       `json:"meta_id" gorm:"primaryKey;column:meta_id"`
    ObjectID           uint       `json:"object_id" gorm:"column:object_id"`
    ObjectType         string     `json:"object_type" gorm:"column:object_type;default:post"`
    // ... other fields
    SchemaData         SchemaData `json:"schema_data" gorm:"column:schema_data"`
    LastUpdated        time.Time  `json:"last_updated" gorm:"column:last_updated"`
}

// SchemaData represents JSON schema data
type SchemaData map[string]interface{}

// Custom Value and Scan methods for SchemaData...
```

### Repository

The repository layer handles database operations and implements the following interface:

```go
type SeoMetaRepository interface {
    Create(ctx context.Context, meta *models.SeoMeta) error
    GetByID(ctx context.Context, id uint) (*models.SeoMeta, error)
    GetByObject(ctx context.Context, objectID uint, objectType string) (*models.SeoMeta, error)
    Update(ctx context.Context, meta *models.SeoMeta) error
    Delete(ctx context.Context, id uint) error
    DeleteByObject(ctx context.Context, objectID uint, objectType string) error
}
```

### Service

The service layer implements business logic and transactions:

```go
type SeoMetaService struct {
    repo repository.SeoMetaRepository
}

// Methods for handling SEO metadata operations
// - CreateSeoMeta
// - GetSeoMetaByID
// - GetSeoMetaByObject
// - UpdateSeoMeta
// - DeleteSeoMeta
// - DeleteSeoMetaByObject
// - CreateOrUpdateSeoMeta
```

### API Controllers

The API layer handles HTTP requests and responses:

```go
type SeoMetaHandler struct {
    seoMetaService *service.SeoMetaService
}

// HTTP handlers for each endpoint
// - GetSeoMeta
// - GetSeoMetaByObject
// - CreateSeoMeta
// - UpdateSeoMeta
// - DeleteSeoMeta
// - DeleteSeoMetaByObject
// - CreateOrUpdateSeoMeta
```

## Request/Response DTOs

The module uses separate DTOs for requests and responses:

### Request DTOs

```go
// GetSeoMetaRequest - Used for fetching by object
type GetSeoMetaRequest struct {
    ObjectID   uint   `form:"object_id" binding:"required"`
    ObjectType string `form:"object_type" binding:"required"`
}

// CreateSeoMetaRequest - Used for creating new metadata
type CreateSeoMetaRequest struct {
    ObjectID           uint                  `json:"object_id" binding:"required"`
    ObjectType         string                `json:"object_type" binding:"required"`
    SeoTitle           string                `json:"seo_title"`
    // ... other fields
    SchemaData         models.SchemaData     `json:"schema_data"`
}

// UpdateSeoMetaRequest - Used for updating metadata
type UpdateSeoMetaRequest struct {
    // Similar to CreateSeoMetaRequest but without required validations on ObjectID/Type
}
```

### Response DTOs

```go
// SeoMetaResponse - Response format for SEO metadata
func FromModel(meta models.SeoMeta) interface{} {
    // Maps model to response format
    return meta
}
```

## Dependencies

The SEO module has minimal dependencies:

- Database access through GORM
- HTTP routing through Gin
- Standard Go libraries

## Error Handling

The module uses standardized error responses:

```go
// Example error handling in API layer
if err != nil {
    response.Error(c, http.StatusNotFound, "Không tìm thấy SeoMeta", "SEO_META_NOT_FOUND")
    return
}
```

## Performance Considerations

- Indexed fields for optimized queries
- Unique constraints to maintain data integrity
- JSON field for flexible schema data storage