# SEO Module API Reference

This document describes the API endpoints provided by the SEO module.

## Base URL

All endpoints are relative to `/api/v1/seo/`.

## Authentication

All endpoints require proper authentication using JWT token.

## Endpoints

### Get SEO Metadata by ID

Retrieves SEO metadata by its unique identifier.

```
GET /meta/:id
```

#### Parameters

| Name | Located in | Description | Required | Schema |
|------|------------|-------------|----------|--------|
| id   | path       | Unique identifier of the SEO metadata | Yes | integer |

#### Responses

| Code | Description | Schema |
|------|-------------|--------|
| 200  | Success     | SeoMetaResponse |
| 404  | SEO metadata not found | ErrorResponse |

---

### Get SEO Metadata by Object

Retrieves SEO metadata by object ID and object type.

```
GET /meta
```

#### Parameters

| Name | Located in | Description | Required | Schema |
|------|------------|-------------|----------|--------|
| object_id   | query | ID of the associated object | Yes | integer |
| object_type | query | Type of the associated object | Yes | string |

#### Responses

| Code | Description | Schema |
|------|-------------|--------|
| 200  | Success     | SeoMetaResponse |
| 404  | SEO metadata not found | ErrorResponse |

---

### Create SEO Metadata

Creates new SEO metadata for an object.

```
POST /meta
```

#### Request Body

| Name | Description | Required | Schema |
|------|-------------|----------|--------|
| object_id | ID of the associated object | Yes | integer |
| object_type | Type of the associated object | Yes | string |
| seo_title | Title for SEO | No | string |
| meta_description | Meta description | No | string |
| focus_keyphrase | Main keyword phrase | No | string |
| canonical_url | Canonical URL | No | string |
| is_cornerstone | Whether this is cornerstone content | No | integer (0 or 1) |
| og_title | Open Graph title | No | string |
| og_description | Open Graph description | No | string |
| og_image | Open Graph image URL | No | string |
| twitter_title | Twitter Card title | No | string |
| twitter_description | Twitter Card description | No | string |
| twitter_image | Twitter Card image URL | No | string |
| robots_index | Robots index directive | No | string (index, noindex, default) |
| robots_follow | Robots follow directive | No | string (follow, nofollow, default) |
| robots_advanced | Advanced robots directives | No | string |
| seo_score | SEO score | No | string |
| readability_score | Readability score | No | string |
| schema_data | Schema.org structured data | No | object |

#### Responses

| Code | Description | Schema |
|------|-------------|--------|
| 200  | Success     | SeoMetaResponse |
| 400  | Invalid request | ErrorResponse |

---

### Update SEO Metadata

Updates existing SEO metadata.

```
PUT /meta/:id
```

#### Parameters

| Name | Located in | Description | Required | Schema |
|------|------------|-------------|----------|--------|
| id   | path       | Unique identifier of the SEO metadata | Yes | integer |

#### Request Body

Same as Create SEO Metadata request body.

#### Responses

| Code | Description | Schema |
|------|-------------|--------|
| 200  | Success     | SeoMetaResponse |
| 400  | Invalid request | ErrorResponse |
| 404  | SEO metadata not found | ErrorResponse |

---

### Delete SEO Metadata

Deletes SEO metadata by its ID.

```
DELETE /meta/:id
```

#### Parameters

| Name | Located in | Description | Required | Schema |
|------|------------|-------------|----------|--------|
| id   | path       | Unique identifier of the SEO metadata | Yes | integer |

#### Responses

| Code | Description | Schema |
|------|-------------|--------|
| 200  | Success     | object (empty) |
| 404  | SEO metadata not found | ErrorResponse |

---

### Delete SEO Metadata by Object

Deletes SEO metadata by object ID and object type.

```
DELETE /meta
```

#### Request Body

| Name | Description | Required | Schema |
|------|-------------|----------|--------|
| object_id | ID of the associated object | Yes | integer |
| object_type | Type of the associated object | Yes | string |

#### Responses

| Code | Description | Schema |
|------|-------------|--------|
| 200  | Success     | object (empty) |
| 400  | Invalid request | ErrorResponse |

---

### Create or Update SEO Metadata (Upsert)

Creates new SEO metadata or updates existing one if metadata for the given object already exists.

```
POST /meta/upsert
```

#### Request Body

Same as Create SEO Metadata request body.

#### Responses

| Code | Description | Schema |
|------|-------------|--------|
| 200  | Success     | SeoMetaResponse |
| 400  | Invalid request | ErrorResponse |

## Response Objects

### SeoMetaResponse

```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/v1/seo/meta/1",
    "timestamp": "2025-03-15T14:35:22Z",
    "details": null
  },
  "data": {
    "meta_id": 1,
    "object_id": 123,
    "object_type": "post",
    "seo_title": "Example SEO Title",
    "meta_description": "This is an example meta description for SEO purposes.",
    "focus_keyphrase": "example SEO metadata",
    "canonical_url": "https://example.com/posts/123",
    "is_cornerstone": 0,
    "og_title": "Example Open Graph Title",
    "og_description": "This is an example open graph description.",
    "og_image": "https://example.com/images/og-image.jpg",
    "twitter_title": "Example Twitter Card Title",
    "twitter_description": "This is an example twitter card description.",
    "twitter_image": "https://example.com/images/twitter-image.jpg",
    "robots_index": "index",
    "robots_follow": "follow",
    "robots_advanced": "noimageindex",
    "seo_score": "good",
    "readability_score": "ok",
    "schema_data": {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": "Example Article",
      "author": {
        "@type": "Person",
        "name": "John Doe"
      }
    },
    "last_updated": "2025-03-15T14:35:22Z"
  }
}
```

### ErrorResponse

```json
{
  "status": {
    "code": 404,
    "message": "Resource not found",
    "success": false,
    "error_code": "SEO_META_NOT_FOUND",
    "path": "/api/v1/seo/meta/999",
    "timestamp": "2025-03-15T14:30:45Z",
    "details": [
      {
        "field": "id",
        "message": "No SEO metadata found with ID 999"
      }
    ]
  }
}
```