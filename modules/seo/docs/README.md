# SEO Module

The SEO Module provides functionality for managing Search Engine Optimization (SEO) metadata for various content types within the application. It allows for the storage and retrieval of SEO-related information such as meta titles, descriptions, Open Graph data, Twitter cards, robots directives, and structured data.

## Overview

The SEO module is designed to be content-agnostic, meaning it can attach SEO metadata to any type of content in the system through the use of `object_id` and `object_type` fields. This makes it highly reusable across different content types such as blog posts, pages, products, categories, and more.

## Key Features

- **Content-agnostic SEO metadata** - Can be attached to any content type
- **Meta tags management** - Title, description, canonical URLs
- **Social media optimization** - Open Graph and Twitter Card support
- **Search engine directives** - Robots meta tag controls
- **Schema.org structured data** - JSON-LD implementation
- **SEO performance scoring** - Track readability and SEO scores

## Architecture

The SEO module consists of:

- **Models**: Data structures for SEO metadata
- **Repositories**: Database access layer
- **Services**: Business logic layer
- **API**: RESTful endpoints for CRUD operations
- **DTOs**: Data transfer objects for requests and responses

## Related Documentation

- [API Reference](./api-reference.md) - API endpoints documentation
- [Technical Implementation](./implementation.md) - Technical details
- [Integration Guide](./integration.md) - How to integrate with other modules