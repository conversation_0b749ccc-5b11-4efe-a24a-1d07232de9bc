CREATE TABLE `seo_meta` (
  `meta_id` INT UNSIGNED NOT NULL AUTO_INCREMENT,               -- <PERSON><PERSON><PERSON><PERSON> ch<PERSON>h của bảng meta
  `object_id` INT UNSIGNED NOT NULL,                           -- <PERSON><PERSON><PERSON><PERSON> ngo<PERSON>, li<PERSON><PERSON> kết đến ID của nội dung (vd: post_id từ bảng posts)
  `object_type` VARCHAR(50) NOT NULL DEFAULT 'post',              -- <PERSON><PERSON><PERSON> nộ<PERSON> dung (vd: 'post', 'page', 'product', 'category') để có thể mở rộng
  `seo_title` TEXT DEFAULT NULL,                                  -- Ti<PERSON><PERSON> đ<PERSON> (thẻ <title>)
  `meta_description` TEXT DEFAULT NULL,                           -- <PERSON><PERSON> <PERSON>ả <PERSON> (thẻ <meta name="description">)
  `focus_keyphrase` VARCHAR(255) DEFAULT NULL,                    -- Từ khóa tập trung chính
  `canonical_url` VARCHAR(2083) DEFAULT NULL,                     -- <PERSON><PERSON> chuẩn (Canonical URL)
  `is_cornerstone` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,        -- <PERSON><PERSON><PERSON> dấu là nội dung Cornerstone (0 = No, 1 = Yes)

  -- <PERSON><PERSON> liệu Open Graph (Facebook, LinkedIn, ...)
  `og_title` VARCHAR(255) DEFAULT NULL,
  `og_description` TEXT DEFAULT NULL,
  `og_image` VARCHAR(2083) DEFAULT NULL,                          -- URL của hình ảnh Open Graph

  -- Dữ liệu Twitter Card
  `twitter_title` VARCHAR(255) DEFAULT NULL,
  `twitter_description` TEXT DEFAULT NULL,
  `twitter_image` VARCHAR(2083) DEFAULT NULL,                     -- URL của hình ảnh Twitter Card

  -- Cài đặt Robots Meta
  `robots_index` ENUM('index', 'noindex', 'default') DEFAULT 'default', -- Cho phép/không cho phép index (default = theo cài đặt chung)
  `robots_follow` ENUM('follow', 'nofollow', 'default') DEFAULT 'default',-- Cho phép/không cho phép follow links (default = theo cài đặt chung)
  `robots_advanced` VARCHAR(255) DEFAULT NULL,                    -- Các chỉ thị robots nâng cao khác (vd: noimageindex, noarchive, nosnippet)

  -- Điểm số (Lưu dưới dạng VARCHAR để linh hoạt hơn, vd: "good", "ok", "bad" hoặc số)
  `seo_score` VARCHAR(50) DEFAULT NULL,
  `readability_score` VARCHAR(50) DEFAULT NULL,

  -- Schema Markup (Lưu dưới dạng JSON nếu phiên bản MySQL hỗ trợ, nếu không thì TEXT)
  `schema_data` JSON DEFAULT NULL,                                -- Dữ liệu Schema.org tùy chỉnh (nếu có)

  `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Thời gian cập nhật lần cuối

  PRIMARY KEY (`meta_id`),

  -- Đảm bảo mỗi object_id + object_type chỉ có một dòng meta duy nhất
  UNIQUE KEY `idx_object_id_type` (`object_id`, `object_type`),

  -- Index cho khóa ngoại để tăng tốc độ JOIN và truy vấn
  KEY `idx_object_id` (`object_id`),
  KEY `idx_object_type` (`object_type`),
  KEY `idx_focus_keyphrase` (`focus_keyphrase`(191)) -- Index trên keyphrase (giới hạn độ dài để tương thích)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 