package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel/attribute"

	"github.com/webnew/wn-backend-v2/modules/seo/dto/request"
	seoResponse "github.com/webnew/wn-backend-v2/modules/seo/dto/response"
	"github.com/webnew/wn-backend-v2/modules/seo/models"
	"github.com/webnew/wn-backend-v2/modules/seo/service"
	"github.com/webnew/wn-backend-v2/modules/seo/tracing"
	"github.com/webnew/wn-backend-v2/pkg/response"
)

// SeoMetaHandler xử lý các request liên quan đến SeoMeta
type SeoMetaHandler struct {
	seoMetaService *service.SeoMetaService
}

// NewSeoMetaHandler tạo một instance mới của SeoMetaHandler
func NewSeoMetaHandler(seoMetaService *service.SeoMetaService) *SeoMetaHandler {
	return &SeoMetaHandler{
		seoMetaService: seoMetaService,
	}
}

// GetSeoMeta xử lý request lấy thông tin SeoMeta theo ID
func (h *SeoMetaHandler) GetSeoMeta(c *gin.Context) {
	ctx, span := tracing.StartSeoSpan(c.Request.Context(), "API.GetSeoMeta", tracing.SeoOpMetatagGeneration)
	defer span.End()

	idStr := c.Param("id")
	span.SetAttributes(attribute.String("http.route_param.id", idStr))

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		tracing.RecordError(ctx, span, err, "Invalid ID format")
		response.ErrorWithDetails(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID", nil)
		return
	}

	span.SetAttributes(attribute.Int64("seo.meta_id", int64(id)))

	meta, err := h.seoMetaService.GetSeoMetaByID(ctx, uint(id))
	if err != nil {
		tracing.RecordError(ctx, span, err, "SeoMeta not found")
		response.ErrorWithDetails(c, http.StatusNotFound, "Không tìm thấy SeoMeta", "SEO_META_NOT_FOUND", nil)
		return
	}

	metaResponse := seoResponse.FromModel(*meta)
	response.Success(c, http.StatusOK, "Lấy SeoMeta thành công", metaResponse)
}

// GetSeoMetaByObject xử lý request lấy thông tin SeoMeta theo object_id và object_type
func (h *SeoMetaHandler) GetSeoMetaByObject(c *gin.Context) {
	ctx, span := tracing.StartSeoSpan(c.Request.Context(), "API.GetSeoMetaByObject", tracing.SeoOpMetatagGeneration)
	defer span.End()

	var req request.GetSeoMetaRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		tracing.RecordError(ctx, span, err, "Invalid request data")
		response.ErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_REQUEST", nil)
		return
	}

	span.SetAttributes(
		attribute.Int64("seo.object_id", int64(req.ObjectID)),
		attribute.String("seo.object_type", req.ObjectType),
	)

	meta, err := h.seoMetaService.GetSeoMetaByObject(ctx, req.ObjectID, req.ObjectType)
	if err != nil {
		tracing.RecordError(ctx, span, err, "SeoMeta not found for object")
		response.ErrorWithDetails(c, http.StatusNotFound, "Không tìm thấy SeoMeta", "SEO_META_NOT_FOUND", nil)
		return
	}

	span.SetAttributes(attribute.Int64("seo.meta_id", int64(meta.MetaID)))

	metaResponse := seoResponse.FromModel(*meta)
	response.Success(c, http.StatusOK, "Lấy SeoMeta thành công", metaResponse)
}

// CreateSeoMeta xử lý request tạo SeoMeta mới
func (h *SeoMetaHandler) CreateSeoMeta(c *gin.Context) {
	ctx, span := tracing.StartSeoSpan(c.Request.Context(), "API.CreateSeoMeta", tracing.SeoOpMetatagGeneration)
	defer span.End()

	var req request.CreateSeoMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordError(ctx, span, err, "Invalid request data")
		response.ErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_REQUEST", nil)
		return
	}

	span.SetAttributes(
		attribute.Int64("seo.object_id", int64(req.ObjectID)),
		attribute.String("seo.object_type", req.ObjectType),
	)

	// Chuyển đổi từ request sang model
	meta := models.SeoMeta{
		ObjectID:           req.ObjectID,
		ObjectType:         req.ObjectType,
		SeoTitle:           req.SeoTitle,
		MetaDescription:    req.MetaDescription,
		FocusKeyphrase:     req.FocusKeyphrase,
		CanonicalURL:       req.CanonicalURL,
		IsCornerstone:      req.IsCornerstone,
		OgTitle:            req.OgTitle,
		OgDescription:      req.OgDescription,
		OgImage:            req.OgImage,
		TwitterTitle:       req.TwitterTitle,
		TwitterDescription: req.TwitterDescription,
		TwitterImage:       req.TwitterImage,
		RobotsIndex:        req.RobotsIndex,
		RobotsFollow:       req.RobotsFollow,
		RobotsAdvanced:     req.RobotsAdvanced,
		SeoScore:           req.SeoScore,
		ReadabilityScore:   req.ReadabilityScore,
		SchemaData:         req.SchemaData,
		LastUpdated:        time.Now(),
	}

	startTime := time.Now()
	err := h.seoMetaService.CreateSeoMeta(ctx, &meta)
	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("api.duration_ms", duration))

	if err != nil {
		tracing.RecordError(ctx, span, err, "Failed to create SeoMeta")
		response.ErrorWithDetails(c, http.StatusBadRequest, "Không thể tạo SeoMeta: "+err.Error(), "CANNOT_CREATE_SEO_META", nil)
		return
	}

	// Lấy dữ liệu đã tạo
	createdMeta, err := h.seoMetaService.GetSeoMetaByID(ctx, meta.MetaID)
	if err != nil {
		tracing.RecordError(ctx, span, err, "Failed to retrieve created SeoMeta")
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Lỗi khi lấy dữ liệu đã tạo", "INTERNAL_ERROR", nil)
		return
	}

	span.SetAttributes(attribute.Int64("seo.meta_id", int64(createdMeta.MetaID)))

	metaResponse := seoResponse.FromModel(*createdMeta)
	response.Success(c, http.StatusCreated, "Tạo SeoMeta thành công", metaResponse)
}

// UpdateSeoMeta xử lý request cập nhật SeoMeta
func (h *SeoMetaHandler) UpdateSeoMeta(c *gin.Context) {
	ctx, span := tracing.StartSeoSpan(c.Request.Context(), "API.UpdateSeoMeta", tracing.SeoOpMetatagGeneration)
	defer span.End()

	idStr := c.Param("id")
	span.SetAttributes(attribute.String("http.route_param.id", idStr))

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		tracing.RecordError(ctx, span, err, "Invalid ID format")
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID")
		return
	}

	span.SetAttributes(attribute.Int64("seo.meta_id", int64(id)))

	var req request.UpdateSeoMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordError(ctx, span, err, "Invalid request data")
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_REQUEST")
		return
	}

	// Lấy dữ liệu hiện tại
	existingMeta, err := h.seoMetaService.GetSeoMetaByID(ctx, uint(id))
	if err != nil {
		tracing.RecordError(ctx, span, err, "SeoMeta not found")
		response.Error(c, http.StatusNotFound, "Không tìm thấy SeoMeta", "SEO_META_NOT_FOUND")
		return
	}

	span.SetAttributes(
		attribute.Int64("seo.object_id", int64(existingMeta.ObjectID)),
		attribute.String("seo.object_type", existingMeta.ObjectType),
	)

	// Cập nhật các trường
	existingMeta.SeoTitle = req.SeoTitle
	existingMeta.MetaDescription = req.MetaDescription
	existingMeta.FocusKeyphrase = req.FocusKeyphrase
	existingMeta.CanonicalURL = req.CanonicalURL
	existingMeta.IsCornerstone = req.IsCornerstone
	existingMeta.OgTitle = req.OgTitle
	existingMeta.OgDescription = req.OgDescription
	existingMeta.OgImage = req.OgImage
	existingMeta.TwitterTitle = req.TwitterTitle
	existingMeta.TwitterDescription = req.TwitterDescription
	existingMeta.TwitterImage = req.TwitterImage
	existingMeta.RobotsIndex = req.RobotsIndex
	existingMeta.RobotsFollow = req.RobotsFollow
	existingMeta.RobotsAdvanced = req.RobotsAdvanced
	existingMeta.SeoScore = req.SeoScore
	existingMeta.ReadabilityScore = req.ReadabilityScore
	existingMeta.SchemaData = req.SchemaData
	existingMeta.LastUpdated = time.Now()

	startTime := time.Now()
	// Lưu thay đổi
	err = h.seoMetaService.UpdateSeoMeta(ctx, existingMeta)
	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("api.duration_ms", duration))

	if err != nil {
		tracing.RecordError(ctx, span, err, "Failed to update SeoMeta")
		response.Error(c, http.StatusBadRequest, "Không thể cập nhật SeoMeta: "+err.Error(), "CANNOT_UPDATE_SEO_META")
		return
	}

	// Lấy dữ liệu đã cập nhật
	updatedMeta, err := h.seoMetaService.GetSeoMetaByID(ctx, uint(id))
	if err != nil {
		tracing.RecordError(ctx, span, err, "Failed to retrieve updated SeoMeta")
		response.Error(c, http.StatusInternalServerError, "Lỗi khi lấy dữ liệu đã cập nhật", "INTERNAL_ERROR")
		return
	}

	response.Success(c, response.FromModel(*updatedMeta))
}

// DeleteSeoMeta xử lý request xóa SeoMeta
func (h *SeoMetaHandler) DeleteSeoMeta(c *gin.Context) {
	ctx, span := tracing.StartSeoSpan(c.Request.Context(), "API.DeleteSeoMeta", tracing.SeoOpMetatagGeneration)
	defer span.End()

	idStr := c.Param("id")
	span.SetAttributes(attribute.String("http.route_param.id", idStr))

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		tracing.RecordError(ctx, span, err, "Invalid ID format")
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID")
		return
	}

	span.SetAttributes(attribute.Int64("seo.meta_id", int64(id)))

	startTime := time.Now()
	err = h.seoMetaService.DeleteSeoMeta(ctx, uint(id))
	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("api.duration_ms", duration))

	if err != nil {
		tracing.RecordError(ctx, span, err, "Failed to delete SeoMeta")
		response.Error(c, http.StatusBadRequest, "Không thể xóa SeoMeta: "+err.Error(), "CANNOT_DELETE_SEO_META")
		return
	}

	response.Success(c, nil)
}

// DeleteSeoMetaByObject xử lý request xóa SeoMeta theo object_id và object_type
func (h *SeoMetaHandler) DeleteSeoMetaByObject(c *gin.Context) {
	ctx, span := tracing.StartSeoSpan(c.Request.Context(), "API.DeleteSeoMetaByObject", tracing.SeoOpMetatagGeneration)
	defer span.End()

	var req request.GetSeoMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordError(ctx, span, err, "Invalid request data")
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_REQUEST")
		return
	}

	span.SetAttributes(
		attribute.Int64("seo.object_id", int64(req.ObjectID)),
		attribute.String("seo.object_type", req.ObjectType),
	)

	startTime := time.Now()
	err := h.seoMetaService.DeleteSeoMetaByObject(ctx, req.ObjectID, req.ObjectType)
	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("api.duration_ms", duration))

	if err != nil {
		tracing.RecordError(ctx, span, err, "Failed to delete SeoMeta by object")
		response.Error(c, http.StatusBadRequest, "Không thể xóa SeoMeta: "+err.Error(), "CANNOT_DELETE_SEO_META")
		return
	}

	response.Success(c, nil)
}

// CreateOrUpdateSeoMeta xử lý request tạo mới hoặc cập nhật SeoMeta
func (h *SeoMetaHandler) CreateOrUpdateSeoMeta(c *gin.Context) {
	ctx, span := tracing.StartSeoSpan(c.Request.Context(), "API.CreateOrUpdateSeoMeta", tracing.SeoOpMetatagGeneration)
	defer span.End()

	var req request.CreateSeoMetaRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		tracing.RecordError(ctx, span, err, "Invalid request data")
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_REQUEST")
		return
	}

	span.SetAttributes(
		attribute.Int64("seo.object_id", int64(req.ObjectID)),
		attribute.String("seo.object_type", req.ObjectType),
	)

	// Chuyển đổi từ request sang model
	meta := models.SeoMeta{
		ObjectID:           req.ObjectID,
		ObjectType:         req.ObjectType,
		SeoTitle:           req.SeoTitle,
		MetaDescription:    req.MetaDescription,
		FocusKeyphrase:     req.FocusKeyphrase,
		CanonicalURL:       req.CanonicalURL,
		IsCornerstone:      req.IsCornerstone,
		OgTitle:            req.OgTitle,
		OgDescription:      req.OgDescription,
		OgImage:            req.OgImage,
		TwitterTitle:       req.TwitterTitle,
		TwitterDescription: req.TwitterDescription,
		TwitterImage:       req.TwitterImage,
		RobotsIndex:        req.RobotsIndex,
		RobotsFollow:       req.RobotsFollow,
		RobotsAdvanced:     req.RobotsAdvanced,
		SeoScore:           req.SeoScore,
		ReadabilityScore:   req.ReadabilityScore,
		SchemaData:         req.SchemaData,
		LastUpdated:        time.Now(),
	}

	startTime := time.Now()
	err := h.seoMetaService.CreateOrUpdateSeoMeta(ctx, &meta)
	duration := time.Since(startTime).Milliseconds()
	span.SetAttributes(attribute.Int64("api.duration_ms", duration))

	if err != nil {
		tracing.RecordError(ctx, span, err, "Failed to create or update SeoMeta")
		response.Error(c, http.StatusBadRequest, "Không thể tạo hoặc cập nhật SeoMeta: "+err.Error(), "CANNOT_CREATE_UPDATE_SEO_META")
		return
	}

	// Lấy dữ liệu đã tạo/cập nhật
	updatedMeta, err := h.seoMetaService.GetSeoMetaByObject(ctx, req.ObjectID, req.ObjectType)
	if err != nil {
		tracing.RecordError(ctx, span, err, "Failed to retrieve updated SeoMeta")
		response.Error(c, http.StatusInternalServerError, "Lỗi khi lấy dữ liệu", "INTERNAL_ERROR")
		return
	}

	span.SetAttributes(attribute.Int64("seo.meta_id", int64(updatedMeta.MetaID)))

	response.Success(c, response.FromModel(*updatedMeta))
}
