package repository

import (
	"context"

	"github.com/webnew/wn-backend-v2/modules/seo/models"
)

// SeoMetaRepository định nghĩa interface cho repository xử lý SeoMeta
type SeoMetaRepository interface {
	Create(ctx context.Context, meta *models.SeoMeta) error
	GetByID(ctx context.Context, id uint) (*models.SeoMeta, error)
	GetByObject(ctx context.Context, objectID uint, objectType string) (*models.SeoMeta, error)
	Update(ctx context.Context, meta *models.SeoMeta) error
	Delete(ctx context.Context, id uint) error
	DeleteByObject(ctx context.Context, objectID uint, objectType string) error
}
