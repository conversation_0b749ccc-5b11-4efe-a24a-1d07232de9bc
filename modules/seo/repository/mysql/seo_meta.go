package mysql

import (
	"context"
	"errors"
	"fmt"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/seo/models"
)

// SeoMetaRepository đại diện cho repository xử lý dữ liệu SeoMeta trong MySQL
type SeoMetaRepository struct {
	db *sqlx.DB
}

// NewSeoMetaRepository tạo một instance mới của SeoMetaRepository
func NewSeoMetaRepository(db *sqlx.DB) *SeoMetaRepository {
	return &SeoMetaRepository{db: db}
}

// Create tạo một bản ghi SeoMeta mới
func (r *SeoMetaRepository) Create(ctx context.Context, meta *models.SeoMeta) error {
	query := `
		INSERT INTO seo_meta (
			object_id, object_type, seo_title, meta_description, focus_keyphrase, 
			canonical_url, is_cornerstone, og_title, og_description, og_image, 
			twitter_title, twitter_description, twitter_image, robots_index, 
			robots_follow, robots_advanced, seo_score, readability_score, schema_data
		) VALUES (
			:object_id, :object_type, :seo_title, :meta_description, :focus_keyphrase, 
			:canonical_url, :is_cornerstone, :og_title, :og_description, :og_image, 
			:twitter_title, :twitter_description, :twitter_image, :robots_index, 
			:robots_follow, :robots_advanced, :seo_score, :readability_score, :schema_data
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, meta)
	if err != nil {
		return fmt.Errorf("không thể tạo bản ghi SeoMeta: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("không thể lấy ID của bản ghi vừa tạo: %w", err)
	}

	meta.MetaID = uint(id)
	return nil
}

// GetByID lấy SeoMeta theo ID
func (r *SeoMetaRepository) GetByID(ctx context.Context, id uint) (*models.SeoMeta, error) {
	var meta models.SeoMeta
	query := "SELECT * FROM seo_meta WHERE meta_id = ?"

	err := r.db.GetContext(ctx, &meta, query, id)
	if err != nil {
		return nil, fmt.Errorf("không thể tìm thấy SeoMeta với ID %d: %w", id, err)
	}

	return &meta, nil
}

// GetByObject lấy SeoMeta theo object_id và object_type
func (r *SeoMetaRepository) GetByObject(ctx context.Context, objectID uint, objectType string) (*models.SeoMeta, error) {
	var meta models.SeoMeta
	query := "SELECT * FROM seo_meta WHERE object_id = ? AND object_type = ?"

	err := r.db.GetContext(ctx, &meta, query, objectID, objectType)
	if err != nil {
		return nil, fmt.Errorf("không thể tìm thấy SeoMeta cho %s ID %d: %w", objectType, objectID, err)
	}

	return &meta, nil
}

// Update cập nhật thông tin SeoMeta
func (r *SeoMetaRepository) Update(ctx context.Context, meta *models.SeoMeta) error {
	query := `
		UPDATE seo_meta SET
			seo_title = :seo_title,
			meta_description = :meta_description,
			focus_keyphrase = :focus_keyphrase,
			canonical_url = :canonical_url,
			is_cornerstone = :is_cornerstone,
			og_title = :og_title,
			og_description = :og_description,
			og_image = :og_image,
			twitter_title = :twitter_title,
			twitter_description = :twitter_description,
			twitter_image = :twitter_image,
			robots_index = :robots_index,
			robots_follow = :robots_follow,
			robots_advanced = :robots_advanced,
			seo_score = :seo_score,
			readability_score = :readability_score,
			schema_data = :schema_data
		WHERE meta_id = :meta_id
	`

	result, err := r.db.NamedExecContext(ctx, query, meta)
	if err != nil {
		return fmt.Errorf("không thể cập nhật SeoMeta: %w", err)
	}

	count, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("lỗi khi kiểm tra số dòng bị ảnh hưởng: %w", err)
	}

	if count == 0 {
		return errors.New("không tìm thấy bản ghi SeoMeta để cập nhật")
	}

	return nil
}

// Delete xóa SeoMeta theo ID
func (r *SeoMetaRepository) Delete(ctx context.Context, id uint) error {
	query := "DELETE FROM seo_meta WHERE meta_id = ?"
	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("không thể xóa SeoMeta: %w", err)
	}

	count, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("lỗi khi kiểm tra số dòng bị ảnh hưởng: %w", err)
	}

	if count == 0 {
		return errors.New("không tìm thấy bản ghi SeoMeta để xóa")
	}

	return nil
}

// DeleteByObject xóa SeoMeta theo object_id và object_type
func (r *SeoMetaRepository) DeleteByObject(ctx context.Context, objectID uint, objectType string) error {
	query := "DELETE FROM seo_meta WHERE object_id = ? AND object_type = ?"
	result, err := r.db.ExecContext(ctx, query, objectID, objectType)
	if err != nil {
		return fmt.Errorf("không thể xóa SeoMeta cho %s ID %d: %w", objectType, objectID, err)
	}

	count, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("lỗi khi kiểm tra số dòng bị ảnh hưởng: %w", err)
	}

	if count == 0 {
		return errors.New("không tìm thấy bản ghi SeoMeta để xóa")
	}

	return nil
}
