-- <PERSON>ác thẻ tag chung cho tất cả tenant
INSERT INTO blog_tags (tenant_id, name, slug, description, is_active, created_at, updated_at) VALUES
(1, 'PHP', 'php', 'Ngôn ngữ lập trình PHP', TRUE, NOW(), NOW()),
(1, 'JavaScript', 'javascript', 'Ngôn ngữ lập trình JavaScript', TRUE, NOW(), NOW()),
(1, 'Golang', 'golang', 'Ngôn ngữ lập trình Go', TRUE, NOW(), NOW()),
(1, 'React', 'react', 'Thư viện JavaScript React', TRUE, NOW(), NOW()),
(1, 'Vue', 'vue', 'Framework JavaScript Vue', TRUE, NOW(), NOW()),
(1, 'Angular', 'angular', 'Framework JavaScript Angular', TRUE, NOW(), NOW()),
(1, '<PERSON><PERSON>', 'laravel', 'Framework PHP Laravel', TRUE, NOW(), NOW()),
(1, 'Gin', 'gin', 'Framework Go Gin', TRUE, NOW(), NOW()),
(1, 'MySQL', 'mysql', 'Hệ quản trị cơ sở dữ liệu MySQL', TRUE, NOW(), NOW()),
(1, 'PostgreSQL', 'postgresql', 'Hệ quản trị cơ sở dữ liệu PostgreSQL', TRUE, NOW(), NOW()),
(1, 'MongoDB', 'mongodb', 'Cơ sở dữ liệu NoSQL MongoDB', TRUE, NOW(), NOW()),
(1, 'Redis', 'redis', 'In-memory data structure store Redis', TRUE, NOW(), NOW()),
(1, 'Docker', 'docker', 'Nền tảng containerization Docker', TRUE, NOW(), NOW()),
(1, 'Kubernetes', 'kubernetes', 'Nền tảng điều phối container Kubernetes', TRUE, NOW(), NOW()),
(1, 'AWS', 'aws', 'Dịch vụ đám mây Amazon Web Services', TRUE, NOW(), NOW()),
(1, 'GCP', 'gcp', 'Dịch vụ đám mây Google Cloud Platform', TRUE, NOW(), NOW()),
(1, 'Azure', 'azure', 'Dịch vụ đám mây Microsoft Azure', TRUE, NOW(), NOW()),
(1, 'SEO', 'seo', 'Search Engine Optimization', TRUE, NOW(), NOW()),
(1, 'Marketing', 'marketing', 'Digital Marketing', TRUE, NOW(), NOW()),
(1, 'Bảo mật', 'bao-mat', 'Bảo mật và an ninh mạng', TRUE, NOW(), NOW()); 