package models

import (
	"time"
)

// Post represents a blog post
type Post struct {
	PostID        uint       `db:"post_id" json:"post_id" gorm:"primaryKey"`
	TenantID      uint       `db:"tenant_id" json:"tenant_id"`
	Title         string     `db:"title" json:"title"`
	Slug          string     `db:"slug" json:"slug"`
	Description   string     `db:"description" json:"description"`
	Content       string     `db:"content" json:"content"`
	FeaturedImage string     `db:"featured_image" json:"featured_image"`
	Status        string     `db:"status" json:"status"`         // draft, pending, published, private, trash
	Visibility    string     `db:"visibility" json:"visibility"` // public, private, password_protected
	Password      *string    `db:"password" json:"password,omitempty"`
	CommentStatus string     `db:"comment_status" json:"comment_status"` // open, closed
	PublishDate   *time.Time `db:"publish_date" json:"publish_date,omitempty"`
	CreatedAt     time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt     time.Time  `db:"updated_at" json:"updated_at"`
	AuthorID      uint       `db:"author_id" json:"author_id"`
	CreatedBy     *uint      `db:"created_by" json:"created_by,omitempty"`

	// Virtual fields (not stored in DB)
	CategoryIDs []uint  `db:"-" json:"category_ids,omitempty"` // Category IDs
	TagIDs      []uint  `db:"-" json:"tag_ids,omitempty"`      // Tag IDs
	Author      *User   `db:"-" json:"author,omitempty"`       // Author details
	Editor      *User   `db:"-" json:"editor,omitempty"`       // Editor details
}

// User is a minimal representation of user data for embedding in posts
type User struct {
	UserID    uint   `db:"user_id" json:"user_id" gorm:"primaryKey"`
	Username  string `db:"username" json:"username"`
	Email     string `db:"email" json:"email"`
	FirstName string `db:"first_name" json:"first_name"`
	LastName  string `db:"last_name" json:"last_name"`
	AvatarURL string `db:"avatar_url" json:"avatar_url"`
}
