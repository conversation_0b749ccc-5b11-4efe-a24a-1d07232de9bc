package service

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/webnew/wn-backend-v2/modules/blog/dto/request"
	"github.com/webnew/wn-backend-v2/modules/blog/dto/response"
	"github.com/webnew/wn-backend-v2/modules/blog/models"
	"github.com/webnew/wn-backend-v2/modules/blog/repository"
	"github.com/webnew/wn-backend-v2/pkg/auth"
)

// CategoryService defines the interface for category business logic
type CategoryService interface {
	CreateCategory(ctx context.Context, tenantID uint, req request.CreateCategoryRequest) (*response.CategoryResponse, error)
	GetCategory(ctx context.Context, tenantID uint, categoryID uint) (*response.CategoryResponse, error)
	GetCategoryBySlug(ctx context.Context, tenantID uint, slug string) (*response.CategoryResponse, error)
	UpdateCategory(ctx context.Context, tenantID uint, categoryID uint, req request.UpdateCategoryRequest) (*response.CategoryResponse, error)
	DeleteCategory(ctx context.Context, tenantID uint, categoryID uint) error

	GetCategoryTree(ctx context.Context, tenantID uint) (*response.CategoryTreeResponse, error)
	GetCategorySubtree(ctx context.Context, tenantID uint, categoryID uint) (*response.CategoryTreeResponse, error)

	MoveCategory(ctx context.Context, tenantID uint, req request.MoveCategoryRequest) error
	MoveCategoryNode(ctx context.Context, tenantID uint, req request.MoveNodeRequest) error
	UpdateCategoryPosition(ctx context.Context, tenantID uint, req request.UpdatePositionRequest) error
	MoveCategorySibling(ctx context.Context, tenantID uint, req request.UpdatePositionRequest) error
	MoveNodeRelative(ctx context.Context, tenantID uint, req request.MoveNodeRelativeRequest) error
	MoveNodeRoot(ctx context.Context, tenantID uint, req request.MoveNodeRootRequest) error
	RebuildCategoryTree(ctx context.Context, tenantID uint) error

	ListCategories(ctx context.Context, tenantID uint, req request.ListCategoryRequest) (*response.CategoryListResponse, error)
}

// categoryService implements the CategoryService interface
type categoryService struct {
	categoryRepo repository.CategoryRepository
}

// NewCategoryService creates a new CategoryService instance
func NewCategoryService(categoryRepo repository.CategoryRepository) CategoryService {
	return &categoryService{
		categoryRepo: categoryRepo,
	}
}

// CreateCategory creates a new category
func (s *categoryService) CreateCategory(ctx context.Context, tenantID uint, req request.CreateCategoryRequest) (*response.CategoryResponse, error) {
	// Create model from request
	category := &models.Category{
		TenantID:        tenantID,
		ParentID:        req.ParentID,
		Name:            req.Name,
		Slug:            req.Slug,
		Description:     req.Description,
		FeaturedImage:   req.FeaturedImage,
		IsActive:        req.IsActive,
		IsFeatured:      req.IsFeatured,
		MetaTitle:       req.MetaTitle,
		MetaDescription: req.MetaDescription,
		Position:        req.Position,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		// Assuming user ID is in context
		CreatedBy: getUserIDFromContext(ctx),
		UpdatedBy: getUserIDFromContext(ctx),
	}

	// Save to repository
	if err := s.categoryRepo.Create(ctx, category); err != nil {
		return nil, err
	}

	// Get the saved category to return with all fields populated
	savedCategory, err := s.categoryRepo.GetByID(ctx, tenantID, category.CategoryID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.mapCategoryToResponse(savedCategory), nil
}

// GetCategory retrieves a category by ID
func (s *categoryService) GetCategory(ctx context.Context, tenantID uint, categoryID uint) (*response.CategoryResponse, error) {
	// Get from repository
	category, err := s.categoryRepo.GetByID(ctx, tenantID, categoryID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.mapCategoryToResponse(category), nil
}

// GetCategoryBySlug retrieves a category by slug
func (s *categoryService) GetCategoryBySlug(ctx context.Context, tenantID uint, slug string) (*response.CategoryResponse, error) {
	// Get from repository
	category, err := s.categoryRepo.GetBySlug(ctx, tenantID, slug)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.mapCategoryToResponse(category), nil
}

// UpdateCategory updates an existing category
func (s *categoryService) UpdateCategory(ctx context.Context, tenantID uint, categoryID uint, req request.UpdateCategoryRequest) (*response.CategoryResponse, error) {
	// Get existing category
	existingCategory, err := s.categoryRepo.GetByID(ctx, tenantID, categoryID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided in request
	if req.Name != "" {
		existingCategory.Name = req.Name
	}

	if req.Slug != "" {
		existingCategory.Slug = req.Slug
	}

	if req.Description != "" {
		existingCategory.Description = req.Description
	}

	if req.FeaturedImage != "" {
		existingCategory.FeaturedImage = req.FeaturedImage
	}

	if req.IsActive != nil {
		existingCategory.IsActive = *req.IsActive
	}

	if req.IsFeatured != nil {
		existingCategory.IsFeatured = *req.IsFeatured
	}

	if req.MetaTitle != "" {
		existingCategory.MetaTitle = req.MetaTitle
	}

	if req.MetaDescription != "" {
		existingCategory.MetaDescription = req.MetaDescription
	}

	if req.Position != nil {
		existingCategory.Position = *req.Position
	}

	// Handle parent ID changes
	existingCategory.ParentID = req.ParentID

	// Set update metadata
	existingCategory.UpdatedAt = time.Now()
	existingCategory.UpdatedBy = getUserIDFromContext(ctx)

	// Save changes
	if err := s.categoryRepo.Update(ctx, existingCategory); err != nil {
		return nil, err
	}

	// Get updated category
	updatedCategory, err := s.categoryRepo.GetByID(ctx, tenantID, categoryID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	return s.mapCategoryToResponse(updatedCategory), nil
}

// DeleteCategory deletes a category
func (s *categoryService) DeleteCategory(ctx context.Context, tenantID uint, categoryID uint) error {
	return s.categoryRepo.Delete(ctx, tenantID, categoryID)
}

// GetCategoryTree retrieves the full category tree
func (s *categoryService) GetCategoryTree(ctx context.Context, tenantID uint) (*response.CategoryTreeResponse, error) {
	// Get categories đã được sắp xếp theo lft và xây dựng cấu trúc cây từ repository
	categories, err := s.categoryRepo.GetTree(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	categoriesJSON1, _ := json.MarshalIndent(categories, "", "  ")
	log.Printf("GetTree raw categories from repository: %s", string(categoriesJSON1))

	// Chuyển đổi cấu trúc cây từ repository sang response tree items
	treeItems := s.mapCategoriesToTreeItems(categories)

	treeItemsJSON, _ := json.MarshalIndent(treeItems, "", "  ")
	log.Printf("Final tree items for response: %s", string(treeItemsJSON))

	// Trả về response với cấu trúc cây (bao gồm các trường children)
	return &response.CategoryTreeResponse{
		Categories: treeItems,
	}, nil
}

// GetCategorySubtree retrieves a category subtree
func (s *categoryService) GetCategorySubtree(ctx context.Context, tenantID uint, categoryID uint) (*response.CategoryTreeResponse, error) {
	// Get flat list of categories in the subtree
	categories, err := s.categoryRepo.GetSubtree(ctx, tenantID, categoryID)
	if err != nil {
		return nil, err
	}

	// Build tree structure
	tree := buildCategoryTree(categories)

	// Convert to response
	return &response.CategoryTreeResponse{
		Categories: s.mapCategoriesToTreeItems(tree),
	}, nil
}

// MoveCategory moves a category in the tree
func (s *categoryService) MoveCategory(ctx context.Context, tenantID uint, req request.MoveCategoryRequest) error {
	return s.categoryRepo.MoveNode(ctx, tenantID, req.CategoryID, req.TargetID, req.Position)
}

// MoveCategoryNode moves a category to new parent with position
func (s *categoryService) MoveCategoryNode(ctx context.Context, tenantID uint, req request.MoveNodeRequest) error {
	return s.categoryRepo.MoveNodeProcedure(ctx, tenantID, req.CategoryID, req.NewParentID, req.Position)
}

// UpdateCategoryPosition updates the position of a category among its siblings
// @deprecated - Use MoveCategorySibling instead
func (s *categoryService) UpdateCategoryPosition(ctx context.Context, tenantID uint, req request.UpdatePositionRequest) error {
	// This is now deprecated, forward to MoveCategorySibling
	return s.MoveCategorySibling(ctx, tenantID, req)
}

// MoveCategorySibling moves a category relative to another one within the same parent
func (s *categoryService) MoveCategorySibling(ctx context.Context, tenantID uint, req request.UpdatePositionRequest) error {
	return s.categoryRepo.MoveNodeSiblingProcedure(ctx, tenantID, req.CategoryID, req.TargetID)
}

// MoveNodeRelative moves a category relative to another category (as child, before, or after)
func (s *categoryService) MoveNodeRelative(ctx context.Context, tenantID uint, req request.MoveNodeRelativeRequest) error {
	return s.categoryRepo.MoveNodeRelativeProcedure(ctx, tenantID, req.CategoryID, req.TargetID, req.Mode)
}

// MoveNodeRoot moves a category to become a root node at a specific position
func (s *categoryService) MoveNodeRoot(ctx context.Context, tenantID uint, req request.MoveNodeRootRequest) error {
	// Truyền NULL cho parentID để chỉ định node sẽ trở thành root
	return s.categoryRepo.MoveNodeProcedure(ctx, tenantID, req.CategoryID, 0, req.Position)
}

// RebuildCategoryTree rebuilds the entire category tree structure
func (s *categoryService) RebuildCategoryTree(ctx context.Context, tenantID uint) error {
	return s.categoryRepo.RebuildTree(ctx, tenantID)
}

// ListCategories lists categories with pagination
func (s *categoryService) ListCategories(ctx context.Context, tenantID uint, req request.ListCategoryRequest) (*response.CategoryListResponse, error) {
	// Get from repository with pagination
	categories, nextCursor, hasMore, err := s.categoryRepo.List(ctx, tenantID, req)
	if err != nil {
		return nil, err
	}

	// If tree view is requested but not using the tree structure in response
	// Just adding the filter by parent ID
	if req.IncludeTree && req.ParentID != nil {
		// Additional logic could be added here if needed
	}

	// Return flat list
	return &response.CategoryListResponse{
		Categories: s.mapCategoriesToResponses(categories),
		Meta: response.CategoryListMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}, nil
}

// mapCategoryToResponse converts a Category model to CategoryResponse
func (s *categoryService) mapCategoryToResponse(category *models.Category) *response.CategoryResponse {
	return &response.CategoryResponse{
		ID:              category.CategoryID,
		TenantID:        uint(category.TenantID),
		ParentID:        category.ParentID,
		Name:            category.Name,
		Slug:            category.Slug,
		Description:     category.Description,
		FeaturedImage:   category.FeaturedImage,
		Left:            category.Left,
		Right:           category.Right,
		Depth:           category.Depth,
		Position:        category.Position,
		IsActive:        category.IsActive,
		IsFeatured:      category.IsFeatured,
		MetaTitle:       category.MetaTitle,
		MetaDescription: category.MetaDescription,
		PostCount:       category.PostCount,
		CreatedAt:       category.CreatedAt,
		UpdatedAt:       category.UpdatedAt,
		CreatedBy:       category.CreatedBy,
		UpdatedBy:       category.UpdatedBy,
	}
}

// mapCategoriesToResponses converts a slice of Category models to CategoryResponse slice
func (s *categoryService) mapCategoriesToResponses(categories []*models.Category) []*response.CategoryResponse {
	responses := make([]*response.CategoryResponse, len(categories))
	for i, category := range categories {
		responses[i] = s.mapCategoryToResponse(category)
	}
	return responses
}

// buildCategoryTree converts a flat list of categories to a hierarchical tree structure
// using the Nested Set Model properties (Left/Right values)
func buildCategoryTree(categories []*models.Category) []*models.Category {
	if len(categories) == 0 {
		return []*models.Category{}
	}

	// Create a map to store nodes by ID for quick lookup
	categoryMap := make(map[uint]*models.Category)

	// Copy categories to avoid modifying the original slice
	// and populate the map
	for _, category := range categories {
		// Create a copy with empty Children slice
		categoryCopy := *category
		categoryCopy.Children = []*models.Category{}
		categoryMap[categoryCopy.CategoryID] = &categoryCopy
	}

	// Root categories at the top level
	var rootCategories []*models.Category

	// Build the tree using nested set properties
	// A node is the parent of another node if its left value is less than
	// and its right value is greater than the other node's values
	for i, category := range categories {
		// Skip the first category if it's the root (no parent)
		if i == 0 && category.ParentID == nil {
			rootCategories = append(rootCategories, categoryMap[category.CategoryID])
			continue
		}

		// Find the closest parent for this node
		// A parent must have:
		// 1. Left value less than this node's left value
		// 2. Right value greater than this node's right value
		// 3. Be the closest ancestor (have the largest left value of all potential parents)
		var closestParent *models.Category
		closestParentLeft := 0

		for _, potential := range categories {
			if potential.Left < category.Left &&
				potential.Right > category.Right &&
				potential.Left > closestParentLeft {
				closestParent = potential
				closestParentLeft = potential.Left
			}
		}

		// If we found a parent, add this category as its child
		if closestParent != nil {
			parent := categoryMap[closestParent.CategoryID]
			parent.Children = append(parent.Children, categoryMap[category.CategoryID])
		} else {
			// If no parent found, it's a root category
			rootCategories = append(rootCategories, categoryMap[category.CategoryID])
		}
	}

	return rootCategories
}

// mapCategoryToTreeItem converts a Category model with children to CategoryTreeItem
func (s *categoryService) mapCategoryToTreeItem(category *models.Category) *response.CategoryTreeItem {
	item := &response.CategoryTreeItem{
		CategoryResponse: *s.mapCategoryToResponse(category),
		Children:         make([]*response.CategoryTreeItem, len(category.Children)),
	}

	for i, child := range category.Children {
		item.Children[i] = s.mapCategoryToTreeItem(child)
	}

	return item
}

// mapCategoriesToTreeItems converts a slice of Category models to CategoryTreeItem slice
func (s *categoryService) mapCategoriesToTreeItems(categories []*models.Category) []*response.CategoryTreeItem {
	items := make([]*response.CategoryTreeItem, len(categories))
	for i, category := range categories {
		items[i] = s.mapCategoryToTreeItem(category)
	}
	return items
}

// getUserIDFromContext extracts the user ID from context
func getUserIDFromContext(ctx context.Context) uint {
	// Try to get user ID from context
	userIDValue := ctx.Value(auth.UserContextKey)
	if userIDValue != nil {
		// Convert to uint if possible
		if userID, ok := userIDValue.(uint); ok {
			return userID
		}
	}

	// For testing purposes, return a default user ID
	return 1
}
