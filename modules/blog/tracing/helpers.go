package tracing

import (
	"context"
	"fmt"
	"log"

	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"github.com/webnew-backend/wn-backend-v2/modules/blog/configs"
	projectTracing "github.com/webnew-backend/wn-backend-v2/pkg/tracing"
)

// StartSpan bắt đầu một span dựa trên loại exporter đang sử dụng
func StartSpan(ctx context.Context, cfg *configs.Config, operationName string) (context.Context, interface{}) {
	if !cfg.Tracing.Enabled {
		return ctx, nil
	}

	switch cfg.Tracing.ExporterType {
	case "jaeger":
		span := GetOpenTracingTracer().StartSpan(operationName)
		ctxWithSpan := opentracing.ContextWithSpan(ctx, span)
		return ctxWithSpan, span
	case "signoz":
		ctxWithSpan, span := projectTracing.StartSpan(ctx, operationName)
		return ctxWithSpan, span
	default:
		log.Printf("[WARN] Loại exporter không được hỗ trợ: %s", cfg.Tracing.ExporterType)
		return ctx, nil
	}
}

// EndSpan kết thúc span dựa trên loại exporter đang sử dụng
func EndSpan(cfg *configs.Config, span interface{}) {
	if !cfg.Tracing.Enabled || span == nil {
		return
	}

	switch cfg.Tracing.ExporterType {
	case "jaeger":
		if otSpan, ok := span.(opentracing.Span); ok {
			otSpan.Finish()
		}
	case "signoz":
		// OpenTelemetry spans sẽ tự động kết thúc khi context được cancel
		// Không cần thêm hành động gì
	}
}

// AddAttribute thêm thuộc tính vào span
func AddAttribute(cfg *configs.Config, span interface{}, key string, value interface{}) {
	if !cfg.Tracing.Enabled || span == nil {
		return
	}

	switch cfg.Tracing.ExporterType {
	case "jaeger":
		if otSpan, ok := span.(opentracing.Span); ok {
			otSpan.SetTag(key, value)
		}
	case "signoz":
		if otelSpan, ok := span.(trace.Span); ok {
			switch v := value.(type) {
			case string:
				otelSpan.SetAttributes(attribute.String(key, v))
			case int:
				otelSpan.SetAttributes(attribute.Int(key, v))
			case int64:
				otelSpan.SetAttributes(attribute.Int64(key, v))
			case uint:
				otelSpan.SetAttributes(attribute.Int64(key, int64(v)))
			case uint64:
				otelSpan.SetAttributes(attribute.Int64(key, int64(v)))
			case float64:
				otelSpan.SetAttributes(attribute.Float64(key, v))
			case bool:
				otelSpan.SetAttributes(attribute.Bool(key, v))
			default:
				// Chuyển đổi sang string cho các kiểu khác
				otelSpan.SetAttributes(attribute.String(key, fmt.Sprintf("%v", v)))
			}
		}
	}
}

// LogError ghi lại lỗi vào span
func LogError(cfg *configs.Config, span interface{}, err error) {
	if !cfg.Tracing.Enabled || span == nil || err == nil {
		return
	}

	switch cfg.Tracing.ExporterType {
	case "jaeger":
		if otSpan, ok := span.(opentracing.Span); ok {
			ext.Error.Set(otSpan, true)
			otSpan.LogKV("error.message", err.Error())
		}
	case "signoz":
		if otelSpan, ok := span.(trace.Span); ok {
			otelSpan.RecordError(err)
			otelSpan.SetStatus(codes.Error, err.Error())
		}
	}
}

// BlogSpecificAttributes thêm các thuộc tính đặc thù của module blog
func BlogSpecificAttributes(cfg *configs.Config, span interface{}, postID, categoryID, authorID, operation, contentType string) {
	if !cfg.Tracing.Enabled || span == nil {
		return
	}

	if postID != "" {
		AddAttribute(cfg, span, "blog.post_id", postID)
	}
	if categoryID != "" {
		AddAttribute(cfg, span, "blog.category_id", categoryID)
	}
	if authorID != "" {
		AddAttribute(cfg, span, "blog.author_id", authorID)
	}
	if operation != "" {
		AddAttribute(cfg, span, "blog.operation", operation)
	}
	if contentType != "" {
		AddAttribute(cfg, span, "blog.content_type", contentType)
	}
}

// GetTracer trả về tracer tùy thuộc vào loại exporter
func GetTracer() trace.Tracer {
	return otel.Tracer("")
}
