package tracing

import (
	"context"
	"fmt"
	"io"
	"log"

	"github.com/opentracing/opentracing-go"
	"go.opentelemetry.io/otel/trace"

	"github.com/webnew-backend/wn-backend-v2/modules/blog/configs"
	projectTracing "github.com/webnew-backend/wn-backend-v2/pkg/tracing"
)

// Global variables for tracers
var (
	otTracer        opentracing.Tracer
	otCloser        io.Closer
	otelTracer      trace.Tracer
	otelTracerClose func()
)

// InitTracer khởi tạo tracer cho module blog
func InitTracer(ctx context.Context, cfg *configs.Config) error {
	if !cfg.Tracing.Enabled {
		log.Println("Tracing is disabled")
		return nil
	}

	log.Printf("Initializing tracer with exporter type: %s", cfg.Tracing.ExporterType)

	var err error
	switch cfg.Tracing.ExporterType {
	case "jaeger":
		otTracer, otCloser, err = projectTracing.InitJaeger(
			cfg.Tracing.ServiceName,
			cfg.Tracing.Jaeger.Host,
			cfg.Tracing.Jaeger.Port,
		)
		if err != nil {
			return fmt.Errorf("failed to initialize Jaeger tracer: %w", err)
		}
		opentracing.SetGlobalTracer(otTracer)
		return nil
	case "signoz":
		otelTracerClose, err = projectTracing.InitSignozTracer(
			cfg.Tracing.ServiceName,
			cfg.Tracing.Signoz.Endpoint,
		)
		if err != nil {
			return fmt.Errorf("failed to initialize SignOz tracer: %w", err)
		}
		return nil
	default:
		log.Printf("[WARN] Unsupported exporter type: %s", cfg.Tracing.ExporterType)
		return nil
	}
}

// CloseTracer đóng tracer khi module dừng hoạt động
func CloseTracer() {
	if otelTracerClose != nil {
		otelTracerClose()
		log.Println("Closed OpenTelemetry tracer")
	}

	if otCloser != nil {
		if err := otCloser.Close(); err != nil {
			log.Printf("Error closing Jaeger tracer: %v", err)
		} else {
			log.Println("Closed Jaeger tracer")
		}
	}
}

// GetOpenTracingTracer trả về OpenTracing tracer
func GetOpenTracingTracer() opentracing.Tracer {
	return otTracer
}

// IsTracingEnabled kiểm tra xem tracing có được bật không
func IsTracingEnabled(cfg *configs.Config) bool {
	return cfg.Tracing.Enabled
}

// GetExporterType trả về loại exporter đang sử dụng
func GetExporterType(cfg *configs.Config) string {
	return cfg.Tracing.ExporterType
}
