package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/webnew-backend/wn-backend-v2/modules/blog/configs"
	"github.com/webnew-backend/wn-backend-v2/modules/blog/dto/request"
	"github.com/webnew-backend/wn-backend-v2/modules/blog/service"
	"github.com/webnew-backend/wn-backend-v2/modules/blog/tracing"
	"github.com/webnew-backend/wn-backend-v2/pkg/auth"
	"github.com/webnew-backend/wn-backend-v2/pkg/response"
)

// PostHandler handles HTTP requests for blog posts
type PostHandler struct {
	postService service.PostService
	jwtService  *auth.JWTService
	config      *configs.Config
}

// NewPostHandler creates a new post handler instance
func NewPostHandler(postService service.PostService, jwtService *auth.JWTService, cfg *configs.Config) *PostHandler {
	return &PostHandler{
		postService: postService,
		jwtService:  jwtService,
		config:      cfg,
	}
}

// Create handles the creation of a new blog post
func (h *PostHandler) Create(c *gin.Context) {
	// Create span for this operation
	ctx, span := CreateAPISpan(c, "post.create")
	if span != nil {
		defer FinishAPISpan(span, http.StatusCreated, nil)
	}

	// Get tenant ID from context
	var tenantID uint
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		if span != nil {
			FinishAPISpan(span, http.StatusUnauthorized, err)
		}
		return
	}

	// Parse request body
	var req request.CreatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, err)
		}
		return
	}

	// Call service
	post, err := h.postService.CreatePost(ctx, tenantID, req)
	if err != nil {
		handleError(c, err)
		if span != nil {
			FinishAPISpan(span, getStatusCode(err), err)
		}
		return
	}

	// Add post info to span
	if span != nil {
		AddPostInfoToSpan(span, post.ID, "create")
	}

	// Return success response
	response.Success(c, http.StatusCreated, "Post created successfully", post)
}

// Get handles retrieval of a post by ID
func (h *PostHandler) Get(c *gin.Context) {
	// Create span for this operation
	ctx, span := CreateAPISpan(c, "post.get")
	if span != nil {
		defer FinishAPISpan(span, http.StatusOK, nil)
	}

	// Get tenant ID from context
	var tenantID uint
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		if span != nil {
			FinishAPISpan(span, http.StatusUnauthorized, err)
		}
		return
	}

	// Get post ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_ID", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, nil)
		}
		return
	}

	// Convert string ID to uint
	postID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, err)
		}
		return
	}

	// Add post ID to span
	if span != nil {
		AddPostInfoToSpan(span, uint(postID), "get")
	}

	// Call service
	post, err := h.postService.GetPost(ctx, tenantID, uint(postID))
	if err != nil {
		handleError(c, err)
		if span != nil {
			FinishAPISpan(span, getStatusCode(err), err)
		}
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Post retrieved successfully", post)
}

// GetBySlug handles retrieval of a post by slug
func (h *PostHandler) GetBySlug(c *gin.Context) {
	// Create span for this operation
	ctx, span := CreateAPISpan(c, "post.get_by_slug")
	if span != nil {
		defer FinishAPISpan(span, http.StatusOK, nil)
	}

	// Get tenant ID from context
	var tenantID uint
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		if span != nil {
			FinishAPISpan(span, http.StatusUnauthorized, err)
		}
		return
	}

	// Get slug from URL
	slug := c.Param("slug")
	if slug == "" {
		details := []interface{}{map[string]string{"message": "Post slug is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post slug", "MISSING_SLUG", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, nil)
		}
		return
	}

	// Add slug to span
	if span != nil {
		tracing.AddAttribute(h.config, span, "blog.post.slug", slug)
	}

	// Call service
	post, err := h.postService.GetPostBySlug(ctx, tenantID, slug)
	if err != nil {
		handleError(c, err)
		if span != nil {
			FinishAPISpan(span, getStatusCode(err), err)
		}
		return
	}

	// Add post info to span
	if span != nil {
		AddPostInfoToSpan(span, post.ID, "get_by_slug")
	}

	// Return success response
	response.Success(c, http.StatusOK, "Post retrieved successfully", post)
}

// Update handles updating an existing blog post
func (h *PostHandler) Update(c *gin.Context) {
	// Create span for this operation
	ctx, span := CreateAPISpan(c, "post.update")
	if span != nil {
		defer FinishAPISpan(span, http.StatusOK, nil)
	}

	// Get tenant ID from context
	var tenantID uint
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		if span != nil {
			FinishAPISpan(span, http.StatusUnauthorized, err)
		}
		return
	}

	// Get post ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_ID", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, nil)
		}
		return
	}

	// Convert string ID to uint
	postID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, err)
		}
		return
	}

	// Add post ID to span
	if span != nil {
		AddPostInfoToSpan(span, uint(postID), "update")
	}

	// Parse request body
	var req request.UpdatePostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, err)
		}
		return
	}

	// Call service
	post, err := h.postService.UpdatePost(ctx, tenantID, uint(postID), req)
	if err != nil {
		handleError(c, err)
		if span != nil {
			FinishAPISpan(span, getStatusCode(err), err)
		}
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Post updated successfully", post)
}

// Delete handles deletion of a blog post
func (h *PostHandler) Delete(c *gin.Context) {
	// Create span for this operation
	ctx, span := CreateAPISpan(c, "post.delete")
	if span != nil {
		defer FinishAPISpan(span, http.StatusOK, nil)
	}

	// Get tenant ID from context
	var tenantID uint
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		if span != nil {
			FinishAPISpan(span, http.StatusUnauthorized, err)
		}
		return
	}

	// Get post ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Post ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing post ID", "MISSING_ID", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, nil)
		}
		return
	}

	// Convert string ID to uint
	postID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid post ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid post ID", "INVALID_ID", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, err)
		}
		return
	}

	// Add post ID to span
	if span != nil {
		AddPostInfoToSpan(span, uint(postID), "delete")
	}

	// Call service
	err = h.postService.DeletePost(ctx, tenantID, uint(postID))
	if err != nil {
		handleError(c, err)
		if span != nil {
			FinishAPISpan(span, getStatusCode(err), err)
		}
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Post deleted successfully", nil)
}

// List handles listing of blog posts with pagination
func (h *PostHandler) List(c *gin.Context) {
	// Create span for this operation
	ctx, span := CreateAPISpan(c, "post.list")
	if span != nil {
		defer FinishAPISpan(span, http.StatusOK, nil)
	}

	// Get tenant ID from context
	var tenantID uint
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		if span != nil {
			FinishAPISpan(span, http.StatusUnauthorized, err)
		}
		return
	}

	// Parse query parameters
	var req request.ListPostRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		if span != nil {
			FinishAPISpan(span, http.StatusBadRequest, err)
		}
		return
	}

	// Add query params to span
	if span != nil {
		if req.Limit > 0 {
			tracing.AddAttribute(h.config, span, "blog.post.limit", req.Limit)
		}
		if req.Cursor != "" {
			tracing.AddAttribute(h.config, span, "blog.post.cursor", req.Cursor)
		}
		if req.Search != "" {
			tracing.AddAttribute(h.config, span, "blog.post.search", req.Search)
		}
		if req.CategoryID != nil {
			tracing.AddAttribute(h.config, span, "blog.post.category_id", *req.CategoryID)
		}
	}

	// Call service
	result, err := h.postService.ListPosts(ctx, tenantID, req)
	if err != nil {
		handleError(c, err)
		if span != nil {
			FinishAPISpan(span, getStatusCode(err), err)
		}
		return
	}

	// Add result info to span
	if span != nil {
		tracing.AddAttribute(h.config, span, "blog.post.result_count", len(result.Posts))
		tracing.AddAttribute(h.config, span, "blog.post.has_more", result.HasMore)
	}

	// Return success response with pagination metadata
	meta := map[string]interface{}{
		"next_cursor": result.NextCursor,
		"has_more":    result.HasMore,
	}
	response.SuccessWithMeta(c, http.StatusOK, "Posts retrieved successfully", result.Posts, meta)
}
