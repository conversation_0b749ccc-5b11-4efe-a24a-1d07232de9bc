package handlers

import (
	"context"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/webnew-backend/wn-backend-v2/modules/blog/configs"
	"github.com/webnew-backend/wn-backend-v2/modules/blog/tracing"
	"github.com/webnew-backend/wn-backend-v2/pkg/auth"
)

// Config instance to use for tracing
var config *configs.Config

// SetConfig sets the config for the tracing handlers
func SetConfig(cfg *configs.Config) {
	config = cfg
}

// CreateAPISpan creates a span for an API endpoint
func CreateAPISpan(c *gin.Context, operationName string) (context.Context, interface{}) {
	if config == nil || !config.Tracing.Enabled {
		return c.Request.Context(), nil
	}

	ctx, span := tracing.StartSpan(c.Request.Context(), config, operationName)
	if span != nil {
		tracing.AddAttribute(config, span, "http.method", c.Request.Method)
		tracing.AddAttribute(config, span, "http.url", c.Request.URL.String())
		tracing.AddAttribute(config, span, "http.path", c.FullPath())
	}

	return ctx, span
}

// FinishAPISpan completes a span with status code
func FinishAPISpan(span interface{}, statusCode int, err error) {
	if config == nil || !config.Tracing.Enabled || span == nil {
		return
	}

	tracing.AddAttribute(config, span, "http.status_code", statusCode)

	if err != nil {
		tracing.LogError(config, span, err)
	}

	tracing.EndSpan(config, span)
}

// AddPostInfoToSpan adds post-specific information to a trace span
func AddPostInfoToSpan(span interface{}, postID uint, operation string) {
	if config == nil || !config.Tracing.Enabled || span == nil {
		return
	}

	tracing.AddAttribute(config, span, "blog.post_id", postID)
	tracing.AddAttribute(config, span, "blog.operation", operation)
	tracing.AddAttribute(config, span, "blog.content_type", "post")
}

// AddCategoryInfoToSpan adds category-specific information to a trace span
func AddCategoryInfoToSpan(span interface{}, categoryID uint, operation string) {
	if config == nil || !config.Tracing.Enabled || span == nil {
		return
	}

	tracing.AddAttribute(config, span, "blog.category_id", categoryID)
	tracing.AddAttribute(config, span, "blog.operation", operation)
	tracing.AddAttribute(config, span, "blog.content_type", "category")
}

// AddTagInfoToSpan adds tag-specific information to a trace span
func AddTagInfoToSpan(span interface{}, tagID uint, operation string) {
	if config == nil || !config.Tracing.Enabled || span == nil {
		return
	}

	tracing.AddAttribute(config, span, "blog.tag_id", tagID)
	tracing.AddAttribute(config, span, "blog.operation", operation)
	tracing.AddAttribute(config, span, "blog.content_type", "tag")
}

// AddAuthorInfoToSpan adds author-specific information to a trace span
func AddAuthorInfoToSpan(span interface{}, authorID uint, operation string) {
	if config == nil || !config.Tracing.Enabled || span == nil {
		return
	}

	tracing.AddAttribute(config, span, "blog.author_id", authorID)
	tracing.AddAttribute(config, span, "blog.operation", operation)
	tracing.AddAttribute(config, span, "blog.content_type", "author")
}

// uintToString chuyển đổi uint sang string
func uintToString(id uint) string {
	return strconv.FormatUint(uint64(id), 10)
}

// getTenantIDFromContext lấy tenant ID từ context
func getTenantIDFromContext(c *gin.Context) (uint, error) {
	// Lấy claims từ context
	claims, exists := auth.GetClaimsFromContext(c)
	if !exists {
		return 0, auth.ErrAuthClaimsNotFound
	}

	tenantID := uint(claims.TenantID)
	return tenantID, nil
}

// getUserIDFromContext lấy user ID từ context
func getUserIDFromContext(c *gin.Context) (uint, error) {
	// Lấy user ID từ context
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		return 0, auth.ErrAuthUserNotFound
	}

	return uint(userID), nil
}

// getStatusCode trả về mã HTTP dựa trên loại lỗi
func getStatusCode(err error) int {
	// Triển khai logic chuyển đổi lỗi thành HTTP status code
	// Đây là logic mẫu, cần cập nhật dựa trên loại lỗi thực tế
	switch err {
	case auth.ErrAuthClaimsNotFound, auth.ErrAuthUserNotFound:
		return http.StatusUnauthorized
	default:
		return http.StatusInternalServerError
	}
}
