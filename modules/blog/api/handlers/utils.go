package handlers

import (
	"errors"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"

	"github.com/webnew/wn-backend-v2/modules/blog/repository/mysql"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	"github.com/webnew/wn-backend-v2/pkg/response"
)

// Constants for error codes
const (
	ErrCodeNotFound      = "NOT_FOUND"
	ErrCodeInvalidInput  = "INVALID_INPUT"
	ErrCodeDuplicateSlug = "DUPLICATE_SLUG"
	ErrCodeServerError   = "SERVER_ERROR"
)

// getTenantIDFromContext retrieves the tenant ID from the request context
// getTenantIDFromContext retrieves the tenant ID from the request context
func getTenantIDFromContext(c *gin.Context) (uint, error) {
	// Get tenant ID from JWT claims
	claims, exists := auth.GetClaimsFromContext(c)
	if !exists {
		return 0, errors.New("không tìm thấy thông tin xác thực")
	}
	return uint(claims.TenantID), nil
}

// getUserIDFromContext retrieves the user ID from the context
// getUserIDFromContext retrieves the user ID from the context
func getUserIDFromContext(c *gin.Context) uint {
	// Try to get user ID from auth context (set by JWT middleware)
	userIDValue, exists := c.Get(auth.UserContextKey)
	if exists {
		// Convert to uint (JWT middleware stores user ID as int)
		if userID, ok := userIDValue.(int); ok {
			return uint(userID)
		}
	}

	// For testing purposes, return a default user ID
	return 1
}

// apiSuccess sends a successful API response
func apiSuccess(c *gin.Context, statusCode int, data interface{}) {
	response.Success(c, statusCode, "Operation completed successfully", data)
}

// apiSuccessWithMeta sends a successful API response with metadata
func apiSuccessWithMeta(c *gin.Context, statusCode int, data interface{}, meta gin.H) {
	metaMap := make(map[string]interface{})
	for k, v := range meta {
		metaMap[k] = v
	}

	response.SuccessWithMeta(c, statusCode, "Operation completed successfully", data, metaMap)
}

// apiError sends an error API response
func apiError(c *gin.Context, statusCode int, errorCode string, message string) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, nil)
}

// apiErrorWithDetails sends an error API response with detailed information
func apiErrorWithDetails(c *gin.Context, statusCode int, errorCode string, message string, details interface{}) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, details)
}

// handleError is a helper function to handle errors consistently
func handleError(c *gin.Context, err error) {
	statusCode := getStatusCode(err)
	var errorCode string

	switch {
	case errors.Is(err, mysql.ErrNotFound):
		errorCode = "NOT_FOUND"
	case errors.Is(err, mysql.ErrConflict):
		errorCode = "CONFLICT"
	default:
		errorCode = "INTERNAL_ERROR"
	}

	details := []interface{}{map[string]string{"message": err.Error()}}
	response.ErrorWithDetails(c, statusCode, err.Error(), errorCode, details)
}

// getStatusCode maps errors to HTTP status codes
func getStatusCode(err error) int {
	switch {
	case errors.Is(err, mysql.ErrNotFound):
		return http.StatusNotFound
	case errors.Is(err, mysql.ErrConflict):
		return http.StatusConflict
	default:
		return http.StatusInternalServerError
	}
}

// contains checks if a string contains a substring
func contains(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}
