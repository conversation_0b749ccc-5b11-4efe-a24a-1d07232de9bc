package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/webnew/wn-backend-v2/modules/blog/dto/request"
	"github.com/webnew/wn-backend-v2/modules/blog/service"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	"github.com/webnew/wn-backend-v2/pkg/response"
)

// TagHandler handles HTTP requests for blog tags
type TagHandler struct {
	tagService service.TagService
	jwtService *auth.JWTService
}

// NewTagHandler creates a new TagHandler instance
func NewTagHandler(tagService service.TagService, jwtService *auth.JWTService) *TagHandler {
	return &TagHandler{
		tagService: tagService,
		jwtService: jwtService,
	}
}

// Create handles creating a new tag
func (h *TagHandler) Create(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Parse request
	var req request.CreateTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Sử dụng FindOrCreate thay vì Create để tìm hoặc tạo tag
	tag, err := h.tagService.FindOrCreate(c.Request.Context(), tenantID, req)
	if err != nil {
		handleTagServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusCreated, "Tag created successfully", tag)
}

// Get handles getting a tag by ID
func (h *TagHandler) Get(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Get tag ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Tag ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing tag ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	tagID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tag ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tag ID", "INVALID_ID", details)
		return
	}

	// Get tag
	tag, err := h.tagService.GetByID(c.Request.Context(), tenantID, uint(tagID))
	if err != nil {
		handleTagServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Tag retrieved successfully", tag)
}

// GetBySlug handles getting a tag by slug
func (h *TagHandler) GetBySlug(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Get slug from URL
	slug := c.Param("slug")
	if slug == "" {
		details := []interface{}{map[string]string{"message": "Tag slug is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing tag slug", "MISSING_SLUG", details)
		return
	}

	// Get tag
	tag, err := h.tagService.GetBySlug(c.Request.Context(), tenantID, slug)
	if err != nil {
		handleTagServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Tag retrieved successfully", tag)
}

// Update handles updating a tag
func (h *TagHandler) Update(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Get tag ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Tag ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing tag ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	tagID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tag ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tag ID", "INVALID_ID", details)
		return
	}

	// Parse request
	var req request.UpdateTagRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Update tag
	tag, err := h.tagService.Update(c.Request.Context(), tenantID, uint(tagID), req)
	if err != nil {
		handleTagServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Tag updated successfully", tag)
}

// Delete handles deleting a tag
func (h *TagHandler) Delete(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Get tag ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Tag ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing tag ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	tagID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tag ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tag ID", "INVALID_ID", details)
		return
	}

	// Delete tag
	err = h.tagService.Delete(c.Request.Context(), tenantID, uint(tagID))
	if err != nil {
		handleTagServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Tag deleted successfully", nil)
}

// List handles listing tags with pagination
func (h *TagHandler) List(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Parse query
	var req request.ListTagRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Limit should be between 1 and 100
	if req.Limit <= 0 {
		req.Limit = 10
	} else if req.Limit > 100 {
		req.Limit = 100
	}

	// Get tags
	tags, nextCursor, hasMore, err := h.tagService.List(c.Request.Context(), tenantID, req)
	if err != nil {
		handleTagServiceError(c, err)
		return
	}

	// Return success response
	meta := map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    hasMore,
	}
	response.SuccessWithMeta(c, http.StatusOK, "Tags retrieved successfully", tags, meta)
}

// GetWithPostCount handles getting tags with post count
func (h *TagHandler) GetWithPostCount(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	// Get tags with post count
	tags, err := h.tagService.GetTagsWithPostCount(c.Request.Context(), tenantID)
	if err != nil {
		handleTagServiceError(c, err)
		return
	}

	// Return success response
	response.Success(c, http.StatusOK, "Tags with post count retrieved successfully", tags)
}

// handleTagServiceError handles tag service errors
func handleTagServiceError(c *gin.Context, err error) {
	if err == nil {
		return
	}

	errMsg := err.Error()

	switch {
	case errMsg == "tag not found":
		response.ErrorWithDetails(c, http.StatusNotFound, "Tag not found", "TAG_NOT_FOUND", nil)
	case errMsg == "tenant not found":
		response.ErrorWithDetails(c, http.StatusNotFound, "Tenant not found", "TENANT_NOT_FOUND", nil)
	case contains(errMsg, "slug") && contains(errMsg, "already exists"):
		response.ErrorWithDetails(c, http.StatusConflict, errMsg, "TAG_SLUG_EXISTS", nil)
	default:
		details := []interface{}{map[string]string{"message": errMsg}}
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Internal server error", "SERVER_ERROR", details)
	}
}
