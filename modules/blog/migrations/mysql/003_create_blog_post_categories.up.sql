CREATE TABLE IF NOT EXISTS blog_post_categories (
  tenant_id INT UNSIGNED NOT NULL,
  post_id INT UNSIGNED NOT NULL,
  category_id INT UNSIGNED NOT NULL,
  PRIMARY KEY (tenant_id, post_id, category_id),
  CONSTRAINT fk_post_category_post <PERSON>OR<PERSON><PERSON><PERSON> KEY (post_id) REFERENCES blog_posts(post_id) ON DELETE CASCADE,
  CONSTRAINT fk_post_category_category FOREIGN KEY (category_id) REFERENCES blog_categories(category_id) ON DELETE CASCADE,
  INDEX idx_post_category_post (post_id),
  INDEX idx_post_category_category (category_id)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 