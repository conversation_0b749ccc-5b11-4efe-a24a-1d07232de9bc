CREATE PROCEDURE `blog_categories_update_position` ( 
    IN p_tenant_id INT, -- ID của tenant hiện tại
	IN p_category_id BIGINT, -- ID của category cần thay đổi vị trí
	IN p_new_position INT -- Vị trí MONG MUỐN (0-based index)
) -- Thêm nhãn ngay sau BEGIN chính
proc_label : BEGIN-- B<PERSON>ế<PERSON> cục bộ
	DECLARE
		v_parent_id BIGINT;
	DECLARE
		v_current_position INT;
	DECLARE
		v_max_position INT;
	DECLARE
		v_actual_new_position INT;-- === Khai báo handler để tự động ROLLBACK nếu có lỗi ===
	DECLARE
	EXIT HANDLER FOR SQLEXCEPTION BEGIN
			ROLLBACK;
		RESIGNAL;-- <PERSON><PERSON><PERSON> lại lỗi đã xảy ra
		
	END;-- === Bắt đầu Transaction ===
	START TRANSACTION;-- === Bước 1: <PERSON><PERSON><PERSON> thông tin node và khóa (nếu cần thiết) ===
	SELECT
		parent_id,
		position INTO v_parent_id,
		v_current_position 
	FROM
		blog_categories 
	WHERE
		category_id = p_category_id 
		AND tenant_id = p_tenant_id FOR UPDATE;
	IF
		v_current_position IS NULL THEN
			ROLLBACK;
		SIGNAL SQLSTATE '45000' 
		SET MESSAGE_TEXT = 'Category not found.';
		
	END IF;-- === Bước 2: Xác định phạm vi anh em ===
	SELECT COALESCE
		( MAX( position ), - 1 ) INTO v_max_position 
	FROM
		blog_categories 
	WHERE
		parent_id <=> v_parent_id -- Dùng <=> để xử lý NULL an toàn
		
		AND tenant_id = p_tenant_id 
		AND category_id != p_category_id;-- === Bước 3: Chuẩn hóa vị trí mới ===
	IF
		p_new_position < 0 THEN SET v_actual_new_position = 0; ELSEIF p_new_position > v_max_position + 1 THEN
		
		SET v_actual_new_position = v_max_position + 1;
		ELSE 
			SET v_actual_new_position = p_new_position;
		
	END IF;-- Nếu vị trí mới giống vị trí cũ thì không cần làm gì cả
	IF
		v_actual_new_position = v_current_position THEN
			COMMIT;-- Kết thúc transaction sớm
		LEAVE proc_label;-- Thoát khỏi procedure (GIỜ ĐÃ CÓ LABEL HỢP LỆ)
		
	END IF;-- === Bước 4: Dịch chuyển vị trí các node anh em ===
	IF
		v_actual_new_position < v_current_position THEN -- Node di chuyển LÊN
			UPDATE blog_categories SET position = position + 1 WHERE parent_id <=> v_parent_id AND tenant_id = p_tenant_id AND position >= v_actual_new_position 
			AND position < v_current_position; ELSE -- v_actual_new_position > v_current_position
-- Node di chuyển XUỐNG
		UPDATE blog_categories SET position = position - 1 WHERE parent_id <=> v_parent_id AND tenant_id = p_tenant_id AND position > v_current_position 
		AND position <= v_actual_new_position;
		
	END IF;-- === Bước 5: Cập nhật vị trí cho node cần di chuyển ===
	UPDATE blog_categories 
	SET position = v_actual_new_position 
	WHERE
		category_id = p_category_id 
		AND tenant_id = p_tenant_id;-- === Bước 6: Kết thúc Transaction ===
	COMMIT;
	
END