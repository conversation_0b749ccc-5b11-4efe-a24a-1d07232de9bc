CREATE TABLE IF NOT EXISTS blog_posts (
  post_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  description TEXT,
  content LONGTEXT,
  featured_image VARCHAR(255),
  status ENUM('draft', 'pending', 'published', 'private', 'trash') DEFAULT 'draft',
  visibility ENUM('public', 'private', 'password_protected') DEFAULT 'public',
  password VARCHAR(255) NULL,
  comment_status ENUM('open', 'closed') DEFAULT 'open',
  publish_date TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  author_id INT UNSIGNED NOT NULL,
  created_by INT UNSIGNED NOT NULL,
  CONSTRAINT fk_blog_post_author FOR<PERSON><PERSON><PERSON> KEY (created_by) REFERENCES users(user_id) ON DELETE CASCADE,
  CONSTRAINT fk_blog_post_author_id FOREIGN KEY (author_id) REFERENCES users(user_id) ON DELETE CASCADE,
  UNIQUE KEY unique_blog_post_slug_tenant (tenant_id, slug),
  INDEX idx_blog_post_status (status),
  INDEX idx_blog_post_publish (publish_date)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 