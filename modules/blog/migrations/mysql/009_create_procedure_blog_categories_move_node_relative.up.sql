CREATE PROCEDURE blog_categories_move_node_relative (
    IN p_tenant_id BIGINT,
    IN p_node_id BIGINT,     -- ID của node cần di chuyển
    IN p_target_id BIGINT,   -- ID của node đích (cha hoặc anh em)
    IN p_mode VARCHAR(10)    -- Chế độ di chuyển: 'child', 'before', 'after'
)
BEGIN
    DECLARE v_target_parent_id BIGINT;
    DECLARE v_target_position INT;
    DECLARE v_target_node_parent_id BIGINT;
    DECLARE v_target_node_position INT;
    DECLARE v_max_position INT;

    -- <PERSON><PERSON><PERSON> thông tin cơ bản của node đích để xác định parent và position
    SELECT parent_id, position
    INTO v_target_node_parent_id, v_target_node_position
    FROM blog_categories
    WHERE tenant_id = p_tenant_id AND category_id = p_target_id
    LIMIT 1;

    -- <PERSON><PERSON><PERSON> tra node đích tồn tại
    IF v_target_node_position IS NULL THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Target node (target_id) not found.';
    END IF;

    -- <PERSON><PERSON><PERSON> parent_id và position đích dựa trên mode
    CASE UCASE(p_mode) -- Chuyển sang chữ hoa để không phân biệt kiểu chữ
        WHEN 'CHILD' THEN
            -- Di chuyển làm con cuối cùng của target_id
            SET v_target_parent_id = p_target_id;
            -- Tìm vị trí cuối cùng của các con hiện tại + 1
            SELECT COALESCE(MAX(position), -1) + 1
            INTO v_target_position
            FROM blog_categories
            WHERE tenant_id = p_tenant_id AND parent_id = p_target_id;

        WHEN 'BEFORE' THEN
            -- Di chuyển vào trước target_id (cùng cấp)
            SET v_target_parent_id = v_target_node_parent_id; -- Cùng cha với target
            SET v_target_position = v_target_node_position; -- Chèn vào vị trí của target

             -- Kiểm tra target có phải là node gốc không (không thể đặt trước/sau node gốc)
            IF v_target_parent_id IS NULL AND v_target_node_parent_id IS NULL THEN
                 SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Cannot move relatively to a root node using BEFORE/AFTER mode.';
            END IF;


        WHEN 'AFTER' THEN
            -- Di chuyển vào sau target_id (cùng cấp)
            SET v_target_parent_id = v_target_node_parent_id; -- Cùng cha với target
            SET v_target_position = v_target_node_position + 1; -- Chèn vào vị trí sau target

             -- Kiểm tra target có phải là node gốc không (không thể đặt trước/sau node gốc)
             IF v_target_parent_id IS NULL AND v_target_node_parent_id IS NULL THEN
                 SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Cannot move relatively to a root node using BEFORE/AFTER mode.';
            END IF;

        ELSE
            -- Mode không hợp lệ
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Invalid mode specified. Use CHILD, BEFORE, or AFTER.';
    END CASE;

    -- --- Gọi thủ tục di chuyển gốc ---
    -- Transaction và các kiểm tra khác (như di chuyển vào chính nó/con nó)
    -- đã được xử lý bên trong MoveNode.
    CALL blog_categories_move_node(
        p_tenant_id,
        p_node_id,
        v_target_parent_id,
        v_target_position
    );

END