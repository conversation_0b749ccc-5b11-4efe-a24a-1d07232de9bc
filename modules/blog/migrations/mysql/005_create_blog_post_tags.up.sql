CREATE TABLE IF NOT EXISTS blog_post_tags (
  tenant_id INT UNSIGNED NOT NULL,
  post_id INT UNSIGNED NOT NULL,
  tag_id INT UNSIGNED NOT NULL,
  PRIMARY KEY (tenant_id, post_id, tag_id),
  CONSTRAINT fk_post_tag_post <PERSON>OR<PERSON><PERSON><PERSON> KEY (post_id) REFERENCES blog_posts(post_id) ON DELETE CASCADE,
  CONSTRAINT fk_post_tag_tag FOREIGN KEY (tag_id) REFERENCES blog_tags(tag_id) ON DELETE CASCADE,
  INDEX idx_post_tag_post (post_id),
  INDEX idx_post_tag_tag (tag_id)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 