package request

// SeoMetaRequest represents the SEO metadata for a blog post
type SeoMetaRequest struct {
	SeoTitle           string                 `json:"seo_title"`
	MetaDescription    string                 `json:"meta_description"`
	FocusKeyphrase     string                 `json:"focus_keyphrase"`
	CanonicalURL       string                 `json:"canonical_url"`
	IsCornerstone      uint8                  `json:"is_cornerstone"`
	OgTitle            string                 `json:"og_title"`
	OgDescription      string                 `json:"og_description"`
	OgImage            string                 `json:"og_image"`
	TwitterTitle       string                 `json:"twitter_title"`
	TwitterDescription string                 `json:"twitter_description"`
	TwitterImage       string                 `json:"twitter_image"`
	RobotsIndex        string                 `json:"robots_index"`
	RobotsFollow       string                 `json:"robots_follow"`
	RobotsAdvanced     string                 `json:"robots_advanced"`
	SeoScore           string                 `json:"seo_score"`
	ReadabilityScore   string                 `json:"readability_score"`
	SchemaData         map[string]interface{} `json:"schema_data"`
}
