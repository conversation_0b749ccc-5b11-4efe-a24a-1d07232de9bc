package request

import "time"

// CreatePostRequest represents the request to create a new blog post
type CreatePostRequest struct {
	Title         string          `json:"title" binding:"required,max=255"`
	Slug          string          `json:"slug" binding:"omitempty,max=255"`
	Description   string          `json:"description" binding:"omitempty"`
	Content       string          `json:"content" binding:"omitempty"`
	FeaturedImage string          `json:"featured_image" binding:"omitempty,max=255"`
	Status        string          `json:"status" binding:"omitempty,oneof=draft pending published private trash"`
	Visibility    string          `json:"visibility" binding:"omitempty,oneof=public private password_protected"`
	Password      *string         `json:"password" binding:"omitempty,max=255,required_if=Visibility password_protected"`
	CommentStatus string          `json:"comment_status" binding:"omitempty,oneof=open closed"`
	PublishDate   *time.Time      `json:"publish_date" binding:"omitempty"`
	CategoryIDs   []uint          `json:"category_ids" binding:"omitempty"`
	TagIDs        []uint          `json:"tag_ids" binding:"omitempty"`
	SeoMeta       *SeoMetaRequest `json:"seo_meta" binding:"omitempty"`
}
