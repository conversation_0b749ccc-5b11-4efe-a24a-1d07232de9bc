package request

// ListPostRequest represents the request to list posts with pagination
type ListPostRequest struct {
	Cursor         string `form:"cursor"`
	Limit          int    `form:"limit" binding:"omitempty,min=1,max=100"`
	Status         string `form:"status" binding:"omitempty,oneof=draft pending published private trash"`
	CategoryID     *uint `form:"category_id"`
	TagID          *uint `form:"tag_id"`
	AuthorID       *uint `form:"author_id"`
	Search         string `form:"search" binding:"omitempty"`
	OrderBy        string `form:"order_by" binding:"omitempty,oneof=newest oldest title"`
	WithCategories bool   `form:"with_categories"`
	WithTags       bool   `form:"with_tags"`
	WithAuthor     bool   `form:"with_author"`
}
