package request

import "time"

// UpdatePostRequest represents the request to update an existing blog post
type UpdatePostRequest struct {
	Title         string          `json:"title" binding:"omitempty,max=255"`
	Slug          string          `json:"slug" binding:"omitempty,max=255"`
	Description   string          `json:"description" binding:"omitempty"`
	Content       string          `json:"content" binding:"omitempty"`
	FeaturedImage string          `json:"featured_image" binding:"omitempty,max=255"`
	Status        string          `json:"status" binding:"omitempty,oneof=draft pending published private trash"`
	Visibility    string          `json:"visibility" binding:"omitempty,oneof=public private password_protected"`
	Password      *string         `json:"password" binding:"omitempty,max=255"`
	CommentStatus string          `json:"comment_status" binding:"omitempty,oneof=open closed"`
	PublishDate   *time.Time      `json:"publish_date" binding:"omitempty"`
	CategoryIDs   []int64         `json:"category_ids" binding:"omitempty"`
	TagIDs        []int64         `json:"tag_ids" binding:"omitempty"`
	SeoMeta       *SeoMetaRequest `json:"seo_meta" binding:"omitempty"`
}
