package request

// CreateAuthorRequest represents the request for creating a new author
type CreateAuthorRequest struct {
	UserID      uint   `json:"user_id"`
	DisplayName string `json:"display_name" binding:"required"`
	Bio         string `json:"bio"`
	AvatarURL   string `json:"avatar_url"`
	Email       string `json:"email"`
	IsActive    *bool  `json:"is_active"`
}

// UpdateAuthorRequest represents the request for updating an author
type UpdateAuthorRequest struct {
	DisplayName string `json:"display_name"`
	Bio         string `json:"bio"`
	AvatarURL   string `json:"avatar_url"`
	Email       string `json:"email"`
	IsActive    *bool  `json:"is_active"`
}

// ListAuthorRequest represents the request for listing authors
type ListAuthorRequest struct {
	Cursor    string `form:"cursor"`
	Limit     int    `form:"limit" binding:"omitempty,min=1,max=100"`
	Search    string `form:"search"`
	IsActive  *bool  `form:"is_active"`
	SortBy    string `form:"sort_by" binding:"omitempty,oneof=name created_at updated_at"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
}
