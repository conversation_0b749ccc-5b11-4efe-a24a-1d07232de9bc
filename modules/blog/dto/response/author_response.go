package response

import (
	"time"

	"github.com/webnew/wn-backend-v2/modules/blog/models"
)

// AuthorResponse represents the author data returned to clients
type AuthorResponse struct {
	ID          uint      `json:"id"`
	TenantID    uint      `json:"tenant_id"`
	UserID      uint      `json:"user_id"`
	DisplayName string    `json:"display_name"`
	Bio         string    `json:"bio"`
	AvatarURL   string    `json:"avatar_url"`
	Email       string    `json:"email"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// FromAuthorModel converts a model to a response DTO
func FromAuthorModel(author models.BlogAuthor) AuthorResponse {
	return AuthorResponse{
		ID:          author.ID,
		TenantID:    author.TenantID,
		UserID:      author.UserID,
		DisplayName: author.DisplayName,
		Bio:         author.Bio,
		AvatarURL:   author.AvatarURL,
		Email:       author.Email,
		IsActive:    author.IsActive,
		CreatedAt:   author.CreatedAt,
		UpdatedAt:   author.UpdatedAt,
	}
}

// AuthorListResponse represents a paginated list of authors
type AuthorListResponse struct {
	Authors []AuthorResponse `json:"authors"`
	Meta    struct {
		NextCursor string `json:"next_cursor"`
		HasMore    bool   `json:"has_more"`
	} `json:"meta"`
}

// NewAuthorListResponse creates a new paginated list response
func NewAuthorListResponse(authors []models.BlogAuthor, nextCursor string, hasMore bool) AuthorListResponse {
	response := AuthorListResponse{}
	response.Authors = make([]AuthorResponse, len(authors))

	for i, author := range authors {
		response.Authors[i] = FromAuthorModel(author)
	}

	response.Meta.NextCursor = nextCursor
	response.Meta.HasMore = hasMore

	return response
}
