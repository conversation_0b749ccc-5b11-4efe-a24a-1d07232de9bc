package response

import (
	"time"

	"github.com/webnew/wn-backend-v2/modules/blog/models"
)

// TagResponse represents a tag for API responses
type TagResponse struct {
	ID          uint      `json:"id"`
	Name        string    `json:"name"`
	Slug        string    `json:"slug"`
	Description string    `json:"description"`
	IsActive    bool      `json:"is_active"`
	PostCount   int       `json:"post_count,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// FromTag converts a tag model to a tag response
func FromTag(tag *models.Tag) TagResponse {
	return TagResponse{
		ID:          tag.TagID,
		Name:        tag.Name,
		Slug:        tag.Slug,
		Description: tag.Description,
		IsActive:    tag.IsActive,
		PostCount:   tag.PostCount,
		CreatedAt:   tag.CreatedAt,
		UpdatedAt:   tag.UpdatedAt,
	}
}

// FromTagList converts a list of tag models to a list of tag responses
func FromTagList(tags []*models.Tag) []TagResponse {
	responses := make([]TagResponse, len(tags))
	for i, tag := range tags {
		responses[i] = FromTag(tag)
	}
	return responses
}
