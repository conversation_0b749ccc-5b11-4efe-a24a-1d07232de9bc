package response

import "time"

// UserInfo represents minimal user information for posts
type UserInfo struct {
	ID        uint   `json:"id"`
	Username  string `json:"username"`
	Email     string `json:"email,omitempty"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	AvatarURL string `json:"avatar_url"`
}

// PostResponse represents a post in the response
type PostResponse struct {
	ID            uint               `json:"id"`
	Title         string             `json:"title"`
	Slug          string             `json:"slug"`
	Description   string             `json:"description"`
	Content       string             `json:"content,omitempty"`
	FeaturedImage string             `json:"featured_image"`
	Status        string             `json:"status"`
	Visibility    string             `json:"visibility"`
	CommentStatus string             `json:"comment_status"`
	PublishDate   *time.Time         `json:"publish_date,omitempty"`
	CreatedAt     time.Time          `json:"created_at"`
	UpdatedAt     time.Time          `json:"updated_at"`
	Author        *UserInfo          `json:"author,omitempty"`
	Editor        *UserInfo          `json:"editor,omitempty"`
	Categories    []CategoryResponse `json:"categories,omitempty"`
	Tags          []PostTagResponse  `json:"tags,omitempty"`
	SeoMeta       *SeoMetaResponse   `json:"seo_meta,omitempty"`
}

// PostTagResponse represents a tag in the post response
type PostTagResponse struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	Slug string `json:"slug"`
}
