package response

// CategoryListResponse represents a list of categories with pagination metadata
type CategoryListResponse struct {
	Categories []*CategoryResponse `json:"categories"`
	Meta       CategoryListMeta    `json:"meta"`
}

// CategoryListMeta represents pagination metadata for category lists
type CategoryListMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
}

// CategoryTreeResponse represents a tree of categories
type CategoryTreeResponse struct {
	Categories []*CategoryTreeItem `json:"categories"`
}
