package response

import "time"

// CategoryResponse represents a category in the response
type CategoryResponse struct {
	ID              uint      `json:"id"`
	TenantID        uint      `json:"tenant_id"`
	ParentID        *uint     `json:"parent_id"`
	Name            string    `json:"name"`
	Slug            string    `json:"slug"`
	Description     string    `json:"description"`
	FeaturedImage   string    `json:"featured_image"`
	Left            int       `json:"lft"`
	Right           int       `json:"rgt"`
	Depth           int       `json:"depth"`
	Position        int       `json:"position"`
	IsActive        bool      `json:"is_active"`
	IsFeatured      bool      `json:"is_featured"`
	MetaTitle       string    `json:"meta_title"`
	MetaDescription string    `json:"meta_description"`
	PostCount       int       `json:"post_count,omitempty"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	CreatedBy       uint      `json:"created_by"`
	UpdatedBy       uint      `json:"updated_by"`
}

// CategoryTreeItem represents a category with its children in a tree structure
type CategoryTreeItem struct {
	CategoryResponse
	Children []*CategoryTreeItem `json:"children,omitempty"`
}
