package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"

	"github.com/webnew/wn-backend-v2/modules/blog/dto/request"
	"github.com/webnew/wn-backend-v2/modules/blog/models"
)

// PostRepository implements the repository.PostRepository interface with MySQL
type PostRepository struct {
	db *sqlx.DB
}

// NewPostRepository creates a new PostRepository instance
func NewPostRepository(db *sqlx.DB) *PostRepository {
	return &PostRepository{
		db: db,
	}
}

// Create adds a new post to the database
func (r *PostRepository) Create(ctx context.Context, post *models.Post) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Generate slug if not provided
	if post.Slug == "" {
		post.Slug = generatePostSlug(post.Title)
	}

	// Check if slug exists for this tenant
	var count int
	err = tx.GetContext(ctx, &count, "SELECT COUNT(*) FROM blog_posts WHERE tenant_id = ? AND slug = ?",
		post.TenantID, post.Slug)
	if err != nil {
		return fmt.Errorf("failed to check slug uniqueness: %w", err)
	}

	if count > 0 {
		return ErrConflict
	}

	// Set default values if not provided
	if post.Status == "" {
		post.Status = "draft"
	}
	if post.Visibility == "" {
		post.Visibility = "public"
	}
	if post.CommentStatus == "" {
		post.CommentStatus = "open"
	}

	// Insert the new post
	query := `
		INSERT INTO blog_posts (
			tenant_id, title, slug, description, content, featured_image,
			status, visibility, password, comment_status, publish_date,
			created_at, updated_at, author_id, created_by
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	now := time.Now()
	result, err := tx.ExecContext(ctx, query,
		post.TenantID, post.Title, post.Slug, post.Description, post.Content,
		post.FeaturedImage, post.Status, post.Visibility, post.Password,
		post.CommentStatus, post.PublishDate, now, now, post.AuthorID, post.CreatedBy)

	if err != nil {
		return fmt.Errorf("failed to insert post: %w", err)
	}

	// Get the inserted ID
	postID, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get inserted id: %w", err)
	}

	post.PostID = uint(postID) // Convert int64 to uint
	post.CreatedAt = now
	post.UpdatedAt = now

	// Save categories if provided
	if len(post.CategoryIDs) > 0 {
		err = r.SetPostCategories(ctx, post.TenantID, uint(postID), post.CategoryIDs) // Convert int64 to uint
		if err != nil {
			return fmt.Errorf("failed to set post categories: %w", err)
		}
	}

	// Save tags if provided
	if len(post.TagIDs) > 0 {
		err = r.SetPostTags(ctx, post.TenantID, uint(postID), post.TagIDs) // Convert int64 to uint
		if err != nil {
			return fmt.Errorf("failed to set post tags: %w", err)
		}
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetByID retrieves a post by its ID
func (r *PostRepository) GetByID(ctx context.Context, tenantID, postID uint) (*models.Post, error) {
	var post models.Post

	query := `
		SELECT * FROM blog_posts
		WHERE tenant_id = ? AND post_id = ?
	`

	err := r.db.GetContext(ctx, &post, query, tenantID, postID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	return &post, nil
}

// GetBySlug retrieves a post by its slug
func (r *PostRepository) GetBySlug(ctx context.Context, tenantID uint, slug string) (*models.Post, error) {
	var post models.Post

	query := `
		SELECT * FROM blog_posts
		WHERE tenant_id = ? AND slug = ?
	`

	err := r.db.GetContext(ctx, &post, query, tenantID, slug)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get post by slug: %w", err)
	}

	return &post, nil
}

// Update updates an existing post
func (r *PostRepository) Update(ctx context.Context, post *models.Post) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Check if the post exists
	var existingPost models.Post
	err = tx.GetContext(ctx, &existingPost, "SELECT post_id, slug FROM blog_posts WHERE tenant_id = ? AND post_id = ?",
		post.TenantID, post.PostID)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrNotFound
		}
		return fmt.Errorf("failed to get existing post: %w", err)
	}

	// If slug is changing, check for uniqueness
	if post.Slug != existingPost.Slug && post.Slug != "" {
		var count int
		err = tx.GetContext(ctx, &count, "SELECT COUNT(*) FROM blog_posts WHERE tenant_id = ? AND slug = ? AND post_id != ?",
			post.TenantID, post.Slug, post.PostID)
		if err != nil {
			return fmt.Errorf("failed to check slug uniqueness: %w", err)
		}

		if count > 0 {
			return ErrConflict
		}
	}

	// Update the post
	query := `
		UPDATE blog_posts SET
			title = COALESCE(?, title),
			slug = COALESCE(?, slug),
			description = COALESCE(?, description),
			content = COALESCE(?, content),
			featured_image = COALESCE(?, featured_image),
			status = COALESCE(?, status),
			visibility = COALESCE(?, visibility),
			password = ?,
			comment_status = COALESCE(?, comment_status),
			publish_date = ?,
			updated_at = ?,
			created_by = ?
		WHERE tenant_id = ? AND post_id = ?
	`

	// Prepare values, handling optional fields
	var password *string
	if post.Password != nil && *post.Password != "" {
		password = post.Password
	}

	_, err = tx.ExecContext(ctx, query,
		nullIfEmpty(post.Title),
		nullIfEmpty(post.Slug),
		nullIfEmpty(post.Description),
		nullIfEmpty(post.Content),
		nullIfEmpty(post.FeaturedImage),
		nullIfEmpty(post.Status),
		nullIfEmpty(post.Visibility),
		password,
		nullIfEmpty(post.CommentStatus),
		post.PublishDate,
		time.Now(),
		post.CreatedBy,
		post.TenantID,
		post.PostID)

	if err != nil {
		return fmt.Errorf("failed to update post: %w", err)
	}

	// Update categories if provided
	if post.CategoryIDs != nil {
		err = r.SetPostCategories(ctx, post.TenantID, post.PostID, post.CategoryIDs)
		if err != nil {
			return fmt.Errorf("failed to update post categories: %w", err)
		}
	}

	// Update tags if provided
	if post.TagIDs != nil {
		err = r.SetPostTags(ctx, post.TenantID, post.PostID, post.TagIDs)
		if err != nil {
			return fmt.Errorf("failed to update post tags: %w", err)
		}
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// Delete removes a post
func (r *PostRepository) Delete(ctx context.Context, tenantID, postID uint) error {
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}

	defer func() {
		if err != nil {
			_ = tx.Rollback()
		}
	}()

	// Delete post-category relationships
	_, err = tx.ExecContext(ctx, "DELETE FROM blog_post_categories WHERE tenant_id = ? AND post_id = ?",
		tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to delete post categories: %w", err)
	}

	// Delete post-tag relationships
	_, err = tx.ExecContext(ctx, "DELETE FROM blog_post_tags WHERE tenant_id = ? AND post_id = ?",
		tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to delete post tags: %w", err)
	}

	// Delete the post
	result, err := tx.ExecContext(ctx, "DELETE FROM blog_posts WHERE tenant_id = ? AND post_id = ?",
		tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to delete post: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return ErrNotFound
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// List retrieves a paginated list of posts
func (r *PostRepository) List(ctx context.Context, tenantID uint, req request.ListPostRequest) ([]*models.Post, string, bool, error) {
	var posts []*models.Post

	// Base query
	baseQuery := `
		SELECT p.*
		FROM blog_posts p
	`

	// Conditions
	conditions := []string{"p.tenant_id = ?"}
	args := []interface{}{tenantID}

	// Add filters
	if req.Status != "" {
		conditions = append(conditions, "p.status = ?")
		args = append(args, req.Status)
	}

	if req.CategoryID != nil {
		baseQuery += " JOIN blog_post_categories pc ON p.post_id = pc.post_id AND p.tenant_id = pc.tenant_id"
		conditions = append(conditions, "pc.category_id = ?")
		args = append(args, *req.CategoryID)
	}

	if req.TagID != nil {
		baseQuery += " JOIN blog_post_tags pt ON p.post_id = pt.post_id AND p.tenant_id = pt.tenant_id"
		conditions = append(conditions, "pt.tag_id = ?")
		args = append(args, *req.TagID)
	}

	if req.AuthorID != nil {
		conditions = append(conditions, "p.author_id = ?")
		args = append(args, *req.AuthorID)
	}

	if req.Search != "" {
		conditions = append(conditions, "(p.title LIKE ? OR p.description LIKE ? OR p.content LIKE ?)")
		searchPattern := "%" + req.Search + "%"
		args = append(args, searchPattern, searchPattern, searchPattern)
	}

	// Apply cursor pagination
	if req.Cursor != "" {
		decodedCursor, err := decodeCursor(req.Cursor)
		if err != nil {
			return nil, "", false, fmt.Errorf("invalid cursor: %w", err)
		}

		// Assuming post_id is always the cursor key
		conditions = append(conditions, "p.post_id < ?")
		args = append(args, decodedCursor)
	}

	// Combine conditions
	whereClause := ""
	if len(conditions) > 0 {
		whereClause = " WHERE " + strings.Join(conditions, " AND ")
	}

	// Order and limit
	orderBy := "p.post_id DESC" // Default sorting by ID descending
	if req.OrderBy == "newest" {
		orderBy = "p.publish_date DESC, p.post_id DESC"
	} else if req.OrderBy == "oldest" {
		orderBy = "p.publish_date ASC, p.post_id ASC"
	} else if req.OrderBy == "title" {
		orderBy = "p.title ASC, p.post_id DESC"
	}

	limit := 10 // Default limit
	if req.Limit > 0 {
		limit = req.Limit
	}
	if limit > 100 {
		limit = 100 // Enforce max limit
	}

	// Complete query
	query := baseQuery + whereClause + " ORDER BY " + orderBy + " LIMIT ?"
	args = append(args, limit+1) // Fetch one extra for has_more check

	// Execute query
	err := r.db.SelectContext(ctx, &posts, query, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list posts: %w", err)
	}

	// Check if there are more results
	hasMore := false
	var nextCursor string
	if len(posts) > limit {
		hasMore = true
		posts = posts[:limit]
	}

	// Generate next cursor if there are more results
	if hasMore && len(posts) > 0 {
		nextCursor = encodeCursor(posts[len(posts)-1].PostID)
	}

	return posts, nextCursor, hasMore, nil
}

// GetPostCategories retrieves the categories for a post
func (r *PostRepository) GetPostCategories(ctx context.Context, tenantID, postID uint) ([]uint, error) {
	var categoryIDs []uint

	query := `
		SELECT category_id
		FROM blog_post_categories
		WHERE tenant_id = ? AND post_id = ?
	`

	err := r.db.SelectContext(ctx, &categoryIDs, query, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get post categories: %w", err)
	}

	return categoryIDs, nil
}

// SetPostCategories sets the categories for a post
func (r *PostRepository) SetPostCategories(ctx context.Context, tenantID, postID uint, categoryIDs []uint) error {
	// Lấy danh sách category IDs hiện tại
	var existingCategoryIDs []uint
	err := r.db.SelectContext(ctx, &existingCategoryIDs,
		"SELECT category_id FROM blog_post_categories WHERE tenant_id = ? AND post_id = ?",
		tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to get existing categories: %w", err)
	}

	// Tìm category IDs cần xóa (có trong existingCategoryIDs nhưng không có trong categoryIDs)
	var toRemove []uint
	for _, existingID := range existingCategoryIDs {
		found := false
		for _, newID := range categoryIDs {
			if existingID == newID {
				found = true
				break
			}
		}
		if !found {
			toRemove = append(toRemove, existingID)
		}
	}

	// Tìm category IDs cần thêm (có trong categoryIDs nhưng không có trong existingCategoryIDs)
	var toAdd []uint
	for _, newID := range categoryIDs {
		found := false
		for _, existingID := range existingCategoryIDs {
			if newID == existingID {
				found = true
				break
			}
		}
		if !found {
			toAdd = append(toAdd, newID)
		}
	}

	// Xóa các categories không còn cần thiết
	if len(toRemove) > 0 {
		query := "DELETE FROM blog_post_categories WHERE tenant_id = ? AND post_id = ? AND category_id IN (?"
		args := []interface{}{tenantID, postID, toRemove[0]}

		for i := 1; i < len(toRemove); i++ {
			query += ",?"
			args = append(args, toRemove[i])
		}
		query += ")"

		_, err = r.db.ExecContext(ctx, query, args...)
		if err != nil {
			return fmt.Errorf("failed to delete categories: %w", err)
		}
	}

	// Thêm categories mới
	if len(toAdd) > 0 {
		query := "INSERT INTO blog_post_categories (tenant_id, post_id, category_id) VALUES "
		var values []string
		var args []interface{}

		for _, categoryID := range toAdd {
			values = append(values, "(?, ?, ?)")
			args = append(args, tenantID, postID, categoryID)
		}

		query += strings.Join(values, ", ")
		_, err = r.db.ExecContext(ctx, query, args...)
		if err != nil {
			return fmt.Errorf("failed to insert categories: %w", err)
		}
	}

	return nil
}

// GetPostTags retrieves the tags for a post
func (r *PostRepository) GetPostTags(ctx context.Context, tenantID, postID uint) ([]uint, error) {
	var tagIDs []uint

	query := `
		SELECT tag_id
		FROM blog_post_tags
		WHERE tenant_id = ? AND post_id = ?
	`

	err := r.db.SelectContext(ctx, &tagIDs, query, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get post tags: %w", err)
	}

	return tagIDs, nil
}

// SetPostTags sets the tags for a post
func (r *PostRepository) SetPostTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	// Lấy danh sách tag IDs hiện tại
	var existingTagIDs []uint
	err := r.db.SelectContext(ctx, &existingTagIDs,
		"SELECT tag_id FROM blog_post_tags WHERE tenant_id = ? AND post_id = ?",
		tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to get existing tags: %w", err)
	}

	// Tìm tag IDs cần xóa (có trong existingTagIDs nhưng không có trong tagIDs)
	var toRemove []uint
	for _, existingID := range existingTagIDs {
		found := false
		for _, newID := range tagIDs {
			if existingID == newID {
				found = true
				break
			}
		}
		if !found {
			toRemove = append(toRemove, existingID)
		}
	}

	// Tìm tag IDs cần thêm (có trong tagIDs nhưng không có trong existingTagIDs)
	var toAdd []uint
	for _, newID := range tagIDs {
		found := false
		for _, existingID := range existingTagIDs {
			if newID == existingID {
				found = true
				break
			}
		}
		if !found {
			toAdd = append(toAdd, newID)
		}
	}

	// Xóa các tags không còn cần thiết
	if len(toRemove) > 0 {
		query := "DELETE FROM blog_post_tags WHERE tenant_id = ? AND post_id = ? AND tag_id IN (?"
		args := []interface{}{tenantID, postID, toRemove[0]}

		for i := 1; i < len(toRemove); i++ {
			query += ",?"
			args = append(args, toRemove[i])
		}
		query += ")"

		// Tăng timeout cho thao tác xóa
		deleteCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
		defer cancel()

		_, err = r.db.ExecContext(deleteCtx, query, args...)
		if err != nil {
			return fmt.Errorf("failed to delete tags: %w", err)
		}
	}

	// Thêm tags mới - thực hiện một lệnh INSERT với nhiều giá trị
	if len(toAdd) > 0 {
		// Tạo context mới với timeout dài hơn
		insertCtx, cancel := context.WithTimeout(ctx, 15*time.Second)
		defer cancel()

		// Xây dựng truy vấn để chèn nhiều giá trị cùng lúc
		query := "INSERT IGNORE INTO blog_post_tags (tenant_id, post_id, tag_id) VALUES "
		var placeholders []string
		var args []interface{}

		for _, tagID := range toAdd {
			placeholders = append(placeholders, "(?, ?, ?)")
			args = append(args, tenantID, postID, tagID)
		}

		query += strings.Join(placeholders, ", ")

		// Thực hiện truy vấn INSERT với tất cả tag cùng lúc
		_, err = r.db.ExecContext(insertCtx, query, args...)
		if err != nil {
			return fmt.Errorf("failed to insert tags: %w", err)
		}
	}

	return nil
}

// CountPosts counts posts with optional filters
func (r *PostRepository) CountPosts(ctx context.Context, tenantID uint, status *string, categoryID *uint) (int, error) {
	baseQuery := "SELECT COUNT(*) FROM blog_posts p"
	conditions := []string{"p.tenant_id = ?"}
	args := []interface{}{tenantID}

	if status != nil {
		conditions = append(conditions, "p.status = ?")
		args = append(args, *status)
	}

	if categoryID != nil {
		baseQuery += " JOIN blog_post_categories pc ON p.post_id = pc.post_id AND p.tenant_id = pc.tenant_id"
		conditions = append(conditions, "pc.category_id = ?")
		args = append(args, *categoryID)
	}

	whereClause := " WHERE " + strings.Join(conditions, " AND ")
	query := baseQuery + whereClause

	var count int
	err := r.db.GetContext(ctx, &count, query, args...)
	if err != nil {
		return 0, fmt.Errorf("failed to count posts: %w", err)
	}

	return count, nil
}

// Helper functions

// nullIfEmpty returns nil if the string is empty
func nullIfEmpty(s string) interface{} {
	if s == "" {
		return nil
	}
	return s
}

// generatePostSlug creates a URL-friendly slug from a string
func generatePostSlug(input string) string {
	// Convert to lowercase
	slug := strings.ToLower(input)

	// Replace non-alphanumeric characters with hyphens
	slug = strings.Map(func(r rune) rune {
		if (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') {
			return r
		}
		return '-'
	}, slug)

	// Replace multiple hyphens with a single one
	for strings.Contains(slug, "--") {
		slug = strings.ReplaceAll(slug, "--", "-")
	}

	// Trim hyphens from beginning and end
	slug = strings.Trim(slug, "-")

	// Truncate if too long
	if len(slug) > 255 {
		slug = slug[:255]
	}

	return slug
}

// encodeCursor encodes the cursor value
func encodeCursor(id uint) string {
	return strconv.FormatUint(uint64(id), 10)
}

// decodeCursor decodes the cursor value
func decodeCursor(cursor string) (uint, error) {
	val, err := strconv.ParseUint(cursor, 10, 64)
	return uint(val), err
}
