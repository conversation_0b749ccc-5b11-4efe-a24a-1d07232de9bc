package configs

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/spf13/viper"
)

// Config cấu hình cho RBAC module
type Config struct {
	Server  ServerConfig  `mapstructure:"server"`
	DB      DBConfig      `mapstructure:"db"`
	JWT     JWTConfig     `mapstructure:"jwt"`
	Tracing TracingConfig `mapstructure:"tracing"`
	HTTP    HTTPConfig    `yaml:"http"`
	GRPC    GRPCConfig    `yaml:"grpc"`
}

// ServerConfig cấu hình cho server
type ServerConfig struct {
	Host string `mapstructure:"host"`
	Port int    `mapstructure:"port"`
}

// DBConfig cấu hình cho database
type DBConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
}

// JWTConfig đại diện cho cấu hình JWT
type JWTConfig struct {
	AccessSecret          string        `mapstructure:"access_signing_key"`
	RefreshSecret         string        `mapstructure:"refresh_signing_key"`
	AccessExpiryDuration  string        `mapstructure:"access_token_expiration"`
	RefreshExpiryDuration string        `mapstructure:"refresh_token_expiration"`
	AccessTokenDuration   time.Duration // Thời gian hết hạn đã chuyển đổi
	RefreshTokenDuration  time.Duration // Thời gian hết hạn đã chuyển đổi
	Issuer                string        `mapstructure:"issuer"`
}

// TracingConfig cấu hình cho tracing
type TracingConfig struct {
	Enabled      bool         `mapstructure:"enabled"`
	ServiceName  string       `mapstructure:"service_name"`
	ExporterType string       `mapstructure:"exporter_type"`
	Signoz       SignozConfig `mapstructure:"signoz"`
	Jaeger       JaegerConfig `mapstructure:"jaeger"`
	SampleRatio  float64      `mapstructure:"sample_ratio"`
}

// SignozConfig cấu hình cho Signoz exporter
type SignozConfig struct {
	Endpoint string `mapstructure:"endpoint"`
}

// JaegerConfig cấu hình cho Jaeger exporter
type JaegerConfig struct {
	Host string `mapstructure:"host"`
	Port string `mapstructure:"port"`
}

// HTTPConfig chứa cấu hình HTTP server
type HTTPConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

// GRPCConfig chứa cấu hình GRPC server
type GRPCConfig struct {
	Host string `yaml:"host"`
	Port int    `yaml:"port"`
}

func LoadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("../configs")
	viper.AddConfigPath("../../configs")

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	// Chuyển đổi chuỗi thời gian thành time.Duration
	accessDuration, err := time.ParseDuration(config.JWT.AccessExpiryDuration)
	if err == nil {
		config.JWT.AccessTokenDuration = accessDuration
	}

	refreshDuration, err := time.ParseDuration(config.JWT.RefreshExpiryDuration)
	if err == nil {
		config.JWT.RefreshTokenDuration = refreshDuration
	}

	// Override with environment variables if set
	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.DB.Host = dbHost
	}

	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		if port, err := strconv.Atoi(dbPort); err == nil {
			config.DB.Port = port
		}
	}

	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		config.DB.Username = dbUser
	}

	if dbPass := os.Getenv("DB_PASSWORD"); dbPass != "" {
		config.DB.Password = dbPass
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.DB.Database = dbName
	}

	if httpPort := os.Getenv("HTTP_PORT"); httpPort != "" {
		if port, err := strconv.Atoi(httpPort); err == nil {
			config.Server.Port = port
		}
	}

	// JWT env vars
	if jwtAccessSecret := os.Getenv("JWT_ACCESS_SIGNING_KEY"); jwtAccessSecret != "" {
		config.JWT.AccessSecret = jwtAccessSecret
	}

	if jwtRefreshSecret := os.Getenv("JWT_REFRESH_SIGNING_KEY"); jwtRefreshSecret != "" {
		config.JWT.RefreshSecret = jwtRefreshSecret
	}

	// Ưu tiên lấy issuer từ AUTH_JWT_ISSUER nếu có
	if jwtIssuer := os.Getenv("AUTH_JWT_ISSUER"); jwtIssuer != "" {
		config.JWT.Issuer = jwtIssuer
	} else if jwtIssuer := os.Getenv("JWT_ISSUER"); jwtIssuer != "" {
		config.JWT.Issuer = jwtIssuer
	}

	if config.JWT.Issuer == "" {
		return nil, fmt.Errorf("Thiếu biến môi trường AUTH_JWT_ISSUER hoặc JWT_ISSUER cho issuer")
	}

	if jwtAccessExpiry := os.Getenv("JWT_ACCESS_TOKEN_EXPIRATION"); jwtAccessExpiry != "" {
		config.JWT.AccessExpiryDuration = jwtAccessExpiry
	}

	if jwtRefreshExpiry := os.Getenv("JWT_REFRESH_TOKEN_EXPIRATION"); jwtRefreshExpiry != "" {
		config.JWT.RefreshExpiryDuration = jwtRefreshExpiry
	}

	// Handle tracing configuration from environment variables
	if tracingEnabled := os.Getenv("TRACING_ENABLED"); tracingEnabled != "" {
		config.Tracing.Enabled = tracingEnabled == "true"
	}

	if tracingServiceName := os.Getenv("TRACING_SERVICE_NAME"); tracingServiceName != "" {
		config.Tracing.ServiceName = tracingServiceName
	}

	if tracingExporterType := os.Getenv("TRACING_EXPORTER_TYPE"); tracingExporterType != "" {
		config.Tracing.ExporterType = tracingExporterType
	}

	if signozEndpoint := os.Getenv("SIGNOZ_ENDPOINT"); signozEndpoint != "" {
		config.Tracing.Signoz.Endpoint = signozEndpoint
	}

	if jaegerHost := os.Getenv("JAEGER_AGENT_HOST"); jaegerHost != "" {
		config.Tracing.Jaeger.Host = jaegerHost
	}

	if jaegerPort := os.Getenv("JAEGER_AGENT_PORT"); jaegerPort != "" {
		config.Tracing.Jaeger.Port = jaegerPort
	}

	if sampleRatio := os.Getenv("TRACING_SAMPLE_RATIO"); sampleRatio != "" {
		if ratio, err := strconv.ParseFloat(sampleRatio, 64); err == nil {
			config.Tracing.SampleRatio = ratio
		}
	}

	// Set default values if they are not set
	if config.DB.Host == "" {
		config.DB.Host = "mysql"
	}
	if config.DB.Port == 0 {
		config.DB.Port = 3306
	}
	if config.DB.Username == "" {
		config.DB.Username = "root"
	}
	if config.DB.Database == "" {
		config.DB.Database = "blog_v4"
	}
	if config.Server.Host == "" {
		config.Server.Host = "0.0.0.0"
	}
	if config.Server.Port == 0 {
		config.Server.Port = 9040
	}
	if config.JWT.AccessSecret == "" {
		config.JWT.AccessSecret = "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
	}
	if config.JWT.RefreshSecret == "" {
		config.JWT.RefreshSecret = "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
	}
	if config.JWT.AccessExpiryDuration == "" {
		config.JWT.AccessExpiryDuration = "168h"
	}
	if config.JWT.RefreshExpiryDuration == "" {
		config.JWT.RefreshExpiryDuration = "168h"
	}
	if config.JWT.Issuer == "" {
		config.JWT.Issuer = "wn-backend"
	}
	if config.Tracing.ServiceName == "" {
		config.Tracing.ServiceName = "rbac-service"
	}
	if config.Tracing.ExporterType == "" {
		config.Tracing.ExporterType = "signoz"
	}
	if config.Tracing.SampleRatio == 0 {
		config.Tracing.SampleRatio = 1.0
	}

	// Print config in Docker environment
	if os.Getenv("DOCKER_ENV") == "true" {
		PrintConfig(&config)
	}

	return &config, nil
}

// PrintConfig prints all configuration values in a formatted JSON
func PrintConfig(config *Config) {
	// Create a copy of the config with the password masked for security
	configCopy := *config
	configCopy.DB.Password = "********"

	// Marshal config to JSON for pretty printing
	configJSON, err := json.MarshalIndent(configCopy, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling config: %v\n", err)
		return
	}

	fmt.Printf("=== RBAC MODULE CONFIGURATION ===\n%s\n==============================\n", string(configJSON))
}
