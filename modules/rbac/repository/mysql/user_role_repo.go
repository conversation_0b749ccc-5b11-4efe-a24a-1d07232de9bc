// modules/rbac/repository/mysql/user_role_repo.go
package mysql

import (
	"wnapi/internal/pkg/logger"
	internalRepo "wnapi/modules/rbac/internal/repository/mysql"
	"wnapi/modules/rbac/repository"

	"gorm.io/gorm"
)

// NewUserRoleRepository tạo một instance mới của UserRoleRepository
func NewUserRoleRepository(db *gorm.DB, logger logger.Logger) repository.UserRoleRepository {
	return internalRepo.NewMySQLUserRoleRepository(db, logger)
}
