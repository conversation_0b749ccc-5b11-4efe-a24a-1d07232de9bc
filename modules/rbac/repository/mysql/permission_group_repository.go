package mysql

import (
	"context"
	"database/sql"
	"fmt"

	"wnapi/modules/rbac/models"

	"github.com/jmoiron/sqlx"
)

type PermissionGroupRepository interface {
	Create(ctx context.Context, group *models.PermissionGroup) error
	Update(ctx context.Context, group *models.PermissionGroup) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*models.PermissionGroup, error)
	List(ctx context.Context, tenantID *uint, limit int, cursor *uint) ([]*models.PermissionGroup, *uint, bool, error)
}

type permissionGroupRepository struct {
	db *sql.DB
}

func NewPermissionGroupRepository(db *sqlx.DB) PermissionGroupRepository {
	return &permissionGroupRepository{db: db.DB}
}

func (r *permissionGroupRepository) Create(ctx context.Context, group *models.PermissionGroup) error {
	query := `INSERT INTO rbac_permission_groups (tenant_id, permission_group_name, permission_group_description, created_by, created_at, updated_by, updated_at)
	  VALUES (?, ?, ?, ?, NOW(), ?, NOW())`
	_, err := r.db.ExecContext(ctx, query, group.TenantID, group.PermissionGroupName, group.PermissionGroupDescription, group.CreatedBy, group.UpdatedBy)
	return err
}

func (r *permissionGroupRepository) Update(ctx context.Context, group *models.PermissionGroup) error {
	query := `UPDATE rbac_permission_groups SET permission_group_name = ?, permission_group_description = ?, updated_by = ?, updated_at = NOW() WHERE group_id = ?`
	_, err := r.db.ExecContext(ctx, query, group.PermissionGroupName, group.PermissionGroupDescription, group.UpdatedBy, group.GroupID)
	return err
}

func (r *permissionGroupRepository) Delete(ctx context.Context, id uint) error {
	query := `DELETE FROM rbac_permission_groups WHERE group_id = ?`
	_, err := r.db.ExecContext(ctx, query, id)
	return err
}

func (r *permissionGroupRepository) GetByID(ctx context.Context, id uint) (*models.PermissionGroup, error) {
	query := `SELECT * FROM rbac_permission_groups WHERE group_id = ?`
	var group models.PermissionGroup
	err := r.db.QueryRowContext(ctx, query, id).Scan(
		&group.GroupID, &group.TenantID, &group.PermissionGroupName, &group.PermissionGroupDescription,
		&group.CreatedBy, &group.CreatedAt, &group.UpdatedBy, &group.UpdatedAt,
	)
	if err != nil {
		return nil, err
	}
	return &group, nil
}

func (r *permissionGroupRepository) List(ctx context.Context, tenantID *uint, limit int, cursor *uint) ([]*models.PermissionGroup, *uint, bool, error) {
	where := ""
	args := []interface{}{}
	if tenantID != nil {
		where = "WHERE tenant_id = ?"
		args = append(args, *tenantID)
	}
	if cursor != nil {
		if where == "" {
			where = "WHERE group_id > ?"
		} else {
			where += " AND group_id > ?"
		}
		args = append(args, *cursor)
	}
	query := fmt.Sprintf(`SELECT * FROM rbac_permission_groups %s ORDER BY group_id LIMIT ?`, where)
	args = append(args, limit+1)
	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, nil, false, err
	}
	defer rows.Close()
	groups := []*models.PermissionGroup{}
	var lastID *uint
	for rows.Next() {
		var g models.PermissionGroup
		err := rows.Scan(&g.GroupID, &g.TenantID, &g.PermissionGroupName, &g.PermissionGroupDescription, &g.CreatedBy, &g.CreatedAt, &g.UpdatedBy, &g.UpdatedAt)
		if err != nil {
			return nil, nil, false, err
		}
		groups = append(groups, &g)
		lastID = &g.GroupID
	}
	hasMore := false
	if len(groups) > limit {
		hasMore = true
		groups = groups[:limit]
	}
	var nextCursor *uint
	if hasMore {
		nextCursor = lastID
	}
	return groups, nextCursor, hasMore, nil
}
