package mysql

import (
	"context"
	"database/sql"
	"errors"

	"github.com/jmoiron/sqlx"

	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

type userRoleRepository struct {
	db *sqlx.DB
}

// NewUserRoleRepository tạo một instance mới của UserRoleRepository
func NewUserRoleRepository(db *sqlx.DB) repository.UserRoleRepository {
	return &userRoleRepository{
		db: db,
	}
}

// AssignRole gán vai trò cho người dùng
func (r *userRoleRepository) AssignRole(ctx context.Context, tenantID uint, userID uint, roleID uint, createdBy *uint) error {
	// Kiểm tra role có tồn tại trong tenant không
	existsQuery := `SELECT COUNT(*) FROM rbac_roles WHERE role_id = ? AND tenant_id = ?`

	var count int
	err := r.db.GetContext(ctx, &count, existsQuery, roleID, tenantID)
	if err != nil {
		return err
	}

	if count == 0 {
		return errors.New("role not found or not in specified tenant")
	}

	// Kiểm tra xem gán vai trò này đã tồn tại chưa
	checkQuery := `SELECT COUNT(*) FROM rbac_user_roles WHERE user_id = ? AND role_id = ? AND tenant_id = ?`

	err = r.db.GetContext(ctx, &count, checkQuery, userID, roleID, tenantID)
	if err != nil {
		return err
	}

	// Nếu đã tồn tại, trả về thành công luôn
	if count > 0 {
		return nil
	}

	// Thêm mới
	insertQuery := `
		INSERT INTO rbac_user_roles (tenant_id, user_id, role_id, created_by)
		VALUES (?, ?, ?, ?)
	`

	_, err = r.db.ExecContext(ctx, insertQuery, tenantID, userID, roleID, createdBy)
	return err
}

// RevokeRole thu hồi vai trò từ người dùng
func (r *userRoleRepository) RevokeRole(ctx context.Context, tenantID uint, userID uint, roleID uint) error {
	query := `DELETE FROM rbac_user_roles WHERE user_id = ? AND role_id = ? AND tenant_id = ?`

	result, err := r.db.ExecContext(ctx, query, userID, roleID, tenantID)
	if err != nil {
		return err
	}

	// Kiểm tra có dòng nào bị xóa không
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return errors.New("user does not have specified role in this tenant")
	}

	return nil
}

// GetUserRoles lấy danh sách vai trò của người dùng
func (r *userRoleRepository) GetUserRoles(ctx context.Context, tenantID uint, userID uint) ([]*models.UserRole, error) {
	query := `
		SELECT * FROM rbac_user_roles
		WHERE tenant_id = ? AND user_id = ?
	`

	var userRoles []*models.UserRole
	err := r.db.SelectContext(ctx, &userRoles, query, tenantID, userID)
	if err != nil {
		return nil, err
	}

	return userRoles, nil
}

// HasRole kiểm tra người dùng có vai trò cụ thể không
func (r *userRoleRepository) HasRole(ctx context.Context, tenantID uint, userID uint, roleID uint) (bool, error) {
	query := `
		SELECT COUNT(*) FROM rbac_user_roles
		WHERE tenant_id = ? AND user_id = ? AND role_id = ?
	`

	var count int
	err := r.db.GetContext(ctx, &count, query, tenantID, userID, roleID)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetUsersWithRole lấy danh sách người dùng có vai trò cụ thể
func (r *userRoleRepository) GetUsersWithRole(ctx context.Context, tenantID uint, roleID uint) ([]uint, error) {
	query := `
		SELECT user_id FROM rbac_user_roles
		WHERE tenant_id = ? AND role_id = ?
	`

	var userIDs []uint
	err := r.db.SelectContext(ctx, &userIDs, query, tenantID, roleID)
	if err != nil {
		if err == sql.ErrNoRows {
			return []uint{}, nil
		}
		return nil, err
	}

	return userIDs, nil
}
