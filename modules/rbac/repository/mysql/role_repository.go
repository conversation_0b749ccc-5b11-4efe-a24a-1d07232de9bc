package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/jmoiron/sqlx"

	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

type roleRepository struct {
	db *sqlx.DB
}

// NewRoleRepository tạo một instance mới của RoleRepository
func NewRoleRepository(db *sqlx.DB) repository.RoleRepository {
	return &roleRepository{
		db: db,
	}
}

// Create tạo một role mới
func (r *roleRepository) Create(ctx context.Context, tenantID uint, role *models.Role) error {
	query := `
		INSERT INTO rbac_roles (
			tenant_id, role_code, role_name, role_description
		) VALUES (
			:tenant_id, :role_code, :role_name, :role_description
		)
	`

	role.TenantID = &tenantID
	result, err := r.db.NamedExecContext(ctx, query, role)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	role.RoleID = uint(id)
	return nil
}

// GetByID lấy role theo ID
func (r *roleRepository) GetByID(ctx context.Context, tenantID uint, roleID uint) (*models.Role, error) {
	query := `
		SELECT role_id, tenant_id, role_code, role_name, role_description, created_at, updated_at
		FROM rbac_roles
		WHERE tenant_id = ? AND role_id = ?
	`

	var role models.Role
	err := r.db.GetContext(ctx, &role, query, tenantID, roleID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("role not found")
		}
		return nil, err
	}

	return &role, nil
}

// Update cập nhật role
func (r *roleRepository) Update(ctx context.Context, role *models.Role) error {
	query := `
		UPDATE rbac_roles SET
			role_code = :role_code,
			role_name = :role_name,
			role_description = :role_description
		WHERE role_id = :role_id AND tenant_id = :tenant_id
	`

	_, err := r.db.NamedExecContext(ctx, query, role)
	return err
}

// Delete xóa role
func (r *roleRepository) Delete(ctx context.Context, tenantID uint, roleID uint) error {
	query := `DELETE FROM rbac_roles WHERE tenant_id = ? AND role_id = ?`

	_, err := r.db.ExecContext(ctx, query, tenantID, roleID)
	return err
}

// List lấy danh sách roles với phân trang cursor
func (r *roleRepository) List(ctx context.Context, tenantID uint, req request.ListRoleRequest) ([]*models.Role, string, bool, error) {
	var (
		roles       []*models.Role
		whereClause = "WHERE tenant_id = ?"
		args        = []interface{}{tenantID}
		query       string
		limit       = 20
	)

	if req.Limit > 0 {
		limit = req.Limit
	}

	// Nếu có cursor, thêm điều kiện để phân trang
	if req.Cursor != "" {
		whereClause += " AND role_id > ?"
		args = append(args, req.Cursor)
	}

	// Xây dựng query
	query = fmt.Sprintf(`
		SELECT role_id, tenant_id, role_code, role_name, role_description, created_at, updated_at
		FROM rbac_roles
		%s
		ORDER BY role_id
		LIMIT %d
	`, whereClause, limit+1) // +1 để kiểm tra có phần tử tiếp theo không

	// Thực hiện query
	err := r.db.SelectContext(ctx, &roles, query, args...)
	if err != nil {
		return nil, "", false, err
	}

	// Kiểm tra có phần tử tiếp theo không
	hasMore := false
	nextCursor := ""

	if len(roles) > limit {
		hasMore = true
		nextCursor = fmt.Sprintf("%d", roles[limit-1].RoleID)
		roles = roles[:limit]
	}

	return roles, nextCursor, hasMore, nil
}

// GetByCode lấy role theo code
func (r *roleRepository) GetByCode(ctx context.Context, tenantID uint, code string) (*models.Role, error) {
	query := `
		SELECT role_id, tenant_id, role_code, role_name, role_description, created_at, updated_at
		FROM rbac_roles
		WHERE tenant_id = ? AND role_code = ?
	`

	var role models.Role
	err := r.db.GetContext(ctx, &role, query, tenantID, code)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &role, nil
}

// GetRolePermissions lấy danh sách quyền của vai trò
func (r *roleRepository) GetRolePermissions(ctx context.Context, roleID uint) ([]uint, error) {
	query := `
		SELECT permission_id FROM rbac_role_permissions
		WHERE role_id = ?
	`

	var permissionIDs []uint
	err := r.db.SelectContext(ctx, &permissionIDs, query, roleID)
	if err != nil {
		return nil, err
	}

	return permissionIDs, nil
}

// SetRolePermissions thiết lập quyền cho vai trò
func (r *roleRepository) SetRolePermissions(ctx context.Context, roleID uint, permissionIDs []uint, grantedBy *uint) error {
	// Bắt đầu transaction
	tx, err := r.db.BeginTxx(ctx, nil)
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// Lấy danh sách quyền hiện tại của vai trò
	var currentPermissions []uint
	err = tx.SelectContext(ctx, &currentPermissions, "SELECT permission_id FROM rbac_role_permissions WHERE role_id = ?", roleID)
	if err != nil {
		return err
	}

	// Lọc bỏ các permission ID không hợp lệ (0 hoặc null)
	var validPermissionIDs []uint
	for _, permID := range permissionIDs {
		if permID > 0 {
			validPermissionIDs = append(validPermissionIDs, permID)
		}
	}

	// Tạo map để dễ dàng kiểm tra
	currentPermMap := make(map[uint]bool)
	for _, permID := range currentPermissions {
		if permID > 0 {
			currentPermMap[permID] = true
		}
	}

	newPermMap := make(map[uint]bool)
	for _, permID := range validPermissionIDs {
		newPermMap[permID] = true
	}

	// Xác định quyền cần xóa (có trong current nhưng không có trong new)
	var permissionsToDelete []uint
	for permID := range currentPermMap {
		if !newPermMap[permID] {
			permissionsToDelete = append(permissionsToDelete, permID)
		}
	}

	// Xác định quyền cần thêm (có trong new nhưng không có trong current)
	var permissionsToAdd []uint
	for permID := range newPermMap {
		if !currentPermMap[permID] {
			permissionsToAdd = append(permissionsToAdd, permID)
		}
	}

	// Xóa các quyền không còn cần thiết
	if len(permissionsToDelete) > 0 {
		query, args, err := sqlx.In("DELETE FROM rbac_role_permissions WHERE role_id = ? AND permission_id IN (?)", roleID, permissionsToDelete)
		if err != nil {
			return err
		}
		query = tx.Rebind(query)
		_, err = tx.ExecContext(ctx, query, args...)
		if err != nil {
			return err
		}
	}

	// Thêm các quyền mới
	if len(permissionsToAdd) > 0 {
		insertQuery := `
			INSERT INTO rbac_role_permissions (role_id, permission_id, created_by, created_at)
			VALUES (?, ?, ?, NOW())
		`

		for _, permID := range permissionsToAdd {
			// Kiểm tra lại một lần nữa để đảm bảo permission ID hợp lệ
			if permID > 0 {
				_, err = tx.ExecContext(ctx, insertQuery, roleID, permID, grantedBy)
				if err != nil {
					return err
				}
			}
		}
	}

	return tx.Commit()
}
