package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/jmoiron/sqlx"

	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

type permissionRepository struct {
	db *sqlx.DB
}

// NewPermissionRepository tạo một instance mới của PermissionRepository
func NewPermissionRepository(db *sqlx.DB) repository.PermissionRepository {
	return &permissionRepository{
		db: db,
	}
}

// Create tạo một permission mới
func (r *permissionRepository) Create(ctx context.Context, permission *models.Permission) error {
	query := `
		INSERT INTO rbac_permissions (
			permission_code, permission_name, permission_description,
			tenant_id, group_id, created_by
		) VALUES (
			:permission_code, :permission_name, :permission_description,
			:tenant_id, :group_id, :created_by
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, permission)
	if err != nil {
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	permission.PermissionID = uint(id)
	return nil
}

// GetByID lấy permission theo ID
func (r *permissionRepository) GetByID(ctx context.Context, permissionID uint) (*models.Permission, error) {
	query := `
		SELECT * FROM rbac_permissions
		WHERE permission_id = ?
	`

	var permission models.Permission
	err := r.db.GetContext(ctx, &permission, query, permissionID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.New("permission not found")
		}
		return nil, err
	}

	return &permission, nil
}

// Update cập nhật permission
func (r *permissionRepository) Update(ctx context.Context, permission *models.Permission) error {
	query := `
		UPDATE rbac_permissions SET
			permission_name = :permission_name,
			permission_description = :permission_description,
			group_id = :group_id,
			updated_by = :updated_by
		WHERE permission_id = :permission_id
	`

	_, err := r.db.NamedExecContext(ctx, query, permission)
	return err
}

// Delete xóa permission
func (r *permissionRepository) Delete(ctx context.Context, permissionID uint) error {
	query := `DELETE FROM rbac_permissions WHERE permission_id = ?`

	_, err := r.db.ExecContext(ctx, query, permissionID)
	return err
}

// addWhereCondition là hàm helper để thêm điều kiện WHERE vào câu truy vấn
func addWhereCondition(whereClause string, condition string, args []any, values ...any) (string, []any) {
	if whereClause != "" {
		whereClause += " AND " + condition
	} else {
		whereClause = "WHERE " + condition
	}
	return whereClause, append(args, values...)
}

// List lấy danh sách permissions với phân trang cursor
func (r *permissionRepository) List(ctx context.Context, req request.ListPermissionRequest) ([]*models.Permission, string, bool, error) {
	var (
		permissions []*models.Permission
		whereClause string
		args        []any
		limit       = 20
	)

	if req.Limit > 0 {
		limit = req.Limit
	}

	// Nếu có cursor, thêm điều kiện để phân trang
	if req.Cursor != "" {
		whereClause, args = addWhereCondition(whereClause, "permission_id > ?", args, req.Cursor)
	}

	// Thêm điều kiện lọc theo group_id nếu có
	if req.GroupID > 0 {
		whereClause, args = addWhereCondition(whereClause, "group_id = ?", args, req.GroupID)
	}

	// Thêm điều kiện lọc theo tenant_id nếu có
	if req.TenantID > 0 {
		whereClause, args = addWhereCondition(whereClause, "(tenant_id = ? OR tenant_id IS NULL)", args, req.TenantID)
	}

	// Thêm điều kiện tìm kiếm theo từ khóa nếu có
	if req.Search != "" {
		searchTerm := "%" + req.Search + "%"
		whereClause, args = addWhereCondition(
			whereClause,
			"(permission_name LIKE ? OR permission_code LIKE ? OR permission_description LIKE ?)",
			args,
			searchTerm, searchTerm, searchTerm,
		)
	}

	// Xây dựng query
	query := fmt.Sprintf(`
		SELECT * FROM rbac_permissions
		%s
		ORDER BY permission_id
		LIMIT %d
	`, whereClause, limit+1) // +1 để kiểm tra có phần tử tiếp theo không

	// Thực hiện query
	err := r.db.SelectContext(ctx, &permissions, query, args...)
	if err != nil {
		return nil, "", false, err
	}

	// Kiểm tra có phần tử tiếp theo không
	hasMore := false
	nextCursor := ""

	if len(permissions) > limit {
		hasMore = true
		nextCursor = fmt.Sprintf("%d", permissions[limit-1].PermissionID)
		permissions = permissions[:limit]
	}

	return permissions, nextCursor, hasMore, nil
}

// GetByCode lấy permission theo code
func (r *permissionRepository) GetByCode(ctx context.Context, code string) (*models.Permission, error) {
	query := `
		SELECT * FROM rbac_permissions
		WHERE permission_code = ?
	`

	var permission models.Permission
	err := r.db.GetContext(ctx, &permission, query, code)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}

	return &permission, nil
}
