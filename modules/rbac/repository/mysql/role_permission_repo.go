// modules/rbac/repository/mysql/role_permission_repo.go
package mysql

import (
	"wnapi/internal/pkg/logger"
	internalRepo "wnapi/modules/rbac/internal/repository/mysql"
	"wnapi/modules/rbac/repository"

	"gorm.io/gorm"
)

// NewRolePermissionRepository tạo một instance mới của RolePermissionRepository
func NewRolePermissionRepository(db *gorm.DB, logger logger.Logger) repository.RolePermissionRepository {
	return internalRepo.NewMySQLRolePermissionRepository(db, logger)
}
