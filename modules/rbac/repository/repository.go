package repository

import (
	"context"

	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/models"
)

// PermissionRepository định ngh<PERSON>a interface cho thao tác dữ liệu quyền
type PermissionRepository interface {
	// Thao tác CRUD cơ bản
	Create(ctx context.Context, permission *models.Permission) error
	GetByID(ctx context.Context, permissionID uint) (*models.Permission, error)
	Update(ctx context.Context, permission *models.Permission) error
	Delete(ctx context.Context, permissionID uint) error

	// Danh sách với phân trang cursor
	List(ctx context.Context, req request.ListPermissionRequest) ([]*models.Permission, string, bool, error)

	// Lấy quyền theo mã
	GetByCode(ctx context.Context, code string) (*models.Permission, error)
}

// RoleRepository định nghĩa interface cho thao tác dữ liệu vai trò
type RoleRepository interface {
	// Thao tác CRUD cơ bản
	Create(ctx context.Context, tenantID uint, role *models.Role) error
	GetByID(ctx context.Context, tenantID uint, roleID uint) (*models.Role, error)
	Update(ctx context.Context, role *models.Role) error
	Delete(ctx context.Context, tenantID uint, roleID uint) error

	// Danh sách với phân trang cursor
	List(ctx context.Context, tenantID uint, req request.ListRoleRequest) ([]*models.Role, string, bool, error)

	// Lấy vai trò theo mã
	GetByCode(ctx context.Context, tenantID uint, code string) (*models.Role, error)

	// Quản lý quyền của vai trò
	GetRolePermissions(ctx context.Context, roleID uint) ([]uint, error)
	SetRolePermissions(ctx context.Context, roleID uint, permissionIDs []uint, grantedBy *uint) error
}

// UserRoleRepository định nghĩa interface cho thao tác dữ liệu liên kết người dùng và vai trò
type UserRoleRepository interface {
	// Gán và thu hồi vai trò
	AssignRole(ctx context.Context, tenantID uint, userID uint, roleID uint, createdBy *uint) error
	RevokeRole(ctx context.Context, tenantID uint, userID uint, roleID uint) error

	// Lấy danh sách vai trò của người dùng
	GetUserRoles(ctx context.Context, tenantID uint, userID uint) ([]*models.UserRole, error)

	// Kiểm tra người dùng có vai trò cụ thể không
	HasRole(ctx context.Context, tenantID uint, userID uint, roleID uint) (bool, error)

	// Lấy danh sách người dùng có vai trò cụ thể
	GetUsersWithRole(ctx context.Context, tenantID uint, roleID uint) ([]uint, error)
}

// PermissionGroupRepository định nghĩa interface cho thao tác dữ liệu nhóm quyền
type PermissionGroupRepository interface {
	// Thao tác CRUD cơ bản
	Create(ctx context.Context, group *models.PermissionGroup) error
	GetByID(ctx context.Context, id uint) (*models.PermissionGroup, error)
	Update(ctx context.Context, group *models.PermissionGroup) error
	Delete(ctx context.Context, id uint) error

	// Danh sách với phân trang cursor
	List(ctx context.Context, tenantID *uint, limit int, cursor *uint) ([]*models.PermissionGroup, *uint, bool, error)
}
