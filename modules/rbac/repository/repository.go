// modules/rbac/repository/repository.go
package repository

import (
	"context"

	"wnapi/modules/rbac/models"
)

// UserRoleRepository là interface cho các thao tác liên quan đến vai trò người dùng
type UserRoleRepository interface {
	// GetUserRoles lấy tất cả vai trò của một người dùng trong tenant
	GetUserRoles(ctx context.Context, tenantID uint, userID uint) ([]models.Role, error)

	// CheckUserHasRole kiểm tra xem người dùng có vai trò cụ thể không
	CheckUserHasRole(ctx context.Context, tenantID uint, userID uint, roleID uint) (bool, error)

	// GetRoleIDs lấy danh sách ID vai trò của người dùng
	GetRoleIDs(ctx context.Context, tenantID uint, userID uint) ([]uint, error)
}

// RolePermissionRepository là interface cho các thao tác liên quan đến quyền của vai trò
type RolePermissionRepository interface {
	// GetRolePermissions lấy tất cả quyền của một vai trò
	GetRolePermissions(ctx context.Context, roleID uint) ([]models.Permission, error)

	// CheckRoleHasPermission kiểm tra xem vai trò có quyền cụ thể không
	CheckRoleHasPermission(ctx context.Context, roleID uint, permissionCode string) (bool, error)

	// GetPermissionsByRoleIDs lấy tất cả quyền từ danh sách vai trò
	GetPermissionsByRoleIDs(ctx context.Context, roleIDs []uint) ([]models.Permission, error)
}
