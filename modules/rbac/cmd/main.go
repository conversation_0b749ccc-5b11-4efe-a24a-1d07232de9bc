package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"wnapi/modules/rbac"
	"wnapi/modules/rbac/configs"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// Load config
	cfg, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}
	// Create and start module
	module := rbac.NewModuleWithConfig(cfg)
	if err := module.Start(); err != nil {
		log.Fatalf("Error starting module: %v", err)
	}

	// Wait for termination signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("Shutting down rbac service...")

	// Stop module
	if err := module.Stop(); err != nil {
		log.Fatalf("Error stopping module: %v", err)
	}

	log.Println("RBAC service exited")
}
