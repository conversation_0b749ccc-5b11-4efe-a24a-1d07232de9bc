CREATE TABLE rbac_permissions (
  permission_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  permission_code VARCHAR(255) NOT NULL UNIQUE,
  tenant_id INT UNSIGNED REFERENCES rbac_tenants(tenant_id), -- NULL = global
  group_id INT UNSIGNED REFERENCES rbac_permission_groups(group_id),
  permission_name VARCHAR(255) NOT NULL,
  permission_description TEXT,
  created_by INT UNSIGNED,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_by INT UNSIGNED,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_permissions_tenant (tenant_id)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;