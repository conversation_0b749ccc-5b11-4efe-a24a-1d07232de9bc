CREATE TABLE rbac_roles (
  role_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  role_code VA<PERSON>HAR(255) NOT NULL UNIQUE,
  tenant_id INT UNSIGNED REFERENCES rbac_tenants(tenant_id), -- NULL = global
  role_name VARCHAR(255) NOT NULL,
  role_description TEXT,
  created_by INT UNSIGNED,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_by INT UNSIGNED,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_roles_tenant (tenant_id)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;