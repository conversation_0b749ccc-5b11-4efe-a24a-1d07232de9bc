CREATE TABLE rbac_role_permissions (
  tenant_id INT UNSIGNED NOT NULL,
  role_id INT UNSIGNED NOT NULL REFERENCES rbac_roles(role_id),
  role_permission_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  permission_id INT UNSIGNED NOT NULL REFERENCES rbac_permissions(permission_id),
  created_by INT UNSIGNED,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_by INT UNSIGNED,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_role_permissions_tenant (tenant_id)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;