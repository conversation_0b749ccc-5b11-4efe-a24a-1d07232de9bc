CREATE TABLE rbac_user_roles (
  tenant_id INT UNSIGNED NOT NULL,
  user_id INT UNSIGNED NOT NULL REFERENCES rbac_users(id),
  role_id INT UNSIGNED NOT NULL REFERENCES rbac_roles(role_id),
  created_by INT UNSIGNED,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_by INT UNSIGNED,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (tenant_id, user_id, role_id)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;