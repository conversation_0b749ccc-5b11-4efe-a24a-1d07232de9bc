package service

import (
	"context"

	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository/mysql"
)

type PermissionGroupService interface {
	Create(ctx context.Context, group *models.PermissionGroup) error
	Update(ctx context.Context, group *models.PermissionGroup) error
	Delete(ctx context.Context, id uint) error
	GetByID(ctx context.Context, id uint) (*models.PermissionGroup, error)
	List(ctx context.Context, tenantID *uint, limit int, cursor *uint) ([]*models.PermissionGroup, *uint, bool, error)
}

type permissionGroupService struct {
	repo mysql.PermissionGroupRepository
}

func NewPermissionGroupService(repo mysql.PermissionGroupRepository) PermissionGroupService {
	return &permissionGroupService{repo: repo}
}

func (s *permissionGroupService) Create(ctx context.Context, group *models.PermissionGroup) error {
	return s.repo.Create(ctx, group)
}

func (s *permissionGroupService) Update(ctx context.Context, group *models.PermissionGroup) error {
	return s.repo.Update(ctx, group)
}

func (s *permissionGroupService) Delete(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

func (s *permissionGroupService) GetByID(ctx context.Context, id uint) (*models.PermissionGroup, error) {
	return s.repo.GetByID(ctx, id)
}

func (s *permissionGroupService) List(ctx context.Context, tenantID *uint, limit int, cursor *uint) ([]*models.PermissionGroup, *uint, bool, error) {
	return s.repo.List(ctx, tenantID, limit, cursor)
}
