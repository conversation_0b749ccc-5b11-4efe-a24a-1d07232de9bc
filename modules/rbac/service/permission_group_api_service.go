package service

import (
	"context"
	"time"

	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/dto/response"
	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

// PermissionGroupAPIService defines the interface for permission group business logic used by API handlers
type PermissionGroupAPIService interface {
	CreatePermissionGroup(ctx context.Context, req request.CreatePermissionGroupRequest) (*response.PermissionGroupResponse, error)
	GetPermissionGroup(ctx context.Context, id uint) (*response.PermissionGroupResponse, error)
	UpdatePermissionGroup(ctx context.Context, id uint, req request.UpdatePermissionGroupRequest) (*response.PermissionGroupResponse, error)
	DeletePermissionGroup(ctx context.Context, id uint) error
	ListPermissionGroups(ctx context.Context, req request.ListPermissionGroupRequest) (*response.PermissionGroupListResponse, error)
}

// permissionGroupAPIService implements PermissionGroupAPIService
type permissionGroupAPIService struct {
	repo repository.PermissionGroupRepository
}

// NewPermissionGroupAPIService creates a new permission group API service
func NewPermissionGroupAPIService(repo repository.PermissionGroupRepository) PermissionGroupAPIService {
	return &permissionGroupAPIService{repo: repo}
}

func (s *permissionGroupAPIService) CreatePermissionGroup(ctx context.Context, req request.CreatePermissionGroupRequest) (*response.PermissionGroupResponse, error) {
	group := &models.PermissionGroup{
		TenantID:                   req.TenantID,
		PermissionGroupName:        req.PermissionGroupName,
		PermissionGroupDescription: req.PermissionGroupDescription,
		CreatedAt:                  time.Now(),
		UpdatedAt:                  time.Now(),
	}

	if err := s.repo.Create(ctx, group); err != nil {
		return nil, err
	}

	return s.modelToResponse(group), nil
}

func (s *permissionGroupAPIService) GetPermissionGroup(ctx context.Context, id uint) (*response.PermissionGroupResponse, error) {
	group, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return s.modelToResponse(group), nil
}

func (s *permissionGroupAPIService) UpdatePermissionGroup(ctx context.Context, id uint, req request.UpdatePermissionGroupRequest) (*response.PermissionGroupResponse, error) {
	// First get the existing group
	group, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	group.PermissionGroupName = req.PermissionGroupName
	group.PermissionGroupDescription = req.PermissionGroupDescription
	group.UpdatedAt = time.Now()

	if err := s.repo.Update(ctx, group); err != nil {
		return nil, err
	}

	return s.modelToResponse(group), nil
}

func (s *permissionGroupAPIService) DeletePermissionGroup(ctx context.Context, id uint) error {
	return s.repo.Delete(ctx, id)
}

func (s *permissionGroupAPIService) ListPermissionGroups(ctx context.Context, req request.ListPermissionGroupRequest) (*response.PermissionGroupListResponse, error) {
	groups, nextCursor, hasMore, err := s.repo.List(ctx, req.TenantID, req.Limit, req.Cursor)
	if err != nil {
		return nil, err
	}

	var groupResponses []*response.PermissionGroupResponse
	for _, group := range groups {
		groupResponses = append(groupResponses, s.modelToResponse(group))
	}

	return &response.PermissionGroupListResponse{
		Groups:     groupResponses,
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}, nil
}

func (s *permissionGroupAPIService) modelToResponse(group *models.PermissionGroup) *response.PermissionGroupResponse {
	return &response.PermissionGroupResponse{
		GroupID:                    group.GroupID,
		TenantID:                   group.TenantID,
		PermissionGroupName:        group.PermissionGroupName,
		PermissionGroupDescription: group.PermissionGroupDescription,
		CreatedBy:                  group.CreatedBy,
		CreatedAt:                  group.CreatedAt,
		UpdatedBy:                  group.UpdatedBy,
		UpdatedAt:                  group.UpdatedAt,
	}
}
