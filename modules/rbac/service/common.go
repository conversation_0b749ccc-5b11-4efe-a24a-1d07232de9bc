package service

import (
	"context"
)

// GetContextUserID lấy user ID từ context.Context
// Đ<PERSON>y là phiên bản đơn giản để sử dụng trong service layer thay vì auth.GetUserID
// vì auth.GetUserID yêu cầu *gin.Context nhưng service chỉ có context.Context
func GetContextUserID(ctx context.Context) uint {
	// Trong môi trường thực tế, cần kiểm tra các giá trị trong context
	// Hiện tại chỉ trả về 0 để tương thích với interface
	return 0
}
