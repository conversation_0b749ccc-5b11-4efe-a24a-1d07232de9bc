package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/internal"

	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/dto/response"
	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

// RoleService định nghĩa interface cho logic nghiệp vụ vai trò
type RoleService interface {
	CreateRole(ctx context.Context, tenantID uint, req request.CreateRoleRequest) (*response.RoleResponse, error)
	GetRole(ctx context.Context, tenantID uint, roleID uint) (*response.RoleResponse, error)
	UpdateRole(ctx context.Context, tenantID uint, roleID uint, req request.UpdateRoleRequest) (*response.RoleResponse, error)
	DeleteRole(ctx context.Context, tenantID uint, roleID uint) error
	ListRoles(ctx context.Context, tenantID uint, req request.ListRoleRequest) (*response.RoleListResponse, error)
	GetRoleByCode(ctx context.Context, tenantID uint, roleCode string) (*models.Role, error)
	AssignPermissionToRole(ctx context.Context, roleID, permissionID int) error
	RevokePermissionFromRole(ctx context.Context, roleID, permissionID int) error
	GetRolePermissions(ctx context.Context, roleID int) ([]*internal.Permission, error)
}

type roleService struct {
	roleRepo       repository.RoleRepository
	permissionRepo repository.PermissionRepository
	logger         logger.Logger
}

// NewRoleService tạo một instance mới của RoleService
func NewRoleService(roleRepo repository.RoleRepository, permissionRepo repository.PermissionRepository, logger logger.Logger) RoleService {
	return &roleService{
		roleRepo:       roleRepo,
		permissionRepo: permissionRepo,
		logger:         logger,
	}
}

// CreateRole tạo một vai trò mới
func (s *roleService) CreateRole(ctx context.Context, tenantID uint, req request.CreateRoleRequest) (*response.RoleResponse, error) {
	// Kiểm tra xem role code đã tồn tại chưa
	existingRole, err := s.roleRepo.GetByCode(ctx, tenantID, req.RoleCode)
	if err != nil && err != sql.ErrNoRows {
		return nil, err
	}

	if existingRole != nil {
		return nil, errors.New("role code already exists for this tenant")
	}

	// Tạo role mới
	tenantIDPtr := &tenantID
	role := &models.Role{
		TenantID:        tenantIDPtr,
		RoleCode:        req.RoleCode,
		RoleName:        req.RoleName,
		RoleDescription: req.RoleDescription,
	}

	// Lưu role vào database
	if err := s.roleRepo.Create(ctx, tenantID, role); err != nil {
		return nil, err
	}

	// Nếu có permissions, cập nhật permissions cho role
	if req.Permissions != nil && len(req.Permissions) > 0 {
		// Lấy UserID từ context nếu có
		var grantedBy *uint = nil
		userID := GetContextUserID(ctx)
		if userID > 0 {
			uintVal := userID
			grantedBy = &uintVal
		}

		if err := s.roleRepo.SetRolePermissions(ctx, role.RoleID, req.Permissions, grantedBy); err != nil {
			return nil, err
		}
		role.Permissions = req.Permissions
	}

	return s.toRoleResponse(role), nil
}

// GetRole lấy thông tin vai trò theo ID
func (s *roleService) GetRole(ctx context.Context, tenantID uint, roleID uint) (*response.RoleResponse, error) {
	// Lấy thông tin vai trò
	role, err := s.roleRepo.GetByID(ctx, tenantID, roleID)
	if err != nil {
		return nil, err
	}

	// Lấy danh sách quyền của vai trò
	permissions, err := s.roleRepo.GetRolePermissions(ctx, roleID)
	if err != nil {
		return nil, err
	}
	role.Permissions = permissions

	return s.toRoleResponse(role), nil
}

// UpdateRole cập nhật thông tin vai trò
func (s *roleService) UpdateRole(ctx context.Context, tenantID uint, roleID uint, req request.UpdateRoleRequest) (*response.RoleResponse, error) {
	// Lấy thông tin vai trò hiện tại
	role, err := s.roleRepo.GetByID(ctx, tenantID, roleID)
	if err != nil {
		return nil, err
	}

	// Cập nhật các trường nếu được cung cấp trong request
	if req.RoleCode != "" {
		// Kiểm tra code mới có trùng không
		if req.RoleCode != role.RoleCode {
			existingRole, err := s.roleRepo.GetByCode(ctx, tenantID, req.RoleCode)
			if err == nil && existingRole != nil {
				return nil, errors.New("role code already exists for this tenant")
			}
		}
		role.RoleCode = req.RoleCode
	}

	if req.RoleName != "" {
		role.RoleName = req.RoleName
	}

	if req.RoleDescription != nil {
		role.RoleDescription = req.RoleDescription
	}

	// Lưu thay đổi
	if err := s.roleRepo.Update(ctx, role); err != nil {
		return nil, err
	}

	// Nếu có permissions, cập nhật permissions cho role
	if req.Permissions != nil {
		// Lấy UserID từ context nếu có
		var grantedBy *uint = nil
		userID := GetContextUserID(ctx)
		if userID > 0 {
			uintVal := userID
			grantedBy = &uintVal
		}

		if err := s.roleRepo.SetRolePermissions(ctx, roleID, req.Permissions, grantedBy); err != nil {
			return nil, err
		}
		role.Permissions = req.Permissions
	} else {
		// Lấy danh sách quyền hiện tại của vai trò
		permissions, err := s.roleRepo.GetRolePermissions(ctx, roleID)
		if err == nil {
			role.Permissions = permissions
		}
	}

	return s.toRoleResponse(role), nil
}

// DeleteRole xóa vai trò
func (s *roleService) DeleteRole(ctx context.Context, tenantID uint, roleID uint) error {
	// Kiểm tra xem vai trò có tồn tại không
	_, err := s.roleRepo.GetByID(ctx, tenantID, roleID)
	if err != nil {
		return err
	}

	return s.roleRepo.Delete(ctx, tenantID, roleID)
}

// ListRoles lấy danh sách vai trò với phân trang
func (s *roleService) ListRoles(ctx context.Context, tenantID uint, req request.ListRoleRequest) (*response.RoleListResponse, error) {
	roles, nextCursor, hasMore, err := s.roleRepo.List(ctx, tenantID, req)
	if err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	roleResponses := make([]response.RoleResponse, 0, len(roles))
	for _, role := range roles {
		// Lấy permissions nếu cần
		if req.WithPermissions {
			permissions, err := s.roleRepo.GetRolePermissions(ctx, role.RoleID)
			if err == nil {
				role.Permissions = permissions
			}
		}
		roleResponses = append(roleResponses, *s.toRoleResponse(role))
	}

	return &response.RoleListResponse{
		Roles:      roleResponses,
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}, nil
}

// GetRoleByCode lấy thông tin vai trò theo mã
func (s *roleService) GetRoleByCode(ctx context.Context, tenantID uint, roleCode string) (*models.Role, error) {
	// Tìm kiếm vai trò theo mã
	role, err := s.roleRepo.GetByCode(ctx, tenantID, roleCode)
	if err != nil {
		return nil, fmt.Errorf("không thể lấy vai trò theo mã %s: %w", roleCode, err)
	}

	return role, nil
}

// toRoleResponse chuyển đổi từ model sang response
func (s *roleService) toRoleResponse(role *models.Role) *response.RoleResponse {
	return &response.RoleResponse{
		RoleID:          role.RoleID,
		TenantID:        role.TenantID,
		RoleCode:        role.RoleCode,
		RoleName:        role.RoleName,
		RoleDescription: role.RoleDescription,
		CreatedBy:       role.CreatedBy,
		CreatedAt:       role.CreatedAt,
		UpdatedBy:       role.UpdatedBy,
		UpdatedAt:       role.UpdatedAt,
		Permissions:     role.Permissions,
	}
}

// AssignPermissionToRole gán quyền cho vai trò
func (s *roleService) AssignPermissionToRole(ctx context.Context, roleID, permissionID int) error {
	// TODO: Triển khai gán quyền cho vai trò
	s.logger.Info("Gán quyền ID %d cho vai trò ID %d", permissionID, roleID)
	return nil
}

// RevokePermissionFromRole thu hồi quyền từ vai trò
func (s *roleService) RevokePermissionFromRole(ctx context.Context, roleID, permissionID int) error {
	// TODO: Triển khai thu hồi quyền từ vai trò
	s.logger.Info("Thu hồi quyền ID %d từ vai trò ID %d", permissionID, roleID)
	return nil
}

// GetRolePermissions lấy danh sách quyền của vai trò
func (s *roleService) GetRolePermissions(ctx context.Context, roleID int) ([]*internal.Permission, error) {
	// TODO: Triển khai lấy danh sách quyền của vai trò
	s.logger.Info("Lấy danh sách quyền của vai trò ID %d", roleID)
	return []*internal.Permission{}, nil
}
