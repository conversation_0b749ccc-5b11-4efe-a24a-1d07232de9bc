// modules/rbac/service/permission_service.go
package service

import (
	"context"

	"wnapi/internal/pkg/permission"
	internalService "wnapi/modules/rbac/internal/service"
)

// Ensure PermissionService implements permission.PermissionChecker
var _ permission.PermissionChecker = (*PermissionService)(nil)

// PermissionService là service được export ra bên ngoài module
type PermissionService struct {
	internal *internalService.PermissionService
}

// NewPermissionService tạo một PermissionService mới
func NewPermissionService(internal *internalService.PermissionService) *PermissionService {
	return &PermissionService{
		internal: internal,
	}
}

// UserHasPermission kiểm tra xem người dùng có quyền cụ thể không
func (s *PermissionService) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	return s.internal.UserHasPermission(ctx, tenantID, userID, permissionCode)
}

// UserHasAnyPermission kiểm tra xem người dùng có ít nhất một trong các quyền không
func (s *PermissionService) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	return s.internal.UserHasAnyPermission(ctx, tenantID, userID, permissions)
}

// UserHasAllPermissions kiểm tra xem người dùng có tất cả các quyền không
func (s *PermissionService) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	return s.internal.UserHasAllPermissions(ctx, tenantID, userID, permissions)
}
