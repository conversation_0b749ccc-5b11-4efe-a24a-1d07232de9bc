package service

import (
	"context"
	"errors"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/dto/response"
	"wnapi/modules/rbac/models"
	"wnapi/modules/rbac/repository"
)

// PermissionService định ngh<PERSON>a interface cho logic nghiệ<PERSON> v<PERSON> quyền
type PermissionService interface {
	CreatePermission(ctx context.Context, req request.CreatePermissionRequest) (*response.PermissionResponse, error)
	GetPermission(ctx context.Context, permissionID uint) (*response.PermissionResponse, error)
	UpdatePermission(ctx context.Context, permissionID uint, req request.UpdatePermissionRequest) (*response.PermissionResponse, error)
	DeletePermission(ctx context.Context, permissionID uint) error
	ListPermissions(ctx context.Context, req request.ListPermissionRequest) (*response.PermissionListResponse, error)
	UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error)
	CheckUserPermission(ctx context.Context, userID int, permissionName string) (bool, error)
}

type permissionService struct {
	permissionRepo      repository.PermissionRepository
	roleRepo            repository.RoleRepository
	userRoleRepo        repository.UserRoleRepository
	permissionGroupRepo repository.PermissionGroupRepository
	logger              logger.Logger
}

// NewPermissionService tạo một instance mới của PermissionService
func NewPermissionService(
	permissionRepo repository.PermissionRepository,
	roleRepo repository.RoleRepository,
	userRoleRepo repository.UserRoleRepository,
	permissionGroupRepo repository.PermissionGroupRepository,
	logger logger.Logger,
) PermissionService {
	return &permissionService{
		permissionRepo:      permissionRepo,
		roleRepo:            roleRepo,
		userRoleRepo:        userRoleRepo,
		permissionGroupRepo: permissionGroupRepo,
		logger:              logger,
	}
}

// CreatePermission tạo một quyền mới
func (s *permissionService) CreatePermission(ctx context.Context, req request.CreatePermissionRequest) (*response.PermissionResponse, error) {
	// Kiểm tra xem permission code đã tồn tại chưa
	existingPerm, err := s.permissionRepo.GetByCode(ctx, req.PermissionCode)
	if err == nil && existingPerm != nil {
		return nil, errors.New("permission code already exists")
	}

	// Tạo model từ request
	permission := &models.Permission{
		PermissionCode:        req.PermissionCode,
		TenantID:              req.TenantID,
		GroupID:               req.GroupID,
		PermissionName:        req.PermissionName,
		PermissionDescription: req.PermissionDescription,
	}

	// Lưu vào repository
	if err := s.permissionRepo.Create(ctx, permission); err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	return s.toPermissionResponse(permission), nil
}

// GetPermission lấy thông tin quyền theo ID
func (s *permissionService) GetPermission(ctx context.Context, permissionID uint) (*response.PermissionResponse, error) {
	permission, err := s.permissionRepo.GetByID(ctx, permissionID)
	if err != nil {
		return nil, err
	}
	return s.toPermissionResponse(permission), nil
}

// UpdatePermission cập nhật thông tin quyền
func (s *permissionService) UpdatePermission(ctx context.Context, permissionID uint, req request.UpdatePermissionRequest) (*response.PermissionResponse, error) {
	// Lấy thông tin quyền hiện tại
	permission, err := s.permissionRepo.GetByID(ctx, permissionID)
	if err != nil {
		return nil, err
	}

	// Cập nhật các trường nếu được cung cấp trong request
	if req.PermissionCode != "" {
		// Kiểm tra code mới có trùng không
		if req.PermissionCode != permission.PermissionCode {
			existingPerm, err := s.permissionRepo.GetByCode(ctx, req.PermissionCode)
			if err == nil && existingPerm != nil {
				return nil, errors.New("permission code already exists")
			}
		}
		permission.PermissionCode = req.PermissionCode
	}

	if req.PermissionName != "" {
		permission.PermissionName = req.PermissionName
	}

	if req.PermissionDescription != nil {
		permission.PermissionDescription = req.PermissionDescription
	}

	if req.GroupID != nil {
		permission.GroupID = req.GroupID
	}

	// Lưu thay đổi
	if err := s.permissionRepo.Update(ctx, permission); err != nil {
		return nil, err
	}

	return s.toPermissionResponse(permission), nil
}

// DeletePermission xóa quyền
func (s *permissionService) DeletePermission(ctx context.Context, permissionID uint) error {
	return s.permissionRepo.Delete(ctx, permissionID)
}

// ListPermissions lấy danh sách quyền với phân trang
func (s *permissionService) ListPermissions(ctx context.Context, req request.ListPermissionRequest) (*response.PermissionListResponse, error) {
	permissions, nextCursor, hasMore, err := s.permissionRepo.List(ctx, req)
	if err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	permResponses := make([]response.PermissionResponse, 0, len(permissions))
	for _, perm := range permissions {
		permResponses = append(permResponses, *s.toPermissionResponse(perm))
	}

	return &response.PermissionListResponse{
		Permissions: permResponses,
		NextCursor:  nextCursor,
		HasMore:     hasMore,
	}, nil
}

// toPermissionResponse chuyển đổi từ model sang response
func (s *permissionService) toPermissionResponse(permission *models.Permission) *response.PermissionResponse {
	resp := &response.PermissionResponse{
		PermissionID:          permission.PermissionID,
		PermissionCode:        permission.PermissionCode,
		TenantID:              permission.TenantID,
		GroupID:               permission.GroupID,
		PermissionName:        permission.PermissionName,
		PermissionDescription: permission.PermissionDescription,
		CreatedBy:             permission.CreatedBy,
		CreatedAt:             permission.CreatedAt,
		UpdatedBy:             permission.UpdatedBy,
		UpdatedAt:             permission.UpdatedAt,
	}

	// Nếu có group_id, lấy thông tin nhóm quyền
	if permission.GroupID != nil && *permission.GroupID > 0 {
		group, err := s.permissionGroupRepo.GetByID(context.Background(), *permission.GroupID)
		if err == nil && group != nil {
			resp.Group = &response.PermissionGroupInfo{
				GroupID:                    group.GroupID,
				PermissionGroupName:        group.PermissionGroupName,
				PermissionGroupDescription: group.PermissionGroupDescription,
			}
		}
	}

	return resp
}

// UserHasPermission kiểm tra xem người dùng có quyền cụ thể không
func (s *permissionService) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	// Lấy danh sách vai trò của người dùng trong tenant
	userRoles, err := s.userRoleRepo.GetUserRoles(ctx, tenantID, userID)
	if err != nil {
		return false, err
	}

	if len(userRoles) == 0 {
		return false, nil // Người dùng không có vai trò
	}

	// Kiểm tra từng vai trò xem có chứa quyền cần kiểm tra không
	for _, ur := range userRoles {
		// Lấy danh sách quyền của vai trò
		permissions, err := s.roleRepo.GetRolePermissions(ctx, ur.RoleID)
		if err != nil {
			continue // Bỏ qua vai trò này nếu có lỗi
		}

		// Kiểm tra từng quyền
		for _, permID := range permissions {
			// Lấy thông tin quyền
			perm, err := s.permissionRepo.GetByID(ctx, permID)
			if err != nil {
				continue // Bỏ qua quyền này nếu có lỗi
			}

			// So sánh mã quyền
			if perm.PermissionCode == permissionCode {
				return true, nil // Tìm thấy quyền
			}
		}
	}

	return false, nil // Không tìm thấy quyền
}

// CheckUserPermission kiểm tra người dùng có quyền cụ thể không
func (s *permissionService) CheckUserPermission(ctx context.Context, userID int, permissionName string) (bool, error) {
	// TODO: Triển khai kiểm tra quyền người dùng
	// 1. Lấy danh sách vai trò của người dùng
	// 2. Lấy danh sách quyền của từng vai trò
	// 3. Kiểm tra xem có quyền cần thiết không

	// Tạm thời luôn trả về true
	s.logger.Info("Kiểm tra quyền %s cho người dùng ID %d", permissionName, userID)
	return true, nil
}
