// modules/rbac/service/internal.go
package service

import (
	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/internal/service"
	"wnapi/modules/rbac/repository"
)

// NewRBACPermissionService tạo một PermissionService internal mới và export ra bên ngoài
func NewRBACPermissionService(
	userRoleRepo repository.UserRoleRepository,
	rolePermRepo repository.RolePermissionRepository,
	log logger.Logger,
) *service.PermissionService {
	return service.NewRBACPermissionService(
		userRoleRepo,
		rolePermRepo,
		log,
	)
}
