package service

import (
	"context"
	"errors"

	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/dto/response"
	"wnapi/modules/rbac/repository"
)

// UserRoleService định ngh<PERSON>a interface cho logic nghiệp vụ liên quan đến việc gán vai trò cho người dùng
type UserRoleService interface {
	AssignRoleToUser(ctx context.Context, tenantID uint, req request.AssignUserRoleRequest) error
	RevokeRoleFromUser(ctx context.Context, tenantID uint, userID uint, roleID uint) error
	GetUserRoles(ctx context.Context, tenantID uint, userID uint) (*response.UserRoleListResponse, error)
	HasRole(ctx context.Context, tenantID uint, userID uint, roleID uint) (bool, error)
	GetUsersWithRole(ctx context.Context, tenantID uint, roleID uint) ([]uint, error)
}

type userRoleService struct {
	userRoleRepo repository.UserRoleRepository
	roleRepo     repository.RoleRepository
}

// NewUserRoleService tạo một instance mới của UserRoleService
func NewUserRoleService(userRoleRepo repository.UserRoleRepository, roleRepo repository.RoleRepository) UserRoleService {
	return &userRoleService{
		userRoleRepo: userRoleRepo,
		roleRepo:     roleRepo,
	}
}

// AssignRoleToUser gán vai trò cho người dùng
func (s *userRoleService) AssignRoleToUser(ctx context.Context, tenantID uint, req request.AssignUserRoleRequest) error {
	// Kiểm tra vai trò tồn tại
	role, err := s.roleRepo.GetByID(ctx, tenantID, req.RoleID)
	if err != nil {
		return errors.New("role not found")
	}

	// Kiểm tra vai trò thuộc tenant
	// Nếu role.TenantID không phải nil và giá trị của nó khác với tenantID hiện tại
	if role.TenantID != nil && *role.TenantID != tenantID {
		return errors.New("role does not belong to tenant")
	}

	// Lấy UserID từ context nếu có
	var createdBy *uint = nil
	userID := GetContextUserID(ctx)
	if userID > 0 {
		uintVal := userID
		createdBy = &uintVal
	}

	// Gán vai trò
	return s.userRoleRepo.AssignRole(ctx, tenantID, req.UserID, req.RoleID, createdBy)
}

// RevokeRoleFromUser thu hồi vai trò từ người dùng
func (s *userRoleService) RevokeRoleFromUser(ctx context.Context, tenantID uint, userID uint, roleID uint) error {
	// Kiểm tra vai trò tồn tại
	role, err := s.roleRepo.GetByID(ctx, tenantID, roleID)
	if err != nil {
		return errors.New("role not found")
	}

	// Kiểm tra vai trò thuộc tenant
	// Nếu role.TenantID không phải nil và giá trị của nó khác với tenantID hiện tại
	if role.TenantID != nil && *role.TenantID != tenantID {
		return errors.New("role does not belong to tenant")
	}

	// Thu hồi vai trò
	return s.userRoleRepo.RevokeRole(ctx, tenantID, userID, roleID)
}

// GetUserRoles lấy danh sách vai trò của người dùng
func (s *userRoleService) GetUserRoles(ctx context.Context, tenantID uint, userID uint) (*response.UserRoleListResponse, error) {
	// Lấy tất cả vai trò của người dùng
	userRoles, err := s.userRoleRepo.GetUserRoles(ctx, tenantID, userID)
	if err != nil {
		return nil, err
	}

	// Chuyển đổi sang response
	userRoleResponses := make([]response.UserRoleResponse, 0, len(userRoles))
	for _, ur := range userRoles {
		// Lấy thông tin chi tiết về vai trò
		role, err := s.roleRepo.GetByID(ctx, tenantID, ur.RoleID)
		if err != nil {
			continue // Bỏ qua vai trò lỗi
		}

		// Lấy permissions của vai trò
		permissions, err := s.roleRepo.GetRolePermissions(ctx, ur.RoleID)
		if err == nil {
			role.Permissions = permissions
		}

		// Tạo response
		urResp := response.UserRoleResponse{
			TenantID:  ur.TenantID,
			UserID:    ur.UserID,
			RoleID:    ur.RoleID,
			CreatedBy: ur.CreatedBy,
			CreatedAt: ur.CreatedAt,
			UpdatedBy: ur.UpdatedBy,
			UpdatedAt: ur.UpdatedAt,
			Role: &response.RoleResponse{
				RoleID:          role.RoleID,
				TenantID:        role.TenantID,
				RoleCode:        role.RoleCode,
				RoleName:        role.RoleName,
				RoleDescription: role.RoleDescription,
				CreatedBy:       role.CreatedBy,
				CreatedAt:       role.CreatedAt,
				UpdatedBy:       role.UpdatedBy,
				UpdatedAt:       role.UpdatedAt,
				Permissions:     role.Permissions,
			},
		}
		userRoleResponses = append(userRoleResponses, urResp)
	}

	return &response.UserRoleListResponse{
		UserRoles: userRoleResponses,
	}, nil
}

// HasRole kiểm tra người dùng có vai trò cụ thể không
func (s *userRoleService) HasRole(ctx context.Context, tenantID uint, userID uint, roleID uint) (bool, error) {
	return s.userRoleRepo.HasRole(ctx, tenantID, userID, roleID)
}

// GetUsersWithRole lấy danh sách người dùng có vai trò cụ thể
func (s *userRoleService) GetUsersWithRole(ctx context.Context, tenantID uint, roleID uint) ([]uint, error) {
	return s.userRoleRepo.GetUsersWithRole(ctx, tenantID, roleID)
}
