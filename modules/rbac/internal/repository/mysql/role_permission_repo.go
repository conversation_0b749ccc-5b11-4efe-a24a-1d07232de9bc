// modules/rbac/internal/repository/mysql/role_permission_repo.go
package mysql

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/models"

	"gorm.io/gorm"
)

// MySQLRolePermissionRepository là triển khai MySQL cho RolePermissionRepository
type MySQLRolePermissionRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRolePermissionRepository tạo repository mới
func NewMySQLRolePermissionRepository(db *gorm.DB, log logger.Logger) *MySQLRolePermissionRepository {
	return &MySQLRolePermissionRepository{
		db:     db,
		logger: log,
	}
}

// GetRolePermissions lấy tất cả quyền của một vai trò
func (r *MySQLRolePermissionRepository) GetRolePermissions(ctx context.Context, roleID uint) ([]models.Permission, error) {
	var permissions []models.Permission

	err := r.db.WithContext(ctx).
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", roleID).
		Find(&permissions).Error

	if err != nil {
		return nil, err
	}

	return permissions, nil
}

// CheckRoleHasPermission kiểm tra xem vai trò có quyền cụ thể không
func (r *MySQLRolePermissionRepository) CheckRoleHasPermission(ctx context.Context, roleID uint, permissionCode string) (bool, error) {
	var count int64

	err := r.db.WithContext(ctx).
		Model(&models.RolePermission{}).
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("role_permissions.role_id = ? AND permissions.code = ?", roleID, permissionCode).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetPermissionsByRoleIDs lấy tất cả quyền từ danh sách vai trò
func (r *MySQLRolePermissionRepository) GetPermissionsByRoleIDs(ctx context.Context, roleIDs []uint) ([]models.Permission, error) {
	if len(roleIDs) == 0 {
		return []models.Permission{}, nil
	}

	var permissions []models.Permission

	err := r.db.WithContext(ctx).
		Distinct().
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id IN ?", roleIDs).
		Find(&permissions).Error

	if err != nil {
		return nil, err
	}

	return permissions, nil
}
