// modules/rbac/internal/repository/mysql/user_role_repo.go
package mysql

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/models"

	"gorm.io/gorm"
)

// MySQLUserRoleRepository là triển khai MySQL cho UserRoleRepository
type MySQLUserRoleRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLUserRoleRepository tạo repository mới
func NewMySQLUserRoleRepository(db *gorm.DB, log logger.Logger) *MySQLUserRoleRepository {
	return &MySQLUserRoleRepository{
		db:     db,
		logger: log,
	}
}

// GetUserRoles lấy tất cả vai trò của một người dùng trong tenant
func (r *MySQLUserRoleRepository) GetUserRoles(ctx context.Context, tenantID uint, userID uint) ([]models.Role, error) {
	var roles []models.Role

	err := r.db.WithContext(ctx).
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND user_roles.tenant_id = ?", userID, tenantID).
		Find(&roles).Error

	if err != nil {
		return nil, err
	}

	return roles, nil
}

// CheckUserHasRole kiểm tra xem người dùng có vai trò cụ thể không
func (r *MySQLUserRoleRepository) CheckUserHasRole(ctx context.Context, tenantID uint, userID uint, roleID uint) (bool, error) {
	var count int64

	err := r.db.WithContext(ctx).
		Model(&models.UserRole{}).
		Where("user_id = ? AND tenant_id = ? AND role_id = ?", userID, tenantID, roleID).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// GetRoleIDs lấy danh sách ID vai trò của người dùng
func (r *MySQLUserRoleRepository) GetRoleIDs(ctx context.Context, tenantID uint, userID uint) ([]uint, error) {
	var userRoles []models.UserRole

	err := r.db.WithContext(ctx).
		Where("user_id = ? AND tenant_id = ?", userID, tenantID).
		Find(&userRoles).Error

	if err != nil {
		return nil, err
	}

	roleIDs := make([]uint, len(userRoles))
	for i, ur := range userRoles {
		roleIDs[i] = ur.RoleID
	}

	return roleIDs, nil
}
