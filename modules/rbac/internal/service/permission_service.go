// modules/rbac/internal/service/permission_service.go
package service

import (
	"context"
	"fmt"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/internal/repository"
)

// PermissionService triển khai logic kiểm tra quyền
type PermissionService struct {
	userRoleRepo repository.UserRoleRepository
	rolePermRepo repository.RolePermissionRepository
	logger       logger.Logger
}

// NewRBACPermissionService tạo một PermissionService mới
func NewRBACPermissionService(
	userRoleRepo repository.UserRoleRepository,
	rolePermRepo repository.RolePermissionRepository,
	log logger.Logger,
) *PermissionService {
	return &PermissionService{
		userRoleRepo: userRoleRepo,
		rolePermRepo: rolePermRepo,
		logger:       log,
	}
}

// UserHasPermission kiểm tra xem người dùng có quyền cụ thể không
func (s *PermissionService) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	// L<PERSON>y tất cả roleIDs của user
	roleIDs, err := s.userRoleRepo.GetRoleIDs(ctx, tenantID, userID)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy vai trò của người dùng: %w", err)
	}

	if len(roleIDs) == 0 {
		s.logger.Debug("Người dùng không có vai trò nào",
			"user_id", userID, "tenant_id", tenantID)
		return false, nil
	}

	// Lấy tất cả permissions của các role
	permissions, err := s.rolePermRepo.GetPermissionsByRoleIDs(ctx, roleIDs)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy quyền của các vai trò: %w", err)
	}

	// Kiểm tra xem permissionCode có trong danh sách không
	for _, perm := range permissions {
		if perm.Code == permissionCode {
			return true, nil
		}
	}

	s.logger.Debug("Người dùng không có quyền",
		"user_id", userID, "tenant_id", tenantID, "permission", permissionCode)
	return false, nil
}

// UserHasAnyPermission kiểm tra xem người dùng có ít nhất một trong các quyền không
func (s *PermissionService) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 {
		return true, nil
	}

	// Lấy tất cả roleIDs của user
	roleIDs, err := s.userRoleRepo.GetRoleIDs(ctx, tenantID, userID)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy vai trò của người dùng: %w", err)
	}

	if len(roleIDs) == 0 {
		return false, nil
	}

	// Lấy tất cả permissions của các role
	userPerms, err := s.rolePermRepo.GetPermissionsByRoleIDs(ctx, roleIDs)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy quyền của các vai trò: %w", err)
	}

	// Tạo map cho tìm kiếm nhanh
	permMap := make(map[string]bool)
	for _, perm := range userPerms {
		permMap[perm.Code] = true
	}

	// Kiểm tra từng permission trong danh sách
	for _, permCode := range permissions {
		if permMap[permCode] {
			return true, nil
		}
	}

	return false, nil
}

// UserHasAllPermissions kiểm tra xem người dùng có tất cả các quyền không
func (s *PermissionService) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 {
		return true, nil
	}

	// Lấy tất cả roleIDs của user
	roleIDs, err := s.userRoleRepo.GetRoleIDs(ctx, tenantID, userID)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy vai trò của người dùng: %w", err)
	}

	if len(roleIDs) == 0 {
		return false, nil
	}

	// Lấy tất cả permissions của các role
	userPerms, err := s.rolePermRepo.GetPermissionsByRoleIDs(ctx, roleIDs)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy quyền của các vai trò: %w", err)
	}

	// Tạo map cho tìm kiếm nhanh
	permMap := make(map[string]bool)
	for _, perm := range userPerms {
		permMap[perm.Code] = true
	}

	// Kiểm tra từng permission trong danh sách
	for _, permCode := range permissions {
		if !permMap[permCode] {
			return false, nil
		}
	}

	return true, nil
}
