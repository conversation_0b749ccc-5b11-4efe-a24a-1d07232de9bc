package internal

import (
	"context"
	"time"
)

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrInvalidPermission là lỗi khi thông tin quyền không hợp lệ
	ErrInvalidPermission ServiceError = "invalid_permission"
	// ErrPermissionAlreadyExists là lỗi khi quyền đã tồn tại
	ErrPermissionAlreadyExists ServiceError = "permission_already_exists"
	// ErrRoleAlreadyExists là lỗi khi vai trò đã tồn tại
	ErrRoleAlreadyExists ServiceError = "role_already_exists"
	// ErrPermissionNotFound là lỗi khi không tìm thấy quyền
	ErrPermissionNotFound ServiceError = "permission_not_found"
	// ErrRoleNotFound là lỗi khi không tìm thấy vai trò
	ErrRoleNotFound ServiceError = "role_not_found"
	// ErrUserRoleAlreadyExists là lỗi khi người dùng đã có vai trò
	ErrUserRoleAlreadyExists ServiceError = "user_role_already_exists"
	// ErrUserRoleNotFound là lỗi khi không tìm thấy vai trò của người dùng
	ErrUserRoleNotFound ServiceError = "user_role_not_found"
)

func (e ServiceError) Error() string {
	return string(e)
}

// Permission đại diện cho quyền trong hệ thống
type Permission struct {
	ID          int       `db:"id" json:"id"`
	Name        string    `db:"name" json:"name"`
	Description string    `db:"description" json:"description"`
	GroupID     int       `db:"group_id" json:"group_id"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`
}

// Role đại diện cho vai trò trong hệ thống
type Role struct {
	ID          int       `db:"id" json:"id"`
	Name        string    `db:"name" json:"name"`
	Description string    `db:"description" json:"description"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`
}

// UserRole đại diện cho mối quan hệ giữa người dùng và vai trò
type UserRole struct {
	ID        int       `db:"id" json:"id"`
	UserID    int       `db:"user_id" json:"user_id"`
	RoleID    int       `db:"role_id" json:"role_id"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
}

// PermissionGroup đại diện cho nhóm quyền
type PermissionGroup struct {
	ID          int       `db:"id" json:"id"`
	Name        string    `db:"name" json:"name"`
	Description string    `db:"description" json:"description"`
	CreatedAt   time.Time `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time `db:"updated_at" json:"updated_at"`
}

// RolePermission đại diện cho mối quan hệ giữa vai trò và quyền
type RolePermission struct {
	ID           int       `db:"id" json:"id"`
	RoleID       int       `db:"role_id" json:"role_id"`
	PermissionID int       `db:"permission_id" json:"permission_id"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
}

// PermissionService định nghĩa interface cho permission service
type PermissionService interface {
	Create(ctx context.Context, permission *Permission) (*Permission, error)
	Get(ctx context.Context, id int) (*Permission, error)
	List(ctx context.Context, limit, offset int) ([]*Permission, error)
	Update(ctx context.Context, permission *Permission) (*Permission, error)
	Delete(ctx context.Context, id int) error
	CheckUserPermission(ctx context.Context, userID int, permissionName string) (bool, error)
}

// RoleService định nghĩa interface cho role service
type RoleService interface {
	Create(ctx context.Context, role *Role) (*Role, error)
	Get(ctx context.Context, id int) (*Role, error)
	List(ctx context.Context, limit, offset int) ([]*Role, error)
	Update(ctx context.Context, role *Role) (*Role, error)
	Delete(ctx context.Context, id int) error
	AssignPermissionToRole(ctx context.Context, roleID, permissionID int) error
	RevokePermissionFromRole(ctx context.Context, roleID, permissionID int) error
	GetRolePermissions(ctx context.Context, roleID int) ([]*Permission, error)
}

// UserRoleService định nghĩa interface cho user role service
type UserRoleService interface {
	AssignRoleToUser(ctx context.Context, userID, roleID int) error
	RevokeRoleFromUser(ctx context.Context, userID, roleID int) error
	GetUserRoles(ctx context.Context, userID int) ([]*Role, error)
	GetUsersWithRole(ctx context.Context, roleID int, limit, offset int) ([]int, error)
	CheckUserRole(ctx context.Context, userID, roleID int) (bool, error)
}

// PermissionGroupService định nghĩa interface cho permission group service
type PermissionGroupService interface {
	Create(ctx context.Context, group *PermissionGroup) (*PermissionGroup, error)
	Get(ctx context.Context, id int) (*PermissionGroup, error)
	List(ctx context.Context, limit, offset int) ([]*PermissionGroup, error)
	Update(ctx context.Context, group *PermissionGroup) (*PermissionGroup, error)
	Delete(ctx context.Context, id int) error
}
