package request

// UpdatePermissionRequest đại diện cho yêu cầu cập nhật một quyền
type UpdatePermissionRequest struct {
	PermissionCode        string  `json:"permission_code" binding:"omitempty,max=100"`
	GroupID               *uint   `json:"group_id"`
	PermissionName        string  `json:"permission_name" binding:"omitempty,max=100"`
	PermissionDescription *string `json:"permission_description" binding:"omitempty"`
}
