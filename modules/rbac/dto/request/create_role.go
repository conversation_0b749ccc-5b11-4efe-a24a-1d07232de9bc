package request

// CreateRoleRequest đại diện cho yêu cầu tạo một vai trò mới
type CreateRoleRequest struct {
	RoleCode        string  `json:"role_code" binding:"required,max=100"`
	TenantID        *uint   `json:"tenant_id"`
	RoleName        string  `json:"role_name" binding:"required,max=255"`
	RoleDescription *string `json:"role_description" binding:"omitempty"`
	Permissions     []uint  `json:"permissions" binding:"omitempty"`
}
