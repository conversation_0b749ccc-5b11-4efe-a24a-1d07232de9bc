package request

// CreatePermissionRequest đại diện cho yêu cầu tạo một quyền mới
type CreatePermissionRequest struct {
	PermissionCode        string  `json:"permission_code" binding:"required,max=100"`
	TenantID              *uint   `json:"tenant_id"`
	GroupID               *uint   `json:"group_id"`
	PermissionName        string  `json:"permission_name" binding:"required,max=100"`
	PermissionDescription *string `json:"permission_description" binding:"omitempty"`
}
