package response

import (
	"time"
)

// PermissionGroupInfo đại diện cho thông tin nhóm quyền
type PermissionGroupInfo struct {
	GroupID                    uint    `json:"group_id"`
	PermissionGroupName        string  `json:"permission_group_name"`
	PermissionGroupDescription *string `json:"permission_group_description"`
}

// PermissionResponse đại diện cho phản hồi thông tin quyền
type PermissionResponse struct {
	PermissionID          uint                 `json:"permission_id"`
	PermissionCode        string               `json:"permission_code"`
	TenantID              *uint                `json:"tenant_id"`
	GroupID               *uint                `json:"group_id"`
	Group                 *PermissionGroupInfo `json:"group,omitempty"`
	PermissionName        string               `json:"permission_name"`
	PermissionDescription *string              `json:"permission_description"`
	CreatedBy             *uint                `json:"created_by"`
	CreatedAt             time.Time            `json:"created_at"`
	UpdatedBy             *uint                `json:"updated_by"`
	UpdatedAt             time.Time            `json:"updated_at"`
}

// PermissionListResponse đại diện cho phản hồi danh sách quyền với phân trang
type PermissionListResponse struct {
	Permissions []PermissionResponse `json:"permissions"`
	NextCursor  string               `json:"next_cursor"`
	HasMore     bool                 `json:"has_more"`
}
