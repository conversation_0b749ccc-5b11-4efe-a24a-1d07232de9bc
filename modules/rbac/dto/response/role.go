package response

import (
	"time"
)

// RoleResponse đại diện cho phản hồi thông tin vai trò
type RoleResponse struct {
	RoleID          uint      `json:"role_id"`
	RoleCode        string    `json:"role_code"`
	TenantID        *uint     `json:"tenant_id"`
	RoleName        string    `json:"role_name"`
	RoleDescription *string   `json:"role_description"`
	CreatedBy       *uint     `json:"created_by"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedBy       *uint     `json:"updated_by"`
	UpdatedAt       time.Time `json:"updated_at"`
	Permissions     []uint    `json:"permissions,omitempty"`
}

// RoleListResponse đại diện cho phản hồi danh sách vai trò với phân trang
type RoleListResponse struct {
	Roles      []RoleResponse `json:"roles"`
	NextCursor string         `json:"next_cursor"`
	<PERSON><PERSON><PERSON>    bool           `json:"has_more"`
}
