package response

import (
	"time"
)

// UserRoleResponse đại diện cho phản hồi thông tin liên kết người dùng và vai trò
type UserRoleResponse struct {
	TenantID  uint      `json:"tenant_id"`
	UserID    uint      `json:"user_id"`
	RoleID    uint      `json:"role_id"`
	CreatedBy *uint     `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedBy *uint     `json:"updated_by"`
	UpdatedAt time.Time `json:"updated_at"`

	// Thông tin vai trò
	Role *RoleResponse `json:"role,omitempty"`
}

// UserRoleListResponse đại diện cho phản hồi danh sách liên kết người dùng và vai trò
type UserRoleListResponse struct {
	UserRoles []UserRoleResponse `json:"user_roles"`
}
