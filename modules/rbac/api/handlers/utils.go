package handlers

import (
	"net/http"
	"time"

	"wnapi/internal/pkg/response"

	"github.com/gin-gonic/gin"
)

// Mã lỗi chung
const (
	ErrorCodeInvalidPermissionData = "INVALID_PERMISSION_DATA"
	ErrorCodePermissionNotFound    = "PERMISSION_NOT_FOUND"
	ErrorCodePermissionExists      = "PERMISSION_ALREADY_EXISTS"

	ErrorCodeInvalidRoleData = "INVALID_ROLE_DATA"
	ErrorCodeRoleNotFound    = "ROLE_NOT_FOUND"
	ErrorCodeRoleExists      = "ROLE_ALREADY_EXISTS"
	ErrorCodeSystemRole      = "SYSTEM_ROLE_PROTECTED"

	ErrorCodeInvalidUserRoleData = "INVALID_USER_ROLE_DATA"
	ErrorCodeUserRoleNotFound    = "USER_ROLE_NOT_FOUND"
	ErrorCodeInternalServer      = "INTERNAL_SERVER_ERROR"
)

// getTenantIDFromContext lấy tenant ID từ context request
// TODO: Implement với JWT claims
func getTenantIDFromContext(c *gin.Context) (int, error) {
	// Tạm thời trả về tenant ID cố định
	return 1, nil
}

// getUserIDFromContext lấy user ID từ context request
// TODO: Implement với JWT claims
func getUserIDFromContext(c *gin.Context) (int, error) {
	// Tạm thời trả về user ID cố định
	return 1, nil
}

// apiSuccess gửi response API thành công
func apiSuccess(c *gin.Context, statusCode int, message string, data interface{}) {
	if statusCode == http.StatusOK {
		response.Success(c, data, nil)
	} else {
		// Với status code khác 200, sử dụng JSON trực tiếp
		c.JSON(statusCode, response.Response{
			Status: response.Status{
				Code:      statusCode,
				Message:   message,
				Success:   true,
				ErrorCode: "",
				Path:      c.Request.URL.Path,
				Timestamp: time.Now().Format(time.RFC3339),
				Details:   nil,
			},
			Data: data,
			Meta: nil,
		})
	}
}

// apiSuccessWithMeta gửi response API thành công kèm metadata
func apiSuccessWithMeta(c *gin.Context, statusCode int, message string, data interface{}, metaData map[string]interface{}) {
	meta := &response.Meta{
		NextCursor: metaData["next_cursor"].(string),
		HasMore:    metaData["has_more"].(bool),
	}

	response.Success(c, data, meta)
}

// apiError gửi response API lỗi
func apiError(c *gin.Context, statusCode int, errorCode, message string) {
	response.Error(c, statusCode, message, errorCode)
}

// apiErrorWithDetails gửi response API lỗi kèm chi tiết
func apiErrorWithDetails(c *gin.Context, statusCode int, errorCode, message string, details interface{}) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, details)
}

// handleInvalidPermissionData xử lý lỗi dữ liệu quyền không hợp lệ
func handleInvalidPermissionData(c *gin.Context, err error) {
	details := []interface{}{
		map[string]string{
			"message": err.Error(),
		},
	}
	response.BadRequest(c, "Dữ liệu quyền không hợp lệ", ErrorCodeInvalidPermissionData, details)
}

// handlePermissionNotFound xử lý lỗi không tìm thấy quyền
func handlePermissionNotFound(c *gin.Context, err error) {
	response.NotFound(c, "Không tìm thấy quyền")
}

// handleInvalidRoleData xử lý lỗi dữ liệu vai trò không hợp lệ
func handleInvalidRoleData(c *gin.Context, err error) {
	details := []interface{}{
		map[string]string{
			"message": err.Error(),
		},
	}
	response.BadRequest(c, "Dữ liệu vai trò không hợp lệ", ErrorCodeInvalidRoleData, details)
}

// handleRoleNotFound xử lý lỗi không tìm thấy vai trò
func handleRoleNotFound(c *gin.Context, err error) {
	response.NotFound(c, "Không tìm thấy vai trò")
}

// handleServerError xử lý lỗi server
func handleServerError(c *gin.Context, err error) {
	response.InternalServerError(c, "Lỗi server")
}
