package handlers

import (
	"net/http"
	"strconv"

	"wnapi/internal/pkg/response"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/service"

	"github.com/gin-gonic/gin"
)

// RoleHandler xử lý các HTTP request liên quan đến vai trò
type RoleHandler struct {
	roleService service.RoleService
}

// NewRoleHandler tạo một instance mới của RoleHandler
func NewRoleHandler(roleService service.RoleService) *RoleHandler {
	return &RoleHandler{
		roleService: roleService,
	}
}

// Create xử lý tạo mới vai trò
func (h *RoleHandler) Create(c *gin.Context) {
	// Parse request
	var req request.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusUnauthorized, "UNAUTHORIZED", "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	role, err := h.roleService.CreateRole(c.Request.Context(), uint(tenantID), req)
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, role, nil)
}

// Get xử lý lấy thông tin vai trò theo ID
func (h *RoleHandler) Get(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusUnauthorized, "UNAUTHORIZED", "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	role, err := h.roleService.GetRole(c.Request.Context(), uint(tenantID), uint(roleID))
	if err != nil {
		handleRoleNotFound(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, role, nil)
}

// Update xử lý cập nhật vai trò
func (h *RoleHandler) Update(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Parse request
	var req request.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusUnauthorized, "UNAUTHORIZED", "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	role, err := h.roleService.UpdateRole(c.Request.Context(), uint(tenantID), uint(roleID), req)
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, role, nil)
}

// Delete xử lý xóa vai trò
func (h *RoleHandler) Delete(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusUnauthorized, "UNAUTHORIZED", "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	err = h.roleService.DeleteRole(c.Request.Context(), uint(tenantID), uint(roleID))
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, nil, nil)
}

// List xử lý lấy danh sách vai trò với phân trang
func (h *RoleHandler) List(c *gin.Context) {
	// Parse query parameters
	var req request.ListRoleRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Thiết lập giá trị mặc định nếu cần
	if req.Limit <= 0 {
		req.Limit = 20 // Mặc định 20 item mỗi trang
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusUnauthorized, "UNAUTHORIZED", "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	result, err := h.roleService.ListRoles(c.Request.Context(), uint(tenantID), req)
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	meta := &response.Meta{
		NextCursor: result.NextCursor,
		HasMore:    result.HasMore,
	}
	response.Success(c, result.Roles, meta)
}

// GetRolePermissions xử lý lấy danh sách quyền của vai trò
func (h *RoleHandler) GetRolePermissions(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidRoleData(c, err)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusUnauthorized, "UNAUTHORIZED", "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service để lấy thông tin vai trò (bao gồm danh sách quyền)
	role, err := h.roleService.GetRole(c.Request.Context(), uint(tenantID), uint(roleID))
	if err != nil {
		handleRoleNotFound(c, err)
		return
	}

	// Trả về danh sách quyền
	response.Success(c, role.Permissions, nil)
}
