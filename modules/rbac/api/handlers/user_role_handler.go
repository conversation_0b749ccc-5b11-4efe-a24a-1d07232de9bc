package handlers

import (
	"strconv"

	"wnapi/internal/pkg/response"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/service"

	"github.com/gin-gonic/gin"
)

// UserRoleHandler xử lý các HTTP request liên quan đến liên kết người dùng và vai trò
type UserRoleHandler struct {
	userRoleService service.UserRoleService
}

// NewUserRoleHandler tạo một instance mới của UserRoleHandler
func NewUserRoleHandler(userRoleService service.UserRoleService) *UserRoleHandler {
	return &UserRoleHandler{
		userRoleService: userRoleService,
	}
}

// AssignRole xử lý gán vai trò cho người dùng
func (h *UserRoleHandler) AssignRole(c *gin.Context) {
	// Parse request
	var req request.AssignUserRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "Dữ liệu không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	err = h.userRoleService.AssignRoleToUser(c.Request.Context(), uint(tenantID), req)
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, gin.H{
		"user_id": req.UserID,
		"role_id": req.RoleID,
	}, nil)
}

// RevokeRole xử lý thu hồi vai trò từ người dùng
func (h *UserRoleHandler) RevokeRole(c *gin.Context) {
	// Parse user ID và role ID từ path
	userID, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		response.BadRequest(c, "User ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	roleID, err := strconv.Atoi(c.Param("role_id"))
	if err != nil {
		response.BadRequest(c, "Role ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	err = h.userRoleService.RevokeRoleFromUser(c.Request.Context(), uint(tenantID), uint(userID), uint(roleID))
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, nil, nil)
}

// GetUserRoles xử lý lấy danh sách vai trò của người dùng
func (h *UserRoleHandler) GetUserRoles(c *gin.Context) {
	// Parse user ID từ path
	userID, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		response.BadRequest(c, "User ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	roles, err := h.userRoleService.GetUserRoles(c.Request.Context(), uint(tenantID), uint(userID))
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, roles, nil)
}

// CheckUserRole xử lý kiểm tra người dùng có vai trò cụ thể không
func (h *UserRoleHandler) CheckUserRole(c *gin.Context) {
	// Parse user ID và role ID từ path
	userID, err := strconv.Atoi(c.Param("user_id"))
	if err != nil {
		response.BadRequest(c, "User ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	roleID, err := strconv.Atoi(c.Param("role_id"))
	if err != nil {
		response.BadRequest(c, "Role ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	hasRole, err := h.userRoleService.HasRole(c.Request.Context(), uint(tenantID), uint(userID), uint(roleID))
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, gin.H{"has_role": hasRole}, nil)
}

// GetUsersWithRole xử lý lấy danh sách người dùng có vai trò cụ thể
func (h *UserRoleHandler) GetUsersWithRole(c *gin.Context) {
	// Parse role ID từ path
	roleID, err := strconv.Atoi(c.Param("role_id"))
	if err != nil {
		response.BadRequest(c, "Role ID không hợp lệ", ErrorCodeInvalidUserRoleData, nil)
		return
	}

	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		response.Unauthorized(c, "Không tìm thấy thông tin tenant")
		return
	}

	// Gọi service
	userIDs, err := h.userRoleService.GetUsersWithRole(c.Request.Context(), uint(tenantID), uint(roleID))
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, gin.H{"user_ids": userIDs}, nil)
}
