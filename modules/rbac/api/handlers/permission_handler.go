package handlers

import (
	"strconv"

	"wnapi/internal/pkg/response"
	"wnapi/modules/rbac/dto/request"
	"wnapi/modules/rbac/service"

	"github.com/gin-gonic/gin"
)

// PermissionHandler xử lý các HTTP request liên quan đến quyền
type PermissionHandler struct {
	permissionService service.PermissionService
}

// NewPermissionHandler tạo một instance mới của PermissionHandler
func NewPermissionHandler(permissionService service.PermissionService) *PermissionHandler {
	return &PermissionHandler{
		permissionService: permissionService,
	}
}

// Create xử lý tạo mới quyền
func (h *PermissionHandler) Create(c *gin.Context) {
	// Parse request
	var req request.CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleInvalidPermissionData(c, err)
		return
	}

	// Gọi service
	permission, err := h.permissionService.CreatePermission(c.Request.Context(), req)
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, permission, nil)
}

// Get xử lý lấy thông tin quyền theo ID
func (h *PermissionHandler) Get(c *gin.Context) {
	// Parse permission ID từ path
	permissionID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidPermissionData(c, err)
		return
	}

	// Gọi service
	permission, err := h.permissionService.GetPermission(c.Request.Context(), uint(permissionID))
	if err != nil {
		handlePermissionNotFound(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, permission, nil)
}

// Update xử lý cập nhật quyền
func (h *PermissionHandler) Update(c *gin.Context) {
	// Parse permission ID từ path
	permissionID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidPermissionData(c, err)
		return
	}

	// Parse request
	var req request.UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleInvalidPermissionData(c, err)
		return
	}

	// Gọi service
	permission, err := h.permissionService.UpdatePermission(c.Request.Context(), uint(permissionID), req)
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, permission, nil)
}

// Delete xử lý xóa quyền
func (h *PermissionHandler) Delete(c *gin.Context) {
	// Parse permission ID từ path
	permissionID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		handleInvalidPermissionData(c, err)
		return
	}

	// Gọi service
	err = h.permissionService.DeletePermission(c.Request.Context(), uint(permissionID))
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	response.Success(c, nil, nil)
}

// List xử lý lấy danh sách quyền với phân trang
func (h *PermissionHandler) List(c *gin.Context) {
	// Parse query parameters
	var req request.ListPermissionRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		handleInvalidPermissionData(c, err)
		return
	}

	// Thiết lập giá trị mặc định nếu cần
	if req.Limit <= 0 {
		req.Limit = 20 // Mặc định 20 item mỗi trang
	}

	// Gọi service
	result, err := h.permissionService.ListPermissions(c.Request.Context(), req)
	if err != nil {
		handleServerError(c, err)
		return
	}

	// Trả về kết quả thành công
	meta := &response.Meta{
		NextCursor: result.NextCursor,
		HasMore:    result.HasMore,
	}
	response.Success(c, result.Permissions, meta)
}
