-- Default roles for a new tenant
-- To be used with parameters:
-- @TENANT_ID: ID of the new tenant

-- Create tenant-specific admin role
INSERT INTO rbac_roles (role_code, tenant_id, role_name, role_description, created_at, updated_at) VALUES
(CONCAT('admin_', @TENANT_ID), @TENANT_ID, 'Quản trị viên', 'Vai trò quản trị viên cho tenant này', NOW(), NOW());

-- Set variable for the new role ID
SET @ADMIN_ROLE_ID = LAST_INSERT_ID();

-- Assign permissions to the tenant admin role
INSERT INTO rbac_role_permissions (role_id, permission_id, created_at, updated_at)
SELECT @ADMIN_ROLE_ID, permission_id, NOW(), NOW()
FROM rbac_permissions
WHERE permission_code NOT IN ('tenants.create', 'tenants.delete', 'tenants.update', 'tenants.manage_status', 'tenants.manage_plan');

-- Create tenant-specific editor role
INSERT INTO rbac_roles (role_code, tenant_id, role_name, role_description, created_at, updated_at) VALUES
(CONCAT('editor_', @TENANT_ID), @TENANT_ID, 'Biên tập viên', 'Vai trò biên tập viên cho tenant này', NOW(), NOW());

-- Set variable for the new role ID
SET @EDITOR_ROLE_ID = LAST_INSERT_ID();

-- Assign permissions to the tenant editor role
INSERT INTO rbac_role_permissions (role_id, permission_id, created_at, updated_at)
SELECT @EDITOR_ROLE_ID, permission_id, NOW(), NOW()
FROM rbac_permissions
WHERE permission_code IN ('content.create', 'content.read', 'content.update', 'content.publish', 'content.unpublish', 
                          'media.upload', 'media.download');

-- Create tenant-specific viewer role
INSERT INTO rbac_roles (role_code, tenant_id, role_name, role_description, created_at, updated_at) VALUES
(CONCAT('viewer_', @TENANT_ID), @TENANT_ID, 'Người xem', 'Vai trò chỉ xem cho tenant này', NOW(), NOW());

-- Set variable for the new role ID
SET @VIEWER_ROLE_ID = LAST_INSERT_ID();

-- Assign permissions to the tenant viewer role
INSERT INTO rbac_role_permissions (role_id, permission_id, created_at, updated_at)
SELECT @VIEWER_ROLE_ID, permission_id, NOW(), NOW()
FROM rbac_permissions
WHERE permission_code IN ('content.read', 'media.download'); 