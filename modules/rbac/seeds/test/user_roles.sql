-- Test user roles for development environment
-- <PERSON><PERSON><PERSON> sử có các test users từ user module

-- <PERSON><PERSON> vai trò Super Admin cho user ID 1 (User đ<PERSON><PERSON> tiên)
INSERT INTO rbac_user_roles (user_id, role_id, created_at, updated_at) VALUES
(1, 1, NOW(), NOW());

-- <PERSON><PERSON> vai trò Tenant Admin cho user ID 2
INSERT INTO rbac_user_roles (user_id, role_id, created_at, updated_at) VALUES
(2, 2, NOW(), NOW());

-- <PERSON><PERSON> vai trò Content Manager cho user ID 3
INSERT INTO rbac_user_roles (user_id, role_id, created_at, updated_at) VALUES
(3, 3, NOW(), NOW());

-- <PERSON><PERSON><PERSON> định có tenant ABC với ID = 1 
-- <PERSON><PERSON> vai trò admin của tenant cho user ID 2
INSERT INTO rbac_user_roles (user_id, role_id, created_at, updated_at) 
SELECT 2, role_id, NOW(), NOW() 
FROM rbac_roles 
WHERE role_code = 'admin_1';

-- <PERSON><PERSON> vai trò editor của tenant cho user ID 3
INSERT INTO rbac_user_roles (user_id, role_id, created_at, updated_at) 
SELECT 3, role_id, NOW(), NOW() 
FROM rbac_roles 
WHERE role_code = 'editor_1';

-- Gán vai trò viewer của tenant cho user ID 4
INSERT INTO rbac_user_roles (user_id, role_id, created_at, updated_at) 
SELECT 4, role_id, NOW(), NOW() 
FROM rbac_roles 
WHERE role_code = 'viewer_1'; 