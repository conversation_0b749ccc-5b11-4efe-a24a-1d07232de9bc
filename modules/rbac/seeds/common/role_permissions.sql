-- Role permissions associations
INSERT INTO rbac_role_permissions (role_id, permission_id, created_at, updated_at)
WITH role_permissions AS (
    -- Super Admin: all permissions
    SELECT 1 AS role_id, permission_id, NOW() AS created_at, NOW() AS updated_at
    FROM rbac_permissions
    
    UNION ALL
    
    -- Tenant Admin: all permissions except tenant management
    SELECT 2 AS role_id, permission_id, NOW() AS created_at, NOW() AS updated_at
    FROM rbac_permissions 
    WHERE permission_code NOT IN ('tenants.create', 'tenants.delete', 'tenants.manage_plan')
    
    UNION ALL
    
    -- Content Manager: content management and media permissions
    SELECT 3 AS role_id, permission_id, NOW() AS created_at, NOW() AS updated_at
    FROM rbac_permissions 
    WHERE group_id = 3 OR permission_code IN ('media.upload', 'media.download', 'media.delete')
    
    UNION ALL
    
    -- Editor: content creation/editing and media permissions
    SELECT 4 AS role_id, permission_id, NOW() AS created_at, NOW() AS updated_at
    FROM rbac_permissions 
    WHERE permission_code IN ('content.create', 'content.read', 'content.update', 'content.publish', 'content.unpublish', 
                              'media.upload', 'media.download')
    
    UNION ALL
    
    -- Viewer: read-only permissions
    SELECT 5 AS role_id, permission_id, NOW() AS created_at, NOW() AS updated_at
    FROM rbac_permissions 
    WHERE permission_code IN ('content.read', 'media.download', 'reports.view')
)
SELECT * FROM role_permissions; 