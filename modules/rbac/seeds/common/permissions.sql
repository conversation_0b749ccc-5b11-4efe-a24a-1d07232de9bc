-- Common permissions
-- N<PERSON><PERSON><PERSON> Quản lý người dùng
INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
('users.create', NULL, 1, 'Tạo người dùng', 'Quyền tạo người dùng mới trong hệ thống', NOW(), NOW()),
('users.read', NULL, 1, 'Xem người dùng', 'Quyền xem thông tin người dùng trong hệ thống', NOW(), NOW()),
('users.update', NULL, 1, 'Cập nhật người dùng', '<PERSON>uyền cập nhật thông tin người dùng trong hệ thống', NOW(), NOW()),
('users.delete', NULL, 1, 'Xóa người dùng', 'Quyền xóa người dùng khỏi hệ thống', NOW(), NOW()),
('users.manage_status', NULL, 1, '<PERSON>uản lý trạng thái người dùng', '<PERSON>uyề<PERSON> kích hoạt hoặc vô hiệu hóa tài khoản người dùng', NOW(), NOW());

-- Nhóm Quản lý vai trò
INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
('roles.create', NULL, 2, 'Tạo vai trò', 'Quyền tạo vai trò mới trong hệ thống', NOW(), NOW()),
('roles.read', NULL, 2, 'Xem vai trò', 'Quyền xem thông tin vai trò trong hệ thống', NOW(), NOW()),
('roles.update', NULL, 2, 'Cập nhật vai trò', 'Quyền cập nhật thông tin vai trò trong hệ thống', NOW(), NOW()),
('roles.delete', NULL, 2, 'Xóa vai trò', 'Quyền xóa vai trò khỏi hệ thống', NOW(), NOW()),
('permissions.assign', NULL, 2, 'Gán quyền', 'Quyền gán quyền cho vai trò', NOW(), NOW()),
('permissions.revoke', NULL, 2, 'Thu hồi quyền', 'Quyền thu hồi quyền từ vai trò', NOW(), NOW()),
('roles.assign', NULL, 2, 'Gán vai trò', 'Quyền gán vai trò cho người dùng', NOW(), NOW()),
('roles.revoke', NULL, 2, 'Thu hồi vai trò', 'Quyền thu hồi vai trò từ người dùng', NOW(), NOW());

-- Nhóm Quản lý nội dung
INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
('content.create', NULL, 3, 'Tạo nội dung', 'Quyền tạo nội dung mới trong hệ thống', NOW(), NOW()),
('content.read', NULL, 3, 'Xem nội dung', 'Quyền xem nội dung trong hệ thống', NOW(), NOW()),
('content.update', NULL, 3, 'Cập nhật nội dung', 'Quyền cập nhật nội dung trong hệ thống', NOW(), NOW()),
('content.delete', NULL, 3, 'Xóa nội dung', 'Quyền xóa nội dung khỏi hệ thống', NOW(), NOW()),
('content.publish', NULL, 3, 'Xuất bản nội dung', 'Quyền xuất bản nội dung', NOW(), NOW()),
('content.unpublish', NULL, 3, 'Hủy xuất bản nội dung', 'Quyền hủy xuất bản nội dung', NOW(), NOW());

-- Nhóm Quản lý cài đặt
INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
('settings.read', NULL, 4, 'Xem cài đặt', 'Quyền xem cài đặt hệ thống', NOW(), NOW()),
('settings.update', NULL, 4, 'Cập nhật cài đặt', 'Quyền cập nhật cài đặt hệ thống', NOW(), NOW());

-- Nhóm Quản lý media
INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
('media.upload', NULL, 5, 'Tải lên media', 'Quyền tải lên tệp media', NOW(), NOW()),
('media.read', NULL, 5, 'Xem media', 'Quyền xem thông tin tệp media', NOW(), NOW()),
('media.download', NULL, 5, 'Tải xuống media', 'Quyền tải xuống tệp media', NOW(), NOW()),
('media.update', NULL, 5, 'Cập nhật media', 'Quyền cập nhật thông tin tệp media', NOW(), NOW()),
('media.delete', NULL, 5, 'Xóa media', 'Quyền xóa tệp media', NOW(), NOW()),
('media.folders.create', NULL, 5, 'Tạo thư mục media', 'Quyền tạo thư mục media mới', NOW(), NOW()),
('media.folders.read', NULL, 5, 'Xem thư mục media', 'Quyền xem thông tin thư mục media', NOW(), NOW()),
('media.folders.update', NULL, 5, 'Cập nhật thư mục media', 'Quyền cập nhật thông tin thư mục media', NOW(), NOW()),
('media.folders.delete', NULL, 5, 'Xóa thư mục media', 'Quyền xóa thư mục media', NOW(), NOW());

-- Nhóm Báo cáo và thống kê
INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
('reports.view', NULL, 6, 'Xem báo cáo', 'Quyền xem báo cáo và thống kê', NOW(), NOW()),
('reports.export', NULL, 6, 'Xuất báo cáo', 'Quyền xuất báo cáo ra file', NOW(), NOW());

-- Nhóm Quản lý tenant
INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
('tenants.create', NULL, 7, 'Tạo tenant', 'Quyền tạo tenant mới trong hệ thống', NOW(), NOW()),
('tenants.read', NULL, 7, 'Xem tenant', 'Quyền xem thông tin tenant trong hệ thống', NOW(), NOW()),
('tenants.update', NULL, 7, 'Cập nhật tenant', 'Quyền cập nhật thông tin tenant trong hệ thống', NOW(), NOW()),
('tenants.delete', NULL, 7, 'Xóa tenant', 'Quyền xóa tenant khỏi hệ thống', NOW(), NOW()),
('tenants.manage_status', NULL, 7, 'Quản lý trạng thái tenant', 'Quyền kích hoạt hoặc vô hiệu hóa tenant', NOW(), NOW()),
('tenants.manage_plan', NULL, 7, 'Quản lý gói dịch vụ tenant', 'Quyền thay đổi gói dịch vụ của tenant', NOW(), NOW());

-- Nhóm Thông báo
INSERT INTO rbac_permission_groups (tenant_id, permission_group_name, permission_group_description, created_at, updated_at) VALUES
(NULL, 'Notification', 'Các quyền liên quan đến quản lý thông báo', NOW(), NOW());

INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
-- Notifications
('notifications.read', NULL, 10, 'Xem thông báo', 'Quyền xem thông báo', NOW(), NOW()),
('notifications.create', NULL, 10, 'Tạo thông báo', 'Quyền tạo thông báo mới', NOW(), NOW()),
('notifications.update', NULL, 10, 'Cập nhật thông báo', 'Quyền cập nhật thông báo', NOW(), NOW()),
('notifications.delete', NULL, 10, 'Xóa thông báo', 'Quyền xóa thông báo', NOW(), NOW()),

-- Notification Channels
('notification.channels.read', NULL, 10, 'Xem kênh thông báo', 'Quyền xem kênh thông báo', NOW(), NOW()),
('notification.channels.create', NULL, 10, 'Tạo kênh thông báo', 'Quyền tạo kênh thông báo mới', NOW(), NOW()),
('notification.channels.update', NULL, 10, 'Cập nhật kênh thông báo', 'Quyền cập nhật kênh thông báo', NOW(), NOW()),
('notification.channels.delete', NULL, 10, 'Xóa kênh thông báo', 'Quyền xóa kênh thông báo', NOW(), NOW()),

-- Notification Templates
('notification.templates.read', NULL, 10, 'Xem mẫu thông báo', 'Quyền xem mẫu thông báo', NOW(), NOW()),
('notification.templates.create', NULL, 10, 'Tạo mẫu thông báo', 'Quyền tạo mẫu thông báo mới', NOW(), NOW()),
('notification.templates.update', NULL, 10, 'Cập nhật mẫu thông báo', 'Quyền cập nhật mẫu thông báo', NOW(), NOW()),
('notification.templates.delete', NULL, 10, 'Xóa mẫu thông báo', 'Quyền xóa mẫu thông báo', NOW(), NOW()),

-- Notification Preferences
('notification.preferences.read', NULL, 10, 'Xem tùy chọn thông báo', 'Quyền xem tùy chọn thông báo', NOW(), NOW()),
('notification.preferences.create', NULL, 10, 'Tạo tùy chọn thông báo', 'Quyền tạo tùy chọn thông báo mới', NOW(), NOW()),
('notification.preferences.update', NULL, 10, 'Cập nhật tùy chọn thông báo', 'Quyền cập nhật tùy chọn thông báo', NOW(), NOW()),
('notification.preferences.delete', NULL, 10, 'Xóa tùy chọn thông báo', 'Quyền xóa tùy chọn thông báo', NOW(), NOW());

-- Nhóm SEO
INSERT INTO rbac_permission_groups (tenant_id, permission_group_name, permission_group_description, created_at, updated_at) VALUES
(NULL, 'SEO', 'Các quyền liên quan đến quản lý SEO', NOW(), NOW());

INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
-- SEO Meta
('seo.meta.read', NULL, 11, 'Xem thông tin SEO', 'Quyền xem thông tin SEO meta', NOW(), NOW()),
('seo.meta.create', NULL, 11, 'Tạo thông tin SEO', 'Quyền tạo thông tin SEO meta mới', NOW(), NOW()),
('seo.meta.update', NULL, 11, 'Cập nhật thông tin SEO', 'Quyền cập nhật thông tin SEO meta', NOW(), NOW()),
('seo.meta.delete', NULL, 11, 'Xóa thông tin SEO', 'Quyền xóa thông tin SEO meta', NOW(), NOW());

-- Nhóm Quản lý Blog
INSERT INTO rbac_permission_groups (tenant_id, permission_group_name, permission_group_description, created_at, updated_at) VALUES
(NULL, 'Blog', 'Các quyền liên quan đến quản lý blog', NOW(), NOW());

INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
-- Categories
('blog.categories.read', NULL, 8, 'Xem danh mục blog', 'Quyền xem thông tin danh mục blog', NOW(), NOW()),
('blog.categories.create', NULL, 8, 'Tạo danh mục blog', 'Quyền tạo danh mục blog mới', NOW(), NOW()),
('blog.categories.update', NULL, 8, 'Cập nhật danh mục blog', 'Quyền cập nhật thông tin danh mục blog', NOW(), NOW()),
('blog.categories.delete', NULL, 8, 'Xóa danh mục blog', 'Quyền xóa danh mục blog', NOW(), NOW()),

-- Posts
('blog.posts.read', NULL, 8, 'Xem bài viết blog', 'Quyền xem thông tin bài viết blog', NOW(), NOW()),
('blog.posts.create', NULL, 8, 'Tạo bài viết blog', 'Quyền tạo bài viết blog mới', NOW(), NOW()),
('blog.posts.update', NULL, 8, 'Cập nhật bài viết blog', 'Quyền cập nhật thông tin bài viết blog', NOW(), NOW()),
('blog.posts.delete', NULL, 8, 'Xóa bài viết blog', 'Quyền xóa bài viết blog', NOW(), NOW()),

-- Tags
('blog.tags.read', NULL, 8, 'Xem tag blog', 'Quyền xem thông tin tag blog', NOW(), NOW()),
('blog.tags.create', NULL, 8, 'Tạo tag blog', 'Quyền tạo tag blog mới', NOW(), NOW()),
('blog.tags.update', NULL, 8, 'Cập nhật tag blog', 'Quyền cập nhật thông tin tag blog', NOW(), NOW()),
('blog.tags.delete', NULL, 8, 'Xóa tag blog', 'Quyền xóa tag blog', NOW(), NOW()),

-- Authors
('blog.authors.read', NULL, 8, 'Xem tác giả blog', 'Quyền xem thông tin tác giả blog', NOW(), NOW()),
('blog.authors.create', NULL, 8, 'Tạo tác giả blog', 'Quyền tạo tác giả blog mới', NOW(), NOW()),
('blog.authors.update', NULL, 8, 'Cập nhật tác giả blog', 'Quyền cập nhật thông tin tác giả blog', NOW(), NOW()),
('blog.authors.delete', NULL, 8, 'Xóa tác giả blog', 'Quyền xóa tác giả blog', NOW(), NOW());

-- Nhóm Quản lý Ecommerce
INSERT INTO rbac_permission_groups (tenant_id, permission_group_name, permission_group_description, created_at, updated_at) VALUES
(NULL, 'Ecommerce', 'Các quyền liên quan đến quản lý bán hàng', NOW(), NOW());

INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
-- Categories
('ecom.categories.read', NULL, 9, 'Xem danh mục sản phẩm', 'Quyền xem thông tin danh mục sản phẩm', NOW(), NOW()),
('ecom.categories.create', NULL, 9, 'Tạo danh mục sản phẩm', 'Quyền tạo danh mục sản phẩm mới', NOW(), NOW()),
('ecom.categories.update', NULL, 9, 'Cập nhật danh mục sản phẩm', 'Quyền cập nhật thông tin danh mục sản phẩm', NOW(), NOW()),
('ecom.categories.delete', NULL, 9, 'Xóa danh mục sản phẩm', 'Quyền xóa danh mục sản phẩm', NOW(), NOW()),

-- Product Options
('ecom.product-options.read', NULL, 9, 'Xem tùy chọn sản phẩm', 'Quyền xem thông tin tùy chọn sản phẩm', NOW(), NOW()),
('ecom.product-options.create', NULL, 9, 'Tạo tùy chọn sản phẩm', 'Quyền tạo tùy chọn sản phẩm mới', NOW(), NOW()),
('ecom.product-options.update', NULL, 9, 'Cập nhật tùy chọn sản phẩm', 'Quyền cập nhật thông tin tùy chọn sản phẩm', NOW(), NOW()),
('ecom.product-options.delete', NULL, 9, 'Xóa tùy chọn sản phẩm', 'Quyền xóa tùy chọn sản phẩm', NOW(), NOW()),

-- Product Option Values
('ecom.product-option-values.read', NULL, 9, 'Xem giá trị tùy chọn', 'Quyền xem thông tin giá trị tùy chọn sản phẩm', NOW(), NOW()),
('ecom.product-option-values.create', NULL, 9, 'Tạo giá trị tùy chọn', 'Quyền tạo giá trị tùy chọn sản phẩm mới', NOW(), NOW()),
('ecom.product-option-values.update', NULL, 9, 'Cập nhật giá trị tùy chọn', 'Quyền cập nhật thông tin giá trị tùy chọn sản phẩm', NOW(), NOW()),
('ecom.product-option-values.delete', NULL, 9, 'Xóa giá trị tùy chọn', 'Quyền xóa giá trị tùy chọn sản phẩm', NOW(), NOW()),

-- Product Option Assignments
('ecom.product-option-assignments.read', NULL, 9, 'Xem gán tùy chọn', 'Quyền xem thông tin gán tùy chọn cho sản phẩm', NOW(), NOW()),
('ecom.product-option-assignments.create', NULL, 9, 'Tạo gán tùy chọn', 'Quyền gán tùy chọn cho sản phẩm', NOW(), NOW()),
('ecom.product-option-assignments.update', NULL, 9, 'Cập nhật gán tùy chọn', 'Quyền cập nhật thông tin gán tùy chọn sản phẩm', NOW(), NOW()),
('ecom.product-option-assignments.delete', NULL, 9, 'Xóa gán tùy chọn', 'Quyền xóa gán tùy chọn khỏi sản phẩm', NOW(), NOW()),

-- Products
('ecom.products.read', NULL, 9, 'Xem sản phẩm', 'Quyền xem thông tin sản phẩm', NOW(), NOW()),
('ecom.products.create', NULL, 9, 'Tạo sản phẩm', 'Quyền tạo sản phẩm mới', NOW(), NOW()),
('ecom.products.update', NULL, 9, 'Cập nhật sản phẩm', 'Quyền cập nhật thông tin sản phẩm', NOW(), NOW()),
('ecom.products.delete', NULL, 9, 'Xóa sản phẩm', 'Quyền xóa sản phẩm', NOW(), NOW());

-- Nhóm Payment
INSERT INTO rbac_permission_groups (tenant_id, permission_group_name, permission_group_description, created_at, updated_at) VALUES
(NULL, 'Payment', 'Các quyền liên quan đến quản lý thanh toán', NOW(), NOW());

INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
-- Providers
('payment.providers.read', NULL, 12, 'Xem nhà cung cấp thanh toán', 'Quyền xem thông tin nhà cung cấp thanh toán', NOW(), NOW()),
('payment.providers.create', NULL, 12, 'Tạo nhà cung cấp thanh toán', 'Quyền tạo nhà cung cấp thanh toán mới', NOW(), NOW()),
('payment.providers.update', NULL, 12, 'Cập nhật nhà cung cấp thanh toán', 'Quyền cập nhật thông tin nhà cung cấp thanh toán', NOW(), NOW()),
('payment.providers.delete', NULL, 12, 'Xóa nhà cung cấp thanh toán', 'Quyền xóa nhà cung cấp thanh toán', NOW(), NOW()),

-- Payment Methods
('payment.methods.read', NULL, 12, 'Xem phương thức thanh toán', 'Quyền xem thông tin phương thức thanh toán', NOW(), NOW()),
('payment.methods.create', NULL, 12, 'Tạo phương thức thanh toán', 'Quyền tạo phương thức thanh toán mới', NOW(), NOW()),
('payment.methods.update', NULL, 12, 'Cập nhật phương thức thanh toán', 'Quyền cập nhật thông tin phương thức thanh toán', NOW(), NOW()),
('payment.methods.delete', NULL, 12, 'Xóa phương thức thanh toán', 'Quyền xóa phương thức thanh toán', NOW(), NOW()),

-- Transactions
('payment.transactions.read', NULL, 12, 'Xem giao dịch thanh toán', 'Quyền xem thông tin giao dịch thanh toán', NOW(), NOW()),
('payment.transactions.create', NULL, 12, 'Tạo giao dịch thanh toán', 'Quyền tạo giao dịch thanh toán mới', NOW(), NOW()),
('payment.transactions.update', NULL, 12, 'Cập nhật giao dịch thanh toán', 'Quyền cập nhật thông tin giao dịch thanh toán', NOW(), NOW()),

-- Refunds
('payment.refunds.read', NULL, 12, 'Xem hoàn tiền', 'Quyền xem thông tin hoàn tiền', NOW(), NOW()),
('payment.refunds.create', NULL, 12, 'Tạo hoàn tiền', 'Quyền tạo yêu cầu hoàn tiền mới', NOW(), NOW()),
('payment.refunds.update', NULL, 12, 'Cập nhật hoàn tiền', 'Quyền cập nhật thông tin hoàn tiền', NOW(), NOW()),

-- Virtual Accounts
('payment.virtual-accounts.read', NULL, 12, 'Xem tài khoản ảo', 'Quyền xem thông tin tài khoản ảo', NOW(), NOW()),
('payment.virtual-accounts.create', NULL, 12, 'Tạo tài khoản ảo', 'Quyền tạo tài khoản ảo mới', NOW(), NOW()),
('payment.virtual-accounts.update', NULL, 12, 'Cập nhật tài khoản ảo', 'Quyền cập nhật thông tin tài khoản ảo', NOW(), NOW()),

-- MoMo
('payment.momo.read', NULL, 12, 'Xem thanh toán MoMo', 'Quyền xem thông tin thanh toán MoMo', NOW(), NOW()),
('payment.momo.create', NULL, 12, 'Tạo thanh toán MoMo', 'Quyền tạo thanh toán MoMo mới', NOW(), NOW()),
('payment.momo.update', NULL, 12, 'Cập nhật thanh toán MoMo', 'Quyền cập nhật thông tin thanh toán MoMo', NOW(), NOW()),

-- ZaloPay
('payment.zalopay.read', NULL, 12, 'Xem thanh toán ZaloPay', 'Quyền xem thông tin thanh toán ZaloPay', NOW(), NOW()),
('payment.zalopay.create', NULL, 12, 'Tạo thanh toán ZaloPay', 'Quyền tạo thanh toán ZaloPay mới', NOW(), NOW()),
('payment.zalopay.update', NULL, 12, 'Cập nhật thanh toán ZaloPay', 'Quyền cập nhật thông tin thanh toán ZaloPay', NOW(), NOW()),

-- Webhooks
('payment.webhooks.read', NULL, 12, 'Xem webhook thanh toán', 'Quyền xem thông tin webhook thanh toán', NOW(), NOW()),
('payment.webhooks.create', NULL, 12, 'Tạo webhook thanh toán', 'Quyền tạo webhook thanh toán mới', NOW(), NOW()),
('payment.webhooks.update', NULL, 12, 'Cập nhật webhook thanh toán', 'Quyền cập nhật thông tin webhook thanh toán', NOW(), NOW());

-- Nhóm Site
INSERT INTO rbac_permission_groups (tenant_id, permission_group_name, permission_group_description, created_at, updated_at) VALUES
(NULL, 'Site', 'Các quyền liên quan đến quản lý website', NOW(), NOW());

INSERT INTO rbac_permissions (permission_code, tenant_id, group_id, permission_name, permission_description, created_at, updated_at) VALUES
-- Websites
('site.websites.read', NULL, 13, 'Xem website', 'Quyền xem thông tin website', NOW(), NOW()),
('site.websites.create', NULL, 13, 'Tạo website', 'Quyền tạo website mới', NOW(), NOW()),
('site.websites.update', NULL, 13, 'Cập nhật website', 'Quyền cập nhật thông tin website', NOW(), NOW()),
('site.websites.delete', NULL, 13, 'Xóa website', 'Quyền xóa website', NOW(), NOW()),

-- Pages
('site.pages.read', NULL, 13, 'Xem trang', 'Quyền xem thông tin trang website', NOW(), NOW()),
('site.pages.create', NULL, 13, 'Tạo trang', 'Quyền tạo trang mới', NOW(), NOW()),
('site.pages.update', NULL, 13, 'Cập nhật trang', 'Quyền cập nhật thông tin trang', NOW(), NOW()),
('site.pages.delete', NULL, 13, 'Xóa trang', 'Quyền xóa trang', NOW(), NOW()),

-- Themes
('site.themes.read', NULL, 13, 'Xem giao diện', 'Quyền xem thông tin giao diện', NOW(), NOW()),
('site.themes.create', NULL, 13, 'Tạo giao diện', 'Quyền tạo giao diện mới', NOW(), NOW()),
('site.themes.update', NULL, 13, 'Cập nhật giao diện', 'Quyền cập nhật thông tin giao diện', NOW(), NOW()),
('site.themes.delete', NULL, 13, 'Xóa giao diện', 'Quyền xóa giao diện', NOW(), NOW()),

-- Templates
('site.templates.read', NULL, 13, 'Xem mẫu giao diện', 'Quyền xem thông tin mẫu giao diện', NOW(), NOW()),
('site.templates.create', NULL, 13, 'Tạo mẫu giao diện', 'Quyền tạo mẫu giao diện mới', NOW(), NOW()),
('site.templates.update', NULL, 13, 'Cập nhật mẫu giao diện', 'Quyền cập nhật thông tin mẫu giao diện', NOW(), NOW()),
('site.templates.delete', NULL, 13, 'Xóa mẫu giao diện', 'Quyền xóa mẫu giao diện', NOW(), NOW());