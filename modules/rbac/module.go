package rbac

import (
	"context"
	"path/filepath"
	"time"

	"wnapi/internal/core"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/api"
	"wnapi/modules/rbac/configs"
	"wnapi/modules/rbac/internal"
	"wnapi/modules/rbac/repository/mysql"
	"wnapi/modules/rbac/service"
)

// StandaloneService định nghĩa interface cho dịch vụ standalone
type StandaloneService interface {
	Start() error
	Stop() error
}

// StandaloneModule triển khai standalone RBAC service
type StandaloneModule struct {
	config  *configs.Config
	logger  logger.Logger
	handler *api.Handler
	// Add any other dependencies needed for standalone operation
}

// NewModuleWithConfig tạo standalone module từ config
func NewModuleWithConfig(config *configs.Config) StandaloneService {
	// Initialize logger for standalone mode
	log := logger.NewConsoleLogger("rbac-standalone", logger.LevelInfo)

	// For standalone mode, we'll need to initialize database connection
	// This is simplified - in a real implementation you might want to use the same
	// database manager pattern as the main app
	log.Info("Initializing standalone RBAC module")

	return &StandaloneModule{
		config: config,
		logger: log,
		// handler will be initialized in Start() method
	}
}

// Start khởi động standalone service
func (s *StandaloneModule) Start() error {
	s.logger.Info("Starting standalone RBAC service")

	// TODO: Initialize database connection
	// TODO: Initialize repositories and services
	// TODO: Initialize API handler
	// TODO: Start HTTP server

	s.logger.Info("Standalone RBAC service started successfully")
	return nil
}

// Stop dừng standalone service
func (s *StandaloneModule) Stop() error {
	s.logger.Info("Stopping standalone RBAC service")

	// TODO: Close database connections
	// TODO: Stop HTTP server
	// TODO: Cleanup resources

	s.logger.Info("Standalone RBAC service stopped successfully")
	return nil
}

func init() {
	core.RegisterModuleFactory("rbac", NewModule)
}

// Module triển khai RBAC module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Đọc cấu hình từ biến môi trường
	rbacConfig, err := internal.LoadRBACConfig()
	if err != nil {
		logger.Warn("Không thể đọc cấu hình từ biến môi trường, sử dụng giá trị mặc định: %v", err)

		// Sử dụng cấu hình mặc định nếu không đọc được từ biến môi trường
		rbacConfig = &internal.RBACConfig{
			JWTAccessSecret:    "default_jwt_access_secret_change_me_in_production",
			JWTRefreshSecret:   "default_jwt_refresh_secret_change_me_in_production",
			AccessTokenExpiry:  24 * time.Hour,
			RefreshTokenExpiry: 168 * time.Hour,
			JWTIssuer:          "wn-backend",
		}
	}

	// Ghi đè cấu hình từ config map nếu có (để tương thích ngược)
	if jwtSecret, ok := config["jwt_access_secret"].(string); ok && jwtSecret != "" {
		rbacConfig.JWTAccessSecret = jwtSecret
	}

	if jwtRefreshSecret, ok := config["jwt_refresh_secret"].(string); ok && jwtRefreshSecret != "" {
		rbacConfig.JWTRefreshSecret = jwtRefreshSecret
	}

	if accessExpiry, ok := config["access_token_expiry"].(string); ok && accessExpiry != "" {
		if duration, err := time.ParseDuration(accessExpiry); err == nil {
			rbacConfig.AccessTokenExpiry = duration
		}
	}

	if refreshExpiry, ok := config["refresh_token_expiry"].(string); ok && refreshExpiry != "" {
		if duration, err := time.ParseDuration(refreshExpiry); err == nil {
			rbacConfig.RefreshTokenExpiry = duration
		}
	}

	// Khởi tạo repository
	roleRepo := mysql.NewRoleRepository(app.GetDBManager().DB)
	permissionRepo := mysql.NewPermissionRepository(app.GetDBManager().DB)
	userRoleRepo := mysql.NewUserRoleRepository(app.GetDBManager().DB)
	permissionGroupRepo := mysql.NewPermissionGroupRepository(app.GetDBManager().DB)

	// Khởi tạo service
	permissionService := service.NewPermissionService(permissionRepo, roleRepo, userRoleRepo, permissionGroupRepo, logger)
	roleService := service.NewRoleService(roleRepo, permissionRepo, logger)
	userRoleService := service.NewUserRoleService(userRoleRepo, roleRepo)
	permissionGroupAPIService := service.NewPermissionGroupAPIService(permissionGroupRepo)

	// Khởi tạo handler
	handler := api.NewHandler(
		permissionService,
		roleService,
		userRoleService,
		permissionGroupAPIService,
	)

	return &Module{
		name:    "rbac",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
	}, nil
}

// Name trả về tên của module
func (m *Module) Name() string {
	return m.name
}

// Init khởi tạo module
func (m *Module) Init(ctx context.Context) error {
	m.logger.Info("Đang khởi tạo module RBAC")
	return nil
}

// RegisterRoutes đăng ký các route của module
func (m *Module) RegisterRoutes(server *core.Server) error {
	m.logger.Info("Đăng ký routes cho module RBAC")
	return registerRoutes(server, m.handler)
}

// Cleanup dọn dẹp tài nguyên của module
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Đang dọn dẹp module RBAC")
	return nil
}

// GetMigrationPath trả về đường dẫn chứa migrations
func (m *Module) GetMigrationPath() string {
	return filepath.Join("modules", "rbac", "migrations")
}

// GetMigrationOrder trả về thứ tự ưu tiên khi chạy migration của module
func (m *Module) GetMigrationOrder() int {
	return 2 // RBAC module chạy sau Auth module
}
