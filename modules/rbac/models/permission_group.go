package models

import "time"

// PermissionGroup ánh xạ bảng rbac_permission_groups
// Tên field phải giống tên cột trong database
// Thêm gorm:"primaryKey" cho id
// <PERSON><PERSON><PERSON> bả<PERSON> đúng chuẩn audit

type PermissionGroup struct {
	GroupID                    uint      `db:"group_id" json:"group_id" gorm:"primaryKey"`
	TenantID                   *uint     `db:"tenant_id" json:"tenant_id"`
	PermissionGroupName        string    `db:"permission_group_name" json:"permission_group_name"`
	PermissionGroupDescription *string   `db:"permission_group_description" json:"permission_group_description"`
	CreatedBy                  *uint     `db:"created_by" json:"created_by"`
	CreatedAt                  time.Time `db:"created_at" json:"created_at"`
	UpdatedBy                  *uint     `db:"updated_by" json:"updated_by"`
	UpdatedAt                  time.Time `db:"updated_at" json:"updated_at"`
}
