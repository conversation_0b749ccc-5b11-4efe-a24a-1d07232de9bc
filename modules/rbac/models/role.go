package models

import (
	"time"
)

// Role đại diện cho một vai trò trong hệ thống
type Role struct {
	RoleID          uint      `db:"role_id" json:"role_id" gorm:"primaryKey"`
	RoleCode        string    `db:"role_code" json:"role_code"`
	TenantID        *uint     `db:"tenant_id" json:"tenant_id"`
	RoleName        string    `db:"role_name" json:"role_name"`
	RoleDescription *string   `db:"role_description" json:"role_description"`
	CreatedBy       *uint     `db:"created_by" json:"created_by"`
	CreatedAt       time.Time `db:"created_at" json:"created_at"`
	UpdatedBy       *uint     `db:"updated_by" json:"updated_by"`
	UpdatedAt       time.Time `db:"updated_at" json:"updated_at"`

	// Virtual fields
	Permissions []uint `db:"-" json:"permissions,omitempty"` // Permission IDs
}
