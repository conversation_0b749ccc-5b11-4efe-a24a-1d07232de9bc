package models

import (
	"time"
)

// UserRole đại diện cho mối quan hệ giữa người dùng và vai trò
type UserRole struct {
	TenantID  uint      `db:"tenant_id" json:"tenant_id" gorm:"primaryKey"`
	UserID    uint      `db:"user_id" json:"user_id" gorm:"primaryKey"`
	RoleID    uint      `db:"role_id" json:"role_id" gorm:"primaryKey"`
	CreatedBy *uint     `db:"created_by" json:"created_by"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
	UpdatedBy *uint     `db:"updated_by" json:"updated_by"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
}
