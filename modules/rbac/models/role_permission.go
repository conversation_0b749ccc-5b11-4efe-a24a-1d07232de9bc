package models

import (
	"time"
)

// RolePermission đại diện cho mối quan hệ giữa vai trò và quyền
type RolePermission struct {
	RoleID       uint      `db:"role_id" json:"role_id" gorm:"primaryKey"`
	PermissionID uint      `db:"permission_id" json:"permission_id" gorm:"primaryKey"`
	CreatedBy    *uint     `db:"created_by" json:"created_by"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedBy    *uint     `db:"updated_by" json:"updated_by"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
}
