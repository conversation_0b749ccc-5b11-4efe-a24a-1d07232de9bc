// modules/rbac/models/models.go
package models

import (
	"time"

	"gorm.io/gorm"
)

// Role đại diện cho một vai trò trong hệ thống
type Role struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	TenantID    uint           `json:"tenant_id" gorm:"index"`
	Name        string         `json:"name" gorm:"size:255;not null"`
	Description string         `json:"description" gorm:"size:500"`
	IsSystem    bool           `json:"is_system" gorm:"default:false"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// Permission đại diện cho một quyền trong hệ thống
type Permission struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Code        string         `json:"code" gorm:"size:255;not null;uniqueIndex"`
	Name        string         `json:"name" gorm:"size:255;not null"`
	Description string         `json:"description" gorm:"size:500"`
	ModuleName  string         `json:"module_name" gorm:"size:100;index"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// UserRole là bảng trung gian giữa User và Role
type UserRole struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    uint      `json:"user_id" gorm:"index:idx_user_tenant_role,unique"`
	TenantID  uint      `json:"tenant_id" gorm:"index:idx_user_tenant_role,unique"`
	RoleID    uint      `json:"role_id" gorm:"index:idx_user_tenant_role,unique"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// RolePermission là bảng trung gian giữa Role và Permission
type RolePermission struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	RoleID       uint      `json:"role_id" gorm:"index:idx_role_permission,unique"`
	PermissionID uint      `json:"permission_id" gorm:"index:idx_role_permission,unique"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}
