package models

import (
	"time"
)

// Permission đại diện cho một quyền trong hệ thống
type Permission struct {
	PermissionID          uint      `db:"permission_id" json:"permission_id" gorm:"primaryKey"`
	PermissionCode        string    `db:"permission_code" json:"permission_code"`
	TenantID              *uint     `db:"tenant_id" json:"tenant_id"`
	GroupID               *uint     `db:"group_id" json:"group_id"`
	PermissionName        string    `db:"permission_name" json:"permission_name"`
	PermissionDescription *string   `db:"permission_description" json:"permission_description"`
	CreatedBy             *uint     `db:"created_by" json:"created_by"`
	CreatedAt             time.Time `db:"created_at" json:"created_at"`
	UpdatedBy             *uint     `db:"updated_by" json:"updated_by"`
	UpdatedAt             time.Time `db:"updated_at" json:"updated_at"`
}
