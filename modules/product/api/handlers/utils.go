package handlers

import (
	"errors"
	"net/http"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/response"
	"wnapi/modules/product/repository/mysql"
	// TODO: Add auth import when available
	// // "wnapi/internal/pkg/auth"
)

// Constants for error codes
const (
	ErrCodeNotFound      = "NOT_FOUND"
	ErrCodeInvalidInput  = "INVALID_INPUT"
	ErrCodeDuplicateSlug = "DUPLICATE_SLUG"
	ErrCodeServerError   = "SERVER_ERROR"
)

// getTenantIDInt64FromContext retrieves the tenant ID from the request context as int64
func getTenantIDInt64FromContext(c *gin.Context) (int64, error) {
	// TODO: Implement when auth package is available
	// tenantID := // auth.GetTenantID(c)
	// For now, return a default tenant ID for compilation
	return 1, nil
}

// getTenantIDFromContext retrieves the tenant ID from the request context as int
func getTenantIDFromContext(c *gin.Context) (int, error) {
	// TODO: Implement when auth package is available
	// tenantID := // auth.GetTenantID(c)
	// For now, return a default tenant ID for compilation
	return 1, nil
}

// getTenantIDStringFromContext retrieves the tenant ID from the request context as string
func getTenantIDStringFromContext(c *gin.Context) (string, error) {
	// TODO: Implement when auth package is available
	// tenantID := // auth.GetTenantID(c)
	// For now, return a default tenant ID for compilation
	return "1", nil
}

// GenerateSlug converts a string to URL-friendly slug
func GenerateSlug(input string) string {
	// Convert to lowercase
	slug := strings.ToLower(input)

	// Replace special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	return slug
}

// getUserIDFromContext retrieves the user ID from the context
func getUserIDFromContext(c *gin.Context) int64 {
	// TODO: Implement when auth package is available
	// userID, exists := // auth.GetUserIDFromContext(c)
	// For now, return a default user ID for compilation
	return 1
}

// apiSuccess sends a successful API response
func apiSuccess(c *gin.Context, statusCode int, data interface{}) {
	response.Success(c, data, nil)
}

// apiSuccessWithMeta sends a successful API response with metadata
func apiSuccessWithMeta(c *gin.Context, statusCode int, data interface{}, meta gin.H) {
	responseMeta := &response.Meta{}
	if nextCursor, ok := meta["next_cursor"].(string); ok {
		responseMeta.NextCursor = nextCursor
	}
	if hasMore, ok := meta["has_more"].(bool); ok {
		responseMeta.HasMore = hasMore
	}
	response.Success(c, data, responseMeta)
}

// apiError sends an error API response
func apiError(c *gin.Context, statusCode int, errorCode string, message string) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, nil)
}

// apiErrorWithErr sends an error API response with error details
func apiErrorWithErr(c *gin.Context, statusCode int, message string, err error) {
	errorCode := "INTERNAL_ERROR"
	switch statusCode {
	case 400:
		errorCode = "BAD_REQUEST"
	case 401:
		errorCode = "UNAUTHORIZED"
	case 403:
		errorCode = "FORBIDDEN"
	case 404:
		errorCode = "NOT_FOUND"
	case 500:
		errorCode = "INTERNAL_SERVER_ERROR"
	}
	response.ErrorWithDetails(c, statusCode, message, errorCode, err.Error())
}

// apiErrorWithDetails sends an error API response with detailed information
func apiErrorWithDetails(c *gin.Context, statusCode int, errorCode string, message string, details interface{}) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, details)
}

// handleError is a helper function to handle errors consistently
func handleError(c *gin.Context, err error) {
	statusCode := getStatusCode(err)
	var errorCode string

	switch {
	case errors.Is(err, mysql.ErrNotFound):
		errorCode = "NOT_FOUND"
	case errors.Is(err, mysql.ErrConflict):
		errorCode = "CONFLICT"
	default:
		errorCode = "INTERNAL_ERROR"
	}

	details := []interface{}{map[string]string{"message": err.Error()}}
	response.ErrorWithDetails(c, statusCode, err.Error(), errorCode, details)
}

// getStatusCode maps errors to HTTP status codes
func getStatusCode(err error) int {
	switch {
	case errors.Is(err, mysql.ErrNotFound):
		return http.StatusNotFound
	case errors.Is(err, mysql.ErrConflict):
		return http.StatusConflict
	default:
		return http.StatusInternalServerError
	}
}

// contains checks if a string contains a substring
func contains(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}
