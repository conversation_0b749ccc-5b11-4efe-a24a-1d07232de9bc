package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/dto/response"
	"wnapi/modules/product/service"

	// "wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/pagination"
	pkgResponse "wnapi/internal/pkg/response"
)

// ProductAttributeOptionHandler xử lý các yêu cầu liên quan đến tùy chọn thuộc tính sản phẩm
type ProductAttributeOptionHandler struct {
	service *service.ProductAttributeOptionService
	// TODO: Add JWT service when auth package is available
	// jwtService *auth.JWTService
}

// NewProductAttributeOptionHandler tạo một handler mới cho tùy chọn thuộc tính sản phẩm
func NewProductAttributeOptionHandler(service *service.ProductAttributeOptionService /* jwtService *auth.JWTService */) *ProductAttributeOptionHandler {
	return &ProductAttributeOptionHandler{
		service: service,
		// jwtService: jwtService,
	}
}

// ListProductAttributeOptions xử lý yêu cầu lấy danh sách tùy chọn thuộc tính sản phẩm
func (h *ProductAttributeOptionHandler) ListProductAttributeOptions(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Binding tham số request
	var req request.ListProductAttributeOptionsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Tham số không hợp lệ", err)
		return
	}

	var options []*response.ProductAttributeOptionResponse
	var nextCursor *pagination.Cursor
	var attributeID uint

	// Nếu có attributeID, lấy danh sách tùy chọn theo attributeID
	if req.AttributeID != nil {
		attributeID = *req.AttributeID
		// Lấy danh sách tùy chọn thuộc tính theo ID thuộc tính
		optionModels, err := h.service.GetByAttributeID(uint(tenantID), attributeID)
		if err != nil {
			pkgResponse.Error(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách tùy chọn thuộc tính", err)
			return
		}
		options = response.NewProductAttributeOptionResponseList(optionModels)
	} else {
		// Lấy danh sách tất cả tùy chọn thuộc tính với phân trang
		optionModels, cursor, err := h.service.List(uint(tenantID), req.Cursor, req.Limit)
		if err != nil {
			pkgResponse.Error(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách tùy chọn thuộc tính", err)
			return
		}
		options = response.NewProductAttributeOptionResponseList(optionModels)
		nextCursor = cursor
	}

	// Tạo metadata phân trang
	meta := map[string]interface{}{
		"has_more": nextCursor != nil,
	}
	if nextCursor != nil {
		encodedCursor := nextCursor.Encode()
		meta["next_cursor"] = encodedCursor
	}

	// Trả về kết quả
	pkgResponse.SuccessWithMeta(c, http.StatusOK, "Lấy danh sách tùy chọn thuộc tính thành công", options, meta)
}

// GetProductAttributeOption xử lý yêu cầu lấy chi tiết tùy chọn thuộc tính
func (h *ProductAttributeOptionHandler) GetProductAttributeOption(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy ID tùy chọn từ tham số
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID không hợp lệ", err)
		return
	}

	// Lấy thông tin tùy chọn
	option, err := h.service.GetByID(uint(tenantID), uint(id))
	if err != nil {
		pkgResponse.Error(c, http.StatusNotFound, "Không tìm thấy tùy chọn thuộc tính", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeOptionResponse(option)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Lấy thông tin tùy chọn thuộc tính thành công", responseData)
}

// CreateProductAttributeOption xử lý yêu cầu tạo tùy chọn thuộc tính mới
func (h *ProductAttributeOptionHandler) CreateProductAttributeOption(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Parse request body
	var req request.CreateProductAttributeOptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", err)
		return
	}

	// Tạo tùy chọn mới
	option, err := h.service.Create(uint(tenantID), &req)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Lỗi khi tạo tùy chọn thuộc tính", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeOptionResponse(option)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Tạo tùy chọn thuộc tính thành công", responseData)
}

// UpdateProductAttributeOption xử lý yêu cầu cập nhật tùy chọn thuộc tính
func (h *ProductAttributeOptionHandler) UpdateProductAttributeOption(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy ID tùy chọn từ tham số
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID không hợp lệ", err)
		return
	}

	// Parse request body
	var req request.UpdateProductAttributeOptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", err)
		return
	}

	// Cập nhật tùy chọn
	option, err := h.service.Update(uint(tenantID), uint(id), &req)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Lỗi khi cập nhật tùy chọn thuộc tính", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeOptionResponse(option)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Cập nhật tùy chọn thuộc tính thành công", responseData)
}

// DeleteProductAttributeOption xử lý yêu cầu xóa tùy chọn thuộc tính
func (h *ProductAttributeOptionHandler) DeleteProductAttributeOption(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy ID tùy chọn từ tham số
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID không hợp lệ", err)
		return
	}

	// Xóa tùy chọn
	err = h.service.Delete(uint(tenantID), uint(id))
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Lỗi khi xóa tùy chọn thuộc tính", err)
		return
	}

	// Trả về kết quả thành công
	pkgResponse.Success(c, http.StatusOK, "Xóa tùy chọn thuộc tính thành công", nil)
}

// GetAllProductAttributeOptions xử lý yêu cầu lấy tất cả tùy chọn thuộc tính
func (h *ProductAttributeOptionHandler) GetAllProductAttributeOptions(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy tất cả tùy chọn
	options, err := h.service.GetAll(uint(tenantID))
	if err != nil {
		pkgResponse.Error(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách tùy chọn thuộc tính", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeOptionResponseList(options)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Lấy tất cả tùy chọn thuộc tính thành công", responseData)
}

// GetProductAttributeOptionsByAttributeID xử lý yêu cầu lấy tùy chọn thuộc tính theo ID thuộc tính
func (h *ProductAttributeOptionHandler) GetProductAttributeOptionsByAttributeID(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy ID thuộc tính từ tham số
	attributeIDStr := c.Param("attribute_id")
	attributeID, err := strconv.ParseUint(attributeIDStr, 10, 64)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID thuộc tính không hợp lệ", err)
		return
	}

	// Lấy tùy chọn theo thuộc tính
	options, err := h.service.GetByAttributeID(uint(tenantID), uint(attributeID))
	if err != nil {
		pkgResponse.Error(c, http.StatusInternalServerError, "Lỗi khi lấy tùy chọn theo thuộc tính", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeOptionResponseList(options)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Lấy tùy chọn thuộc tính theo thuộc tính thành công", responseData)
}
