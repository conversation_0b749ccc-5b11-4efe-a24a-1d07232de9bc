package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/dto/response"
	"wnapi/modules/product/service"
	// "wnapi/internal/pkg/auth"
	pkgResponse "wnapi/internal/pkg/response"
)

// ProductAttributeHandler xử lý các yêu cầu liên quan đến thuộc tính sản phẩm
type ProductAttributeHandler struct {
	service    *service.ProductAttributeService
	// TODO: Add JWT service when auth package is available
	// jwtService *auth.JWTService
}

// NewProductAttributeHandler tạo một handler mới cho thuộc tính sản phẩm
func NewProductAttributeHandler(service *service.ProductAttributeService /* jwtService *auth.JWTService */) *ProductAttributeHandler {
	return &ProductAttributeHandler{
		service: service,
		// jwtService: jwtService,
	}
}

// ListProductAttributes xử lý yêu cầu lấy danh sách thuộc tính sản phẩm
func (h *ProductAttributeHandler) ListProductAttributes(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		pkgResponse.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		pkgResponse.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse query parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid limit format"}}
		pkgResponse.ErrorWithDetails(c, http.StatusBadRequest, "Invalid limit", "INVALID_LIMIT", details)
		return
	}

	// Get attributes list
	attributes, nextCursor, err := h.service.List(uint(tenantID), cursor, limit)
	if err != nil {
		handleError(c, err)
		return
	}

	// Convert to response
	responseData := response.NewProductAttributeResponseList(attributes)

	// Create pagination metadata
	meta := gin.H{
		"has_more": nextCursor != nil,
	}
	if nextCursor != nil {
		encodedCursor := nextCursor.Encode()
		meta["next_cursor"] = encodedCursor
	}

	// Return success response
	apiSuccessWithMeta(c, http.StatusOK, responseData, meta)
}

// GetProductAttribute xử lý yêu cầu lấy thông tin chi tiết thuộc tính sản phẩm
func (h *ProductAttributeHandler) GetProductAttribute(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		pkgResponse.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		pkgResponse.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get attribute ID from parameter
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid attribute ID format"}}
		pkgResponse.ErrorWithDetails(c, http.StatusBadRequest, "Invalid attribute ID", "INVALID_ID", details)
		return
	}

	// Get attribute info
	attribute, err := h.service.GetByID(uint(tenantID), uint(id))
	if err != nil {
		handleError(c, err)
		return
	}

	// Convert to response
	responseData := response.NewProductAttributeResponse(attribute)

	// Return success response
	apiSuccess(c, http.StatusOK, responseData)
}

// GetProductAttributeByCode xử lý yêu cầu lấy thông tin thuộc tính sản phẩm theo mã
func (h *ProductAttributeHandler) GetProductAttributeByCode(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy mã thuộc tính từ parameter
	code := c.Param("code")
	if code == "" {
		pkgResponse.Error(c, http.StatusBadRequest, "Mã không hợp lệ", fmt.Errorf("mã không được để trống"))
		return
	}

	// Lấy thông tin thuộc tính
	attribute, err := h.service.GetByCode(uint(tenantID), code)
	if err != nil {
		pkgResponse.Error(c, http.StatusNotFound, "Không tìm thấy thuộc tính sản phẩm", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeResponse(attribute)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Lấy thông tin thuộc tính sản phẩm thành công", responseData)
}

// CreateProductAttribute xử lý yêu cầu tạo thuộc tính sản phẩm mới
func (h *ProductAttributeHandler) CreateProductAttribute(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Parse request body
	var req request.CreateProductAttributeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", err)
		return
	}

	// Tạo thuộc tính mới
	attribute, err := h.service.Create(uint(tenantID), &req)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Lỗi khi tạo thuộc tính sản phẩm", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeResponse(attribute)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Tạo thuộc tính sản phẩm thành công", responseData)
}

// UpdateProductAttribute xử lý yêu cầu cập nhật thuộc tính sản phẩm
func (h *ProductAttributeHandler) UpdateProductAttribute(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy ID thuộc tính từ parameter
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID không hợp lệ", err)
		return
	}

	// Parse request body
	var req request.UpdateProductAttributeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", err)
		return
	}

	// Cập nhật thuộc tính
	attribute, err := h.service.Update(uint(tenantID), uint(id), &req)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Lỗi khi cập nhật thuộc tính sản phẩm", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeResponse(attribute)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Cập nhật thuộc tính sản phẩm thành công", responseData)
}

// DeleteProductAttribute xử lý yêu cầu xóa thuộc tính sản phẩm
func (h *ProductAttributeHandler) DeleteProductAttribute(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy ID thuộc tính từ parameter
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID không hợp lệ", err)
		return
	}

	// Xóa thuộc tính
	err = h.service.Delete(uint(tenantID), uint(id))
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "Lỗi khi xóa thuộc tính sản phẩm", err)
		return
	}

	// Trả về kết quả thành công
	pkgResponse.Success(c, http.StatusOK, "Xóa thuộc tính sản phẩm thành công", nil)
}

// GetAllProductAttributes xử lý yêu cầu lấy tất cả thuộc tính sản phẩm
func (h *ProductAttributeHandler) GetAllProductAttributes(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy tất cả thuộc tính
	attributes, err := h.service.GetAll(uint(tenantID))
	if err != nil {
		pkgResponse.Error(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách thuộc tính sản phẩm", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeResponseList(attributes)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Lấy tất cả thuộc tính sản phẩm thành công", responseData)
}

// GetProductAttributesByGroupID xử lý yêu cầu lấy thuộc tính sản phẩm theo ID nhóm
func (h *ProductAttributeHandler) GetProductAttributesByGroupID(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy ID nhóm từ parameter
	groupIDStr := c.Param("group_id")
	groupID, err := strconv.ParseUint(groupIDStr, 10, 64)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID nhóm không hợp lệ", err)
		return
	}

	// Lấy thuộc tính theo nhóm
	attributes, err := h.service.GetByGroupID(uint(tenantID), uint(groupID))
	if err != nil {
		pkgResponse.Error(c, http.StatusInternalServerError, "Lỗi khi lấy thuộc tính theo nhóm", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeResponseList(attributes)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Lấy thuộc tính sản phẩm theo nhóm thành công", responseData)
}

// GetConfigurableProductAttributes xử lý yêu cầu lấy thuộc tính sản phẩm có thể cấu hình
func (h *ProductAttributeHandler) GetConfigurableProductAttributes(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy thuộc tính có thể cấu hình
	attributes, err := h.service.GetConfigurable(uint(tenantID))
	if err != nil {
		pkgResponse.Error(c, http.StatusInternalServerError, "Lỗi khi lấy thuộc tính có thể cấu hình", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeResponseList(attributes)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Lấy thuộc tính sản phẩm có thể cấu hình thành công", responseData)
}

// GetFilterableProductAttributes xử lý yêu cầu lấy thuộc tính sản phẩm có thể lọc
func (h *ProductAttributeHandler) GetFilterableProductAttributes(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		pkgResponse.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		pkgResponse.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Lấy thuộc tính có thể lọc
	attributes, err := h.service.GetFilterable(uint(tenantID))
	if err != nil {
		pkgResponse.Error(c, http.StatusInternalServerError, "Lỗi khi lấy thuộc tính có thể lọc", err)
		return
	}

	// Chuyển đổi thành response
	responseData := response.NewProductAttributeResponseList(attributes)

	// Trả về kết quả
	pkgResponse.Success(c, http.StatusOK, "Lấy thuộc tính sản phẩm có thể lọc thành công", responseData)
}
