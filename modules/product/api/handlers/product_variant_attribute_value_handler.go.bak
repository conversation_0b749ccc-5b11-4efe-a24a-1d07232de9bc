package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/service"
	"wnapi/internal/pkg/response"
)

// ProductVariantAttributeValueHandler xử lý các yêu cầu liên quan đến giá trị thuộc tính của biến thể sản phẩm
type ProductVariantAttributeValueHandler struct {
	service service.ProductVariantAttributeValueService
}

// NewProductVariantAttributeValueHandler tạo một handler mới cho giá trị thuộc tính biến thể sản phẩm
func NewProductVariantAttributeValueHandler(service service.ProductVariantAttributeValueService) *ProductVariantAttributeValueHandler {
	return &ProductVariantAttributeValueHandler{
		service: service,
	}
}

// List trả về danh sách giá trị thuộc tính biến thể sản phẩm theo các điều kiện lọc
func (h *ProductVariantAttributeValueHandler) List(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	// Xử lý phân trang
	cursor := c.DefaultQuery("cursor", "")
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// Lấy các tham số lọc
	variantIDStr := c.DefaultQuery("variant_id", "")
	attributeIDStr := c.DefaultQuery("attribute_id", "")

	var variantID uint64
	var attributeID uint64

	if variantIDStr != "" {
		variantID, _ = strconv.ParseUint(variantIDStr, 10, 64)
	}

	if attributeIDStr != "" {
		attributeID, _ = strconv.ParseUint(attributeIDStr, 10, 64)
	}

	// Gọi service để lấy danh sách
	result, err := h.service.ListVariantAttributeValues(c.Request.Context(), tenantID, uint(variantID), uint(attributeID), cursor, limit)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Lỗi khi lấy danh sách giá trị thuộc tính biến thể", err)
		return
	}

	// Trả về kết quả
	response.SuccessWithMeta(c, http.StatusOK, "Lấy danh sách thành công", result.Values, gin.H{
		"next_cursor": result.Meta.NextCursor,
		"has_more":    result.Meta.HasMore,
	})
}

// GetByID trả về giá trị thuộc tính biến thể sản phẩm theo ID
func (h *ProductVariantAttributeValueHandler) GetByID(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	valueIDStr := c.Param("id")
	valueID, err := strconv.ParseUint(valueIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", err)
		return
	}

	value, err := h.service.GetVariantAttributeValue(c.Request.Context(), tenantID, uint(valueID))
	if err != nil {
		response.Error(c, http.StatusNotFound, "Không tìm thấy giá trị thuộc tính biến thể", err)
		return
	}

	response.Success(c, http.StatusOK, "Lấy thông tin thành công", value)
}

// Create tạo giá trị thuộc tính biến thể sản phẩm mới
func (h *ProductVariantAttributeValueHandler) Create(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	var req request.CreateVariantAttributeValueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", err)
		return
	}

	value, err := h.service.CreateVariantAttributeValue(c.Request.Context(), tenantID, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Lỗi khi tạo giá trị thuộc tính biến thể", err)
		return
	}

	response.Success(c, http.StatusCreated, "Tạo giá trị thuộc tính biến thể thành công", value)
}

// Update cập nhật giá trị thuộc tính biến thể sản phẩm
func (h *ProductVariantAttributeValueHandler) Update(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	valueIDStr := c.Param("id")
	valueID, err := strconv.ParseUint(valueIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", err)
		return
	}

	var req request.UpdateVariantAttributeValueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", err)
		return
	}

	value, err := h.service.UpdateVariantAttributeValue(c.Request.Context(), tenantID, uint(valueID), &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Lỗi khi cập nhật giá trị thuộc tính biến thể", err)
		return
	}

	response.Success(c, http.StatusOK, "Cập nhật giá trị thuộc tính biến thể thành công", value)
}

// Delete xóa giá trị thuộc tính biến thể sản phẩm
func (h *ProductVariantAttributeValueHandler) Delete(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	valueIDStr := c.Param("id")
	valueID, err := strconv.ParseUint(valueIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID không hợp lệ", err)
		return
	}

	err = h.service.DeleteVariantAttributeValue(c.Request.Context(), tenantID, uint(valueID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Lỗi khi xóa giá trị thuộc tính biến thể", err)
		return
	}

	response.Success(c, http.StatusOK, "Xóa giá trị thuộc tính biến thể thành công", gin.H{"message": "Xóa giá trị thuộc tính biến thể thành công"})
}

// GetByVariantID trả về tất cả giá trị thuộc tính của một biến thể sản phẩm
func (h *ProductVariantAttributeValueHandler) GetByVariantID(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	variantIDStr := c.Param("variant_id")
	variantID, err := strconv.ParseUint(variantIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID biến thể không hợp lệ", err)
		return
	}

	values, err := h.service.GetVariantAttributeValuesByVariantID(c.Request.Context(), tenantID, uint(variantID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Lỗi khi lấy giá trị thuộc tính biến thể", err)
		return
	}

	response.Success(c, http.StatusOK, "Lấy danh sách giá trị thuộc tính biến thể thành công", values)
}

// BatchCreate tạo nhiều giá trị thuộc tính biến thể cùng lúc
func (h *ProductVariantAttributeValueHandler) BatchCreate(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	var req request.BatchCreateVariantAttributeValueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", err)
		return
	}

	values, err := h.service.BatchCreateVariantAttributeValues(c.Request.Context(), tenantID, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Lỗi khi tạo giá trị thuộc tính biến thể", err)
		return
	}

	response.Success(c, http.StatusCreated, "Tạo giá trị thuộc tính biến thể thành công", values)
}

// DeleteByVariantID xóa tất cả giá trị thuộc tính của một biến thể sản phẩm
func (h *ProductVariantAttributeValueHandler) DeleteByVariantID(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không thể xác định tenant", err)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	variantIDStr := c.Param("variant_id")
	variantID, err := strconv.ParseUint(variantIDStr, 10, 64)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID biến thể không hợp lệ", err)
		return
	}

	err = h.service.DeleteVariantAttributeValuesByVariantID(c.Request.Context(), tenantID, uint(variantID))
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "Lỗi khi xóa giá trị thuộc tính biến thể", err)
		return
	}

	response.Success(c, http.StatusOK, "Xóa giá trị thuộc tính biến thể thành công", gin.H{"message": "Xóa giá trị thuộc tính biến thể thành công"})
}
