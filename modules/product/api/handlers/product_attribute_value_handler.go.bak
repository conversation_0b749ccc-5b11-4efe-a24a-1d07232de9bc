package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/service"
	// "wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
)

// ProductAttributeValueHandler xử lý các HTTP request liên quan đến giá trị thuộc tính sản phẩm
type ProductAttributeValueHandler struct {
	valueService service.ProductAttributeValueService
	jwtService   *// auth.JWTService
}

// NewProductAttributeValueHandler tạo một instance mới của ProductAttributeValueHandler
func NewProductAttributeValueHandler(valueService service.ProductAttributeValueService, jwtService *// auth.JWTService) *ProductAttributeValueHandler {
	return &ProductAttributeValueHandler{
		valueService: valueService,
		jwtService:   jwtService,
	}
}

// CreateProductAttributeValue xử lý tạo mới một giá trị thuộc tính sản phẩm
func (h *ProductAttributeValueHandler) CreateProductAttributeValue(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.CreateProductAttributeValueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Gọi service
	attributeValue, err := h.valueService.CreateProductAttributeValue(c.Request.Context(), tenantID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Trả về response thành công
	response.Success(c, http.StatusCreated, "Giá trị thuộc tính sản phẩm đã được tạo thành công", attributeValue)
}

// BatchCreateProductAttributeValues xử lý tạo mới nhiều giá trị thuộc tính sản phẩm
func (h *ProductAttributeValueHandler) BatchCreateProductAttributeValues(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.BatchCreateProductAttributeValueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Gọi service
	attributeValues, err := h.valueService.BatchCreateProductAttributeValues(c.Request.Context(), tenantID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Trả về response thành công
	response.Success(c, http.StatusCreated, "Giá trị thuộc tính sản phẩm đã được tạo thành công", attributeValues)
}

// GetProductAttributeValue xử lý lấy thông tin một giá trị thuộc tính sản phẩm theo ID
func (h *ProductAttributeValueHandler) GetProductAttributeValue(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Lấy value ID từ URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Value ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing value ID", "MISSING_ID", details)
		return
	}

	// Chuyển đổi sang int
	valueID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid value ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid value ID", "INVALID_ID", details)
		return
	}

	// Gọi service
	attributeValue, err := h.valueService.GetProductAttributeValue(c.Request.Context(), tenantID, valueID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Trả về response thành công
	response.Success(c, http.StatusOK, "Giá trị thuộc tính sản phẩm đã được lấy thành công", attributeValue)
}

// UpdateProductAttributeValue xử lý cập nhật một giá trị thuộc tính sản phẩm
func (h *ProductAttributeValueHandler) UpdateProductAttributeValue(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Lấy value ID từ URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Value ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing value ID", "MISSING_ID", details)
		return
	}

	// Chuyển đổi sang int
	valueID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid value ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid value ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.UpdateProductAttributeValueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Gọi service
	attributeValue, err := h.valueService.UpdateProductAttributeValue(c.Request.Context(), tenantID, valueID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Trả về response thành công
	response.Success(c, http.StatusOK, "Giá trị thuộc tính sản phẩm đã được cập nhật thành công", attributeValue)
}

// DeleteProductAttributeValue xử lý xóa một giá trị thuộc tính sản phẩm
func (h *ProductAttributeValueHandler) DeleteProductAttributeValue(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Lấy value ID từ URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Value ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing value ID", "MISSING_ID", details)
		return
	}

	// Chuyển đổi sang int
	valueID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid value ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid value ID", "INVALID_ID", details)
		return
	}

	// Gọi service
	err = h.valueService.DeleteProductAttributeValue(c.Request.Context(), tenantID, valueID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Trả về response thành công
	response.Success(c, http.StatusOK, "Giá trị thuộc tính sản phẩm đã được xóa thành công", nil)
}

// GetProductAttributeValuesByProductID xử lý lấy tất cả giá trị thuộc tính của một sản phẩm
func (h *ProductAttributeValueHandler) GetProductAttributeValuesByProductID(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Lấy product ID từ URL
	productIDStr := c.Param("product_id")
	if productIDStr == "" {
		details := []interface{}{map[string]string{"message": "Product ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing product ID", "MISSING_ID", details)
		return
	}

	// Chuyển đổi sang int
	productID, err := strconv.Atoi(productIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid product ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid product ID", "INVALID_ID", details)
		return
	}

	// Gọi service
	attributeValues, err := h.valueService.GetProductAttributeValuesByProductID(c.Request.Context(), tenantID, productID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Trả về response thành công
	response.Success(c, http.StatusOK, "Giá trị thuộc tính sản phẩm đã được lấy thành công", attributeValues)
}

// GetProductAttributeValuesByAttributeID xử lý lấy tất cả giá trị của một thuộc tính với phân trang
func (h *ProductAttributeValueHandler) GetProductAttributeValuesByAttributeID(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Lấy attribute ID từ URL
	attributeIDStr := c.Param("attribute_id")
	if attributeIDStr == "" {
		details := []interface{}{map[string]string{"message": "Attribute ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing attribute ID", "MISSING_ID", details)
		return
	}

	// Chuyển đổi sang int
	attributeID, err := strconv.Atoi(attributeIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid attribute ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid attribute ID", "INVALID_ID", details)
		return
	}

	// Parse query parameters
	var req request.ListProductAttributeValuesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Gọi service
	result, err := h.valueService.GetProductAttributeValuesByAttributeID(c.Request.Context(), tenantID, attributeID, req.Cursor, req.Limit)
	if err != nil {
		handleError(c, err)
		return
	}

	// Trả về response thành công
	meta := map[string]interface{}{
		"next_cursor": result.Meta.NextCursor,
		"has_more":    result.Meta.HasMore,
	}
	response.SuccessWithMeta(c, http.StatusOK, "Giá trị thuộc tính sản phẩm đã được lấy thành công", result.AttributeValues, meta)
}

// DeleteProductAttributeValuesByProductID xử lý xóa tất cả giá trị thuộc tính của một sản phẩm
func (h *ProductAttributeValueHandler) DeleteProductAttributeValuesByProductID(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Lấy product ID từ URL
	productIDStr := c.Param("product_id")
	if productIDStr == "" {
		details := []interface{}{map[string]string{"message": "Product ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing product ID", "MISSING_ID", details)
		return
	}

	// Chuyển đổi sang int
	productID, err := strconv.Atoi(productIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid product ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid product ID", "INVALID_ID", details)
		return
	}

	// Gọi service
	err = h.valueService.DeleteProductAttributeValuesByProductID(c.Request.Context(), tenantID, productID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Trả về response thành công
	response.Success(c, http.StatusOK, "Giá trị thuộc tính sản phẩm đã được xóa thành công", nil)
}
