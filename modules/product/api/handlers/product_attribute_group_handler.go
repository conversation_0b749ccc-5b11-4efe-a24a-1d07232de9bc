package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/response"
	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/service"
	// TODO: Add auth import when available
	// // "wnapi/internal/pkg/auth"
)

// ProductAttributeGroupHandler handles HTTP requests for product attribute groups
type ProductAttributeGroupHandler struct {
	groupService service.ProductAttributeGroupService
	// TODO: Add JWT service when auth package is available
	// jwtService   *// auth.JWTService
}

// NewProductAttributeGroupHandler creates a new product attribute group handler instance
func NewProductAttributeGroupHandler(groupService service.ProductAttributeGroupService /* jwtService *auth.JWTService */) *ProductAttributeGroupHandler {
	return &ProductAttributeGroupHandler{
		groupService: groupService,
		// jwtService:   jwtService,
	}
}

// CreateProductAttributeGroup handles the creation of a new product attribute group
func (h *ProductAttributeGroupHandler) CreateProductAttributeGroup(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.CreateProductAttributeGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	group, err := h.groupService.CreateProductAttributeGroup(c.Request.Context(), tenantID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	c.JSON(http.StatusCreated, gin.H{
		"status": gin.H{
			"code":    http.StatusCreated,
			"message": "Product attribute group created successfully",
			"success": true,
		},
		"data": group,
	})
}

// GetProductAttributeGroup handles retrieving a product attribute group by ID
func (h *ProductAttributeGroupHandler) GetProductAttributeGroup(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get group ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Group ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing group ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	groupID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid group ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid group ID", "INVALID_ID", details)
		return
	}

	// Call service
	group, err := h.groupService.GetProductAttributeGroup(c.Request.Context(), tenantID, groupID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, group)
}

// GetProductAttributeGroupByCode handles retrieving a product attribute group by code
func (h *ProductAttributeGroupHandler) GetProductAttributeGroupByCode(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get code from URL
	code := c.Param("code")
	if code == "" {
		details := []interface{}{map[string]string{"message": "Group code is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing group code", "MISSING_CODE", details)
		return
	}

	// Call service
	group, err := h.groupService.GetProductAttributeGroupByCode(c.Request.Context(), tenantID, code)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, group)
}

// UpdateProductAttributeGroup handles updating an existing product attribute group
func (h *ProductAttributeGroupHandler) UpdateProductAttributeGroup(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get group ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Group ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing group ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	groupID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid group ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid group ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.UpdateProductAttributeGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	group, err := h.groupService.UpdateProductAttributeGroup(c.Request.Context(), tenantID, groupID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, group)
}

// DeleteProductAttributeGroup handles deleting a product attribute group
func (h *ProductAttributeGroupHandler) DeleteProductAttributeGroup(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Get group ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Group ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing group ID", "MISSING_ID", details)
		return
	}

	// Convert to int
	groupID, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid group ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid group ID", "INVALID_ID", details)
		return
	}

	// Call service
	err = h.groupService.DeleteProductAttributeGroup(c.Request.Context(), tenantID, groupID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, nil)
}

// ListProductAttributeGroups handles listing product attribute groups with pagination
func (h *ProductAttributeGroupHandler) ListProductAttributeGroups(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Parse query parameters
	var req request.ListProductAttributeGroupRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Call service
	result, err := h.groupService.ListProductAttributeGroups(c.Request.Context(), tenantID, &req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	meta := gin.H{
		"next_cursor": result.Meta.NextCursor,
		"has_more":    result.Meta.HasMore,
	}
	apiSuccessWithMeta(c, http.StatusOK, result.AttributeGroups, meta)
}

// GetAllProductAttributeGroups handles retrieving all product attribute groups for a tenant
func (h *ProductAttributeGroupHandler) GetAllProductAttributeGroups(c *gin.Context) {
	// Get tenant ID from context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Unauthorized", "UNAUTHORIZED", details)
		return
	}

	tenantID, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid tenant ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid tenant ID", "INVALID_ID", details)
		return
	}

	// Call service
	groups, err := h.groupService.GetAllProductAttributeGroups(c.Request.Context(), tenantID)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, groups)
}
