package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/service"
	"wnapi/internal/pkg/response"
)

// ProductVariantHandler xử lý HTTP requests liên quan đến biến thể sản phẩm
type ProductVariantHandler struct {
	variantService service.ProductVariantService
}

// NewProductVariantHandler tạo mới ProductVariantHandler
func NewProductVariantHandler(variantService service.ProductVariantService) *ProductVariantHandler {
	return &ProductVariantHandler{
		variantService: variantService,
	}
}

// Create xử lý yêu cầu tạo mới biến thể sản phẩm
func (h *ProductVariantHandler) Create(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", err)
		return
	}

	// Parse request body
	var req request.CreateProductVariantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, fmt.Sprintf("Dữ liệu không hợp lệ: %s", err.Error()), err)
		return
	}

	// Gọi service để tạo biến thể
	tenantIDInt, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	variant, err := h.variantService.CreateVariant(c.Request.Context(), tenantIDInt, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, fmt.Sprintf("Lỗi khi tạo biến thể sản phẩm: %s", err.Error()), err)
		return
	}

	// Return success response
	c.JSON(http.StatusCreated, gin.H{
		"status": gin.H{
			"code":    http.StatusCreated,
			"message": "Product variant created successfully",
			"success": true,
		},
		"data": variant,
	})
}

// BatchCreate xử lý yêu cầu tạo nhiều biến thể sản phẩm
func (h *ProductVariantHandler) BatchCreate(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", err)
		return
	}

	// Parse request body
	var req request.BatchCreateProductVariantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, fmt.Sprintf("Dữ liệu không hợp lệ: %s", err.Error()), err)
		return
	}

	// Gọi service để tạo nhiều biến thể
	tenantIDInt, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	variants, err := h.variantService.BatchCreateVariants(c.Request.Context(), tenantIDInt, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, fmt.Sprintf("Lỗi khi tạo biến thể sản phẩm: %s", err.Error()), err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, variants)
}

// Get xử lý yêu cầu lấy chi tiết biến thể sản phẩm
func (h *ProductVariantHandler) Get(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", err)
		return
	}

	// Lấy variant ID từ path parameter
	variantID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID biến thể không hợp lệ", err)
		return
	}

	// Gọi service để lấy chi tiết biến thể
	tenantIDInt, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	variant, err := h.variantService.GetVariant(c.Request.Context(), tenantIDInt, variantID)
	if err != nil {
		response.Error(c, http.StatusNotFound, fmt.Sprintf("Không tìm thấy biến thể sản phẩm: %s", err.Error()), err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, variant)
}

// Update xử lý yêu cầu cập nhật biến thể sản phẩm
func (h *ProductVariantHandler) Update(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", err)
		return
	}

	// Lấy variant ID từ path parameter
	variantID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID biến thể không hợp lệ", err)
		return
	}

	// Parse request body
	var req request.UpdateProductVariantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, fmt.Sprintf("Dữ liệu không hợp lệ: %s", err.Error()), err)
		return
	}

	// Gọi service để cập nhật biến thể
	tenantIDInt, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	variant, err := h.variantService.UpdateVariant(c.Request.Context(), tenantIDInt, variantID, &req)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, fmt.Sprintf("Lỗi khi cập nhật biến thể sản phẩm: %s", err.Error()), err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, variant)
}

// Delete xử lý yêu cầu xóa biến thể sản phẩm
func (h *ProductVariantHandler) Delete(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", err)
		return
	}

	// Lấy variant ID từ path parameter
	variantID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID biến thể không hợp lệ", err)
		return
	}

	// Gọi service để xóa biến thể
	tenantIDInt, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	err = h.variantService.DeleteVariant(c.Request.Context(), tenantIDInt, variantID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, fmt.Sprintf("Lỗi khi xóa biến thể sản phẩm: %s", err.Error()), err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, nil)
}

// ListByProduct xử lý yêu cầu lấy danh sách biến thể theo sản phẩm
func (h *ProductVariantHandler) ListByProduct(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", err)
		return
	}

	// Lấy product ID từ path parameter
	productID, err := strconv.Atoi(c.Param("productId"))
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID sản phẩm không hợp lệ", err)
		return
	}

	// Gọi service để lấy danh sách biến thể
	tenantIDInt, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	variants, err := h.variantService.GetVariantsByProductID(c.Request.Context(), tenantIDInt, productID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, fmt.Sprintf("Lỗi khi lấy danh sách biến thể sản phẩm: %s", err.Error()), err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, variants)
}

// List xử lý yêu cầu lấy danh sách tất cả biến thể có phân trang
func (h *ProductVariantHandler) List(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", err)
		return
	}

	// Lấy các tham số phân trang
	var req request.ListProductVariantsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.Error(c, http.StatusBadRequest, fmt.Sprintf("Tham số không hợp lệ: %s", err.Error()), err)
		return
	}

	// Gọi service để lấy danh sách biến thể
	tenantIDInt, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	variantsList, err := h.variantService.ListVariants(c.Request.Context(), tenantIDInt, req.Cursor, req.Limit)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, fmt.Sprintf("Lỗi khi lấy danh sách biến thể sản phẩm: %s", err.Error()), err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, variantsList)
}

// DeleteByProduct xử lý yêu cầu xóa tất cả biến thể của một sản phẩm
func (h *ProductVariantHandler) DeleteByProduct(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantIDStr, err := getTenantIDStringFromContext(c)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Không tìm thấy thông tin tenant", err)
		return
	}

	// Lấy product ID từ path parameter
	productID, err := strconv.Atoi(c.Param("productId"))
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID sản phẩm không hợp lệ", err)
		return
	}

	// Gọi service để xóa tất cả biến thể của sản phẩm
	tenantIDInt, err := strconv.Atoi(tenantIDStr)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "ID tenant không hợp lệ", err)
		return
	}

	err = h.variantService.DeleteVariantsByProductID(c.Request.Context(), tenantIDInt, productID)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, fmt.Sprintf("Lỗi khi xóa biến thể sản phẩm: %s", err.Error()), err)
		return
	}

	// Return success response
	apiSuccess(c, http.StatusOK, nil)
}
