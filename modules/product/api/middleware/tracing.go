package middleware

import (
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

// TracingMiddleware adds OpenTelemetry tracing to all API requests
func TracingMiddleware() gin.HandlerFunc {
	tracer := otel.Tracer("ecom-api")

	return func(c *gin.Context) {
		// Extract the request path and method
		path := c.Request.URL.Path
		method := c.Request.Method

		// Start a new span for this request
		ctx, span := tracer.Start(
			c.Request.Context(),
			"ecom-api-request",
			trace.WithAttributes(
				attribute.String("http.method", method),
				attribute.String("http.url", path),
			),
		)
		defer span.End()

		// Set the context with the span
		c.Request = c.Request.WithContext(ctx)

		// Process the request
		c.Next()

		// Add response status code to span
		span.SetAttributes(attribute.Int("http.status_code", c.Writer.Status()))
	}
}
