package repository

import (
	"context"

	"wnapi/modules/product/models"
	"wnapi/internal/pkg/pagination"
)

// ProductAttributeOptionRepository định ngh<PERSON>a interface cho thao tác với bảng ecom_product_attribute_options
type ProductAttributeOptionRepository interface {
	// Cá<PERSON> phương thức CRUD cơ bản
	Create(ctx context.Context, option *models.ProductAttributeOption) error
	GetByID(ctx context.Context, tenantID, optionID int) (*models.ProductAttributeOption, error)
	Update(ctx context.Context, option *models.ProductAttributeOption) error
	Delete(ctx context.Context, tenantID, optionID int) error

	// Cá<PERSON> phương thức liệt kê và tìm kiếm
	List(ctx context.Context, tenantID int, cursor string, limit int, orderField, orderDirection string) ([]*models.ProductAttributeOption, *pagination.CursorInfo, error)
	ListByAttributeID(ctx context.Context, tenantID, attributeID int) ([]*models.ProductAttributeOption, error)
	GetByValue(ctx context.Context, tenantID, attributeID int, value string) (*models.ProductAttributeOption, error)
	DeleteByAttributeID(ctx context.Context, tenantID, attributeID int) error
}
