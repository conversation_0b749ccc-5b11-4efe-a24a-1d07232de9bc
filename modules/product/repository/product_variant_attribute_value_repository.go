package repository

import (
	"context"

	"wnapi/modules/product/models"
)

// ProductVariantAttributeValueRepository định <PERSON>h<PERSON>a interface cho thao tác với bảng ecom_product_variant_attribute_values
type ProductVariantAttributeValueRepository interface {
	// <PERSON><PERSON><PERSON> phương thức CRUD cơ bản
	Create(ctx context.Context, value *models.ProductVariantAttributeValue) error
	CreateBatch(ctx context.Context, values []*models.ProductVariantAttributeValue) error
	GetByID(ctx context.Context, tenantID, valueID int) (*models.ProductVariantAttributeValue, error)
	Update(ctx context.Context, value *models.ProductVariantAttributeValue) error
	Delete(ctx context.Context, tenantID, valueID int) error

	// Các phương thức truy vấn nâng cao
	ListByVariantID(ctx context.Context, tenantID, variantID int) ([]*models.ProductVariantAttributeValue, error)
	GetByVariantAndAttributeID(ctx context.Context, tenantID, variantID int, attributeID uint) (*models.ProductVariantAttributeValue, error)
	ListByAttributeID(ctx context.Context, tenantID, attributeID int) ([]*models.ProductVariantAttributeValue, error)

	// Các phương thức xóa nâng cao
	DeleteByVariantID(ctx context.Context, tenantID, variantID int) error
	DeleteByAttributeID(ctx context.Context, tenantID, attributeID int) error
}
