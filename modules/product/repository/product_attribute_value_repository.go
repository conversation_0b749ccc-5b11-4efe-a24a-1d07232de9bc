package repository

import (
	"context"

	"wnapi/modules/product/models"
)

// ProductAttributeValueRepository định ngh<PERSON>a interface cho thao tác với bảng ecom_product_attribute_values
type ProductAttributeValueRepository interface {
	// <PERSON><PERSON><PERSON> phương thức CRUD cơ bản
	Create(ctx context.Context, value *models.ProductAttributeValue) error
	CreateBatch(ctx context.Context, values []*models.ProductAttributeValue) error
	GetByID(ctx context.Context, tenantID, valueID int) (*models.ProductAttributeValue, error)
	Update(ctx context.Context, value *models.ProductAttributeValue) error
	Delete(ctx context.Context, tenantID, valueID int) error

	// Các phương thức truy vấn nâng cao
	GetByProductID(ctx context.Context, tenantID, productID int) ([]*models.ProductAttributeValue, error)
	GetByProductIDAndAttributeID(ctx context.Context, tenantID, productID int, attributeID uint) (*models.ProductAttributeValue, error)
	GetByAttributeIDAndOptionID(ctx context.Context, tenantID int, attributeID, optionID uint) ([]*models.ProductAttributeValue, error)
	GetByAttributeID(ctx context.Context, tenantID, attributeID int) ([]*models.ProductAttributeValue, error)

	// Các phương thức xóa nâng cao
	DeleteByProductID(ctx context.Context, tenantID, productID int) error
	DeleteByAttributeID(ctx context.Context, tenantID int, attributeID uint) error
}
