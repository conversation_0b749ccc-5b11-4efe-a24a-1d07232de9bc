package repository

import (
	"context"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/models"
)

// CategoryRepository defines the interface for category-related operations
type CategoryRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, category *models.Category) error
	GetByID(ctx context.Context, tenantID, categoryID int) (*models.Category, error)
	GetBySlug(ctx context.Context, tenantID int, slug string) (*models.Category, error)
	Update(ctx context.Context, category *models.Category) error
	Delete(ctx context.Context, tenantID, categoryID int) error

	// Tree operations
	GetTree(ctx context.Context, tenantID int) ([]*models.Category, error)
	GetSubtree(ctx context.Context, tenantID, categoryID int) ([]*models.Category, error)
	GetAncestors(ctx context.Context, tenantID, categoryID int) ([]*models.Category, error)
	GetChildren(ctx context.Context, tenantID, categoryID int) ([]*models.Category, error)
	RebuildTree(ctx context.Context, tenantID int) error

	// Movement operations
	MoveNode(ctx context.Context, tenantID, nodeIDToMove int, newParentID *int, targetPosition int) error
	ChangeParent(ctx context.Context, tenantID, categoryID, newParentID int) error

	// Procedure operations
	MoveNodeProcedure(ctx context.Context, tenantID, categoryID, newParentID int, position int) error
	UpdatePositionProcedure(ctx context.Context, tenantID, categoryID int, position int) error
	MoveNodeSiblingProcedure(ctx context.Context, tenantID, categoryID, targetID int) error
	MoveNodeRelativeProcedure(ctx context.Context, tenantID, categoryID, targetID int, mode string) error

	// List with cursor pagination
	List(ctx context.Context, tenantID int, req request.ListCategoryRequest) ([]*models.Category, string, bool, error)

	// Statistics
	GetCategoriesWithPostCount(ctx context.Context, tenantID int) ([]*models.Category, error)
}
