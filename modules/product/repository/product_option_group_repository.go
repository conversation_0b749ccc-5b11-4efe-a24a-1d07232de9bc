package repository

import (
	"context"

	"wnapi/modules/product/models"
	"wnapi/internal/pkg/pagination"
)

// ProductOptionGroupRepository định nghĩa interface cho thao tác với bảng ecom_product_option_groups
type ProductOptionGroupRepository interface {
	// <PERSON><PERSON><PERSON> phương thức CRUD cơ bản
	Create(ctx context.Context, group *models.ProductOptionGroup) error
	GetByID(ctx context.Context, tenantID, groupID uint) (*models.ProductOptionGroup, error)
	Update(ctx context.Context, group *models.ProductOptionGroup) error
	Delete(ctx context.Context, tenantID, groupID uint) error

	// Cá<PERSON> phương thức liệt kê
	List(ctx context.Context, tenantID uint, cursor string, limit int, orderField, orderDirection string) ([]*models.ProductOptionGroup, *pagination.CursorInfo, error)
	GetAll(ctx context.Context, tenantID uint) ([]*models.ProductOptionGroup, error)
}
