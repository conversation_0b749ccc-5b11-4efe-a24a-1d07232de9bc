package repository

import (
	"context"

	"wnapi/modules/product/models"
	"wnapi/internal/pkg/pagination"
)

// ProductOptionValueRepository định nghĩa interface cho thao tác với bảng ecom_product_option_values
type ProductOptionValueRepository interface {
	// <PERSON><PERSON><PERSON> phương thức CRUD cơ bản
	Create(ctx context.Context, value *models.ProductOptionValue) error
	GetByID(ctx context.Context, tenantID, valueID uint) (*models.ProductOptionValue, error)
	Update(ctx context.Context, value *models.ProductOptionValue) error
	Delete(ctx context.Context, tenantID, valueID uint) error

	// Cá<PERSON> phương thức liệt kê và tìm kiếm
	List(ctx context.Context, tenantID uint, cursor string, limit int, orderField, orderDirection string) ([]*models.ProductOptionValue, *pagination.CursorInfo, error)
	ListByGroupID(ctx context.Context, tenantID, groupID uint) ([]*models.ProductOptionValue, error)
	GetDefaultByGroupID(ctx context.Context, tenantID, groupID uint) (*models.ProductOptionValue, error)
}
