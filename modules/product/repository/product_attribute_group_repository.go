package repository

import (
	"context"

	"wnapi/modules/product/models"
)

// ProductAttributeGroupRepository defines the interface for product attribute group-related operations
type ProductAttributeGroupRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, group *models.ProductAttributeGroup) error
	GetByID(ctx context.Context, tenantID, groupID int) (*models.ProductAttributeGroup, error)
	GetByCode(ctx context.Context, tenantID int, code string) (*models.ProductAttributeGroup, error)
	Update(ctx context.Context, group *models.ProductAttributeGroup) error
	Delete(ctx context.Context, tenantID, groupID int) error
	
	// List operations
	List(ctx context.Context, tenantID int, cursor string, limit int) ([]*models.ProductAttributeGroup, string, bool, error)
	GetAll(ctx context.Context, tenantID int) ([]*models.ProductAttributeGroup, error)
}
