package repository

import (
	"context"

	"wnapi/modules/product/models"
	"wnapi/internal/pkg/pagination"
)

// ProductOptionsLinkRepository định nghĩa interface cho thao tác với bảng ecom_product_options_link
type ProductOptionsLinkRepository interface {
	// <PERSON><PERSON><PERSON> phương thức CRUD cơ bản
	Create(ctx context.Context, link *models.ProductOptionsLink) error
	GetByID(ctx context.Context, tenantID, productOptionID uint) (*models.ProductOptionsLink, error)
	Update(ctx context.Context, link *models.ProductOptionsLink) error
	Delete(ctx context.Context, tenantID, productOptionID uint) error

	// Cá<PERSON> phương thức liệt kê và tìm kiếm
	List(ctx context.Context, tenantID uint, cursor string, limit int, orderField, orderDirection string) ([]*models.ProductOptionsLink, *pagination.CursorInfo, error)
	GetByProductID(ctx context.Context, tenantID, productID uint) ([]*models.ProductOptionsLink, error)
	GetByProductAndGroup(ctx context.Context, tenantID, productID, groupID uint) (*models.ProductOptionsLink, error)
	DeleteByProductID(ctx context.Context, tenantID, productID uint) error
}
