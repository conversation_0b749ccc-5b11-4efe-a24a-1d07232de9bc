package mysql

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
	"wnapi/internal/pkg/pagination"
)

// ProductVariantRepository implements repository.ProductVariantRepository with MySQL
type ProductVariantRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

func NewProductVariantRepository(db *sqlx.DB, gormDB *gorm.DB) repository.ProductVariantRepository {
	return &ProductVariantRepository{
		db:     db,
		gormDB: gormDB,
	}
}

func (r *ProductVariantRepository) Create(ctx context.Context, variant *models.ProductVariant) error {
	now := time.Now()
	if variant.CreatedAt.IsZero() {
		variant.CreatedAt = now
	}
	if variant.UpdatedAt.IsZero() {
		variant.UpdatedAt = now
	}
	result := r.gormDB.WithContext(ctx).Create(variant)
	if result.Error != nil {
		return fmt.Errorf("failed to create product variant: %w", result.Error)
	}
	return nil
}

func (r *ProductVariantRepository) CreateBatch(ctx context.Context, variants []*models.ProductVariant) error {
	if len(variants) == 0 {
		return nil
	}
	now := time.Now()
	for _, v := range variants {
		if v.CreatedAt.IsZero() {
			v.CreatedAt = now
		}
		if v.UpdatedAt.IsZero() {
			v.UpdatedAt = now
		}
	}
	tx := r.gormDB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	result := tx.Create(variants)
	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create batch product variants: %w", result.Error)
	}
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	return nil
}

func (r *ProductVariantRepository) GetByID(ctx context.Context, tenantID, variantID int) (*models.ProductVariant, error) {
	var variant models.ProductVariant
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND variant_id = ?", tenantID, variantID).First(&variant)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product variant: %w", result.Error)
	}
	return &variant, nil
}

func (r *ProductVariantRepository) Update(ctx context.Context, variant *models.ProductVariant) error {
	variant.UpdatedAt = time.Now()
	result := r.gormDB.WithContext(ctx).Save(variant)
	if result.Error != nil {
		return fmt.Errorf("failed to update product variant: %w", result.Error)
	}
	return nil
}

func (r *ProductVariantRepository) Delete(ctx context.Context, tenantID, variantID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND variant_id = ?", tenantID, variantID).Delete(&models.ProductVariant{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product variant: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return ErrNotFound
	}
	return nil
}

func (r *ProductVariantRepository) ListByProductID(ctx context.Context, tenantID, productID int) ([]*models.ProductVariant, error) {
	var variants []*models.ProductVariant
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND product_id = ?", tenantID, productID).Order("variant_id ASC").Find(&variants)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to list product variants by product: %w", result.Error)
	}
	return variants, nil
}

func (r *ProductVariantRepository) DeleteByProductID(ctx context.Context, tenantID, productID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND product_id = ?", tenantID, productID).Delete(&models.ProductVariant{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product variants by product: %w", result.Error)
	}
	return nil
}

func (r *ProductVariantRepository) ListByTenantID(ctx context.Context, tenantID int, cursor string, limit int) ([]*models.ProductVariant, string, bool, error) {
	if limit <= 0 {
		limit = 10
	}
	query := r.gormDB.WithContext(ctx).Model(&models.ProductVariant{}).Where("tenant_id = ?", tenantID)
	if cursor != "" {
		id, err := strconv.Atoi(cursor)
		if err == nil && id > 0 {
			query = query.Where("variant_id > ?", id)
		}
	}
	query = query.Order("variant_id ASC").Limit(limit + 1)
	var variants []*models.ProductVariant
	if err := query.Find(&variants).Error; err != nil {
		return nil, "", false, fmt.Errorf("failed to list product variants with pagination: %w", err)
	}
	hasMore := false
	nextCursor := ""
	if len(variants) > limit {
		hasMore = true
		variants = variants[:limit]
	}
	if hasMore && len(variants) > 0 {
		nextCursor = fmt.Sprintf("%d", variants[len(variants)-1].VariantID)
	}
	return variants, nextCursor, hasMore, nil
}

// GetBySKU lấy biến thể sản phẩm theo SKU
func (r *ProductVariantRepository) GetBySKU(ctx context.Context, tenantID int, sku string) (*models.ProductVariant, error) {
	var variant models.ProductVariant
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND sku = ?", tenantID, sku).First(&variant)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product variant by SKU: %w", result.Error)
	}
	return &variant, nil
}

// List lấy danh sách biến thể sản phẩm với phân trang cursor
func (r *ProductVariantRepository) List(ctx context.Context, tenantID int, cursor string, limit int, orderField, orderDirection string) ([]*models.ProductVariant, *pagination.CursorInfo, error) {
	if limit <= 0 {
		limit = 10
	}

	if orderField == "" {
		orderField = "variant_id"
	}

	if orderDirection == "" {
		orderDirection = "ASC"
	}

	query := r.gormDB.WithContext(ctx).Model(&models.ProductVariant{}).Where("tenant_id = ?", tenantID)

	if cursor != "" {
		id, err := strconv.Atoi(cursor)
		if err == nil && id > 0 {
			if orderDirection == "ASC" {
				query = query.Where(fmt.Sprintf("%s > ?", orderField), id)
			} else {
				query = query.Where(fmt.Sprintf("%s < ?", orderField), id)
			}
		}
	}

	query = query.Order(fmt.Sprintf("%s %s", orderField, orderDirection)).Limit(limit + 1)

	var variants []*models.ProductVariant
	if err := query.Find(&variants).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to list product variants: %w", err)
	}

	hasMore := false
	nextCursor := ""
	if len(variants) > limit {
		hasMore = true
		variants = variants[:limit]
	}

	if hasMore && len(variants) > 0 {
		var lastID int
		if orderField == "variant_id" {
			lastID = int(variants[len(variants)-1].VariantID)
		} else {
			// Nếu order field không phải là variant_id, lấy variant_id của bản ghi cuối cùng
			lastID = int(variants[len(variants)-1].VariantID)
		}
		nextCursor = fmt.Sprintf("%d", lastID)
	}

	cursorInfo := &pagination.CursorInfo{
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}

	return variants, cursorInfo, nil
}
