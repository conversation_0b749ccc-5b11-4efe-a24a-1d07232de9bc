package mysql

import (
	"context"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/modules/product/dto/common"
	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
)

// ProductRepository implements the repository.ProductRepository interface with MySQL
type ProductRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewProductRepository creates a new ProductRepository instance
func NewProductRepository(db *sqlx.DB, gormDB *gorm.DB) repository.ProductRepository {
	return &ProductRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create adds a new product to the database
func (r *ProductRepository) Create(ctx context.Context, product *models.Product) error {
	// Set timestamps if not already set
	now := time.Now()
	if product.CreatedAt.IsZero() {
		product.CreatedAt = now
	}
	if product.UpdatedAt.IsZero() {
		product.UpdatedAt = now
	}

	// Create product using GORM
	result := r.gormDB.WithContext(ctx).Create(product)
	if result.Error != nil {
		return fmt.Errorf("failed to create product: %w", result.Error)
	}

	return nil
}

// GetByID retrieves a product by its ID
func (r *ProductRepository) GetByID(ctx context.Context, tenantID, productID int) (*models.Product, error) {
	var product models.Product
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND product_id = ?", tenantID, productID).First(&product)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product: %w", result.Error)
	}

	return &product, nil
}

// Update updates an existing product
func (r *ProductRepository) Update(ctx context.Context, product *models.Product) error {
	// Set updated timestamp
	product.UpdatedAt = time.Now()

	// Update product using GORM
	result := r.gormDB.WithContext(ctx).Save(product)
	if result.Error != nil {
		return fmt.Errorf("failed to update product: %w", result.Error)
	}

	return nil
}

// Delete removes a product from the database
func (r *ProductRepository) Delete(ctx context.Context, tenantID, productID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND product_id = ?", tenantID, productID).Delete(&models.Product{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrNotFound
	}

	return nil
}

// List retrieves a list of products with pagination
func (r *ProductRepository) List(ctx context.Context, tenantID int, cursor string, limit int, filters map[string]interface{}) ([]*models.Product, string, bool, error) {
	if limit <= 0 {
		limit = 10 // Default limit
	}

	// Start query
	query := r.gormDB.WithContext(ctx).Model(&models.Product{}).Where("tenant_id = ?", tenantID)

	// Apply filters
	for key, value := range filters {
		if value != nil && value != "" {
			query = query.Where(fmt.Sprintf("%s = ?", key), value)
		}
	}

	// Apply cursor-based pagination
	if cursor != "" {
		query = query.Where("product_id > ?", cursor)
	}

	// Order by ID and limit results
	query = query.Order("product_id ASC").Limit(limit + 1) // +1 to check if there are more results

	// Execute query
	var products []*models.Product
	if err := query.Find(&products).Error; err != nil {
		return nil, "", false, fmt.Errorf("failed to list products: %w", err)
	}

	// Check if there are more results
	hasMore := false
	nextCursor := ""
	if len(products) > limit {
		hasMore = true
		products = products[:limit]
	}

	// Set next cursor if there are more results
	if hasMore && len(products) > 0 {
		nextCursor = fmt.Sprintf("%d", products[len(products)-1].ProductID)
	}

	return products, nextCursor, hasMore, nil
}

// Search searches for products by keyword
func (r *ProductRepository) Search(ctx context.Context, tenantID int, keyword string, cursor string, limit int, filters map[string]interface{}) ([]*models.Product, string, bool, error) {
	if limit <= 0 {
		limit = 10 // Default limit
	}

	// Start query
	query := r.gormDB.WithContext(ctx).Model(&models.Product{}).Where("tenant_id = ?", tenantID)

	// Apply keyword search
	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ? OR product_code LIKE ?",
			"%"+keyword+"%", "%"+keyword+"%", "%"+keyword+"%")
	}

	// Apply filters
	for key, value := range filters {
		if value != nil && value != "" {
			query = query.Where(fmt.Sprintf("%s = ?", key), value)
		}
	}

	// Apply cursor-based pagination
	if cursor != "" {
		query = query.Where("product_id > ?", cursor)
	}

	// Order by ID and limit results
	query = query.Order("product_id ASC").Limit(limit + 1) // +1 to check if there are more results

	// Execute query
	var products []*models.Product
	if err := query.Find(&products).Error; err != nil {
		return nil, "", false, fmt.Errorf("failed to search products: %w", err)
	}

	// Check if there are more results
	hasMore := false
	nextCursor := ""
	if len(products) > limit {
		hasMore = true
		products = products[:limit]
	}

	// Set next cursor if there are more results
	if hasMore && len(products) > 0 {
		nextCursor = fmt.Sprintf("%d", products[len(products)-1].ProductID)
	}

	return products, nextCursor, hasMore, nil
}

// GetAll retrieves all products for a tenant
func (r *ProductRepository) GetAll(ctx context.Context, tenantID int) ([]*models.Product, error) {
	var products []*models.Product
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ?", tenantID).Find(&products)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get all products: %w", result.Error)
	}

	return products, nil
}

// GetProductCategories retrieves all categories that have products
func (r *ProductRepository) GetProductCategories(ctx context.Context, tenantID int) ([]map[string]interface{}, error) {
	// Construct the query to get categories with product counts
	query := `
		SELECT c.category_id, c.name
		FROM ecom_categories c
		WHERE c.tenant_id = ?
		ORDER BY c.name ASC
	`

	// Execute the query
	rows, err := r.db.QueryxContext(ctx, query, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product categories: %w", err)
	}
	defer rows.Close()

	// Process the result
	categories := []map[string]interface{}{}
	for rows.Next() {
		var category struct {
			CategoryID int    `db:"category_id"`
			Name       string `db:"name"`
		}
		if err := rows.StructScan(&category); err != nil {
			return nil, fmt.Errorf("failed to scan category: %w", err)
		}

		categories = append(categories, map[string]interface{}{
			"category_id": category.CategoryID,
			"name":        category.Name,
		})
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating categories: %w", err)
	}

	return categories, nil
}

// GetProductAttributesAndVariants lấy thông tin attributes và variants của sản phẩm
func (r *ProductRepository) GetProductAttributesAndVariants(ctx context.Context, tenantID, productID int) ([]common.AttributeDTO, []common.VariantDTO, bool, error) {
	var attributes []common.AttributeDTO
	var variants []common.VariantDTO
	var manageVariantPricing bool

	// Kiểm tra sản phẩm có tồn tại không
	product, err := r.GetByID(ctx, tenantID, productID)
	if err != nil {
		return nil, nil, false, fmt.Errorf("failed to get product: %w", err)
	}

	// Kiểm tra sản phẩm có phải là CONFIGURABLE không
	if product.ProductType != "CONFIGURABLE" {
		return attributes, variants, false, nil
	}

	// Lấy thông tin configurable attributes của sản phẩm
	var configurableAttrs []*models.ProductConfigurableAttribute
	if err := r.gormDB.WithContext(ctx).
		Where("tenant_id = ? AND product_id = ?", tenantID, productID).
		Find(&configurableAttrs).Error; err != nil {
		return nil, nil, false, fmt.Errorf("failed to get configurable attributes: %w", err)
	}

	// Lấy thông tin chi tiết của các attribute và options
	for _, confAttr := range configurableAttrs {
		var attribute models.ProductAttribute
		if err := r.gormDB.WithContext(ctx).
			Where("tenant_id = ? AND attribute_id = ?", tenantID, confAttr.AttributeID).
			First(&attribute).Error; err != nil {
			continue // Skip if attribute not found
		}

		// Lấy các options của attribute
		var options []*models.ProductAttributeOption
		if err := r.gormDB.WithContext(ctx).
			Where("tenant_id = ? AND attribute_id = ?", tenantID, attribute.AttributeID).
			Order("display_order ASC").
			Find(&options).Error; err != nil {
			continue // Skip if options not found
		}

		// Map thành AttributeDTO
		values := make([]string, 0, len(options))
		for _, option := range options {
			values = append(values, option.Value)
		}

		attributes = append(attributes, common.AttributeDTO{
			Name:           attribute.Name,
			Values:         values,
			IsConfigurable: true,
			IsFilterable:   attribute.IsFilterable,
		})
	}

	// Lấy thông tin các variants của sản phẩm
	var productVariants []*models.ProductVariant
	if err := r.gormDB.WithContext(ctx).
		Where("tenant_id = ? AND product_id = ?", tenantID, productID).
		Find(&productVariants).Error; err != nil {
		return attributes, nil, false, fmt.Errorf("failed to get product variants: %w", err)
	}

	// Nếu có ít nhất một variant có price khác base_price thì set manageVariantPricing = true
	for _, variant := range productVariants {
		if variant.Price != nil && *variant.Price != product.BasePrice {
			manageVariantPricing = true
			break
		}
	}

	// Map thành VariantDTO
	for _, variant := range productVariants {
		// Lấy thuộc tính của variant
		var variantAttrs []*models.ProductVariantAttributeValue
		if err := r.gormDB.WithContext(ctx).
			Where("tenant_id = ? AND variant_id = ?", tenantID, variant.VariantID).
			Find(&variantAttrs).Error; err != nil {
			continue
		}

		// Tạo map attribute
		variantAttrMap := make(map[string]string)
		for _, varAttr := range variantAttrs {
			// Lấy tên attribute
			var attr models.ProductAttribute
			if err := r.gormDB.WithContext(ctx).
				Where("tenant_id = ? AND attribute_id = ?", tenantID, varAttr.AttributeID).
				First(&attr).Error; err != nil {
				continue
			}

			// Lấy giá trị option
			var option models.ProductAttributeOption
			if err := r.gormDB.WithContext(ctx).
				Where("tenant_id = ? AND option_id = ?", tenantID, varAttr.Value).
				First(&option).Error; err != nil {
				continue
			}

			variantAttrMap[attr.Name] = option.Value
		}

		// Tính giá chênh lệch so với base price
		var priceAdjustment float64
		if variant.Price != nil {
			priceAdjustment = *variant.Price - product.BasePrice
		}

		// Tạo combination string (có thể cần cải thiện hơn)
		combination := ""
		for attrName, attrValue := range variantAttrMap {
			if combination != "" {
				combination += " / "
			}
			combination += attrName + ": " + attrValue
		}

		// Giá trị mặc định cho các trường có thể null
		var price float64
		if variant.Price != nil {
			price = *variant.Price
		}

		var costPrice float64
		if variant.CostPrice != nil {
			costPrice = *variant.CostPrice
		}

		// Giá trị mặc định cho các trường khác
		stockStatus := "IN_STOCK"
		var weight float64
		weightUnit := "kg"
		isActive := variant.IsActive

		variants = append(variants, common.VariantDTO{
			ID:              fmt.Sprintf("%d", variant.VariantID),
			Combination:     combination,
			PriceAdjustment: priceAdjustment,
			Price:           price,
			CostPrice:       costPrice,
			SKU:             variant.SKU,
			StockStatus:     stockStatus,
			Weight:          weight,
			WeightUnit:      weightUnit,
			IsVisible:       isActive,
			Attributes:      variantAttrMap,
		})
	}

	return attributes, variants, manageVariantPricing, nil
}
