package mysql

import (
	"context"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/modules/product/models"
)

func setupProductOptionGroupTest(t *testing.T) (*sqlx.DB, sqlmock.Sqlmock, *gorm.DB, func()) {
	mockDB, mock, err := sqlmock.New()
	require.NoError(t, err)

	sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

	dialector := mysql.New(mysql.Config{
		Conn:                      mockDB,
		SkipInitializeWithVersion: true,
	})

	gormDB, err := gorm.Open(dialector, &gorm.Config{})
	require.NoError(t, err)

	cleanup := func() {
		mockDB.Close()
	}

	return sqlxDB, mock, gormDB, cleanup
}

func TestProductOptionGroupRepository_Create(t *testing.T) {
	// Skip test if running in CI
	t.Skip("Skipping test that requires DB connection")

	sqlxDB, mock, gormDB, cleanup := setupProductOptionGroupTest(t)
	defer cleanup()

	repo := NewProductOptionGroupRepository(sqlxDB, gormDB)

	// Sample data
	now := time.Now()
	group := &models.ProductOptionGroup{
		TenantID:     1,
		Name:         "Size",
		Description:  stringPtr("Size options"),
		Type:         "SELECT",
		Required:     true,
		MinSelect:    1,
		MaxSelect:    1,
		DisplayOrder: 1,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	// Expecting SQL queries for the Create operation
	mock.ExpectBegin()
	mock.ExpectExec("INSERT INTO `ecom_product_option_groups`").
		WithArgs(
			sqlmock.AnyArg(), // group_id is auto-incremented
			group.TenantID,
			group.Name,
			group.Description,
			group.Type,
			group.Required,
			group.MinSelect,
			group.MaxSelect,
			group.DisplayOrder,
			sqlmock.AnyArg(), // created_at
			sqlmock.AnyArg(), // updated_at
		).
		WillReturnResult(sqlmock.NewResult(1, 1))
	mock.ExpectCommit()

	// Call repository method
	err := repo.Create(context.Background(), group)
	assert.NoError(t, err)
	assert.Equal(t, uint(1), group.GroupID)
}

func TestProductOptionGroupRepository_GetByID(t *testing.T) {
	// Skip test if running in CI
	t.Skip("Skipping test that requires DB connection")

	sqlxDB, mock, gormDB, cleanup := setupProductOptionGroupTest(t)
	defer cleanup()

	repo := NewProductOptionGroupRepository(sqlxDB, gormDB)

	// Sample data
	now := time.Now()
	expectedGroup := &models.ProductOptionGroup{
		GroupID:      1,
		TenantID:     1,
		Name:         "Size",
		Description:  stringPtr("Size options"),
		Type:         "SELECT",
		Required:     true,
		MinSelect:    1,
		MaxSelect:    1,
		DisplayOrder: 1,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	// Mock rows
	rows := sqlmock.NewRows([]string{
		"group_id", "tenant_id", "name", "description", "type", "required",
		"min_select", "max_select", "display_order", "created_at", "updated_at",
	}).
		AddRow(
			expectedGroup.GroupID, expectedGroup.TenantID, expectedGroup.Name,
			expectedGroup.Description, expectedGroup.Type, expectedGroup.Required,
			expectedGroup.MinSelect, expectedGroup.MaxSelect, expectedGroup.DisplayOrder,
			expectedGroup.CreatedAt, expectedGroup.UpdatedAt,
		)

	// Expecting SQL query for GetByID operation
	mock.ExpectQuery("SELECT (.+) FROM `ecom_product_option_groups` WHERE tenant_id = \\? AND group_id = \\? ORDER BY `ecom_product_option_groups`.`group_id` LIMIT 1").
		WithArgs(1, 1).
		WillReturnRows(rows)

	// Call repository method
	group, err := repo.GetByID(context.Background(), 1, 1)
	assert.NoError(t, err)
	assert.NotNil(t, group)
	assert.Equal(t, expectedGroup.GroupID, group.GroupID)
	assert.Equal(t, expectedGroup.Name, group.Name)
	assert.Equal(t, expectedGroup.Type, group.Type)
}

// Helper function
func stringPtr(s string) *string {
	return &s
}
