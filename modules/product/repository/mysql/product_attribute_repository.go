package mysql

import (
	"fmt"
	"strconv"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/internal/pkg/pagination"
	"wnapi/modules/product/models"
)

// ProductAttributeRepository đại diện cho repository xử lý thuộc tính sản phẩm
type ProductAttributeRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewProductAttributeRepository tạo một ProductAttributeRepository mới
func NewProductAttributeRepository(db *sqlx.DB, gormDB *gorm.DB) *ProductAttributeRepository {
	return &ProductAttributeRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create tạo một thuộc tính sản phẩm mới
func (r *ProductAttributeRepository) Create(attribute *models.ProductAttribute) (*models.ProductAttribute, error) {
	// Tạo bản ghi mới với GORM
	if err := r.gormDB.Create(attribute).Error; err != nil {
		return nil, fmt.Errorf("failed to create product attribute: %w", err)
	}
	return attribute, nil
}

// Update cập nhật một thuộc tính sản phẩm
func (r *ProductAttributeRepository) Update(attribute *models.ProductAttribute) (*models.ProductAttribute, error) {
	// Cập nhật bản ghi với GORM
	if err := r.gormDB.Save(attribute).Error; err != nil {
		return nil, fmt.Errorf("failed to update product attribute: %w", err)
	}
	return attribute, nil
}

// GetByID lấy thuộc tính sản phẩm theo ID
func (r *ProductAttributeRepository) GetByID(tenantID uint, attributeID uint) (*models.ProductAttribute, error) {
	var attribute models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).First(&attribute).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attribute by ID: %w", err)
	}
	return &attribute, nil
}

// GetByCode lấy thuộc tính sản phẩm theo mã
func (r *ProductAttributeRepository) GetByCode(tenantID uint, code string) (*models.ProductAttribute, error) {
	var attribute models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND code = ?", tenantID, code).First(&attribute).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attribute by code: %w", err)
	}
	return &attribute, nil
}

// List lấy danh sách thuộc tính sản phẩm với phân trang
func (r *ProductAttributeRepository) List(tenantID uint, cursor string, limit int) ([]*models.ProductAttribute, *pagination.Cursor, error) {
	var attributes []*models.ProductAttribute
	query := r.gormDB.Where("tenant_id = ?", tenantID)

	// Xử lý phân trang
	if cursor != "" {
		// Giải mã cursor
		cursorData, err := pagination.DecodeCursor(cursor)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid cursor: %w", err)
		}

		// Chuyển đổi cursor thành ID
		cursorID, err := strconv.Atoi(cursorData)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid cursor ID: %w", err)
		}

		// Áp dụng điều kiện cursor
		query = query.Where("attribute_id > ?", cursorID)
	}

	// Áp dụng giới hạn và sắp xếp
	query = query.Order("attribute_id ASC").Limit(limit)

	// Thực hiện truy vấn
	if err := query.Find(&attributes).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to list product attributes: %w", err)
	}

	// Tạo cursor tiếp theo
	var nextCursor *pagination.Cursor
	if len(attributes) > 0 {
		lastAttribute := attributes[len(attributes)-1]
		nextCursor = &pagination.Cursor{
			ID: int(lastAttribute.AttributeID),
		}
	}

	return attributes, nextCursor, nil
}

// Delete xóa thuộc tính sản phẩm
func (r *ProductAttributeRepository) Delete(tenantID uint, attributeID uint) error {
	result := r.gormDB.Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).Delete(&models.ProductAttribute{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product attribute: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("product attribute not found")
	}
	return nil
}

// GetAll lấy tất cả thuộc tính sản phẩm
func (r *ProductAttributeRepository) GetAll(tenantID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ?", tenantID).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get all product attributes: %w", err)
	}
	return attributes, nil
}

// GetByGroupID lấy tất cả thuộc tính sản phẩm theo ID nhóm
func (r *ProductAttributeRepository) GetByGroupID(tenantID uint, groupID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND group_id = ?", tenantID, groupID).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attributes by group ID: %w", err)
	}
	return attributes, nil
}

// GetConfigurable lấy tất cả thuộc tính sản phẩm có thể cấu hình
func (r *ProductAttributeRepository) GetConfigurable(tenantID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND is_configurable = ?", tenantID, true).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get configurable product attributes: %w", err)
	}
	return attributes, nil
}

// GetFilterable lấy tất cả thuộc tính sản phẩm có thể lọc
func (r *ProductAttributeRepository) GetFilterable(tenantID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND is_filterable = ?", tenantID, true).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get filterable product attributes: %w", err)
	}
	return attributes, nil
}

// GetSearchable lấy tất cả thuộc tính sản phẩm có thể tìm kiếm
func (r *ProductAttributeRepository) GetSearchable(tenantID uint) ([]*models.ProductAttribute, error) {
	var attributes []*models.ProductAttribute
	if err := r.gormDB.Where("tenant_id = ? AND is_searchable = ?", tenantID, true).Order("display_order ASC, name ASC").Find(&attributes).Error; err != nil {
		return nil, fmt.Errorf("failed to get searchable product attributes: %w", err)
	}
	return attributes, nil
}
