package mysql

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
)

// ProductConfigurableAttributeRepository implements repository.ProductConfigurableAttributeRepository with MySQL
type ProductConfigurableAttributeRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

func NewProductConfigurableAttributeRepository(db *sqlx.DB, gormDB *gorm.DB) repository.ProductConfigurableAttributeRepository {
	return &ProductConfigurableAttributeRepository{
		db:     db,
		gormDB: gormDB,
	}
}

func (r *ProductConfigurableAttributeRepository) Create(ctx context.Context, attr *models.ProductConfigurableAttribute) error {
	now := time.Now()
	if attr.CreatedAt.IsZero() {
		attr.CreatedAt = now
	}
	if attr.UpdatedAt.IsZero() {
		attr.UpdatedAt = now
	}
	result := r.gormDB.WithContext(ctx).Create(attr)
	if result.Error != nil {
		return fmt.Errorf("failed to create configurable attribute: %w", result.Error)
	}
	return nil
}

func (r *ProductConfigurableAttributeRepository) CreateBatch(ctx context.Context, attrs []*models.ProductConfigurableAttribute) error {
	if len(attrs) == 0 {
		return nil
	}
	now := time.Now()
	for _, attr := range attrs {
		if attr.CreatedAt.IsZero() {
			attr.CreatedAt = now
		}
		if attr.UpdatedAt.IsZero() {
			attr.UpdatedAt = now
		}
	}
	tx := r.gormDB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	result := tx.Create(attrs)
	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create batch configurable attributes: %w", result.Error)
	}
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	return nil
}

func (r *ProductConfigurableAttributeRepository) GetByID(ctx context.Context, tenantID, configurableAttributeID int) (*models.ProductConfigurableAttribute, error) {
	var attr models.ProductConfigurableAttribute
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND configurable_attribute_id = ?", tenantID, configurableAttributeID).First(&attr)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get configurable attribute: %w", result.Error)
	}
	return &attr, nil
}

func (r *ProductConfigurableAttributeRepository) Update(ctx context.Context, attr *models.ProductConfigurableAttribute) error {
	attr.UpdatedAt = time.Now()
	result := r.gormDB.WithContext(ctx).Save(attr)
	if result.Error != nil {
		return fmt.Errorf("failed to update configurable attribute: %w", result.Error)
	}
	return nil
}

func (r *ProductConfigurableAttributeRepository) Delete(ctx context.Context, tenantID, configurableAttributeID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND configurable_attribute_id = ?", tenantID, configurableAttributeID).Delete(&models.ProductConfigurableAttribute{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete configurable attribute: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return ErrNotFound
	}
	return nil
}

func (r *ProductConfigurableAttributeRepository) ListByProductID(ctx context.Context, tenantID, productID int) ([]*models.ProductConfigurableAttribute, error) {
	var attrs []*models.ProductConfigurableAttribute
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND product_id = ?", tenantID, productID).Order("position ASC").Find(&attrs)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to list configurable attributes by product: %w", result.Error)
	}
	return attrs, nil
}

func (r *ProductConfigurableAttributeRepository) DeleteByProductID(ctx context.Context, tenantID, productID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND product_id = ?", tenantID, productID).Delete(&models.ProductConfigurableAttribute{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete configurable attributes by product: %w", result.Error)
	}
	return nil
}

func (r *ProductConfigurableAttributeRepository) ListByTenantID(ctx context.Context, tenantID int, cursor string, limit int) ([]*models.ProductConfigurableAttribute, string, bool, error) {
	if limit <= 0 {
		limit = 10
	}
	query := r.gormDB.WithContext(ctx).Model(&models.ProductConfigurableAttribute{}).Where("tenant_id = ?", tenantID)
	if cursor != "" {
		id, err := strconv.Atoi(cursor)
		if err == nil && id > 0 {
			query = query.Where("configurable_attribute_id > ?", id)
		}
	}
	query = query.Order("configurable_attribute_id ASC").Limit(limit + 1)
	var attrs []*models.ProductConfigurableAttribute
	if err := query.Find(&attrs).Error; err != nil {
		return nil, "", false, fmt.Errorf("failed to list configurable attributes with pagination: %w", err)
	}
	hasMore := false
	nextCursor := ""
	if len(attrs) > limit {
		hasMore = true
		attrs = attrs[:limit]
	}
	if hasMore && len(attrs) > 0 {
		nextCursor = fmt.Sprintf("%d", attrs[len(attrs)-1].ConfigurableAttributeID)
	}
	return attrs, nextCursor, hasMore, nil
}

func (r *ProductConfigurableAttributeRepository) ListByAttributeID(ctx context.Context, tenantID, attributeID int) ([]*models.ProductConfigurableAttribute, error) {
	var attrs []*models.ProductConfigurableAttribute
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).Find(&attrs)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to list configurable attributes by attribute: %w", result.Error)
	}
	return attrs, nil
}
