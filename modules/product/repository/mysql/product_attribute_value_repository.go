package mysql

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
)

// ProductAttributeValueRepository thực hiện repository.ProductAttributeValueRepository interface với MySQL
type ProductAttributeValueRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewProductAttributeValueRepository tạo một instance mới của ProductAttributeValueRepository
func NewProductAttributeValueRepository(db *sqlx.DB, gormDB *gorm.DB) repository.ProductAttributeValueRepository {
	return &ProductAttributeValueRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create thêm một giá trị thuộc tính sản phẩm mới vào cơ sở dữ liệu
func (r *ProductAttributeValueRepository) Create(ctx context.Context, attributeValue *models.ProductAttributeValue) error {
	// Thiết lập timestamp nếu chưa được thiết lập
	now := time.Now()
	if attributeValue.CreatedAt.IsZero() {
		attributeValue.CreatedAt = now
	}
	if attributeValue.UpdatedAt.IsZero() {
		attributeValue.UpdatedAt = now
	}

	// Tạo giá trị thuộc tính sản phẩm sử dụng GORM
	result := r.gormDB.WithContext(ctx).Create(attributeValue)
	if result.Error != nil {
		return fmt.Errorf("failed to create product attribute value: %w", result.Error)
	}

	return nil
}

// CreateBatch thêm nhiều giá trị thuộc tính sản phẩm vào cơ sở dữ liệu trong một transaction
func (r *ProductAttributeValueRepository) CreateBatch(ctx context.Context, attributeValues []*models.ProductAttributeValue) error {
	if len(attributeValues) == 0 {
		return nil
	}

	// Bắt đầu transaction
	tx := r.gormDB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}

	// Đảm bảo transaction được rollback nếu có lỗi
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Thiết lập timestamp cho tất cả các bản ghi
	now := time.Now()
	for _, attributeValue := range attributeValues {
		if attributeValue.CreatedAt.IsZero() {
			attributeValue.CreatedAt = now
		}
		if attributeValue.UpdatedAt.IsZero() {
			attributeValue.UpdatedAt = now
		}
	}

	// Tạo giá trị thuộc tính sản phẩm sử dụng GORM
	result := tx.Create(attributeValues)
	if result.Error != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create product attribute values: %w", result.Error)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// GetByID truy xuất một giá trị thuộc tính sản phẩm bằng ID
func (r *ProductAttributeValueRepository) GetByID(ctx context.Context, tenantID, valueID int) (*models.ProductAttributeValue, error) {
	var attributeValue models.ProductAttributeValue
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND value_id = ?", tenantID, valueID).First(&attributeValue)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product attribute value: %w", result.Error)
	}

	return &attributeValue, nil
}

// Update cập nhật một giá trị thuộc tính sản phẩm đã tồn tại
func (r *ProductAttributeValueRepository) Update(ctx context.Context, attributeValue *models.ProductAttributeValue) error {
	// Thiết lập timestamp cập nhật
	attributeValue.UpdatedAt = time.Now()

	// Cập nhật giá trị thuộc tính sản phẩm sử dụng GORM
	result := r.gormDB.WithContext(ctx).Save(attributeValue)
	if result.Error != nil {
		return fmt.Errorf("failed to update product attribute value: %w", result.Error)
	}

	return nil
}

// Delete xóa một giá trị thuộc tính sản phẩm khỏi cơ sở dữ liệu
func (r *ProductAttributeValueRepository) Delete(ctx context.Context, tenantID, valueID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND value_id = ?", tenantID, valueID).Delete(&models.ProductAttributeValue{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product attribute value: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrNotFound
	}

	return nil
}

// GetByProductID truy xuất tất cả giá trị thuộc tính của một sản phẩm
func (r *ProductAttributeValueRepository) GetByProductID(ctx context.Context, tenantID, productID int) ([]*models.ProductAttributeValue, error) {
	var attributeValues []*models.ProductAttributeValue
	result := r.gormDB.WithContext(ctx).
		Where("tenant_id = ? AND reference_id = ? AND reference_type = 'PRODUCT'", tenantID, productID).
		Find(&attributeValues)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get product attribute values: %w", result.Error)
	}

	return attributeValues, nil
}

// GetByProductAndAttributeID truy xuất giá trị thuộc tính cụ thể của một sản phẩm
func (r *ProductAttributeValueRepository) GetByProductAndAttributeID(ctx context.Context, tenantID, productID, attributeID int) (*models.ProductAttributeValue, error) {
	var attributeValue models.ProductAttributeValue
	result := r.gormDB.WithContext(ctx).
		Where("tenant_id = ? AND reference_id = ? AND reference_type = 'PRODUCT' AND attribute_id = ?", tenantID, productID, attributeID).
		First(&attributeValue)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product attribute value: %w", result.Error)
	}

	return &attributeValue, nil
}

// DeleteByProductID xóa tất cả giá trị thuộc tính của một sản phẩm
func (r *ProductAttributeValueRepository) DeleteByProductID(ctx context.Context, tenantID, productID int) error {
	result := r.gormDB.WithContext(ctx).
		Where("tenant_id = ? AND reference_id = ? AND reference_type = 'PRODUCT'", tenantID, productID).
		Delete(&models.ProductAttributeValue{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete product attribute values: %w", result.Error)
	}

	return nil
}

// GetByAttributeID truy xuất tất cả giá trị thuộc tính theo ID thuộc tính
func (r *ProductAttributeValueRepository) GetByAttributeID(ctx context.Context, tenantID, attributeID int) ([]*models.ProductAttributeValue, error) {
	var attributeValues []*models.ProductAttributeValue
	result := r.gormDB.WithContext(ctx).
		Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).
		Find(&attributeValues)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get product attribute values by attribute ID: %w", result.Error)
	}

	return attributeValues, nil
}

// GetByAttributeIDWithPagination truy xuất giá trị thuộc tính theo ID thuộc tính với phân trang cursor
func (r *ProductAttributeValueRepository) GetByAttributeIDWithPagination(ctx context.Context, tenantID, attributeID int, cursor string, limit int) ([]*models.ProductAttributeValue, string, bool, error) {
	if limit <= 0 {
		limit = 10 // Giới hạn mặc định
	}

	// Bắt đầu truy vấn
	query := r.gormDB.WithContext(ctx).Model(&models.ProductAttributeValue{}).
		Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID)

	// Áp dụng phân trang dựa trên cursor
	if cursor != "" {
		valueID, err := strconv.Atoi(cursor)
		if err == nil && valueID > 0 {
			query = query.Where("value_id > ?", valueID)
		}
	}

	// Sắp xếp theo ID và giới hạn kết quả
	query = query.Order("value_id ASC").Limit(limit + 1) // +1 để kiểm tra xem có thêm kết quả không

	// Thực thi truy vấn
	var attributeValues []*models.ProductAttributeValue
	if err := query.Find(&attributeValues).Error; err != nil {
		return nil, "", false, fmt.Errorf("failed to get product attribute values with pagination: %w", err)
	}

	// Kiểm tra xem có thêm kết quả không
	hasMore := false
	nextCursor := ""
	if len(attributeValues) > limit {
		hasMore = true
		attributeValues = attributeValues[:limit]
	}

	// Thiết lập cursor tiếp theo nếu có thêm kết quả
	if hasMore && len(attributeValues) > 0 {
		nextCursor = fmt.Sprintf("%d", attributeValues[len(attributeValues)-1].ValueID)
	}

	return attributeValues, nextCursor, hasMore, nil
}

// DeleteByAttributeID xóa tất cả giá trị thuộc tính của một thuộc tính
func (r *ProductAttributeValueRepository) DeleteByAttributeID(ctx context.Context, tenantID int, attributeID uint) error {
	result := r.gormDB.WithContext(ctx).
		Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).
		Delete(&models.ProductAttributeValue{})

	if result.Error != nil {
		return fmt.Errorf("failed to delete product attribute values by attribute ID: %w", result.Error)
	}

	return nil
}

// GetByAttributeIDAndOptionID truy xuất tất cả giá trị thuộc tính theo ID thuộc tính và ID tùy chọn
func (r *ProductAttributeValueRepository) GetByAttributeIDAndOptionID(ctx context.Context, tenantID int, attributeID, optionID uint) ([]*models.ProductAttributeValue, error) {
	var attributeValues []*models.ProductAttributeValue
	result := r.gormDB.WithContext(ctx).
		Where("tenant_id = ? AND attribute_id = ? AND attribute_option_id = ?", tenantID, attributeID, optionID).
		Find(&attributeValues)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to get product attribute values by attribute ID and option ID: %w", result.Error)
	}

	return attributeValues, nil
}

// GetByProductIDAndAttributeID truy xuất giá trị thuộc tính cụ thể của một sản phẩm (với đúng kiểu tham số theo interface)
func (r *ProductAttributeValueRepository) GetByProductIDAndAttributeID(ctx context.Context, tenantID, productID int, attributeID uint) (*models.ProductAttributeValue, error) {
	var attributeValue models.ProductAttributeValue
	result := r.gormDB.WithContext(ctx).
		Where("tenant_id = ? AND reference_id = ? AND reference_type = 'PRODUCT' AND attribute_id = ?", tenantID, productID, attributeID).
		First(&attributeValue)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product attribute value: %w", result.Error)
	}

	return &attributeValue, nil
}
