package mysql

import (
	"context"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/modules/product/models"
)

// ProductOptionGroupRepository implements the repository.ProductOptionGroupRepository interface with MySQL
type ProductOptionGroupRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewProductOptionGroupRepository creates a new ProductOptionGroupRepository instance
func NewProductOptionGroupRepository(db *sqlx.DB, gormDB *gorm.DB) *ProductOptionGroupRepository {
	return &ProductOptionGroupRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create adds a new product option group to the database
func (r *ProductOptionGroupRepository) Create(ctx context.Context, group *models.ProductOptionGroup) error {
	// Set timestamps if not already set
	now := time.Now()
	if group.CreatedAt.IsZero() {
		group.CreatedAt = now
	}
	if group.UpdatedAt.IsZero() {
		group.UpdatedAt = now
	}

	// Create group using GORM
	result := r.gormDB.WithContext(ctx).Create(group)
	if result.Error != nil {
		return fmt.Errorf("failed to create product option group: %w", result.Error)
	}

	return nil
}

// GetByID retrieves a product option group by its ID
func (r *ProductOptionGroupRepository) GetByID(ctx context.Context, tenantID, groupID int) (*models.ProductOptionGroup, error) {
	var group models.ProductOptionGroup
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND group_id = ?", tenantID, groupID).First(&group)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product option group: %w", result.Error)
	}

	return &group, nil
}

// Update updates an existing product option group
func (r *ProductOptionGroupRepository) Update(ctx context.Context, group *models.ProductOptionGroup) error {
	// Set updated timestamp
	group.UpdatedAt = time.Now()

	// Update group using GORM
	result := r.gormDB.WithContext(ctx).Save(group)
	if result.Error != nil {
		return fmt.Errorf("failed to update product option group: %w", result.Error)
	}

	return nil
}

// Delete removes a product option group from the database
func (r *ProductOptionGroupRepository) Delete(ctx context.Context, tenantID, groupID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND group_id = ?", tenantID, groupID).Delete(&models.ProductOptionGroup{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product option group: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return ErrNotFound
	}

	return nil
}

// List retrieves a list of product option groups with pagination
func (r *ProductOptionGroupRepository) List(ctx context.Context, tenantID int, cursor string, limit int) ([]*models.ProductOptionGroup, string, bool, error) {
	if limit <= 0 {
		limit = 10 // Default limit
	}

	// Start query
	query := r.gormDB.WithContext(ctx).Model(&models.ProductOptionGroup{}).Where("tenant_id = ?", tenantID)

	// Apply cursor-based pagination
	if cursor != "" {
		query = query.Where("group_id > ?", cursor)
	}

	// Order by ID and limit results
	query = query.Order("group_id ASC").Limit(limit + 1) // +1 to check if there are more results

	// Execute query
	var groups []*models.ProductOptionGroup
	if err := query.Find(&groups).Error; err != nil {
		return nil, "", false, fmt.Errorf("failed to list product option groups: %w", err)
	}

	// Check if there are more results
	hasMore := false
	nextCursor := ""
	if len(groups) > limit {
		hasMore = true
		groups = groups[:limit]
	}

	// Set next cursor if there are more results
	if hasMore && len(groups) > 0 {
		nextCursor = fmt.Sprintf("%d", groups[len(groups)-1].GroupID)
	}

	return groups, nextCursor, hasMore, nil
}
