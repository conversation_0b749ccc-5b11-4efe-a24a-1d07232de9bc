package mysql

import (
	"context"
	"fmt"
	"strconv"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
	"wnapi/internal/pkg/pagination"
)

// ProductVariantAttributeValueRepository triển khai repository cho ProductVariantAttributeValue
type ProductVariantAttributeValueRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewProductVariantAttributeValueRepository tạo một instance mới của ProductVariantAttributeValueRepository
func NewProductVariantAttributeValueRepository(db *sqlx.DB, gormDB *gorm.DB) repository.ProductVariantAttributeValueRepository {
	return &ProductVariantAttributeValueRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create tạo một giá trị thuộc tính biến thể mới
func (r *ProductVariantAttributeValueRepository) Create(ctx context.Context, value *models.ProductVariantAttributeValue) error {
	result := r.gormDB.WithContext(ctx).Create(value)
	if result.Error != nil {
		return fmt.Errorf("failed to create product variant attribute value: %w", result.Error)
	}
	return nil
}

// CreateBatch tạo nhiều giá trị thuộc tính biến thể cùng lúc
func (r *ProductVariantAttributeValueRepository) CreateBatch(ctx context.Context, values []*models.ProductVariantAttributeValue) error {
	result := r.gormDB.WithContext(ctx).CreateInBatches(values, 100)
	if result.Error != nil {
		return fmt.Errorf("failed to batch create product variant attribute values: %w", result.Error)
	}
	return nil
}

// GetByID lấy giá trị thuộc tính biến thể theo ID
func (r *ProductVariantAttributeValueRepository) GetByID(ctx context.Context, tenantID, valueID int) (*models.ProductVariantAttributeValue, error) {
	var value models.ProductVariantAttributeValue
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND value_id = ?", tenantID, valueID).First(&value)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product variant attribute value: %w", result.Error)
	}
	return &value, nil
}

// Update cập nhật giá trị thuộc tính biến thể
func (r *ProductVariantAttributeValueRepository) Update(ctx context.Context, value *models.ProductVariantAttributeValue) error {
	result := r.gormDB.WithContext(ctx).Save(value)
	if result.Error != nil {
		return fmt.Errorf("failed to update product variant attribute value: %w", result.Error)
	}
	return nil
}

// Delete xóa giá trị thuộc tính biến thể
func (r *ProductVariantAttributeValueRepository) Delete(ctx context.Context, tenantID, valueID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND value_id = ?", tenantID, valueID).Delete(&models.ProductVariantAttributeValue{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product variant attribute value: %w", result.Error)
	}
	return nil
}

// List lấy danh sách giá trị thuộc tính biến thể có phân trang
func (r *ProductVariantAttributeValueRepository) List(
	ctx context.Context,
	tenantID int,
	cursor string,
	limit int,
	orderField string,
	orderDirection string,
) ([]*models.ProductVariantAttributeValue, *pagination.CursorInfo, error) {
	if limit <= 0 {
		limit = 10
	}

	if orderField == "" {
		orderField = "value_id"
	}

	if orderDirection != "ASC" && orderDirection != "DESC" {
		orderDirection = "ASC"
	}

	query := r.gormDB.WithContext(ctx).Where("tenant_id = ?", tenantID)

	// Áp dụng phân trang kiểu cursor
	if cursor != "" {
		cursorID, err := strconv.Atoi(cursor)
		if err == nil && cursorID > 0 {
			if orderDirection == "ASC" {
				query = query.Where(orderField+" > ?", cursorID)
			} else {
				query = query.Where(orderField+" < ?", cursorID)
			}
		}
	}

	// Sắp xếp và giới hạn
	query = query.Order(orderField + " " + orderDirection).Limit(limit + 1)

	// Thực hiện truy vấn
	var values []*models.ProductVariantAttributeValue
	result := query.Find(&values)
	if result.Error != nil {
		return nil, nil, fmt.Errorf("failed to list product variant attribute values: %w", result.Error)
	}

	// Xử lý phân trang
	hasMore := false
	var nextCursor string

	if len(values) > limit {
		hasMore = true
		// Loại bỏ phần tử cuối cùng (phần tử thừa dùng để xác định có dữ liệu tiếp theo không)
		values = values[:limit]
	}

	// Nếu có dữ liệu và có trang tiếp theo, thiết lập cursor
	if len(values) > 0 && hasMore {
		lastItem := values[len(values)-1]
		var cursorValue int
		switch orderField {
		case "value_id":
			cursorValue = int(lastItem.ValueID)
		case "variant_id":
			cursorValue = int(lastItem.VariantID)
		case "attribute_id":
			cursorValue = int(lastItem.AttributeID)
		default:
			cursorValue = int(lastItem.ValueID)
		}
		nextCursor = strconv.Itoa(cursorValue)
	}

	cursorInfo := &pagination.CursorInfo{
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}

	return values, cursorInfo, nil
}

// ListByVariantID lấy tất cả giá trị thuộc tính của một biến thể
func (r *ProductVariantAttributeValueRepository) ListByVariantID(ctx context.Context, tenantID, variantID int) ([]*models.ProductVariantAttributeValue, error) {
	var values []*models.ProductVariantAttributeValue
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND variant_id = ?", tenantID, variantID).Find(&values)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get product variant attribute values by variant ID: %w", result.Error)
	}
	return values, nil
}

// ListByAttributeID lấy tất cả giá trị thuộc tính của một thuộc tính
func (r *ProductVariantAttributeValueRepository) ListByAttributeID(ctx context.Context, tenantID, attributeID int) ([]*models.ProductVariantAttributeValue, error) {
	var values []*models.ProductVariantAttributeValue
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).Find(&values)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get product variant attribute values by attribute ID: %w", result.Error)
	}
	return values, nil
}

// DeleteByVariantID xóa tất cả giá trị thuộc tính của một biến thể
func (r *ProductVariantAttributeValueRepository) DeleteByVariantID(ctx context.Context, tenantID, variantID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND variant_id = ?", tenantID, variantID).Delete(&models.ProductVariantAttributeValue{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product variant attribute values by variant ID: %w", result.Error)
	}
	return nil
}

// DeleteByAttributeID xóa tất cả giá trị thuộc tính của một thuộc tính
func (r *ProductVariantAttributeValueRepository) DeleteByAttributeID(ctx context.Context, tenantID, attributeID int) error {
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).Delete(&models.ProductVariantAttributeValue{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product variant attribute values by attribute ID: %w", result.Error)
	}
	return nil
}

// GetByVariantAndAttributeID lấy giá trị thuộc tính biến thể theo biến thể và thuộc tính
func (r *ProductVariantAttributeValueRepository) GetByVariantAndAttributeID(ctx context.Context, tenantID, variantID int, attributeID uint) (*models.ProductVariantAttributeValue, error) {
	var value models.ProductVariantAttributeValue
	result := r.gormDB.WithContext(ctx).Where("tenant_id = ? AND variant_id = ? AND attribute_id = ?", tenantID, variantID, attributeID).First(&value)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, ErrNotFound
		}
		return nil, fmt.Errorf("failed to get product variant attribute value by variant and attribute: %w", result.Error)
	}
	return &value, nil
}
