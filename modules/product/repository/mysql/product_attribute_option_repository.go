package mysql

import (
	"fmt"
	"strconv"

	"github.com/jmoiron/sqlx"
	"gorm.io/gorm"

	"wnapi/internal/pkg/pagination"
	"wnapi/modules/product/models"
)

// ProductAttributeOptionRepository đại diện cho repository xử lý tùy chọn thuộc tính sản phẩm
type ProductAttributeOptionRepository struct {
	db     *sqlx.DB
	gormDB *gorm.DB
}

// NewProductAttributeOptionRepository tạo một ProductAttributeOptionRepository mới
func NewProductAttributeOptionRepository(db *sqlx.DB, gormDB *gorm.DB) *ProductAttributeOptionRepository {
	return &ProductAttributeOptionRepository{
		db:     db,
		gormDB: gormDB,
	}
}

// Create tạo một tùy chọn thuộc tính sản phẩm mới
func (r *ProductAttributeOptionRepository) Create(option *models.ProductAttributeOption) (*models.ProductAttributeOption, error) {
	// Tạo bản ghi mới với GORM
	if err := r.gormDB.Create(option).Error; err != nil {
		return nil, fmt.Errorf("failed to create product attribute option: %w", err)
	}
	return option, nil
}

// Update cập nhật một tùy chọn thuộc tính sản phẩm
func (r *ProductAttributeOptionRepository) Update(option *models.ProductAttributeOption) (*models.ProductAttributeOption, error) {
	// Cập nhật bản ghi với GORM
	if err := r.gormDB.Save(option).Error; err != nil {
		return nil, fmt.Errorf("failed to update product attribute option: %w", err)
	}
	return option, nil
}

// GetByID lấy tùy chọn thuộc tính sản phẩm theo ID
func (r *ProductAttributeOptionRepository) GetByID(tenantID uint, optionID uint) (*models.ProductAttributeOption, error) {
	var option models.ProductAttributeOption
	if err := r.gormDB.Where("tenant_id = ? AND option_id = ?", tenantID, optionID).First(&option).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attribute option by ID: %w", err)
	}
	return &option, nil
}

// GetByAttributeIDAndValue lấy tùy chọn thuộc tính sản phẩm theo ID thuộc tính và giá trị
func (r *ProductAttributeOptionRepository) GetByAttributeIDAndValue(tenantID uint, attributeID uint, value string) (*models.ProductAttributeOption, error) {
	var option models.ProductAttributeOption
	if err := r.gormDB.Where("tenant_id = ? AND attribute_id = ? AND value = ?", tenantID, attributeID, value).First(&option).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attribute option by attribute ID and value: %w", err)
	}
	return &option, nil
}

// List lấy danh sách tùy chọn thuộc tính sản phẩm với phân trang
func (r *ProductAttributeOptionRepository) List(tenantID uint, cursor string, limit int) ([]*models.ProductAttributeOption, *pagination.Cursor, error) {
	var options []*models.ProductAttributeOption
	query := r.gormDB.Where("tenant_id = ?", tenantID)

	// Xử lý phân trang
	if cursor != "" {
		// Giải mã cursor
		cursorData, err := pagination.DecodeCursor(cursor)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid cursor: %w", err)
		}

		// Chuyển đổi cursor thành ID
		cursorID, err := strconv.Atoi(cursorData)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid cursor ID: %w", err)
		}

		// Áp dụng điều kiện cursor
		query = query.Where("option_id > ?", cursorID)
	}

	// Áp dụng giới hạn và sắp xếp
	query = query.Order("option_id ASC").Limit(limit)

	// Thực hiện truy vấn
	if err := query.Find(&options).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to list product attribute options: %w", err)
	}

	// Tạo cursor tiếp theo
	var nextCursor *pagination.Cursor
	if len(options) > 0 {
		lastOption := options[len(options)-1]
		nextCursor = &pagination.Cursor{
			ID: int(lastOption.OptionID),
		}
	}

	return options, nextCursor, nil
}

// Delete xóa tùy chọn thuộc tính sản phẩm
func (r *ProductAttributeOptionRepository) Delete(tenantID uint, optionID uint) error {
	result := r.gormDB.Where("tenant_id = ? AND option_id = ?", tenantID, optionID).Delete(&models.ProductAttributeOption{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete product attribute option: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("product attribute option not found")
	}
	return nil
}

// GetByAttributeID lấy tất cả tùy chọn thuộc tính sản phẩm theo ID thuộc tính
func (r *ProductAttributeOptionRepository) GetByAttributeID(tenantID uint, attributeID uint) ([]*models.ProductAttributeOption, error) {
	var options []*models.ProductAttributeOption
	if err := r.gormDB.Where("tenant_id = ? AND attribute_id = ?", tenantID, attributeID).Order("display_order ASC, value ASC").Find(&options).Error; err != nil {
		return nil, fmt.Errorf("failed to get product attribute options by attribute ID: %w", err)
	}
	return options, nil
}

// GetAll lấy tất cả tùy chọn thuộc tính sản phẩm
func (r *ProductAttributeOptionRepository) GetAll(tenantID uint) ([]*models.ProductAttributeOption, error) {
	var options []*models.ProductAttributeOption
	if err := r.gormDB.Where("tenant_id = ?", tenantID).Order("attribute_id ASC, display_order ASC, value ASC").Find(&options).Error; err != nil {
		return nil, fmt.Errorf("failed to get all product attribute options: %w", err)
	}
	return options, nil
}
