package repository

import (
	"context"

	"wnapi/modules/product/models"
	"wnapi/internal/pkg/pagination"
)

// ProductVariantRepository định nghĩa interface cho thao tác với bảng ecom_product_variants
type ProductVariantRepository interface {
	// <PERSON><PERSON><PERSON> phương thức CRUD cơ bản
	Create(ctx context.Context, variant *models.ProductVariant) error
	CreateBatch(ctx context.Context, variants []*models.ProductVariant) error
	GetByID(ctx context.Context, tenantID, variantID int) (*models.ProductVariant, error)
	Update(ctx context.Context, variant *models.ProductVariant) error
	Delete(ctx context.Context, tenantID, variantID int) error

	// Cá<PERSON> phương thức truy vấn nâng cao
	ListByProductID(ctx context.Context, tenantID, productID int) ([]*models.ProductVariant, error)
	GetBySKU(ctx context.Context, tenantID int, sku string) (*models.ProductVariant, error)

	// <PERSON><PERSON><PERSON> phương thức liệt kê với phân trang
	List(ctx context.Context, tenantID int, cursor string, limit int, orderField, orderDirection string) ([]*models.ProductVariant, *pagination.CursorInfo, error)

	// Các phương thức xóa nâng cao
	DeleteByProductID(ctx context.Context, tenantID, productID int) error
}
