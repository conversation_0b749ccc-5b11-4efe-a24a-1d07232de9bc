package repository

import (
	"context"

	"wnapi/modules/product/models"
)

// ProductConfigurableAttributeRepository định ngh<PERSON>a interface cho thao tác với bảng ecom_product_configurable_attributes
type ProductConfigurableAttributeRepository interface {
	Create(ctx context.Context, attr *models.ProductConfigurableAttribute) error
	CreateBatch(ctx context.Context, attrs []*models.ProductConfigurableAttribute) error
	GetByID(ctx context.Context, tenantID, configurableAttributeID int) (*models.ProductConfigurableAttribute, error)
	Update(ctx context.Context, attr *models.ProductConfigurableAttribute) error
	Delete(ctx context.Context, tenantID, configurableAttributeID int) error

	ListByProductID(ctx context.Context, tenantID, productID int) ([]*models.ProductConfigurableAttribute, error)
	DeleteByProductID(ctx context.Context, tenantID, productID int) error
	ListByTenantID(ctx context.Context, tenantID int, cursor string, limit int) ([]*models.ProductConfigurableAttribute, string, bool, error)
	ListByAttributeID(ctx context.Context, tenantID, attributeID int) ([]*models.ProductConfigurableAttribute, error)
}
