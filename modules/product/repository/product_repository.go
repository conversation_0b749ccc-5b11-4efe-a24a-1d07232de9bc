package repository

import (
	"context"

	"wnapi/modules/product/dto/common"
	"wnapi/modules/product/models"
)

// ProductFilters defines filters for product queries
type ProductFilters struct {
	Limit       *int
	CategoryID  *int
	Status      *string
	ProductType *string
}

// ProductRepository defines the interface for product-related operations
type ProductRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, product *models.Product) error
	GetByID(ctx context.Context, tenantID, productID int) (*models.Product, error)
	Update(ctx context.Context, product *models.Product) error
	Delete(ctx context.Context, tenantID, productID int) error

	// List and search operations
	List(ctx context.Context, tenantID int, cursor string, limit int, filters map[string]interface{}) ([]*models.Product, string, bool, error)
	Search(ctx context.Context, tenantID int, keyword string, cursor string, limit int, filters map[string]interface{}) ([]*models.Product, string, bool, error)
	GetAll(ctx context.Context, tenantID int) ([]*models.Product, error)

	// Category-related operations
	GetProductCategories(ctx context.Context, tenantID int) ([]map[string]interface{}, error)

	// Product attributes and variants
	GetProductAttributesAndVariants(ctx context.Context, tenantID, productID int) ([]common.AttributeDTO, []common.VariantDTO, bool, error)
}
