server:
  host: "0.0.0.0"
  port: 9051

db:
  host: "localhost"
  port: 3307
  username: "root"
  password: "root"
  database: "blog_v4"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

tracing:
  enabled: true
  service_name: "ecom-service"
  exporter_type: "jaeger"
  sample_ratio: 1.0
  signoz:
    endpoint: "localhost:4317"
  jaeger:
    host: "localhost"
    port: "6831"

api:
  base_path: "/api/v1/ecom"

app:
  name: dinein-module
  version: 1.0.0
  debug: true
  env: development

http:
  host: 0.0.0.0
  port: 9052

database:
  driver: mysql
  host: "localhost"
  port: 3307
  username: "root"
  password: "root"
  database: "blog_v4"
  params: "parseTime=true"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 1h

# Legacy telemetry config - can be removed after migrating to tracing section above
telemetry:
  jaeger:
    host: "localhost"
    port: 6831
    enable: true

jwt:
  access_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  refresh_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  access_token_expiration: "168h"
  refresh_token_expiration: "168h"
  issuer: wn-backend