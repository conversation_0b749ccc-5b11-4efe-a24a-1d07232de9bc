CREATE PROCEDURE `ecom_product_categories_move_node`(
    IN p_tenant_id INT,             -- ID của tenant hiện tại
    IN p_node_id_to_move INT,    -- ID của node c<PERSON>n di chuyển
    IN p_new_parent_id INT,      -- ID của node cha mới (NULL nếu muốn chuyển thành root)
    IN p_target_position INT        -- Vị trí mong muốn dưới node cha mới hoặc giữa các root (0-based index)
)
BEGIN
    -- <PERSON><PERSON> báo các biến cục bộ
    DECLARE v_node_lft INT;
    DECLARE v_node_rgt INT;
    DECLARE v_node_depth INT;
    DECLARE v_current_parent_id INT;
    DECLARE v_node_width INT;
    DECLARE v_new_parent_lft INT;    -- Lft của cha mới (chỉ dùng khi không phải root)
    DECLARE v_new_parent_rgt INT;    -- Rgt của cha mới hoặc max(rgt) khi chuyển thành root
    DECLARE v_new_parent_depth INT;  -- Depth của cha mới (-1 nếu chuyển thành root)
    DECLARE v_move_offset INT;
    DECLARE v_depth_offset INT;
    DECLARE v_actual_target_position INT; -- Vị trí thực tế sau khi chuẩn hóa
    DECLARE v_max_sibling_position INT;   -- Vị trí lớn nhất của các anh em (cùng cha hoặc cùng root)
    DECLARE v_is_moving_to_root BOOLEAN DEFAULT FALSE;

    -- === Khai báo handler để tự động ROLLBACK nếu có lỗi ===
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        -- Nếu có lỗi SQL xảy ra, hủy bỏ giao dịch
        ROLLBACK;
        -- Gửi lại tín hiệu lỗi để client hoặc ứng dụng gọi biết
        RESIGNAL;
    END;

    -- === Khai báo handler cho cảnh báo (tùy chọn) ===
    DECLARE EXIT HANDLER FOR SQLWARNING
    BEGIN
        -- Có thể ghi log cảnh báo ở đây nếu cần
        -- Thông thường không cần rollback vì cảnh báo
        RESIGNAL; -- Gửi lại tín hiệu cảnh báo
    END;

    -- === Bắt đầu Transaction ===
    -- Đảm bảo tất cả các thay đổi được thực hiện thành công hoặc không có gì thay đổi cả
    START TRANSACTION;

    -- === Bước 1: Lấy thông tin và khóa node cần di chuyển ===
    -- Lấy các giá trị nested set và parent_id hiện tại của node.
    -- FOR UPDATE để khóa hàng này, ngăn chặn các giao dịch khác sửa đổi nó cùng lúc.
    SELECT
        lft, rgt, depth, parent_id
    INTO
        v_node_lft, v_node_rgt, v_node_depth, v_current_parent_id
    FROM ecom_product_categories
    WHERE category_id = p_node_id_to_move AND tenant_id = p_tenant_id
    FOR UPDATE;

    -- Nếu không tìm thấy node cần di chuyển, báo lỗi và dừng lại.
    IF v_node_lft IS NULL THEN
        -- Không cần ROLLBACK tường minh vì EXIT HANDLER sẽ xử lý
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Node to move not found.';
    END IF;

    -- Tính toán độ rộng của nhánh cần di chuyển (số lượng node trong nhánh + 1)
    SET v_node_width = v_node_rgt - v_node_lft + 1;

    -- === Bước 1b: Xác định và lấy thông tin vị trí đích ===
    -- Kiểm tra xem đích đến có phải là root hay không
    IF p_new_parent_id IS NULL THEN
        -- === Đang di chuyển ra làm ROOT ===
        SET v_is_moving_to_root = TRUE;
        -- Quy ước depth của "cha" của root là -1 để tính toán depth offset dễ dàng
        SET v_new_parent_depth = -1;

        -- Xử lý trường hợp node đã là root (chỉ là sắp xếp lại vị trí)
        IF v_current_parent_id IS NULL THEN
             -- Vẫn cho phép tiếp tục để sắp xếp lại position
             -- Đặt giá trị tạm thời, sẽ tính lại ở Bước 5
             SET v_new_parent_rgt = 0;
        ELSE
            -- Đặt giá trị tạm thời, sẽ tính lại ở Bước 5
            SET v_new_parent_rgt = 0;
        END IF;

    ELSE
        -- === Đang di chuyển vào một node cha cụ thể ===
        SET v_is_moving_to_root = FALSE;

        -- Lấy thông tin và khóa node cha mới
        SELECT
            lft, rgt, depth
        INTO
            v_new_parent_lft, v_new_parent_rgt, v_new_parent_depth
        FROM ecom_product_categories
        WHERE category_id = p_new_parent_id AND tenant_id = p_tenant_id
        FOR UPDATE;

        -- Nếu không tìm thấy node cha mới, báo lỗi
        IF v_new_parent_lft IS NULL THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'New parent node not found.';
        END IF;

        -- === Bước 2: Kiểm tra tính hợp lệ (chỉ khi không di chuyển ra root) ===
        -- Kiểm tra xem có đang cố gắng di chuyển một node vào chính nó hoặc vào một node con của nó không.
        -- Điều này là không hợp lệ trong mô hình nested set.
        IF v_new_parent_lft >= v_node_lft AND v_new_parent_lft < v_node_rgt THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Cannot move a category into itself or its descendants.';
        END IF;

        -- Lưu ý: Trường hợp di chuyển node đến chính cha hiện tại của nó (chỉ thay đổi position)
        -- được logic hiện tại xử lý đúng mà không cần kiểm tra đặc biệt.

    END IF; -- Kết thúc kiểm tra p_new_parent_id IS NULL

    -- === Bước 3: Đánh dấu (tag) nhánh cần di chuyển ===
    -- Tạm thời thay đổi giá trị lft, rgt của nhánh cần di chuyển thành số âm.
    -- Việc này giúp loại trừ nhánh này khỏi các phép tính cập nhật lft/rgt ở bước sau
    -- và dễ dàng xác định các node cần cập nhật ở bước di chuyển cuối cùng.
    UPDATE ecom_product_categories
    SET lft = 0 - lft, rgt = 0 - rgt
    WHERE lft >= v_node_lft AND rgt <= v_node_rgt AND tenant_id = p_tenant_id;

    -- === Bước 4: Đóng khoảng trống ở vị trí cũ ===
    -- Giảm giá trị lft và rgt của các node nằm bên phải vị trí cũ của nhánh đi một khoảng bằng độ rộng của nhánh.
    -- Thao tác này "xóa" khoảng trống mà nhánh để lại.
    UPDATE ecom_product_categories
    SET lft = lft - v_node_width
    WHERE lft > v_node_rgt AND tenant_id = p_tenant_id;

    UPDATE ecom_product_categories
    SET rgt = rgt - v_node_width
    WHERE rgt > v_node_rgt AND tenant_id = p_tenant_id;

    -- === Bước 5: Xác định điểm chèn (Insertion Point) ===
    -- Xác định giá trị RGT sẽ làm mốc để chèn nhánh vào
    IF v_is_moving_to_root THEN
        -- Nếu di chuyển ra root, điểm chèn là sau node có RGT lớn nhất hiện tại trong toàn bộ cây (của tenant này).
        -- COALESCE xử lý trường hợp cây rỗng hoặc chỉ còn nhánh đang di chuyển (đã bị tag âm).
        SELECT COALESCE(MAX(rgt), 0)
        INTO v_new_parent_rgt -- Sử dụng biến này để lưu max_rgt
        FROM ecom_product_categories
        WHERE tenant_id = p_tenant_id AND rgt > 0; -- Chỉ xét các node chưa bị tag
    ELSE
        -- Nếu di chuyển vào node cha cụ thể, lấy lại RGT của cha mới
        -- vì nó có thể đã bị thay đổi ở Bước 4 nếu cha mới nằm bên phải node di chuyển.
        SELECT rgt
        INTO v_new_parent_rgt
        FROM ecom_product_categories
        WHERE category_id = p_new_parent_id AND tenant_id = p_tenant_id;
    END IF;

    -- === Bước 6: Tạo khoảng trống ở vị trí mới ===
    -- Chỉ thực hiện khi di chuyển vào node cha cụ thể.
    -- Khi di chuyển ra root, node được thêm vào cuối, không cần tạo khoảng trống ở giữa.
    IF NOT v_is_moving_to_root THEN
        -- Tăng giá trị lft và rgt của các node nằm bên phải (hoặc trùng) điểm chèn (RGT của cha mới)
        -- lên một khoảng bằng độ rộng của nhánh. Thao tác này tạo ra không gian để chèn nhánh vào.
        -- AND lft > 0 / rgt > 0 để đảm bảo không cập nhật các node đang bị tag âm.
        UPDATE ecom_product_categories
        SET lft = lft + v_node_width
        WHERE lft >= v_new_parent_rgt AND tenant_id = p_tenant_id AND lft > 0;

        UPDATE ecom_product_categories
        SET rgt = rgt + v_node_width
        WHERE rgt >= v_new_parent_rgt AND tenant_id = p_tenant_id AND rgt > 0;
    END IF;

    -- === Bước 7: Di chuyển nhánh đã tag vào vị trí mới (cập nhật lft/rgt/depth) ===
    -- Tính toán độ lệch (offset) cho lft/rgt và depth
    IF v_is_moving_to_root THEN
        -- LFT mới = max_rgt tìm được ở Bước 5 + 1. Offset = LFT mới - LFT cũ (đang âm)
        SET v_move_offset = (v_new_parent_rgt + 1) - v_node_lft;
        -- Depth mới của root là 0. Offset = Depth mới - Depth cũ
        SET v_depth_offset = 0 - v_node_depth;
    ELSE
        -- LFT mới = RGT của cha (đã cập nhật ở Bước 6). Offset = LFT mới - LFT cũ (đang âm)
        SET v_move_offset = v_new_parent_rgt - v_node_lft;
        -- Depth mới = Depth cha + 1. Offset = Depth mới - Depth cũ
        SET v_depth_offset = v_new_parent_depth + 1 - v_node_depth;
    END IF;

    -- Cập nhật lft, rgt, depth cho tất cả các node trong nhánh đã được tag (lft <= 0).
    -- Đổi lft/rgt từ âm thành dương và áp dụng offset. Áp dụng depth offset.
    UPDATE ecom_product_categories
    SET
        lft = 0 - lft + v_move_offset,
        rgt = 0 - rgt + v_move_offset,
        depth = depth + v_depth_offset
    WHERE lft <= 0 AND tenant_id = p_tenant_id;

    -- === Bước 8: Cập nhật parent_id và position cho node gốc của nhánh, đồng thời dịch chuyển position của các node anh em ===

    -- 8.1: Chuẩn hóa vị trí đích (target position)
    -- Xác định vị trí lớn nhất hiện tại của các node anh em mới
    -- (là các node gốc khác nếu di chuyển ra root, hoặc các con khác của cha mới).
    -- Loại trừ chính node đang di chuyển ra khỏi việc tìm max position này.
    IF v_is_moving_to_root THEN
        SELECT COALESCE(MAX(position), -1)
        INTO v_max_sibling_position
        FROM ecom_product_categories
        WHERE parent_id IS NULL
          AND tenant_id = p_tenant_id
          AND category_id != p_node_id_to_move;
    ELSE
        SELECT COALESCE(MAX(position), -1)
        INTO v_max_sibling_position
        FROM ecom_product_categories
        WHERE parent_id = p_new_parent_id
          AND tenant_id = p_tenant_id
          AND category_id != p_node_id_to_move;
    END IF;

    -- Đảm bảo vị trí đích không âm và không vượt quá vị trí cuối cùng + 1.
    IF p_target_position < 0 THEN
        SET v_actual_target_position = 0; -- Vị trí nhỏ nhất là 0
    ELSEIF p_target_position > v_max_sibling_position + 1 THEN
        -- Nếu vị trí yêu cầu quá lớn, đặt nó vào cuối danh sách anh em
        SET v_actual_target_position = v_max_sibling_position + 1;
    ELSE
        -- Vị trí yêu cầu hợp lệ
        SET v_actual_target_position = p_target_position;
    END IF;

    -- 8.2: Dịch chuyển vị trí của các node anh em hiện có để tạo khoảng trống
    -- Tăng position của các node anh em (cùng là root hoặc cùng cha mới)
    -- có vị trí lớn hơn hoặc bằng vị trí đích lên 1 đơn vị.
    -- Loại trừ chính node đang di chuyển ra khỏi cập nhật này.
    IF v_is_moving_to_root THEN
        UPDATE ecom_product_categories
        SET position = position + 1
        WHERE parent_id IS NULL
          AND tenant_id = p_tenant_id
          AND position >= v_actual_target_position
          AND category_id != p_node_id_to_move;
    ELSE
        UPDATE ecom_product_categories
        SET position = position + 1
        WHERE parent_id = p_new_parent_id
          AND tenant_id = p_tenant_id
          AND position >= v_actual_target_position
          AND category_id != p_node_id_to_move;
    END IF;

    -- 8.3: Cập nhật parent_id và position cho node được di chuyển
    -- Gán cha mới (có thể là NULL) và vị trí mới đã được chuẩn hóa.
    UPDATE ecom_product_categories
    SET
        parent_id = p_new_parent_id, -- Sẽ là NULL nếu p_new_parent_id là NULL
        position = v_actual_target_position
    WHERE category_id = p_node_id_to_move AND tenant_id = p_tenant_id;

    -- === Bước 9: Kết thúc Transaction ===
    -- Nếu tất cả các bước trên thành công không có lỗi, lưu thay đổi vào database.
    COMMIT;
END;