CREATE PROCEDURE `ecom_product_categories_move_node_sibling`(
    IN p_tenant_id INT,         -- ID của tenant hiện tại
    IN p_source_category_id INT, -- ID của category cần di chuyển (ví dụ: 9)
    IN p_target_category_id INT  -- ID của category làm mốc (ví dụ: 8, node 9 sẽ đứng TRƯỚC node 8)
)
proc_label: BEGIN
    -- B<PERSON><PERSON><PERSON> cục bộ
    DECLARE v_source_parent_id INT;
    DECLARE v_source_position INT;
    DECLARE v_target_parent_id INT;
    DECLARE v_target_position INT; -- Vị trí hiện tại của node đích, sẽ là vị trí mới của node nguồn

    -- === Khai báo handler để tự động ROLLBACK nếu có lỗi ===
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL; -- <PERSON><PERSON>i lại lỗi đã xảy ra
    END;

    -- === Bắt đầu Transaction ===
    START TRANSACTION;

    -- === Bước 1: Kiểm tra và lấy thông tin node nguồn ===
    SELECT parent_id, position
    INTO v_source_parent_id, v_source_position
    FROM ecom_product_categories
    WHERE category_id = p_source_category_id AND tenant_id = p_tenant_id
    FOR UPDATE; -- Khóa node nguồn

    IF v_source_position IS NULL THEN
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Source category not found.';
    END IF;

    -- === Bước 2: Kiểm tra và lấy thông tin node đích ===
    SELECT parent_id, position
    INTO v_target_parent_id, v_target_position
    FROM ecom_product_categories
    WHERE category_id = p_target_category_id AND tenant_id = p_tenant_id
    FOR UPDATE; -- Khóa node đích

    IF v_target_position IS NULL THEN
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Target category not found.';
    END IF;

    -- === Bước 3: Kiểm tra các điều kiện hợp lệ ===

    -- Không thể di chuyển một node tương đối với chính nó
    IF p_source_category_id = p_target_category_id THEN
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Source and target category cannot be the same.';
    END IF;

    -- Hai node phải là anh em (cùng cha, kể cả gốc)
    IF NOT (v_source_parent_id <=> v_target_parent_id) THEN -- Dùng <=> để so sánh NULL an toàn
        ROLLBACK;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Source and target categories must be siblings (same parent).';
    END IF;

    -- Nếu node nguồn đã ở ngay trước node đích thì không cần làm gì
    IF v_source_position = v_target_position - 1 THEN
         COMMIT; -- Không có thay đổi, commit transaction rỗng
         LEAVE proc_label; -- Thoát
    END IF;

    -- === Bước 4: Dịch chuyển các node anh em để tạo khoảng trống ===
    -- Tăng position của node đích và tất cả các node anh em có vị trí >= vị trí của node đích
    -- lên 1 đơn vị.
    UPDATE ecom_product_categories
    SET position = position + 1
    WHERE
        parent_id <=> v_source_parent_id -- Cùng cha với node nguồn (và node đích)
        AND tenant_id = p_tenant_id
        AND position >= v_target_position -- Từ vị trí của node đích trở đi
        AND category_id != p_source_category_id; -- !!! QUAN TRỌNG: Không tăng position của chính node nguồn nếu nó nằm trong khoảng này

    -- === Bước 5: Cập nhật vị trí cho node nguồn ===
    -- Đặt vị trí của node nguồn bằng vị trí *cũ* của node đích
    UPDATE ecom_product_categories
    SET position = v_target_position
    WHERE category_id = p_source_category_id AND tenant_id = p_tenant_id;

     -- === Bước 6: (Tùy chọn) Chuẩn hóa lại position nếu node nguồn bị kéo từ vị trí thấp hơn ===
     -- Sau 2 lệnh UPDATE trên, có thể có "lỗ hổng" trong dãy position nếu node nguồn
     -- ban đầu nằm ở vị trí < v_target_position. Cần chuẩn hóa lại.
     -- Cách đơn giản nhất là gọi lại procedure chuẩn hóa khác hoặc thực hiện logic chuẩn hóa ở đây.
     -- Ví dụ logic chuẩn hóa đơn giản (có thể tối ưu hơn):
     BEGIN
         DECLARE current_pos INT DEFAULT 0;
         DECLARE sibling_id INT;
         DECLARE done_normalize INT DEFAULT FALSE;
         DECLARE normalize_cursor CURSOR FOR
             SELECT category_id
             FROM ecom_product_categories
             WHERE parent_id <=> v_source_parent_id AND tenant_id = p_tenant_id
             ORDER BY position; -- Sắp xếp theo vị trí hiện tại (đã bị xáo trộn nhẹ)
         DECLARE CONTINUE HANDLER FOR NOT FOUND SET done_normalize = TRUE;

         OPEN normalize_cursor;
         normalize_loop: LOOP
             FETCH normalize_cursor INTO sibling_id;
             IF done_normalize THEN
                 LEAVE normalize_loop;
             END IF;
             UPDATE ecom_product_categories SET position = current_pos WHERE category_id = sibling_id AND tenant_id = p_tenant_id;
             SET current_pos = current_pos + 1;
         END LOOP normalize_loop;
         CLOSE normalize_cursor;
     END;


    -- === Bước 7: Kết thúc Transaction ===
    COMMIT;

END