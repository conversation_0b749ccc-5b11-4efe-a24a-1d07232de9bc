CREATE TABLE IF NOT EXISTS ecom_product_variant_attribute_values (
  value_id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT UNSIGNED NOT NULL,
  variant_id INT UNSIGNED NOT NULL,
  attribute_id INT UNSIGNED NOT NULL,
  value VARCHAR(255) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_variant_attribute (tenant_id, variant_id, attribute_id),
  KEY idx_tenant_variant (tenant_id, variant_id),
  KEY idx_tenant_attribute (tenant_id, attribute_id),
  CONSTRAINT fk_variant_attr_val_variant FOREIGN KEY (variant_id) REFERENCES ecom_product_variants(variant_id) ON DELETE CASCADE,
  CONSTRAINT fk_variant_attr_val_attribute FOREI<PERSON><PERSON>EY (attribute_id) REFERENCES ecom_product_attributes(attribute_id) ON DELETE CASCADE
); 