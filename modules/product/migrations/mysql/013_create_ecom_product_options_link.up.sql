CREATE TABLE IF NOT EXISTS ecom_product_options_link (
  product_option_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT 'ID duy nhất của liên kết',
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  product_id INT UNSIGNED NOT NULL COMMENT 'ID sản phẩm',
  group_id INT UNSIGNED NOT NULL COMMENT 'ID nhóm tùy chọn',
  display_order INT UNSIGNED DEFAULT 0 COMMENT 'Thứ tự hiển thị',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  UNIQUE KEY uk_product_option_group (tenant_id, product_id, group_id) COMMENT '<PERSON>ên kết sản phẩm-nhóm tùy chọn là duy nhất',
  INDEX idx_po_tenant (tenant_id) COMMENT 'Index theo tenant',
  INDEX idx_po_product (product_id) COMMENT 'Index theo sản phẩm',
  INDEX idx_po_group (group_id) COMMENT 'Index theo nhóm tùy chọn',
  FOREIGN KEY (product_id) REFERENCES ecom_products(product_id) ON DELETE CASCADE,
  FOREIGN KEY (group_id) REFERENCES ecom_product_option_groups(group_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Liên kết nhóm tùy chọn sản phẩm với sản phẩm'; 