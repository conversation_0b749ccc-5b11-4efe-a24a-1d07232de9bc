CREATE TABLE IF NOT EXISTS ecom_product_variant_attribute_values (
  value_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT 'ID duy nhất của giá trị thuộc tính biến thể',
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  variant_id INT UNSIGNED NOT NULL COMMENT 'ID biến thể',
  attribute_id INT UNSIGNED NOT NULL COMMENT 'ID thuộc tính',
  attribute_option_id INT UNSIGNED NOT NULL COMMENT 'ID giá trị tùy chọn thuộc tính',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  UNIQUE KEY uk_variant_attribute_option (tenant_id, variant_id, attribute_id, attribute_option_id) COMMENT 'Đả<PERSON> bảo kết hợp là duy nhất',
  UNIQUE KEY uk_variant_attribute (tenant_id, variant_id, attribute_id) COMMENT 'Mỗi thuộc tính chỉ có một giá trị cho biến thể',
  INDEX idx_vav_tenant (tenant_id) COMMENT 'Index theo tenant',
  INDEX idx_vav_variant (variant_id) COMMENT 'Index theo biến thể',
  INDEX idx_vav_attribute (attribute_id) COMMENT 'Index theo thuộc tính',
  INDEX idx_vav_option (attribute_option_id) COMMENT 'Index theo giá trị tùy chọn',
  FOREIGN KEY (variant_id) REFERENCES ecom_product_variants(variant_id) ON DELETE CASCADE,
  FOREIGN KEY (attribute_id) REFERENCES ecom_product_attributes(attribute_id) ON DELETE CASCADE,
  FOREIGN KEY (attribute_option_id) REFERENCES ecom_product_attribute_options(option_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Giá trị thuộc tính cho từng biến thể sản phẩm'; 