 CREATE TABLE IF NOT EXISTS ecom_product_attribute_values (
  value_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT 'ID duy nhất của giá trị thuộc tính',
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  reference_id INT UNSIGNED NOT NULL COMMENT 'ID tham chiếu đến product_id', -- <PERSON><PERSON><PERSON> định bảng này cho product, không phải variant
  reference_type ENUM('PRODUCT') NOT NULL DEFAULT 'PRODUCT' COMMENT 'Loại tham chiếu',
  attribute_id INT UNSIGNED NOT NULL COMMENT 'ID của thuộc tính',
  value_text TEXT NULL COMMENT 'Giá trị dạng text/string',
  value_numeric DECIMAL(18, 6) NULL COMMENT '<PERSON>i<PERSON> trị số (c<PERSON> số nguyên và thập phân)',
  value_date DATETIME NULL COMMENT '<PERSON><PERSON><PERSON> trị ngày tháng',
  value_boolean BOOLEAN NULL COMMENT '<PERSON>i<PERSON> trị boolean',
  attribute_option_id INT UNSIGNED NULL COMMENT 'ID tùy chọn thuộc tính (cho SELECT/MULTISELECT)',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  -- UNIQUE KEY uk_attribute_reference (tenant_id, reference_id, attribute_id, attribute_option_id) COMMENT 'Giá trị thuộc tính là duy nhất cho tham chiếu/thuộc tính',
  UNIQUE KEY uk_product_attribute_ref (tenant_id, reference_id, attribute_id, attribute_option_id) COMMENT 'Giá trị thuộc tính là duy nhất cho sản phẩm/thuộc tính/option (nếu có)',
  INDEX idx_pav_tenant (tenant_id) COMMENT 'Index theo tenant',
  INDEX idx_pav_reference (reference_id) COMMENT 'Index theo sản phẩm',
  INDEX idx_pav_attribute (attribute_id) COMMENT 'Index theo thuộc tính',
  INDEX idx_pav_option (attribute_option_id) COMMENT 'Index theo tùy chọn thuộc tính',
  FOREIGN KEY (reference_id) REFERENCES ecom_products(product_id) ON DELETE CASCADE,
  FOREIGN KEY (attribute_id) REFERENCES ecom_product_attributes(attribute_id) ON DELETE CASCADE,
  FOREIGN KEY (attribute_option_id) REFERENCES ecom_product_attribute_options(option_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Giá trị thuộc tính cho sản phẩm (không phải biến thể)';