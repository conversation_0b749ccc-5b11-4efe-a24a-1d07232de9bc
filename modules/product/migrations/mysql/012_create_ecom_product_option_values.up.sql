CREATE TABLE IF NOT EXISTS ecom_product_option_values (
  value_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT 'ID duy nhất của giá trị tùy chọn',
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  group_id INT UNSIGNED NOT NULL COMMENT 'ID nhóm tùy chọn',
  linked_variant_id INT UNSIGNED NULL COMMENT 'ID biến thể liên kết (nếu có, ví dụ option "Thêm thịt" có thể link tới variant riêng)',
  name VARCHAR(255) NOT NULL COMMENT 'Tên giá trị tùy chọn (ví dụ: Cay cấp 1)',
  description TEXT NULL COMMENT 'Mô tả chi tiết',
  image_url VARCHAR(255) NULL COMMENT 'URL hình ảnh minh họa',
  price_adjustment DECIMAL(18, 2) DEFAULT 0.00 COMMENT 'Đ<PERSON>ề<PERSON> chỉnh gi<PERSON> k<PERSON> chọn (cộng/trừ)',
  price_adjustment_type ENUM('FIXED', 'PERCENTAGE') DEFAULT 'FIXED' COMMENT 'Loại điều chỉnh giá',
  is_default BOOLEAN DEFAULT FALSE COMMENT 'Đây có phải là giá trị mặc định không',
  display_order INT UNSIGNED DEFAULT 0 COMMENT 'Thứ tự hiển thị',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  UNIQUE KEY uk_option_value_name (tenant_id, group_id, name) COMMENT 'Tên giá trị là duy nhất trong nhóm tùy chọn',
  INDEX idx_ov_tenant (tenant_id) COMMENT 'Index theo tenant',
  INDEX idx_ov_group (group_id) COMMENT 'Index theo nhóm tùy chọn',
  INDEX idx_ov_linked_variant (linked_variant_id) COMMENT 'Index theo biến thể liên kết',
  FOREIGN KEY (group_id) REFERENCES ecom_product_option_groups(group_id) ON DELETE CASCADE,
  FOREIGN KEY (linked_variant_id) REFERENCES ecom_product_variants(variant_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lựa chọn cụ thể trong một nhóm tùy chọn sản phẩm'; 