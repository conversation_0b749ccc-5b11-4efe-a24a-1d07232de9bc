CREATE TABLE IF NOT EXISTS ecom_product_attributes (
  attribute_id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  group_id INT UNSIGNED NULL COMMENT 'ID của nhóm thuộc tính',
  name VARCHAR(255) NOT NULL COMMENT 'Tên thuộ<PERSON> t<PERSON> (ví dụ: <PERSON><PERSON><PERSON>ư<PERSON>, <PERSON><PERSON><PERSON> sắ<PERSON>)',
  code VARCHAR(100) NOT NULL COMMENT 'Mã định danh nội bộ của thuộc tính',
  type ENUM('TEXT', 'TEXTAREA', 'NUMBER', 'BOOLEAN', 'DATE', 'DATETIME', 'SELECT', 'MULTISELECT', 'PRICE', 'IMAGE', 'FILE') NOT NULL COMMENT 'Kiểu dữ liệu của giá trị thuộc tính',
  unit VARCHAR(50) NULL COMMENT 'Đơn vị đo lường (nếu có)',
  validation_rules TEXT NULL COMMENT 'Quy tắc kiểm tra (JSON hoặc chuỗi, ví dụ: min, max, regex)',
  is_configurable BOOLEAN DEFAULT FALSE COMMENT 'Thuộc tính này có dùng để tạo biến thể không',
  is_filterable BOOLEAN DEFAULT FALSE COMMENT 'Có cho phép lọc sản phẩm theo thuộc tính này không',
  is_searchable BOOLEAN DEFAULT FALSE COMMENT 'Có cho phép tìm kiếm theo thuộc tính này không',
  is_comparable BOOLEAN DEFAULT FALSE COMMENT 'Có cho phép so sánh sản phẩm dựa trên thuộc tính này không',
  is_required BOOLEAN DEFAULT FALSE COMMENT 'Có bắt buộc nhập cho sản phẩm/biến thể không',
  frontend_input VARCHAR(50) NULL COMMENT 'Gợi ý loại input control ở frontend',
  display_order INT UNSIGNED DEFAULT 0 COMMENT 'Thứ tự hiển thị',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  UNIQUE KEY uk_attribute_code_tenant (tenant_id, code) COMMENT 'Mã thuộc tính là duy nhất trong tenant',
  INDEX idx_attribute_tenant (tenant_id) COMMENT 'Index theo tenant',
  INDEX idx_attribute_group (group_id) COMMENT 'Index theo nhóm thuộc tính',
  INDEX idx_attribute_configurable (is_configurable) COMMENT 'Index theo thuộc tính có thể cấu hình',
  FOREIGN KEY (group_id) REFERENCES ecom_product_attribute_groups(group_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Định nghĩa các thuộc tính sản phẩm/biến thể'; 