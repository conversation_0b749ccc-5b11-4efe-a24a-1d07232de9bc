CREATE TABLE IF NOT EXISTS ecom_product_attribute_groups (
  group_id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  name VARCHAR(255) NOT NULL COMMENT 'Tên nhóm thuộc tính',
  code VARCHAR(100) NOT NULL COMMENT 'Mã định danh nội bộ của nhóm thuộc tính',
  display_order INT UNSIGNED DEFAULT 0 COMMENT 'Thứ tự hiển thị',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  UNIQUE KEY uk_attribute_group_code_tenant (tenant_id, code) COMMENT 'Mã nhóm là duy nhất trong tenant',
  INDEX idx_attribute_group_tenant (tenant_id) COMMENT 'Index theo tenant'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Nhóm các định nghĩa thuộc tính sản phẩm'; 