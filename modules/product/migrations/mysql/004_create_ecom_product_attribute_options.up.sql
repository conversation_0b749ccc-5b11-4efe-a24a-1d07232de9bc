CREATE TABLE IF NOT EXISTS ecom_product_attribute_options (
  option_id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  attribute_id INT UNSIGNED NOT NULL COMMENT 'ID của thuộc tính',
  value VARCHAR(255) NOT NULL COMMENT 'Giá trị cụ thể của tùy chọn (ví dụ: Đỏ, Xanh)',
  label VARCHAR(255) NULL COMMENT 'Nhãn hiển thị (nếu khác value)',
  swatch_type ENUM('COLOR', 'IMAGE', 'TEXT') NULL COMMENT 'Loại swatch (nếu có)',
  swatch_value VARCHAR(255) NULL COMMENT 'Giá trị swatch (mã màu, URL ảnh)',
  display_order INT UNSIGNED DEFAULT 0 COMMENT 'Thứ tự hiển thị',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  UNIQUE KEY uk_attribute_option_value (tenant_id, attribute_id, value) COMMENT 'Giá trị là duy nhất cho thuộc tính trong tenant',
  INDEX idx_attribute_option_tenant (tenant_id) COMMENT 'Index theo tenant',
  INDEX idx_attribute_option_attribute (attribute_id) COMMENT 'Index theo thuộc tính',
  FOREIGN KEY (attribute_id) REFERENCES ecom_product_attributes(attribute_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Giá trị lựa chọn cho thuộc tính sản phẩm SELECT/MULTISELECT'; 