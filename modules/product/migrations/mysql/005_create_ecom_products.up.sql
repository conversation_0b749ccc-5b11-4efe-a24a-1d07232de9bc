CREATE TABLE IF NOT EXISTS ecom_products (
  product_id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  category_id INT UNSIGNED NULL COMMENT 'ID của danh mục sản phẩm chính',
  name VARCHAR(255) NOT NULL COMMENT 'Tên sản phẩm',
  description TEXT NULL COMMENT 'Mô tả ngắn về sản phẩm',
  content LONGTEXT NULL COMMENT 'Nội dung chi tiết về sản phẩm (HTML, Markdown...)',
  slug VARCHAR(255) NOT NULL COMMENT 'Slug (đường dẫn thân thiện) của sản phẩm',
  image_url VARCHAR(255) NULL COMMENT 'URL hình ảnh chính của sản phẩm',
  base_price DECIMAL(18, 2) NOT NULL DEFAULT 0 COMMENT '<PERSON><PERSON><PERSON> c<PERSON> bản của sản phẩm',
  cost_price DECIMAL(18, 2) NULL COMMENT 'Giá vốn của sản phẩm',
  product_type ENUM('SIMPLE', 'CONFIGURABLE', 'BUNDLE', 'VIRTUAL', 'DOWNLOADABLE', 'COMPOSITE', 'GIFT_CARD', 'GROUPED') DEFAULT 'SIMPLE' COMMENT 'Loại sản phẩm',
  status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED', 'PENDING_APPROVAL') DEFAULT 'DRAFT' COMMENT 'Trạng thái xuất bản của sản phẩm',
  product_code VARCHAR(255) NULL COMMENT 'Mã sản phẩm nội bộ',
  is_taxable BOOLEAN DEFAULT TRUE COMMENT 'Sản phẩm có chịu thuế không',
  tax_class_id INT UNSIGNED NULL COMMENT 'ID lớp thuế',
  is_virtual BOOLEAN DEFAULT FALSE COMMENT 'Sản phẩm ảo (không cần vận chuyển)',
  is_downloadable BOOLEAN DEFAULT FALSE COMMENT 'Sản phẩm có thể tải xuống',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  created_by INT UNSIGNED NULL COMMENT 'ID người dùng tạo bản ghi',
  updated_by INT UNSIGNED NULL COMMENT 'ID người dùng cập nhật bản ghi',
  UNIQUE KEY uk_product_slug_tenant (tenant_id, slug) COMMENT 'Slug là duy nhất trong tenant',
  -- UNIQUE KEY uk_product_code_tenant (tenant_id, product_code) COMMENT 'Mã sản phẩm là duy nhất trong tenant',
  INDEX idx_product_tenant (tenant_id) COMMENT 'Index theo tenant',
  INDEX idx_product_category (category_id) COMMENT 'Index theo danh mục',
  INDEX idx_product_status (status) COMMENT 'Index theo trạng thái',
  INDEX idx_product_type (product_type) COMMENT 'Index theo loại sản phẩm',
  FOREIGN KEY (category_id) REFERENCES ecom_product_categories(category_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lưu trữ thông tin cơ bản về sản phẩm'; 