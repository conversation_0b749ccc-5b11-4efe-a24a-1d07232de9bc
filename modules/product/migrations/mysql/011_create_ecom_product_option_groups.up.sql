CREATE TABLE IF NOT EXISTS ecom_product_option_groups (
  group_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT 'ID duy nhất của nhóm tùy chọn',
  tenant_id INT UNSIGNED NOT NULL COMMENT 'ID của người thuê (tenant)',
  name VARCHAR(255) NOT NULL COMMENT 'Tên nhóm tùy chọn (ví dụ: <PERSON><PERSON> cay, <PERSON><PERSON><PERSON> nước dùng)',
  description TEXT NULL COMMENT 'Mô tả nhóm tùy chọn',
  type ENUM('RADIO', 'CHECKBOX', 'SELECT', 'TEXT_INPUT', 'FILE_UPLOAD') NOT NULL DEFAULT 'RADIO' COMMENT 'Kiểu chọn',
  required BOOLEAN DEFAULT FALSE COMMENT 'Bắt buộc chọn không',
  min_select INT UNSIGNED DEFAULT 0 COMMENT '<PERSON>ố lượng tối thiểu phải chọn',
  max_select INT UNSIGNED DEFAULT 1 COMMENT 'S<PERSON> lượng tối đa được chọn',
  display_order INT UNSIGNED DEFAULT 0 COMMENT 'Thứ tự hiển thị',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Thời điểm tạo bản ghi',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Thời điểm cập nhật bản ghi gần nhất',
  INDEX idx_og_tenant (tenant_id) COMMENT 'Index theo tenant'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Nhóm tùy chọn khách hàng thêm vào sản phẩm'; 