package product

import (
	"fmt"
	"log"

	"wnapi/internal/core"
	"wnapi/modules/product/internal"

	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

// BootstrapProductModule khởi tạo module product độc lập (dùng cho local/test)
func BootstrapProductModule(server *core.Server) (*Module, error) {
	cfg, err := internal.LoadProductConfig()
	if err != nil {
		return nil, fmt.Errorf("Không thể load config product: %w", err)
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
		cfg.DBUser,
		cfg.DBPassword,
		cfg.DBHost,
		cfg.DBPort,
		cfg.DBName,
	)

	sqlxdb, err := sqlx.Connect("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("Không thể kết nối DB: %w", err)
	}

	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxdb.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("Không thể khởi tạo GORM: %w", err)
	}

	m := &Module{
		DB:     sqlxdb,
		GormDB: gormDB,
		Config: cfg,
	}

	if server != nil {
		if err := m.RegisterRoutes(server); err != nil {
			return nil, fmt.Errorf("Không thể đăng ký routes cho product: %w", err)
		}
	}

	log.Printf("[product] Bootstrap thành công với DB %s@%s:%d/%s", cfg.DBUser, cfg.DBHost, cfg.DBPort, cfg.DBName)
	return m, nil
}
