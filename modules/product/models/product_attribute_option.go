package models

import (
	"time"
)

// SwatchType định nghĩa các loại hiển thị swatch cho thuộc tính
// Swatch là cách hiển thị trực quan cho các tùy chọn thuộc tính
// - COLOR: Hi<PERSON><PERSON> thị màu sắc (giá trị là mã màu HEX, ví dụ: #FF0000)
// - IMAGE: Hiển thị hình ảnh (giá trị là URL hình ảnh)
// - TEXT: Hiển thị văn bản (giá trị là chuỗi văn bản)
type SwatchType string

const (
	SwatchTypeColor SwatchType = "COLOR" // Hiển thị màu sắc
	SwatchTypeImage SwatchType = "IMAGE" // Hiển thị hình ảnh
	SwatchTypeText  SwatchType = "TEXT"  // Hiển thị văn bản
)

// ProductAttributeOption đại diện cho tùy chọn của thuộc tính sản phẩm
// Đ<PERSON><PERSON>c sử dụng cho các thuộc tính có kiểu SELECT hoặc MULTISELECT
// Ví dụ:
//
//	{
//	  "option_id": 1,
//	  "tenant_id": 1,
//	  "attribute_id": 2,
//	  "value": "red",
//	  "label": "Đỏ",
//	  "swatch_type": "COLOR",
//	  "swatch_value": "#FF0000",
//	  "display_order": 1
//	}
type ProductAttributeOption struct {
	OptionID     uint        `db:"option_id" json:"option_id" gorm:"primaryKey"`        // ID của tùy chọn thuộc tính
	TenantID     uint        `db:"tenant_id" json:"tenant_id"`                          // ID của tenant (cửa hàng)
	AttributeID  uint        `db:"attribute_id" json:"attribute_id"`                    // ID của thuộc tính mà tùy chọn này thuộc về
	Value        string      `db:"value" json:"value"`                                  // Giá trị của tùy chọn (dùng trong code)
	Label        *string     `db:"label" json:"label"`                                  // Nhãn hiển thị cho người dùng
	SwatchType   *SwatchType `db:"swatch_type" json:"swatch_type"`                      // Loại hiển thị swatch (COLOR, IMAGE, TEXT)
	SwatchValue  *string     `db:"swatch_value" json:"swatch_value"`                    // Giá trị swatch (mã màu, URL hình ảnh, văn bản)
	DisplayOrder uint        `db:"display_order" json:"display_order" gorm:"default:0"` // Thứ tự hiển thị
	CreatedAt    time.Time   `db:"created_at" json:"created_at"`                        // Thời gian tạo tùy chọn
	UpdatedAt    time.Time   `db:"updated_at" json:"updated_at"`                        // Thời gian cập nhật tùy chọn gần nhất
}

// TableName chỉ định tên bảng trong cơ sở dữ liệu
func (ProductAttributeOption) TableName() string {
	return "ecom_product_attribute_options"
}
