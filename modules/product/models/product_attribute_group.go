package models

import (
	"time"
)

// ProductAttributeGroup đại diện cho một nhóm các thuộc tính sản phẩm
// Nhóm thuộc tính giúp phân loại và tổ chức các thuộc tính sản phẩm theo chức năng hoặc mục đích sử dụng
// Ví dụ:
//
//	{
//	  "group_id": 1,
//	  "tenant_id": 1,
//	  "name": "Thông số kỹ thuật",
//	  "code": "specifications",
//	  "display_order": 1
//	}
type ProductAttributeGroup struct {
	GroupID      uint      `db:"group_id" json:"group_id" gorm:"primaryKey"`          // ID của nhóm thuộc tính
	TenantID     uint      `db:"tenant_id" json:"tenant_id"`                          // ID của tenant (cửa hàng)
	Name         string    `db:"name" json:"name"`                                    // Tên nhóm thuộc tính
	Code         string    `db:"code" json:"code"`                                    // Mã định danh của nhóm (dùng trong code)
	DisplayOrder uint      `db:"display_order" json:"display_order" gorm:"default:0"` // Thứ tự hiển thị
	CreatedAt    time.Time `db:"created_at" json:"created_at"`                        // Thời gian tạo nhóm thuộc tính
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`                        // Thời gian cập nhật nhóm thuộc tính gần nhất
}

// TableName chỉ định tên bảng trong cơ sở dữ liệu
func (ProductAttributeGroup) TableName() string {
	return "ecom_product_attribute_groups"
}
