package models

import (
	"time"
)

// ProductAttribute đại diện cho định ngh<PERSON>a thuộc t<PERSON>h sản phẩm
type ProductAttribute struct {
	AttributeID     uint      `db:"attribute_id" json:"attribute_id" gorm:"primaryKey"`
	TenantID        uint      `db:"tenant_id" json:"tenant_id"`
	GroupID         *uint     `db:"group_id" json:"group_id"`
	Name            string    `db:"name" json:"name"`
	Code            string    `db:"code" json:"code"`
	Type            string    `db:"type" json:"type"`
	Unit            *string   `db:"unit" json:"unit"`
	ValidationRules *string   `db:"validation_rules" json:"validation_rules"`
	IsConfigurable  bool      `db:"is_configurable" json:"is_configurable"`
	IsFilterable    bool      `db:"is_filterable" json:"is_filterable"`
	IsSearchable    bool      `db:"is_searchable" json:"is_searchable"`
	IsComparable    bool      `db:"is_comparable" json:"is_comparable"`
	IsRequired      bool      `db:"is_required" json:"is_required"`
	FrontendInput   *string   `db:"frontend_input" json:"frontend_input"`
	DisplayOrder    uint      `db:"display_order" json:"display_order" gorm:"default:0"`
	CreatedAt       time.Time `db:"created_at" json:"created_at"`
	UpdatedAt       time.Time `db:"updated_at" json:"updated_at"`
}

// TableName chỉ định tên bảng trong cơ sở dữ liệu
func (ProductAttribute) TableName() string {
	return "ecom_product_attributes"
}
