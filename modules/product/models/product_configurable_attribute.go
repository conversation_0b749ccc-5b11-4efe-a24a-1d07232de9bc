package models

import (
	"time"
)

// ProductConfigurableAttribute đại diện cho thuộc tính cấu hình dùng để tạo biến thể sản phẩm
// X<PERSON><PERSON> định thuộc tính nào được sử dụng để tạo các biến thể cho sản phẩm có thể cấu hình
// Ví dụ:
//
//	{
//	  "configurable_attribute_id": 1,
//	  "tenant_id": 1,
//	  "product_id": 1,
//	  "attribute_id": 2,
//	  "position": 1
//	}
type ProductConfigurableAttribute struct {
	ConfigurableAttributeID uint      `db:"configurable_attribute_id" json:"configurable_attribute_id" gorm:"primaryKey"` // ID của thuộc tính cấu hình
	TenantID                uint      `db:"tenant_id" json:"tenant_id"`                                                   // ID của tenant (cửa hàng)
	ProductID               uint      `db:"product_id" json:"product_id"`                                                 // ID của sản phẩm
	AttributeID             uint      `db:"attribute_id" json:"attribute_id"`                                             // ID của thuộc tính
	<PERSON>sition                uint      `db:"position" json:"position"`                                                     // Vị trí hiển thị của thuộc tính
	CreatedAt               time.Time `db:"created_at" json:"created_at"`                                                 // Thời gian tạo
	UpdatedAt               time.Time `db:"updated_at" json:"updated_at"`                                                 // Thời gian cập nhật gần nhất
}

// TableName chỉ định tên bảng trong cơ sở dữ liệu
func (ProductConfigurableAttribute) TableName() string {
	return "ecom_product_configurable_attributes"
}
