package models

import (
	"time"
)

// ProductVariantAttributeValue đại diện cho một giá trị thuộc tính của biến thể sản phẩm
// Mỗi biến thể sản phẩm có thể có nhiều giá trị thuộc tính khác nhau
// Ví dụ:
//
//	{
//	  "value_id": 1,
//	  "tenant_id": 1,
//	  "variant_id": 1,
//	  "attribute_id": 2,
//	  "value": "red"
//	}
type ProductVariantAttributeValue struct {
	ValueID     uint      `gorm:"column:value_id;primaryKey;autoIncrement"` // ID của giá trị thuộc tính
	TenantID    uint      `gorm:"column:tenant_id;not null"`                // ID của tenant (cửa hàng)
	VariantID   uint      `gorm:"column:variant_id;not null"`               // ID của biến thể sản phẩm
	AttributeID uint      `gorm:"column:attribute_id;not null"`             // ID của thuộc tính
	Value       string    `gorm:"column:value;not null"`                    // Giá trị của thuộc tính
	CreatedAt   time.Time `gorm:"column:created_at;not null"`               // Thời gian tạo
	UpdatedAt   time.Time `gorm:"column:updated_at;not null"`               // Thời gian cập nhật gần nhất
}

// TableName trả về tên bảng trong cơ sở dữ liệu
func (ProductVariantAttributeValue) TableName() string {
	return "ecom_product_variant_attribute_values"
}
