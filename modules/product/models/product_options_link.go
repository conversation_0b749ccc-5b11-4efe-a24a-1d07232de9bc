package models

import (
	"time"
)

// ProductOptionsLink đại diện cho liên kết giữa sản phẩm và nhóm tùy chọn
// Bảng này thiết lập mối quan hệ nhiều-nhiều giữa sản phẩm và nhóm tùy chọn
// Ví dụ: Một sản phẩm pizza có thể có nhiều nhóm tùy chọn như "Topping", "Đế bánh", "Sốt"...
// Ví dụ:
//
//	{
//	  "product_option_id": 1,
//	  "tenant_id": 1,
//	  "product_id": 1,
//	  "group_id": 1,
//	  "display_order": 1
//	}
type ProductOptionsLink struct {
	ProductOptionID uint      `db:"product_option_id" json:"product_option_id" gorm:"primaryKey"` // ID của liên kết
	TenantID        uint      `db:"tenant_id" json:"tenant_id"`                                   // ID của tenant (cửa hàng)
	ProductID       uint      `db:"product_id" json:"product_id"`                                 // ID của sản phẩm
	GroupID         uint      `db:"group_id" json:"group_id"`                                     // ID của nhóm tùy chọn
	DisplayOrder    uint      `db:"display_order" json:"display_order"`                           // Thứ tự hiển thị của nhóm tùy chọn trong sản phẩm
	CreatedAt       time.Time `db:"created_at" json:"created_at"`                                 // Thời gian tạo liên kết
	UpdatedAt       time.Time `db:"updated_at" json:"updated_at"`                                 // Thời gian cập nhật liên kết gần nhất
}

// TableName trả về tên bảng trong database
func (ProductOptionsLink) TableName() string {
	return "ecom_product_options_link"
}
