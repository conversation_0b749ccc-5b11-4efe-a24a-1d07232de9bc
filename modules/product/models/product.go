package models

import (
	"time"
)

// Product đại diện cho một sản phẩm trong hệ thống thương mại điện tử
// Ví dụ:
//
//	{
//	  "product_id": 1,
//	  "tenant_id": 1,
//	  "category_id": 5,
//	  "name": "<PERSON>o thun nam cổ tròn",
//	  "description": "Áo thun nam cổ tròn chất liệu cotton",
//	  "content": "<p><PERSON>o thun nam cổ tròn chất liệu cotton 100%, tho<PERSON><PERSON> mát, thấm hút mồ hôi tốt.</p>",
//	  "slug": "ao-thun-nam-co-tron",
//	  "image_url": "https://example.com/images/ao-thun-nam.jpg",
//	  "base_price": 250000,
//	  "cost_price": 150000,
//	  "product_type": "CONFIGURABLE",
//	  "status": "PUBLISHED",
//	  "product_code": "ATN001",
//	  "is_taxable": true,
//	  "is_virtual": false,
//	  "is_downloadable": false
//	}
type Product struct {
	ProductID      uint      `db:"product_id" json:"product_id" gorm:"primaryKey"`                                                                                                                  // ID của sản phẩm
	TenantID       uint      `db:"tenant_id" json:"tenant_id"`                                                                                                                                      // ID của tenant (cửa hàng)
	CategoryID     *uint     `db:"category_id" json:"category_id"`                                                                                                                                  // ID của danh mục sản phẩm
	Name           string    `db:"name" json:"name"`                                                                                                                                                // Tên sản phẩm
	Description    string    `db:"description" json:"description"`                                                                                                                                  // Mô tả ngắn về sản phẩm
	Content        string    `db:"content" json:"content"`                                                                                                                                          // Nội dung chi tiết về sản phẩm (HTML)
	Slug           string    `db:"slug" json:"slug"`                                                                                                                                                // Đường dẫn URL thân thiện
	ImageURL       string    `db:"image_url" json:"image_url"`                                                                                                                                      // URL hình ảnh chính của sản phẩm
	BasePrice      float64   `db:"base_price" json:"base_price"`                                                                                                                                    // Giá bán của sản phẩm
	CostPrice      *float64  `db:"cost_price" json:"cost_price"`                                                                                                                                    // Giá vốn của sản phẩm
	ProductType    string    `db:"product_type" json:"product_type" gorm:"type:enum('SIMPLE','CONFIGURABLE','BUNDLE','VIRTUAL','DOWNLOADABLE','COMPOSITE','GIFT_CARD','GROUPED');default:'SIMPLE'"` // Loại sản phẩm: đơn giản, có thể cấu hình, gói, ảo, tải xuống, tổng hợp, thẻ quà tặng, nhóm
	Status         string    `db:"status" json:"status" gorm:"type:enum('DRAFT','PUBLISHED','ARCHIVED','PENDING_APPROVAL');default:'DRAFT'"`                                                        // Trạng thái sản phẩm: nháp, đã xuất bản, đã lưu trữ, chờ phê duyệt
	ProductCode    string    `db:"product_code" json:"product_code"`                                                                                                                                // Mã sản phẩm nội bộ
	IsTaxable      bool      `db:"is_taxable" json:"is_taxable" gorm:"default:true"`                                                                                                                // Sản phẩm có chịu thuế hay không
	TaxClassID     *uint     `db:"tax_class_id" json:"tax_class_id"`                                                                                                                                // ID của lớp thuế áp dụng
	IsVirtual      bool      `db:"is_virtual" json:"is_virtual" gorm:"default:false"`                                                                                                               // Sản phẩm có phải là sản phẩm ảo không (không cần vận chuyển)
	IsDownloadable bool      `db:"is_downloadable" json:"is_downloadable" gorm:"default:false"`                                                                                                     // Sản phẩm có thể tải xuống hay không
	CreatedAt      time.Time `db:"created_at" json:"created_at"`                                                                                                                                    // Thời gian tạo sản phẩm
	UpdatedAt      time.Time `db:"updated_at" json:"updated_at"`                                                                                                                                    // Thời gian cập nhật sản phẩm gần nhất
	CreatedBy      *uint     `db:"created_by" json:"created_by"`                                                                                                                                    // ID của người tạo sản phẩm
	UpdatedBy      *uint     `db:"updated_by" json:"updated_by"`                                                                                                                                    // ID của người cập nhật sản phẩm gần nhất
}

// TableName chỉ định tên bảng trong cơ sở dữ liệu
func (Product) TableName() string {
	return "ecom_products"
}
