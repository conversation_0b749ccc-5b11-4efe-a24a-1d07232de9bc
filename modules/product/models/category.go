package models

import (
	"time"
)

// Category đại diện cho một danh mục sản phẩm sử dụng mô hình Nested Set
// Mô hình Nested Set giúp lưu trữ cấu trúc cây phân cấp hiệu quả trong cơ sở dữ liệu quan hệ
// Ví dụ:
//
//	{
//	  "category_id": 5,
//	  "tenant_id": 1,
//	  "parent_id": 2,
//	  "name": "Áo thun nam",
//	  "slug": "ao-thun-nam",
//	  "description": "Các loại áo thun dành cho nam giới",
//	  "featured_image": "https://example.com/images/ao-thun-nam-category.jpg",
//	  "lft": 4,
//	  "rgt": 9,
//	  "depth": 2,
//	  "position": 1,
//	  "is_active": true,
//	  "is_featured": true,
//	  "meta_title": "<PERSON>o thun nam chính hãng giá tốt",
//	  "meta_description": "<PERSON>a áo thun nam chính hãng với giá tốt nhất tại cửa hàng chúng tôi",
//	  "children": [
//	    {
//	      "category_id": 6,
//	      "name": "Áo thun nam cổ tròn",
//	      "lft": 5,
//	      "rgt": 6,
//	      "depth": 3
//	    },
//	    {
//	      "category_id": 7,
//	      "name": "Áo thun nam cổ tim",
//	      "lft": 7,
//	      "rgt": 8,
//	      "depth": 3
//	    }
//	  ],
//	  "product_count": 25
//	}
type Category struct {
	CategoryID      uint      `db:"category_id" json:"category_id" gorm:"primaryKey"` // ID của danh mục
	TenantID        uint      `db:"tenant_id" json:"tenant_id"`                       // ID của tenant (cửa hàng)
	ParentID        *uint     `db:"parent_id" json:"parent_id"`                       // ID của danh mục cha (NULL nếu là danh mục gốc)
	Name            string    `db:"name" json:"name"`                                 // Tên danh mục
	Slug            string    `db:"slug" json:"slug"`                                 // Đường dẫn URL thân thiện
	Description     string    `db:"description" json:"description"`                   // Mô tả về danh mục
	FeaturedImage   string    `db:"featured_image" json:"featured_image"`             // URL hình ảnh đại diện
	Left            int       `db:"lft" json:"lft"`                                   // Giá trị bên trái trong mô hình Nested Set
	Right           int       `db:"rgt" json:"rgt"`                                   // Giá trị bên phải trong mô hình Nested Set
	Depth           int       `db:"depth" json:"depth"`                               // Độ sâu trong cây danh mục
	Position        int       `db:"position" json:"position"`                         // Vị trí trong cùng cấp (giữa các danh mục cùng cha)
	IsActive        bool      `db:"is_active" json:"is_active"`                       // Trạng thái hoạt động của danh mục
	IsFeatured      bool      `db:"is_featured" json:"is_featured"`                   // Danh mục có được đánh dấu nổi bật hay không
	MetaTitle       string    `db:"meta_title" json:"meta_title"`                     // Tiêu đề meta cho SEO
	MetaDescription string    `db:"meta_description" json:"meta_description"`         // Mô tả meta cho SEO
	CreatedAt       time.Time `db:"created_at" json:"created_at"`                     // Thời gian tạo danh mục
	UpdatedAt       time.Time `db:"updated_at" json:"updated_at"`                     // Thời gian cập nhật danh mục gần nhất
	CreatedBy       uint      `db:"created_by" json:"created_by"`                     // ID của người tạo danh mục
	UpdatedBy       uint      `db:"updated_by" json:"updated_by"`                     // ID của người cập nhật danh mục gần nhất

	// Các trường ảo (không lưu trong DB)
	Children     []*Category `db:"-" json:"children,omitempty"`      // Danh sách các danh mục con (để hiển thị dạng cây)
	ProductCount int         `db:"-" json:"product_count,omitempty"` // Số lượng sản phẩm trong danh mục
}

// TableName chỉ định tên bảng trong cơ sở dữ liệu
func (Category) TableName() string {
	return "ecom_product_categories"
}
