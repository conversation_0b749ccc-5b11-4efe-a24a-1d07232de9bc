package models

import (
	"time"
)

// ProductVariant đại diện cho biến thể sản phẩm
// Bi<PERSON><PERSON> thể sản phẩm là các phiên bản khác nhau của cùng một sản phẩm, kh<PERSON><PERSON> nhau về một số thuộc tính như mà<PERSON> sắ<PERSON>, kích thước...
// Ví dụ:
//
//	{
//	  "variant_id": 1,
//	  "tenant_id": 1,
//	  "product_id": 1,
//	  "sku": "ATN001-RED-L",
//	  "price": 250000,
//	  "cost_price": 150000,
//	  "image_url": "https://example.com/images/ao-thun-nam-do-l.jpg",
//	  "is_active": true
//	}
type ProductVariant struct {
	VariantID uint      `db:"variant_id" json:"variant_id" gorm:"primaryKey"` // ID của biến thể sản phẩm
	TenantID  uint      `db:"tenant_id" json:"tenant_id"`                     // ID của tenant (cử<PERSON> hàng)
	ProductID uint      `db:"product_id" json:"product_id"`                   // ID của sản phẩm chính
	SKU       string    `db:"sku" json:"sku"`                                 // Mã SKU (Stock Keeping Unit) của biến thể
	Price     *float64  `db:"price" json:"price"`                             // Giá bán của biến thể (nếu NULL, sẽ sử dụng giá của sản phẩm chính)
	CostPrice *float64  `db:"cost_price" json:"cost_price"`                   // Giá vốn của biến thể
	ImageURL  *string   `db:"image_url" json:"image_url"`                     // URL hình ảnh của biến thể
	IsActive  bool      `db:"is_active" json:"is_active"`                     // Trạng thái hoạt động của biến thể
	CreatedAt time.Time `db:"created_at" json:"created_at"`                   // Thời gian tạo biến thể
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`                   // Thời gian cập nhật biến thể gần nhất
}

// TableName chỉ định tên bảng trong cơ sở dữ liệu
func (ProductVariant) TableName() string {
	return "ecom_product_variants"
}
