package models

import (
	"time"
)

// ProductOptionGroup đại diện cho nhóm tùy chọn sản phẩm (tùy chọn khách hàng)
// Nhóm tùy chọn sản phẩm cho phép khách hàng tùy chỉnh sản phẩm khi mua hàng
// Ví dụ: Nhóm tùy chọn "Topping" cho pizza, "Độ cay" cho món ăn, "Kích thước" cho đồ uống...
// Ví dụ:
//
//	{
//	  "group_id": 1,
//	  "tenant_id": 1,
//	  "name": "Topping cho pizza",
//	  "description": "Chọn topping bổ sung cho pizza của bạn",
//	  "type": "CHECKBOX",
//	  "required": false,
//	  "min_select": 0,
//	  "max_select": 5,
//	  "display_order": 1
//	}
type ProductOptionGroup struct {
	GroupID      uint      `db:"group_id" json:"group_id" gorm:"primaryKey"` // ID của nhóm tùy chọn
	TenantID     uint      `db:"tenant_id" json:"tenant_id"`                 // ID của tenant (cửa hàng)
	Name         string    `db:"name" json:"name"`                           // Tên nhóm tùy chọn
	Description  *string   `db:"description" json:"description"`             // Mô tả về nhóm tùy chọn
	Type         string    `db:"type" json:"type"`                           // Loại tùy chọn: RADIO (chọn một), CHECKBOX (chọn nhiều), SELECT (dropdown)...
	Required     bool      `db:"required" json:"required"`                   // Khách hàng có bắt buộc phải chọn không
	MinSelect    uint      `db:"min_select" json:"min_select"`               // Số lượng tối thiểu phải chọn
	MaxSelect    uint      `db:"max_select" json:"max_select"`               // Số lượng tối đa được chọn
	DisplayOrder uint      `db:"display_order" json:"display_order"`         // Thứ tự hiển thị
	CreatedAt    time.Time `db:"created_at" json:"created_at"`               // Thời gian tạo
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`               // Thời gian cập nhật gần nhất
}

// TableName trả về tên bảng trong database
func (ProductOptionGroup) TableName() string {
	return "ecom_product_option_groups"
}
