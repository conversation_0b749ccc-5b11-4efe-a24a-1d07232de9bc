package models

import (
	"time"
)

// ProductOptionValue đại diện cho giá trị tùy chọn sản phẩm
// Mỗi nhóm tùy chọn sản phẩm có thể có nhiều giá trị tùy chọn
// Ví dụ: Nhóm tùy chọn "Topping" có thể có các giá trị như "Phô mai", "Nấm", "Thịt xông khói"...
// Ví dụ:
//
//	{
//	  "value_id": 1,
//	  "tenant_id": 1,
//	  "group_id": 1,
//	  "linked_variant_id": null,
//	  "name": "Phô mai",
//	  "description": "Phô mai Mozzarella",
//	  "image_url": "https://example.com/images/pho-mai.jpg",
//	  "price_adjustment": 15000,
//	  "price_adjustment_type": "FIXED",
//	  "is_default": false,
//	  "display_order": 1
//	}
type ProductOptionValue struct {
	ValueID             uint      `db:"value_id" json:"value_id" gorm:"primaryKey"`         // ID của giá trị tùy chọn
	TenantID            uint      `db:"tenant_id" json:"tenant_id"`                         // ID của tenant (cửa hàng)
	GroupID             uint      `db:"group_id" json:"group_id"`                           // ID của nhóm tùy chọn
	LinkedVariantID     *uint     `db:"linked_variant_id" json:"linked_variant_id"`         // ID của biến thể sản phẩm liên kết (nếu có)
	Name                string    `db:"name" json:"name"`                                   // Tên giá trị tùy chọn
	Description         *string   `db:"description" json:"description"`                     // Mô tả về giá trị tùy chọn
	ImageURL            *string   `db:"image_url" json:"image_url"`                         // URL hình ảnh minh họa
	PriceAdjustment     float64   `db:"price_adjustment" json:"price_adjustment"`           // Giá trị điều chỉnh giá
	PriceAdjustmentType string    `db:"price_adjustment_type" json:"price_adjustment_type"` // Loại điều chỉnh giá: FIXED (cố định), PERCENTAGE (phần trăm)
	IsDefault           bool      `db:"is_default" json:"is_default"`                       // Có phải là giá trị mặc định không
	DisplayOrder        uint      `db:"display_order" json:"display_order"`                 // Thứ tự hiển thị
	CreatedAt           time.Time `db:"created_at" json:"created_at"`                       // Thời gian tạo
	UpdatedAt           time.Time `db:"updated_at" json:"updated_at"`                       // Thời gian cập nhật gần nhất
}

// TableName trả về tên bảng trong database
func (ProductOptionValue) TableName() string {
	return "ecom_product_option_values"
}
