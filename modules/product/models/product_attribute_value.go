package models

import (
	"database/sql"
	"time"
)

// ProductAttributeValue đại diện cho giá trị thuộc tính sản phẩm
// L<PERSON>u trữ các giá trị thuộc tính cho sản phẩm, hỗ trợ nhiều kiểu dữ liệu khác nhau
// Ví dụ:
//
//	{
//	  "value_id": 1,
//	  "tenant_id": 1,
//	  "reference_id": 1,
//	  "reference_type": "PRODUCT",
//	  "attribute_id": 2,
//	  "value_text": "100% cotton",
//	  "value_numeric": null,
//	  "value_date": null,
//	  "value_boolean": null,
//	  "attribute_option_id": null
//	}
type ProductAttributeValue struct {
	ValueID           uint            `db:"value_id" json:"value_id" gorm:"primaryKey"`                                         // ID của giá trị thuộc tính
	TenantID          uint            `db:"tenant_id" json:"tenant_id"`                                                         // ID của tenant (cửa hàng)
	ReferenceID       uint            `db:"reference_id" json:"reference_id"`                                                   // ID của đối tượng tham chiếu (sản phẩm)
	ReferenceType     string          `db:"reference_type" json:"reference_type" gorm:"type:enum('PRODUCT');default:'PRODUCT'"` // Loại đối tượng tham chiếu (hiện tại chỉ hỗ trợ PRODUCT)
	AttributeID       uint            `db:"attribute_id" json:"attribute_id"`                                                   // ID của thuộc tính
	ValueText         sql.NullString  `db:"value_text" json:"value_text"`                                                       // Giá trị kiểu văn bản
	ValueNumeric      sql.NullFloat64 `db:"value_numeric" json:"value_numeric"`                                                 // Giá trị kiểu số
	ValueDate         sql.NullTime    `db:"value_date" json:"value_date"`                                                       // Giá trị kiểu ngày tháng
	ValueBoolean      sql.NullBool    `db:"value_boolean" json:"value_boolean"`                                                 // Giá trị kiểu boolean
	AttributeOptionID *uint           `db:"attribute_option_id" json:"attribute_option_id"`                                     // ID của tùy chọn thuộc tính (cho thuộc tính kiểu SELECT/MULTISELECT)
	CreatedAt         time.Time       `db:"created_at" json:"created_at"`                                                       // Thời gian tạo
	UpdatedAt         time.Time       `db:"updated_at" json:"updated_at"`                                                       // Thời gian cập nhật gần nhất

	// Các trường ảo (không lưu trong DB)
	Attribute       *ProductAttribute       `db:"-" json:"attribute,omitempty"`        // Thông tin về thuộc tính
	AttributeOption *ProductAttributeOption `db:"-" json:"attribute_option,omitempty"` // Thông tin về tùy chọn thuộc tính
}

// TableName chỉ định tên bảng trong cơ sở dữ liệu
func (ProductAttributeValue) TableName() string {
	return "ecom_product_attribute_values"
}
