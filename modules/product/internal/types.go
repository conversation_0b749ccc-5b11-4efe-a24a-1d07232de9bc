package internal

import (
	"context"
)

// Product entity
// (<PERSON><PERSON> thể mở rộng thêm các trường nếu cần)
type Product struct {
	ID    int64  `json:"id"`
	Name  string `json:"name"`
	Price int64  `json:"price"`
	// ...
}

// ProductRepository interface
// (Chỉ là ví dụ, có thể mở rộng)
type ProductRepository interface {
	GetByID(ctx context.Context, id int64) (*Product, error)
	Create(ctx context.Context, p *Product) error
	Update(ctx context.Context, p *Product) error
	Delete(ctx context.Context, id int64) error
}

// ProductService interface
// (Chỉ là ví dụ, có thể mở rộng)
type ProductService interface {
	GetProduct(ctx context.Context, id int64) (*Product, error)
	CreateProduct(ctx context.Context, p *Product) error
	UpdateProduct(ctx context.Context, p *Product) error
	DeleteProduct(ctx context.Context, id int64) error
}
