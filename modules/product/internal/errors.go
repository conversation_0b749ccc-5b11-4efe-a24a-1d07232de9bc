package internal

import pkgErrors "wnapi/internal/pkg/errors"

type ErrorCode = pkgErrors.ErrorCode

// Định nghĩa mã lỗi riêng cho module product
const (
	ErrCodeProductNotFound ErrorCode = "PRODUCT_NOT_FOUND"
	ErrCodeProductExists   ErrorCode = "PRODUCT_ALREADY_EXISTS"
	ErrCodeInvalidInput    ErrorCode = "INVALID_INPUT"
	// ... bổ sung thêm mã lỗi nếu cần
)

var ErrorMessages = map[ErrorCode]map[string]string{
	ErrCodeProductNotFound: {
		"en": "Product not found",
		"vi": "Không tìm thấy sản phẩm",
	},
	ErrCodeProductExists: {
		"en": "Product already exists",
		"vi": "Sản phẩm đã tồn tại",
	},
	ErrCodeInvalidInput: {
		"en": "Invalid input",
		"vi": "Dữ liệu không hợp lệ",
	},
}

type AppError = pkgErrors.AppError

func New(code ErrorCode, lang string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetMessage(code, lang),
		HTTPStatus: getHTTPStatus(code),
	}
}

func Wrap(code ErrorCode, message string, err error) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatus(code),
		Internal:   err,
	}
}

func NewWithDetails(code ErrorCode, lang string, details string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetMessage(code, lang),
		Details:    details,
		HTTPStatus: getHTTPStatus(code),
	}
}

func NewValidation(code ErrorCode, lang string, fields map[string]string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetMessage(code, lang),
		Fields:     fields,
		HTTPStatus: 400,
	}
}

func GetMessage(code ErrorCode, lang string) string {
	if messages, exists := ErrorMessages[code]; exists {
		if message, exists := messages[lang]; exists {
			return message
		}
		if message, exists := messages["en"]; exists {
			return message
		}
	}
	return pkgErrors.GetCommonMessage(code, lang)
}

func getHTTPStatus(code ErrorCode) int {
	switch code {
	case ErrCodeProductNotFound:
		return 404
	case ErrCodeProductExists:
		return 409
	case ErrCodeInvalidInput:
		return 400
	default:
		return pkgErrors.GetDefaultHTTPStatus(code)
	}
}
