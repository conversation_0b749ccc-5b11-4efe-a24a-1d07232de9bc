package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/modules/product/api"
	"wnapi/modules/product/configs"
	// TODO: Add auth import when available
	// "wnapi/internal/pkg/auth"
)

func main() {
	// Tải cấu hình từ file config.yaml
	cfg, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("Không thể tải cấu hình: %v", err)
	}

	// Khởi tạo Gin router
	router := gin.Default()

	// Cấu hình CORS
	router.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.<PERSON>.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Khởi tạo kết nối database
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		cfg.DB.Username,
		cfg.DB.Password,
		cfg.DB.Host,
		cfg.DB.Port,
		cfg.DB.Database)

	db, err := sqlx.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Không thể kết nối đến cơ sở dữ liệu: %v", err)
	}
	defer db.Close()

	// Cấu hình connection pool
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Ping database để kiểm tra kết nối
	if err := db.Ping(); err != nil {
		log.Fatalf("Không thể ping cơ sở dữ liệu: %v", err)
	}

	// Khởi tạo GORM
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: db.DB,
	}), &gorm.Config{})
	if err != nil {
		log.Fatalf("Không thể khởi tạo GORM: %v", err)
	}

	// TODO: Initialize JWT service when auth package is available
	// jwtConfig := auth.JWTConfig{...}
	// jwtService := auth.NewJWTService(jwtConfig)
	// permService := &mockPermissionService{}

	// Đăng ký routes (simplified for Task 01)
	handler := api.NewHandler(db, gormDB)
	handler.RegisterRoutes(router)

	// In danh sách routes từ handler
	handler.PrintRoutes()

	// In danh sách endpoints cho debug
	printEndpoints(router)

	// Khởi động server
	serverAddr := fmt.Sprintf("%s:%d", cfg.Server.Host, cfg.Server.Port)
	log.Printf("Khởi động server tại %s", serverAddr)
	if err := http.ListenAndServe(serverAddr, router); err != nil {
		log.Fatalf("Không thể khởi động server: %v", err)
	}
}

// mockPermissionService là một triển khai giả của PermissionService để test
type mockPermissionService struct{}

// CheckPermission triển khai interface PermissionService, luôn cho phép mọi quyền trong môi trường development
func (m *mockPermissionService) CheckPermission(ctx *gin.Context, tenantID, userID uint, permission string) (bool, error) {
	// Luôn trả về true cho mục đích test
	return true, nil
}

// HasPermission giữ lại để tương thích với code cũ
func (m *mockPermissionService) HasPermission(userID uint, tenantID uint, permission string) bool {
	// Luôn trả về true cho mục đích test
	return true
}

// printEndpoints in danh sách các endpoints đã đăng ký
func printEndpoints(router *gin.Engine) {
	routes := router.Routes()
	fmt.Println("=== Registered Endpoints ===")
	for _, route := range routes {
		fmt.Printf("[%s] %s\n", route.Method, route.Path)
	}
	fmt.Println("===========================")
}
