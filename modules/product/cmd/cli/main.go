package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"wnapi/modules/product/cmd/cli/commands"
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "ecom",
	Short: "Ecom CLI is a command-line tool for the ecom module",
	Long: `Ecom CLI is a command-line tool for managing and interacting with
the ecommerce module of the WebNew backend system.`,
	Run: func(cmd *cobra.Command, args []string) {
		// If no subcommands are provided, print help
		cmd.Help()
	},
}

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "Print the version number of Ecom CLI",
	Long:  `All software has versions. This is Ecom CLI's`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Println("Ecom CLI v1.0.0")
	},
}

// helloCmd represents the hello command
var helloCmd = &cobra.Command{
	Use:   "hello",
	Short: "Say hello to someone",
	Long:  `A simple command to greet someone by name.`,
	Run: func(cmd *cobra.Command, args []string) {
		name, _ := cmd.Flags().GetString("name")
		if name == "" {
			name = "World"
		}
		fmt.Printf("Hello, %s!\n", name)
	},
}

func init() {
	// Add flags for hello command
	helloCmd.Flags().StringP("name", "n", "", "Name to greet")

	// Add global flags for database connection
	rootCmd.PersistentFlags().String("dsn", "root:root@tcp(localhost:3307)/blog_v4?parseTime=true", "Database connection string")
	rootCmd.PersistentFlags().Int("tenant-id", 1, "Tenant ID to use for operations")

	// Add commands to root command
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(helloCmd)
	rootCmd.AddCommand(commands.ProductCmd)
	rootCmd.AddCommand(commands.CategoryCmd)
	rootCmd.AddCommand(commands.ProductAttributeGroupCmd)
}

// RunCLI executes the CLI application
func RunCLI() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func main() {
	RunCLI()
}
