package commands

import (
	"context"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/spf13/cobra"
	gormMySQL "gorm.io/driver/mysql"
	"gorm.io/gorm"

	"wnapi/modules/product/repository"
	mysqlRepo "wnapi/modules/product/repository/mysql"
	"wnapi/modules/product/service"
)

var (
	// Database connection variables
	db       *sqlx.DB
	gormDB   *gorm.DB
	tenantID int
)

// ProductCmd represents the product command
var ProductCmd = &cobra.Command{
	Use:   "product",
	Short: "Manage products",
	Long:  `Create, read, update, and delete products in the ecom module.`,
	PersistentPreRun: func(cmd *cobra.Command, args []string) {
		// Initialize database connections
		var err error
		dsn, _ := cmd.Flags().GetString("dsn")
		tenantID, _ = cmd.Flags().GetInt("tenant-id")

		// Skip database initialization for help command
		if cmd.Name() == "help" {
			return
		}

		// Connect to database
		db, err = sqlx.Open("mysql", dsn)
		if err != nil {
			cmd.PrintErrf("Error connecting to database: %v\n", err)
			return
		}

		// Set connection parameters
		db.SetMaxOpenConns(10)
		db.SetMaxIdleConns(5)
		db.SetConnMaxLifetime(time.Minute * 5)

		// Initialize GORM
		gormDB, err = gorm.Open(gormMySQL.Open(dsn), &gorm.Config{})
		if err != nil {
			cmd.PrintErrf("Error initializing GORM: %v\n", err)
			return
		}
	},
	PersistentPostRun: func(cmd *cobra.Command, args []string) {
		// Close database connection
		if db != nil {
			db.Close()
		}
	},
}

// createProductRepository creates a new product repository
func createProductRepository() repository.ProductRepository {
	return mysqlRepo.NewProductRepository(db, gormDB)
}

// createProductService creates a new product service
func createProductService() service.ProductService {
	productRepo := createProductRepository()
	productService := service.NewProductService(
		productRepo,
		nil, // Tạm thời truyền nil vào VariantService
		nil, // Tạm thời truyền nil vào VariantAttributeValueService
		nil, // Tạm thời truyền nil vào AttributeService
		nil, // Tạm thời truyền nil vào ConfigurableAttributeRepo
		nil, // Tạm thời truyền nil vào AttributeOptionService
	)
	return productService
}

// createContext creates a new context
func createContext() context.Context {
	return context.Background()
}

// init initializes the product command
func init() {
	// Add product subcommands in each file's init() function
}
