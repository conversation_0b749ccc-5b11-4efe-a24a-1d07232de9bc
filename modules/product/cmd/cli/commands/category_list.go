package commands

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"

	"wnapi/modules/product/dto/request"
)

// ListCategoriesCmd represents the list categories command
var ListCategoriesCmd = &cobra.Command{
	Use:   "list",
	Short: "List categories",
	Long:  `List categories from the ecom module with pagination.`,
	Run: func(cmd *cobra.Command, args []string) {
		// Get flags
		limit, _ := cmd.Flags().GetInt("limit")
		cursor, _ := cmd.Flags().GetString("cursor")
		parentIDStr, _ := cmd.Flags().GetString("parent-id")
		includeTree, _ := cmd.Flags().GetBool("include-tree")

		// Create category service
		categoryService := createCategoryService()

		// Prepare request
		req := request.ListCategoryRequest{
			Limit:       limit,
			Cursor:      cursor,
			IncludeTree: includeTree,
		}

		// Set parent ID if provided
		if parentIDStr != "" {
			parentID, err := strconv.Atoi(parentIDStr)
			if err != nil {
				fmt.Printf("Error: Invalid parent ID: %v\n", err)
				return
			}
			req.ParentID = &parentID
		}

		// List categories
		ctx := createContext()
		categories, err := categoryService.ListCategories(ctx, tenantID, req)
		if err != nil {
			fmt.Printf("Error listing categories: %v\n", err)
			return
		}

		// Print result
		categoriesJSON, _ := json.MarshalIndent(categories, "", "  ")
		fmt.Printf("%s\n", string(categoriesJSON))
	},
}

// GetCategoryTreeCmd represents the get category tree command
var GetCategoryTreeCmd = &cobra.Command{
	Use:   "tree",
	Short: "Get category tree",
	Long:  `Get the full category tree from the ecom module.`,
	Run: func(cmd *cobra.Command, args []string) {
		// Create category service
		categoryService := createCategoryService()

		// Get category tree
		ctx := createContext()
		tree, err := categoryService.GetCategoryTree(ctx, tenantID)
		if err != nil {
			fmt.Printf("Error getting category tree: %v\n", err)
			return
		}

		// Print result
		treeJSON, _ := json.MarshalIndent(tree, "", "  ")
		fmt.Printf("%s\n", string(treeJSON))
	},
}

func init() {
	// Add flags for list categories command
	ListCategoriesCmd.Flags().Int("limit", 10, "Number of categories to return")
	ListCategoriesCmd.Flags().String("cursor", "", "Pagination cursor")
	ListCategoriesCmd.Flags().String("parent-id", "", "Filter by parent ID")
	ListCategoriesCmd.Flags().Bool("include-tree", false, "Include tree structure in response")

	// Add commands to category command
	CategoryCmd.AddCommand(ListCategoriesCmd)
	CategoryCmd.AddCommand(GetCategoryTreeCmd)
}
