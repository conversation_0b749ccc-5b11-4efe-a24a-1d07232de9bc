package commands

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"
)

// GetProductCmd represents the get product command
var GetProductCmd = &cobra.Command{
	Use:   "get [product_id]",
	Short: "Get a product by ID",
	Long:  `Get a product by its ID from the ecom module.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		// Parse product ID
		productID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: Invalid product ID: %v\n", err)
			return
		}

		// Create product service
		productService := createProductService()

		// Get product
		ctx := createContext()
		product, err := productService.GetProduct(ctx, tenantID, productID)
		if err != nil {
			fmt.Printf("Error getting product: %v\n", err)
			return
		}

		// Print result
		productJSON, _ := json.MarshalIndent(product, "", "  ")
		fmt.Printf("%s\n", string(productJSON))
	},
}

func init() {
	// Add command to product command
	ProductCmd.AddCommand(GetProductCmd)
}
