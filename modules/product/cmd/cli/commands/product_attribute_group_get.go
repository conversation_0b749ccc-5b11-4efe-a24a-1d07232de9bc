package commands

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"
)

// GetProductAttributeGroupCmd represents the get product attribute group command
var GetProductAttributeGroupCmd = &cobra.Command{
	Use:   "get [group_id]",
	Short: "Get a product attribute group by ID",
	Long:  `Get a product attribute group by its ID from the ecom module.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		// Check if we should get by code instead
		byCode, _ := cmd.Flags().GetBool("by-code")

		// Create product attribute group service
		attributeGroupService := createProductAttributeGroupService()

		// Get product attribute group
		ctx := createContext()
		var group interface{}
		var err error

		if byCode {
			// If the first argument is a code string
			code := args[0]
			group, err = (*attributeGroupService).GetProductAttributeGroupByCode(ctx, tenantID, code)
		} else {
			// Parse group ID
			groupID, parseErr := strconv.Atoi(args[0])
			if parseErr != nil {
				fmt.Printf("Error: Invalid group ID: %v\n", parseErr)
				return
			}
			// If the first argument is a group ID
			group, err = (*attributeGroupService).GetProductAttributeGroup(ctx, tenantID, groupID)
		}

		if err != nil {
			fmt.Printf("Error getting product attribute group: %v\n", err)
			return
		}

		// Print result
		prettyJSON, _ := json.MarshalIndent(group, "", "  ")
		fmt.Printf("%s\n", string(prettyJSON))
	},
}

func init() {
	// Add flags for get product attribute group command
	GetProductAttributeGroupCmd.Flags().Bool("by-code", false, "Get product attribute group by code instead of ID")
}
