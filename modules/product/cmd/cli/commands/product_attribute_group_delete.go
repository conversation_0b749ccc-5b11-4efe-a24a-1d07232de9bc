package commands

import (
	"fmt"
	"strconv"

	"github.com/spf13/cobra"
)

// DeleteProductAttributeGroupCmd represents the delete product attribute group command
var DeleteProductAttributeGroupCmd = &cobra.Command{
	Use:   "delete [group_id]",
	Short: "Delete a product attribute group",
	Long:  `Delete a product attribute group from the ecom module.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		// Parse group ID
		groupID, parseErr := strconv.Atoi(args[0])
		if parseErr != nil {
			fmt.Printf("Error: Invalid group ID: %v\n", parseErr)
			return
		}

		// Create product attribute group service
		attributeGroupService := createProductAttributeGroupService()

		// Delete product attribute group
		ctx := createContext()
		err := (*attributeGroupService).DeleteProductAttributeGroup(ctx, tenantID, groupID)
		if err != nil {
			fmt.Printf("Error deleting product attribute group: %v\n", err)
			return
		}

		fmt.Printf("Product attribute group with ID %d deleted successfully\n", groupID)
	},
}
