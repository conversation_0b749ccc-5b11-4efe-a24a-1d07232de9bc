package commands

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"

	"wnapi/modules/product/dto/request"
)

// ListProductsCmd represents the list products command
var ListProductsCmd = &cobra.Command{
	Use:   "list",
	Short: "List products",
	Long:  `List products from the ecom module with pagination.`,
	Run: func(cmd *cobra.Command, args []string) {
		// Get flags
		limit, _ := cmd.Flags().GetInt("limit")
		cursor, _ := cmd.Flags().GetString("cursor")
		categoryIDStr, _ := cmd.Flags().GetString("category-id")
		status, _ := cmd.Flags().GetString("status")

		// Create product service
		productService := createProductService()

		// Prepare request
		req := &request.ListProductRequest{
			Limit:  limit,
			Cursor: cursor,
			Status: status,
		}

		// Set category ID if provided
		if categoryIDStr != "" {
			categoryID, err := strconv.Atoi(categoryIDStr)
			if err != nil {
				fmt.Printf("Error: Invalid category ID: %v\n", err)
				return
			}
			req.CategoryID = &categoryID
		}

		// List products
		ctx := createContext()
		products, err := productService.ListProducts(ctx, tenantID, req)
		if err != nil {
			fmt.Printf("Error listing products: %v\n", err)
			return
		}

		// Print result
		productsJSON, _ := json.MarshalIndent(products, "", "  ")
		fmt.Printf("%s\n", string(productsJSON))
	},
}

func init() {
	// Add flags for list products command
	ListProductsCmd.Flags().Int("limit", 10, "Number of products to return")
	ListProductsCmd.Flags().String("cursor", "", "Pagination cursor")
	ListProductsCmd.Flags().String("category-id", "", "Filter by category ID")
	ListProductsCmd.Flags().String("status", "", "Filter by status")

	// Add command to product command
	ProductCmd.AddCommand(ListProductsCmd)
}
