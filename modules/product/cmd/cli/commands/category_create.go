package commands

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"

	"wnapi/modules/product/dto/request"
)

// CreateCategoryCmd represents the create category command
var CreateCategoryCmd = &cobra.Command{
	Use:   "create",
	Short: "Create a new category",
	Long:  `Create a new category in the ecom module.`,
	Run: func(cmd *cobra.Command, args []string) {
		// Get flags
		name, _ := cmd.Flags().GetString("name")
		description, _ := cmd.Flags().GetString("description")
		slug, _ := cmd.Flags().GetString("slug")
		featuredImage, _ := cmd.Flags().GetString("featured-image")
		isActive, _ := cmd.Flags().GetBool("is-active")
		isFeatured, _ := cmd.Flags().GetBool("is-featured")
		metaTitle, _ := cmd.Flags().GetString("meta-title")
		metaDescription, _ := cmd.Flags().GetString("meta-description")
		position, _ := cmd.Flags().GetInt("position")
		parentIDStr, _ := cmd.Flags().GetString("parent-id")

		// Validate required fields
		if name == "" {
			fmt.Println("Error: Category name is required")
			return
		}

		// Create category service
		categoryService := createCategoryService()

		// Prepare request
		req := request.CreateCategoryRequest{
			Name:            name,
			Description:     description,
			Slug:            slug,
			FeaturedImage:   featuredImage,
			IsActive:        isActive,
			IsFeatured:      isFeatured,
			MetaTitle:       metaTitle,
			MetaDescription: metaDescription,
			Position:        position,
		}

		// Set parent ID if provided
		if parentIDStr != "" {
			parentID, err := strconv.Atoi(parentIDStr)
			if err != nil {
				fmt.Printf("Error: Invalid parent ID: %v\n", err)
				return
			}
			req.ParentID = &parentID
		}

		// Create category
		ctx := createContext()
		category, err := categoryService.CreateCategory(ctx, tenantID, req)
		if err != nil {
			fmt.Printf("Error creating category: %v\n", err)
			return
		}

		// Print result
		categoryJSON, _ := json.MarshalIndent(category, "", "  ")
		fmt.Printf("Category created successfully:\n%s\n", string(categoryJSON))
	},
}

func init() {
	// Add flags for create category command
	CreateCategoryCmd.Flags().String("name", "", "Category name (required)")
	CreateCategoryCmd.Flags().String("description", "", "Category description")
	CreateCategoryCmd.Flags().String("slug", "", "Category slug (URL-friendly name)")
	CreateCategoryCmd.Flags().String("featured-image", "", "Category featured image URL")
	CreateCategoryCmd.Flags().Bool("is-active", true, "Whether the category is active")
	CreateCategoryCmd.Flags().Bool("is-featured", false, "Whether the category is featured")
	CreateCategoryCmd.Flags().String("meta-title", "", "Category meta title for SEO")
	CreateCategoryCmd.Flags().String("meta-description", "", "Category meta description for SEO")
	CreateCategoryCmd.Flags().Int("position", 0, "Category position in the same level")
	CreateCategoryCmd.Flags().String("parent-id", "", "Parent category ID")

	// Add command to category command
	CategoryCmd.AddCommand(CreateCategoryCmd)
}
