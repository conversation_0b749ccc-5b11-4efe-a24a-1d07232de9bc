package commands

import (
	"fmt"
	"strconv"

	"github.com/spf13/cobra"
)

// DeleteCategoryCmd represents the delete category command
var DeleteCategoryCmd = &cobra.Command{
	Use:   "delete [category_id]",
	Short: "Delete a category",
	Long:  `Delete a category from the ecom module.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		// Parse category ID
		categoryID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: Invalid category ID: %v\n", err)
			return
		}

		// Create category service
		categoryService := createCategoryService()

		// Delete category
		ctx := createContext()
		err = categoryService.DeleteCategory(ctx, tenantID, categoryID)
		if err != nil {
			fmt.Printf("Error deleting category: %v\n", err)
			return
		}

		fmt.Printf("Category with ID %d deleted successfully\n", categoryID)
	},
}

func init() {
	// Add command to category command
	CategoryCmd.AddCommand(DeleteCategoryCmd)
}
