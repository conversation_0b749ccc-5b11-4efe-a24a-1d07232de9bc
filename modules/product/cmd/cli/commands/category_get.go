package commands

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"
)

// GetCategoryCmd represents the get category command
var GetCategoryCmd = &cobra.Command{
	Use:   "get [category_id]",
	Short: "Get a category by ID",
	Long:  `Get a category by its ID from the ecom module.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		// Parse category ID
		categoryID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: Invalid category ID: %v\n", err)
			return
		}

		// Create category service
		categoryService := createCategoryService()

		// Get category
		ctx := createContext()
		category, err := categoryService.GetCategory(ctx, tenantID, categoryID)
		if err != nil {
			fmt.Printf("Error getting category: %v\n", err)
			return
		}

		// Print result
		categoryJSON, _ := json.MarshalIndent(category, "", "  ")
		fmt.Printf("%s\n", string(categoryJSON))
	},
}

func init() {
	// Add command to category command
	CategoryCmd.AddCommand(GetCategoryCmd)
}
