package commands

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"

	"wnapi/modules/product/dto/request"
)

// CreateProductCmd represents the create product command
var CreateProductCmd = &cobra.Command{
	Use:   "create",
	Short: "Create a new product",
	Long:  `Create a new product in the ecom module.`,
	Run: func(cmd *cobra.Command, args []string) {
		// Get flags
		name, _ := cmd.Flags().GetString("name")
		description, _ := cmd.Flags().GetString("description")
		content, _ := cmd.Flags().GetString("content")
		slug, _ := cmd.Flags().GetString("slug")
		imageURL, _ := cmd.Flags().GetString("image-url")
		basePrice, _ := cmd.Flags().GetFloat64("base-price")
		costPrice, _ := cmd.Flags().GetFloat64("cost-price")
		productType, _ := cmd.Flags().GetString("product-type")
		status, _ := cmd.Flags().GetString("status")
		productCode, _ := cmd.Flags().GetString("product-code")
		isTaxable, _ := cmd.Flags().GetBool("is-taxable")
		isVirtual, _ := cmd.Flags().GetBool("is-virtual")
		isDownloadable, _ := cmd.Flags().GetBool("is-downloadable")
		categoryIDStr, _ := cmd.Flags().GetString("category-id")

		// Validate required fields
		if name == "" {
			fmt.Println("Error: Product name is required")
			return
		}

		if basePrice <= 0 {
			fmt.Println("Error: Base price must be greater than 0")
			return
		}

		// Create product service
		productService := createProductService()

		// Prepare request
		req := &request.CreateProductRequest{
			Name:           name,
			Description:    description,
			Content:        content,
			Slug:           slug,
			ImageURL:       imageURL,
			BasePrice:      basePrice,
			ProductType:    productType,
			Status:         status,
			ProductCode:    productCode,
			IsTaxable:      isTaxable,
			IsVirtual:      isVirtual,
			IsDownloadable: isDownloadable,
		}

		// Set cost price if provided
		if cmd.Flags().Changed("cost-price") {
			req.CostPrice = &costPrice
		}

		// Set category ID if provided
		if categoryIDStr != "" {
			categoryID, err := strconv.Atoi(categoryIDStr)
			if err != nil {
				fmt.Printf("Error: Invalid category ID: %v\n", err)
				return
			}
			req.CategoryID = &categoryID
		}

		// Create product
		ctx := createContext()
		product, err := productService.CreateProduct(ctx, tenantID, req)
		if err != nil {
			fmt.Printf("Error creating product: %v\n", err)
			return
		}

		// Print result
		productJSON, _ := json.MarshalIndent(product, "", "  ")
		fmt.Printf("Product created successfully:\n%s\n", string(productJSON))
	},
}

func init() {
	// Add flags for create product command
	CreateProductCmd.Flags().String("name", "", "Product name (required)")
	CreateProductCmd.Flags().String("description", "", "Product description")
	CreateProductCmd.Flags().String("content", "", "Product content (HTML)")
	CreateProductCmd.Flags().String("slug", "", "Product slug (URL-friendly name)")
	CreateProductCmd.Flags().String("image-url", "", "Product image URL")
	CreateProductCmd.Flags().Float64("base-price", 0, "Product base price (required)")
	CreateProductCmd.Flags().Float64("cost-price", 0, "Product cost price")
	CreateProductCmd.Flags().String("product-type", "SIMPLE", "Product type (SIMPLE, CONFIGURABLE, etc.)")
	CreateProductCmd.Flags().String("status", "DRAFT", "Product status (DRAFT, PUBLISHED, etc.)")
	CreateProductCmd.Flags().String("product-code", "", "Product code")
	CreateProductCmd.Flags().Bool("is-taxable", true, "Whether the product is taxable")
	CreateProductCmd.Flags().Bool("is-virtual", false, "Whether the product is virtual")
	CreateProductCmd.Flags().Bool("is-downloadable", false, "Whether the product is downloadable")
	CreateProductCmd.Flags().String("category-id", "", "Product category ID")

	// Add command to product command
	ProductCmd.AddCommand(CreateProductCmd)
}
