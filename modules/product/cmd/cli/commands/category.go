package commands

import (
	"github.com/spf13/cobra"

	mysqlRepo "wnapi/modules/product/repository/mysql"
	"wnapi/modules/product/service"
)

// CategoryCmd represents the category command
var CategoryCmd = &cobra.Command{
	Use:   "category",
	Short: "Manage categories",
	Long:  `Create, read, update, and delete categories in the ecom module.`,
	PersistentPreRun: func(cmd *cobra.Command, args []string) {
		// Reuse the database connection from product command
		// Skip database initialization for help command
		if cmd.Name() == "help" {
			return
		}

		// Database connection is initialized in product.go
		// We just need to make sure it's initialized
		if db == nil {
			ProductCmd.PersistentPreRun(cmd, args)
		}
	},
	PersistentPostRun: func(cmd *cobra.Command, args []string) {
		// Close database connection
		if db != nil {
			db.Close()
		}
	},
}

// createCategoryRepository creates a new category repository
func createCategoryRepository() *mysqlRepo.CategoryRepository {
	return mysqlRepo.NewCategoryRepository(db)
}

// createCategoryService creates a new category service
func createCategoryService() service.CategoryService {
	categoryRepo := createCategoryRepository()
	return service.NewCategoryService(categoryRepo)
}

func init() {
	// Add category subcommands in each file's init() function
}
