package commands

import (
	"encoding/json"
	"fmt"

	"github.com/spf13/cobra"

	"wnapi/modules/product/dto/request"
)

// ListProductAttributeGroupsCmd represents the list product attribute groups command
var ListProductAttributeGroupsCmd = &cobra.Command{
	Use:   "list",
	Short: "List product attribute groups",
	Long:  `List product attribute groups from the ecom module with pagination.`,
	Run: func(cmd *cobra.Command, args []string) {
		// Get flags
		limit, _ := cmd.Flags().GetInt("limit")
		cursor, _ := cmd.Flags().GetString("cursor")
		all, _ := cmd.Flags().GetBool("all")

		// Create product attribute group service
		attributeGroupService := createProductAttributeGroupService()

		// Get product attribute groups
		ctx := createContext()
		
		if all {
			// Get all product attribute groups
			groups, err := (*attributeGroupService).GetAllProductAttributeGroups(ctx, tenantID)
			if err != nil {
				fmt.Printf("Error getting all product attribute groups: %v\n", err)
				return
			}

			// Print result
			prettyJSON, _ := json.MarshalIndent(groups, "", "  ")
			fmt.Printf("%s\n", string(prettyJSON))
		} else {
			// Prepare request for paginated list
			req := &request.ListProductAttributeGroupRequest{
				Limit:  limit,
				Cursor: cursor,
			}

			// Get paginated list of product attribute groups
			result, err := (*attributeGroupService).ListProductAttributeGroups(ctx, tenantID, req)
			if err != nil {
				fmt.Printf("Error listing product attribute groups: %v\n", err)
				return
			}

			// Print result
			prettyJSON, _ := json.MarshalIndent(result, "", "  ")
			fmt.Printf("%s\n", string(prettyJSON))
		}
	},
}

func init() {
	// Add flags for list product attribute groups command
	ListProductAttributeGroupsCmd.Flags().Int("limit", 10, "Number of product attribute groups to return")
	ListProductAttributeGroupsCmd.Flags().String("cursor", "", "Pagination cursor")
	ListProductAttributeGroupsCmd.Flags().Bool("all", false, "Get all product attribute groups without pagination")
}
