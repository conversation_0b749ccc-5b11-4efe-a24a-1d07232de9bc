package commands

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"

	"wnapi/modules/product/dto/request"
)

// UpdateCategoryCmd represents the update category command
var UpdateCategoryCmd = &cobra.Command{
	Use:   "update [category_id]",
	Short: "Update a category",
	Long:  `Update an existing category in the ecom module.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		// Parse category ID
		categoryID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: Invalid category ID: %v\n", err)
			return
		}

		// Create category service
		categoryService := createCategoryService()

		// Prepare request
		req := request.UpdateCategoryRequest{}

		// Set fields if provided
		if cmd.Flags().Changed("name") {
			name, _ := cmd.Flags().GetString("name")
			req.Name = name
		}

		if cmd.Flags().Changed("description") {
			description, _ := cmd.Flags().GetString("description")
			req.Description = description
		}

		if cmd.Flags().Changed("slug") {
			slug, _ := cmd.Flags().GetString("slug")
			req.Slug = slug
		}

		if cmd.Flags().Changed("featured-image") {
			featuredImage, _ := cmd.Flags().GetString("featured-image")
			req.FeaturedImage = featuredImage
		}

		if cmd.Flags().Changed("is-active") {
			isActive, _ := cmd.Flags().GetBool("is-active")
			req.IsActive = &isActive
		}

		if cmd.Flags().Changed("is-featured") {
			isFeatured, _ := cmd.Flags().GetBool("is-featured")
			req.IsFeatured = &isFeatured
		}

		if cmd.Flags().Changed("meta-title") {
			metaTitle, _ := cmd.Flags().GetString("meta-title")
			req.MetaTitle = metaTitle
		}

		if cmd.Flags().Changed("meta-description") {
			metaDescription, _ := cmd.Flags().GetString("meta-description")
			req.MetaDescription = metaDescription
		}

		if cmd.Flags().Changed("position") {
			position, _ := cmd.Flags().GetInt("position")
			req.Position = &position
		}

		// Update category
		ctx := createContext()
		category, err := categoryService.UpdateCategory(ctx, tenantID, categoryID, req)
		if err != nil {
			fmt.Printf("Error updating category: %v\n", err)
			return
		}

		// Print result
		categoryJSON, _ := json.MarshalIndent(category, "", "  ")
		fmt.Printf("Category updated successfully:\n%s\n", string(categoryJSON))
	},
}

func init() {
	// Add flags for update category command
	UpdateCategoryCmd.Flags().String("name", "", "Category name")
	UpdateCategoryCmd.Flags().String("description", "", "Category description")
	UpdateCategoryCmd.Flags().String("slug", "", "Category slug (URL-friendly name)")
	UpdateCategoryCmd.Flags().String("featured-image", "", "Category featured image URL")
	UpdateCategoryCmd.Flags().Bool("is-active", true, "Whether the category is active")
	UpdateCategoryCmd.Flags().Bool("is-featured", false, "Whether the category is featured")
	UpdateCategoryCmd.Flags().String("meta-title", "", "Category meta title for SEO")
	UpdateCategoryCmd.Flags().String("meta-description", "", "Category meta description for SEO")
	UpdateCategoryCmd.Flags().Int("position", 0, "Category position in the same level")

	// Add command to category command
	CategoryCmd.AddCommand(UpdateCategoryCmd)
}
