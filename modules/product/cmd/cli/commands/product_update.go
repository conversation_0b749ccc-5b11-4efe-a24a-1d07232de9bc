package commands

import (
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/spf13/cobra"

	"wnapi/modules/product/dto/request"
)

// UpdateProductCmd represents the update product command
var UpdateProductCmd = &cobra.Command{
	Use:   "update [product_id]",
	Short: "Update a product",
	Long:  `Update an existing product in the ecom module.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		// Parse product ID
		productID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: Invalid product ID: %v\n", err)
			return
		}

		// Create product service
		productService := createProductService()

		// Prepare request
		req := &request.UpdateProductRequest{}

		// Set fields if provided
		if cmd.Flags().Changed("name") {
			name, _ := cmd.Flags().GetString("name")
			req.Name = name
		}

		if cmd.Flags().Changed("description") {
			description, _ := cmd.Flags().GetString("description")
			req.Description = description
		}

		if cmd.Flags().Changed("content") {
			content, _ := cmd.Flags().GetString("content")
			req.Content = content
		}

		if cmd.Flags().Changed("slug") {
			slug, _ := cmd.Flags().GetString("slug")
			req.Slug = slug
		}

		if cmd.Flags().Changed("image-url") {
			imageURL, _ := cmd.Flags().GetString("image-url")
			req.ImageURL = imageURL
		}

		if cmd.Flags().Changed("base-price") {
			basePrice, _ := cmd.Flags().GetFloat64("base-price")
			req.BasePrice = basePrice
		}

		if cmd.Flags().Changed("cost-price") {
			costPrice, _ := cmd.Flags().GetFloat64("cost-price")
			req.CostPrice = &costPrice
		}

		if cmd.Flags().Changed("product-type") {
			productType, _ := cmd.Flags().GetString("product-type")
			req.ProductType = productType
		}

		if cmd.Flags().Changed("status") {
			status, _ := cmd.Flags().GetString("status")
			req.Status = status
		}

		if cmd.Flags().Changed("product-code") {
			productCode, _ := cmd.Flags().GetString("product-code")
			req.ProductCode = productCode
		}

		if cmd.Flags().Changed("is-taxable") {
			isTaxable, _ := cmd.Flags().GetBool("is-taxable")
			req.IsTaxable = isTaxable
		}

		if cmd.Flags().Changed("is-virtual") {
			isVirtual, _ := cmd.Flags().GetBool("is-virtual")
			req.IsVirtual = isVirtual
		}

		if cmd.Flags().Changed("is-downloadable") {
			isDownloadable, _ := cmd.Flags().GetBool("is-downloadable")
			req.IsDownloadable = isDownloadable
		}

		if cmd.Flags().Changed("category-id") {
			categoryIDStr, _ := cmd.Flags().GetString("category-id")
			if categoryIDStr != "" {
				categoryID, err := strconv.Atoi(categoryIDStr)
				if err != nil {
					fmt.Printf("Error: Invalid category ID: %v\n", err)
					return
				}
				req.CategoryID = &categoryID
			}
		}

		// Update product
		ctx := createContext()
		product, err := productService.UpdateProduct(ctx, tenantID, productID, req)
		if err != nil {
			fmt.Printf("Error updating product: %v\n", err)
			return
		}

		// Print result
		productJSON, _ := json.MarshalIndent(product, "", "  ")
		fmt.Printf("Product updated successfully:\n%s\n", string(productJSON))
	},
}

func init() {
	// Add flags for update product command
	UpdateProductCmd.Flags().String("name", "", "Product name")
	UpdateProductCmd.Flags().String("description", "", "Product description")
	UpdateProductCmd.Flags().String("content", "", "Product content (HTML)")
	UpdateProductCmd.Flags().String("slug", "", "Product slug (URL-friendly name)")
	UpdateProductCmd.Flags().String("image-url", "", "Product image URL")
	UpdateProductCmd.Flags().Float64("base-price", 0, "Product base price")
	UpdateProductCmd.Flags().Float64("cost-price", 0, "Product cost price")
	UpdateProductCmd.Flags().String("product-type", "", "Product type (SIMPLE, CONFIGURABLE, etc.)")
	UpdateProductCmd.Flags().String("status", "", "Product status (DRAFT, PUBLISHED, etc.)")
	UpdateProductCmd.Flags().String("product-code", "", "Product code")
	UpdateProductCmd.Flags().Bool("is-taxable", true, "Whether the product is taxable")
	UpdateProductCmd.Flags().Bool("is-virtual", false, "Whether the product is virtual")
	UpdateProductCmd.Flags().Bool("is-downloadable", false, "Whether the product is downloadable")
	UpdateProductCmd.Flags().String("category-id", "", "Product category ID")

	// Add command to product command
	ProductCmd.AddCommand(UpdateProductCmd)
}
