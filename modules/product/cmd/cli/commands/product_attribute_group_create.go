package commands

import (
	"fmt"

	"github.com/spf13/cobra"

	"wnapi/modules/product/dto/request"
)

// CreateProductAttributeGroupCmd represents the create product attribute group command
var CreateProductAttributeGroupCmd = &cobra.Command{
	Use:   "create",
	Short: "Create a new product attribute group",
	Long:  `Create a new product attribute group in the ecom module.`,
	Run: func(cmd *cobra.Command, args []string) {
		// Get flags
		name, _ := cmd.Flags().GetString("name")
		code, _ := cmd.Flags().GetString("code")
		displayOrder, _ := cmd.Flags().GetUint("display-order")

		// Create product attribute group service
		attributeGroupService := createProductAttributeGroupService()

		// Prepare request
		req := &request.CreateProductAttributeGroupRequest{
			Name:         name,
			Code:         code,
			DisplayOrder: displayOrder,
		}

		// Create product attribute group
		ctx := createContext()
		group, err := (*attributeGroupService).CreateProductAttributeGroup(ctx, tenantID, req)
		if err != nil {
			fmt.Printf("Error creating product attribute group: %v\n", err)
			return
		}

		fmt.Printf("Product attribute group created successfully with ID %d\n", group.ID)
		fmt.Printf("Name: %s\n", group.Name)
		fmt.Printf("Code: %s\n", group.Code)
		fmt.Printf("Display Order: %d\n", group.DisplayOrder)
	},
}

func init() {
	// Add flags for create product attribute group command
	CreateProductAttributeGroupCmd.Flags().String("name", "", "Name of the product attribute group")
	CreateProductAttributeGroupCmd.Flags().String("code", "", "Code of the product attribute group")
	CreateProductAttributeGroupCmd.Flags().Uint("display-order", 0, "Display order of the product attribute group")

	// Mark required flags
	CreateProductAttributeGroupCmd.MarkFlagRequired("name")
	CreateProductAttributeGroupCmd.MarkFlagRequired("code")
}
