package commands

import (
	"fmt"
	"strconv"

	"github.com/spf13/cobra"
)

// DeleteProductCmd represents the delete product command
var DeleteProductCmd = &cobra.Command{
	Use:   "delete [product_id]",
	Short: "Delete a product",
	Long:  `Delete a product from the ecom module.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		// Parse product ID
		productID, err := strconv.Atoi(args[0])
		if err != nil {
			fmt.Printf("Error: Invalid product ID: %v\n", err)
			return
		}

		// Create product service
		productService := createProductService()

		// Delete product
		ctx := createContext()
		err = productService.DeleteProduct(ctx, tenantID, productID)
		if err != nil {
			fmt.Printf("Error deleting product: %v\n", err)
			return
		}

		fmt.Printf("Product with ID %d deleted successfully\n", productID)
	},
}

func init() {
	// Add command to product command
	ProductCmd.AddCommand(DeleteProductCmd)
}
