package commands

import (
	"github.com/spf13/cobra"

	"wnapi/modules/product/repository/mysql"
	"wnapi/modules/product/service"
)

// ProductAttributeGroupCmd represents the product attribute group command
var ProductAttributeGroupCmd = &cobra.Command{
	Use:   "attribute-group",
	Short: "Manage product attribute groups",
	Long:  `Create, read, update, and delete product attribute groups in the ecom module.`,
	PersistentPreRun: func(cmd *cobra.Command, args []string) {
		// Skip database initialization for help command
		if cmd.Name() == "help" {
			return
		}

		// Database connection is initialized in product.go
		// We just need to make sure it's initialized
		if db == nil {
			ProductCmd.PersistentPreRun(cmd, args)
		}
	},
	PersistentPostRun: func(cmd *cobra.Command, args []string) {
		// Close database connection
		if db != nil && cmd.Name() != "help" {
			db.Close()
		}
	},
}

// createProductAttributeGroupService creates a new product attribute group service instance
func createProductAttributeGroupService() *service.ProductAttributeGroupService {
	// Create repository
	attributeGroupRepo := mysql.NewProductAttributeGroupRepository(db, gormDB)

	// Create service
	attributeGroupService := service.NewProductAttributeGroupService(attributeGroupRepo)

	return &attributeGroupService
}

func init() {
	// Add commands to product attribute group command
	ProductAttributeGroupCmd.AddCommand(CreateProductAttributeGroupCmd)
	ProductAttributeGroupCmd.AddCommand(GetProductAttributeGroupCmd)
	ProductAttributeGroupCmd.AddCommand(ListProductAttributeGroupsCmd)
	ProductAttributeGroupCmd.AddCommand(UpdateProductAttributeGroupCmd)
	ProductAttributeGroupCmd.AddCommand(DeleteProductAttributeGroupCmd)
}
