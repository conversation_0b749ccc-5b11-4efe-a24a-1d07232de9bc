package commands

import (
	"fmt"
	"strconv"

	"github.com/spf13/cobra"

	"wnapi/modules/product/dto/request"
)

// UpdateProductAttributeGroupCmd represents the update product attribute group command
var UpdateProductAttributeGroupCmd = &cobra.Command{
	Use:   "update [group_id]",
	Short: "Update a product attribute group",
	Long:  `Update a product attribute group in the ecom module.`,
	Args:  cobra.ExactArgs(1),
	Run: func(cmd *cobra.Command, args []string) {
		// Parse group ID
		groupID, parseErr := strconv.Atoi(args[0])
		if parseErr != nil {
			fmt.Printf("Error: Invalid group ID: %v\n", parseErr)
			return
		}

		// Get flags
		name, _ := cmd.Flags().GetString("name")
		code, _ := cmd.Flags().GetString("code")
		displayOrder, _ := cmd.Flags().GetUint("display-order")

		// Check if any flag is set
		if name == "" && code == "" && !cmd.Flags().Changed("display-order") {
			fmt.Println("Error: At least one field must be provided to update")
			return
		}

		// Create product attribute group service
		attributeGroupService := createProductAttributeGroupService()

		// Prepare request
		req := &request.UpdateProductAttributeGroupRequest{
			Name:         name,
			Code:         code,
			DisplayOrder: displayOrder,
		}

		// Update product attribute group
		ctx := createContext()
		group, err := (*attributeGroupService).UpdateProductAttributeGroup(ctx, tenantID, groupID, req)
		if err != nil {
			fmt.Printf("Error updating product attribute group: %v\n", err)
			return
		}

		fmt.Printf("Product attribute group with ID %d updated successfully\n", group.ID)
		fmt.Printf("Name: %s\n", group.Name)
		fmt.Printf("Code: %s\n", group.Code)
		fmt.Printf("Display Order: %d\n", group.DisplayOrder)
	},
}

func init() {
	// Add flags for update product attribute group command
	UpdateProductAttributeGroupCmd.Flags().String("name", "", "New name of the product attribute group")
	UpdateProductAttributeGroupCmd.Flags().String("code", "", "New code of the product attribute group")
	UpdateProductAttributeGroupCmd.Flags().Uint("display-order", 0, "New display order of the product attribute group")
}
