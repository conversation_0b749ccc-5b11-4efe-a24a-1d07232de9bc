package common

// AttributeDTO represents a product attribute with its values
type AttributeDTO struct {
	Name           string   `json:"name"`
	Values         []string `json:"values"`
	IsConfigurable bool     `json:"is_configurable"`
	IsFilterable   bool     `json:"is_filterable"`
}

// VariantDTO represents a product variant
type VariantDTO struct {
	ID              string            `json:"id,omitempty"`
	Combination     string            `json:"combination"`
	PriceAdjustment float64           `json:"price_adjustment"`
	Price           float64           `json:"price"`
	CostPrice       float64           `json:"cost_price"`
	SKU             string            `json:"sku"`
	StockStatus     string            `json:"stock_status"`
	Weight          float64           `json:"weight"`
	WeightUnit      string            `json:"weight_unit"`
	IsVisible       bool              `json:"is_visible"`
	Attributes      map[string]string `json:"attributes"`
}
