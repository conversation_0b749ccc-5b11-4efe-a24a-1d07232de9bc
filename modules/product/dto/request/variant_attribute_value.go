package request

// CreateVariantAttributeValueRequest đại diện cho request tạo giá trị thuộc tính biến thể
type CreateVariantAttributeValueRequest struct {
	VariantID   uint   `json:"variant_id" binding:"required"`
	AttributeID uint   `json:"attribute_id" binding:"required"`
	Value       string `json:"value" binding:"required"`
}

// UpdateVariantAttributeValueRequest đại diện cho request cập nhật giá trị thuộc tính biến thể
type UpdateVariantAttributeValueRequest struct {
	Value string `json:"value" binding:"required"`
}

// BatchCreateVariantAttributeValueRequest đại diện cho request tạo nhiều giá trị thuộc tính biến thể
type BatchCreateVariantAttributeValueRequest struct {
	VariantID        uint                        `json:"variant_id" binding:"required"`
	ReplaceExisting  bool                        `json:"replace_existing"`
	Values           []VariantAttributeValueItem `json:"values" binding:"required,min=1"`
}

// VariantAttributeValueItem đại diện cho một mục trong request tạo hàng loạt
type VariantAttributeValueItem struct {
	AttributeID uint   `json:"attribute_id" binding:"required"`
	Value       string `json:"value" binding:"required"`
} 