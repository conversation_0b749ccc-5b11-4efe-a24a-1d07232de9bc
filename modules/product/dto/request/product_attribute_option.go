package request

// CreateProductAttributeOptionRequest đại diện cho yêu cầu tạo tùy chọn thuộc tính sản phẩm
type CreateProductAttributeOptionRequest struct {
	AttributeID  uint    `json:"attribute_id" binding:"required"`
	Value        string  `json:"value" binding:"required"`
	Label        *string `json:"label"`
	SwatchType   *string `json:"swatch_type"`
	SwatchValue  *string `json:"swatch_value"`
	DisplayOrder uint    `json:"display_order"`
}

// UpdateProductAttributeOptionRequest đại diện cho yêu cầu cập nhật tùy chọn thuộc tính sản phẩm
type UpdateProductAttributeOptionRequest struct {
	AttributeID  uint    `json:"attribute_id" binding:"required"`
	Value        string  `json:"value" binding:"required"`
	Label        *string `json:"label"`
	SwatchType   *string `json:"swatch_type"`
	SwatchValue  *string `json:"swatch_value"`
	DisplayOrder uint    `json:"display_order"`
}

// ListProductAttributeOptionsRequest đại diện cho yêu cầu lấy danh sách tùy chọn thuộc tính sản phẩm
type ListProductAttributeOptionsRequest struct {
	AttributeID *uint  `form:"attribute_id"`
	Cursor      string `form:"cursor"`
	Limit       int    `form:"limit,default=10"`
}
