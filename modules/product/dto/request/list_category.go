package request

// ListCategoryRequest represents the request to list categories with pagination
type ListCategoryRequest struct {
	Cursor        string `form:"cursor"`
	Limit         int    `form:"limit" binding:"omitempty,min=1,max=100"`
	ParentID      *int   `form:"parent_id"`
	IncludeTree   bool   `form:"include_tree"`
	WithPostCount bool   `form:"with_post_count"`
	OnlyActive    bool   `form:"only_active"`
}
