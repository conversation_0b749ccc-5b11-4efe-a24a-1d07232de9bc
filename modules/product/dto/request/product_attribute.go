package request

// CreateProductAttributeRequest đại diện cho yêu cầu tạo thuộc t<PERSON>h sản phẩm
type CreateProductAttributeRequest struct {
	GroupID         *uint   `json:"group_id"`
	Name            string  `json:"name" binding:"required"`
	Code            string  `json:"code" binding:"required"`
	Type            string  `json:"type" binding:"required"`
	Unit            *string `json:"unit"`
	ValidationRules *string `json:"validation_rules"`
	IsConfigurable  bool    `json:"is_configurable"`
	IsFilterable    bool    `json:"is_filterable"`
	IsSearchable    bool    `json:"is_searchable"`
	IsComparable    bool    `json:"is_comparable"`
	IsRequired      bool    `json:"is_required"`
	FrontendInput   *string `json:"frontend_input"`
	DisplayOrder    uint    `json:"display_order"`
}

// UpdateProductAttributeRequest đại diện cho yêu c<PERSON>u cập nh<PERSON>t thu<PERSON><PERSON> t<PERSON>h sản phẩm
type UpdateProductAttributeRequest struct {
	GroupID         *uint   `json:"group_id"`
	Name            string  `json:"name" binding:"required"`
	Code            string  `json:"code" binding:"required"`
	Type            string  `json:"type" binding:"required"`
	Unit            *string `json:"unit"`
	ValidationRules *string `json:"validation_rules"`
	IsConfigurable  bool    `json:"is_configurable"`
	IsFilterable    bool    `json:"is_filterable"`
	IsSearchable    bool    `json:"is_searchable"`
	IsComparable    bool    `json:"is_comparable"`
	IsRequired      bool    `json:"is_required"`
	FrontendInput   *string `json:"frontend_input"`
	DisplayOrder    uint    `json:"display_order"`
}
