package request

// CreateProductOptionValueRequest đại diện cho yêu cầu tạo giá trị tùy chọn sản phẩm
type CreateProductOptionValueRequest struct {
	GroupID             uint    `json:"group_id" binding:"required"`
	LinkedVariantID     *uint   `json:"linked_variant_id"`
	Name                string  `json:"name" binding:"required"`
	Description         *string `json:"description"`
	ImageURL            *string `json:"image_url"`
	PriceAdjustment     float64 `json:"price_adjustment"`
	PriceAdjustmentType string  `json:"price_adjustment_type" binding:"required,oneof=FIXED PERCENTAGE"`
	IsDefault           bool    `json:"is_default"`
	DisplayOrder        uint    `json:"display_order"`
}

// UpdateProductOptionValueRequest đại diện cho yêu cầu cập nhật giá trị tùy chọn sản phẩm
type UpdateProductOptionValueRequest struct {
	LinkedVariantID     *uint   `json:"linked_variant_id"`
	Name                string  `json:"name" binding:"required"`
	Description         *string `json:"description"`
	ImageURL            *string `json:"image_url"`
	PriceAdjustment     float64 `json:"price_adjustment"`
	PriceAdjustmentType string  `json:"price_adjustment_type" binding:"required,oneof=FIXED PERCENTAGE"`
	IsDefault           bool    `json:"is_default"`
	DisplayOrder        uint    `json:"display_order"`
}

// ListProductOptionValueRequest đại diện cho yêu cầu lấy danh sách giá trị tùy chọn sản phẩm
type ListProductOptionValueRequest struct {
	Cursor         string `form:"cursor"`
	Limit          int    `form:"limit,default=10"`
	OrderField     string `form:"order_field,default=value_id"`
	OrderDirection string `form:"order_direction,default=DESC"`
	GroupID        uint   `form:"group_id"`
}
