package request

// CreateProductOptionsLinkRequest đại diện cho yêu cầu tạo liên kết sản phẩm với nhóm tùy chọn
type CreateProductOptionsLinkRequest struct {
	ProductID    uint `json:"product_id" binding:"required"`
	GroupID      uint `json:"group_id" binding:"required"`
	DisplayOrder uint `json:"display_order"`
}

// UpdateProductOptionsLinkRequest đại diện cho yêu cầu cập nhật liên kết sản phẩm với nhóm tùy chọn
type UpdateProductOptionsLinkRequest struct {
	DisplayOrder uint `json:"display_order"`
}

// ListProductOptionsLinkRequest đại diện cho yêu cầu lấy danh sách liên kết sản phẩm với nhóm tùy chọn
type ListProductOptionsLinkRequest struct {
	Cursor         string `form:"cursor"`
	Limit          int    `form:"limit,default=10"`
	OrderField     string `form:"order_field,default=product_option_id"`
	OrderDirection string `form:"order_direction,default=DESC"`
	ProductID      uint   `form:"product_id"`
}
