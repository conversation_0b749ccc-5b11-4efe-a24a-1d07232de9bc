package request

// CreateProductVariantRequest đại diện cho yêu cầu tạo biến thể sản phẩm mới
type CreateProductVariantRequest struct {
	ProductID uint     `json:"product_id" binding:"required"`
	SKU       string   `json:"sku" binding:"required"`
	Price     *float64 `json:"price"`
	CostPrice *float64 `json:"cost_price"`
	ImageURL  *string  `json:"image_url"`
	IsActive  bool     `json:"is_active"`
}

// UpdateProductVariantRequest đại diện cho yêu cầu cập nhật biến thể sản phẩm
type UpdateProductVariantRequest struct {
	SKU       string   `json:"sku" binding:"required"`
	Price     *float64 `json:"price"`
	CostPrice *float64 `json:"cost_price"`
	ImageURL  *string  `json:"image_url"`
	IsActive  bool     `json:"is_active"`
}

// BatchCreateProductVariantRequest đại diện cho yêu cầu tạo nhiều biến thể sản phẩm
type BatchCreateProductVariantRequest struct {
	ProductID uint                       `json:"product_id" binding:"required"`
	Variants  []ProductVariantCreateData `json:"variants" binding:"required,dive"`
}

// ProductVariantCreateData đại diện cho dữ liệu của một biến thể trong yêu cầu batch
type ProductVariantCreateData struct {
	SKU       string   `json:"sku" binding:"required"`
	Price     *float64 `json:"price"`
	CostPrice *float64 `json:"cost_price"`
	ImageURL  *string  `json:"image_url"`
	IsActive  bool     `json:"is_active"`
}

// ListProductVariantsRequest đại diện cho yêu cầu lấy danh sách biến thể sản phẩm
type ListProductVariantsRequest struct {
	ProductID *uint  `form:"product_id"`
	Cursor    string `form:"cursor"`
	Limit     int    `form:"limit,default=10"`
}
