package request

import "wnapi/modules/product/dto/common"

// CreateProductRequest represents the request to create a new product
type CreateProductRequest struct {
	Name                 string                `json:"name" binding:"required"`
	CategoryID           *int                  `json:"category_id"`
	Description          string                `json:"description"`
	Content              string                `json:"content"`
	Slug                 string                `json:"slug"`
	ImageURL             string                `json:"image_url"`
	BasePrice            float64               `json:"base_price" binding:"required"`
	CostPrice            *float64              `json:"cost_price"`
	ProductType          string                `json:"product_type"`
	Status               string                `json:"status"`
	ProductCode          string                `json:"product_code"`
	IsTaxable            bool                  `json:"is_taxable"`
	TaxClassID           *int                  `json:"tax_class_id"`
	IsVirtual            bool                  `json:"is_virtual"`
	IsDownloadable       bool                  `json:"is_downloadable"`
	Attributes           []common.AttributeDTO `json:"attributes"`
	Variants             []common.VariantDTO   `json:"variants"`
	ManageVariantPricing bool                  `json:"manage_variant_pricing"`
}

// UpdateProductRequest represents the request to update an existing product
type UpdateProductRequest struct {
	Name                 string                `json:"name"`
	CategoryID           *int                  `json:"category_id"`
	Description          string                `json:"description"`
	Content              string                `json:"content"`
	Slug                 string                `json:"slug"`
	ImageURL             string                `json:"image_url"`
	BasePrice            float64               `json:"base_price"`
	CostPrice            *float64              `json:"cost_price"`
	ProductType          string                `json:"product_type"`
	Status               string                `json:"status"`
	ProductCode          string                `json:"product_code"`
	IsTaxable            bool                  `json:"is_taxable"`
	TaxClassID           *int                  `json:"tax_class_id"`
	IsVirtual            bool                  `json:"is_virtual"`
	IsDownloadable       bool                  `json:"is_downloadable"`
	Attributes           []common.AttributeDTO `json:"attributes"`
	Variants             []common.VariantDTO   `json:"variants"`
	ManageVariantPricing bool                  `json:"manage_variant_pricing"`
}

// ListProductRequest represents the request to list products with pagination
type ListProductRequest struct {
	Cursor     string `form:"cursor"`
	Limit      int    `form:"limit"`
	CategoryID *int   `form:"category_id"`
	Status     string `form:"status"`
}

// SearchProductRequest represents the request to search products
type SearchProductRequest struct {
	Keyword    string `form:"keyword" binding:"required"`
	Cursor     string `form:"cursor"`
	Limit      int    `form:"limit"`
	CategoryID *int   `form:"category_id"`
	Status     string `form:"status"`
}
