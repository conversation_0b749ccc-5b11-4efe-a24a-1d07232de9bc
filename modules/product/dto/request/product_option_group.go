package request

// CreateProductOptionGroupRequest đại diện cho yêu cầu tạo nhóm tùy chọn sản phẩm
type CreateProductOptionGroupRequest struct {
	Name         string  `json:"name" binding:"required"`
	Description  *string `json:"description"`
	Type         string  `json:"type" binding:"required,oneof=RADIO CHECKBOX SELECT TEXT_INPUT FILE_UPLOAD"`
	Required     bool    `json:"required"`
	MinSelect    uint    `json:"min_select"`
	MaxSelect    uint    `json:"max_select"`
	DisplayOrder uint    `json:"display_order"`
}

// UpdateProductOptionGroupRequest đại diện cho yêu cầu cập nhật nhóm tùy chọn sản phẩm
type UpdateProductOptionGroupRequest struct {
	Name         string  `json:"name" binding:"required"`
	Description  *string `json:"description"`
	Type         string  `json:"type" binding:"required,oneof=RADIO CHECKBOX SELECT TEXT_INPUT FILE_UPLOAD"`
	Required     bool    `json:"required"`
	MinSelect    uint    `json:"min_select"`
	MaxSelect    uint    `json:"max_select"`
	DisplayOrder uint    `json:"display_order"`
}

// ListProductOptionGroupRequest đại diện cho yêu cầu lấy danh sách nhóm tùy chọn sản phẩm
type ListProductOptionGroupRequest struct {
	Cursor         string `form:"cursor"`
	Limit          int    `form:"limit,default=10"`
	OrderField     string `form:"order_field,default=group_id"`
	OrderDirection string `form:"order_direction,default=DESC"`
}
