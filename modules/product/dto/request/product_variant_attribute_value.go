package request

// CreateProductVariantAttributeValueRequest đại diện cho yêu cầu tạo giá trị thuộc tính biến thể
type CreateProductVariantAttributeValueRequest struct {
	VariantID         uint `json:"variant_id" binding:"required"`
	AttributeID       uint `json:"attribute_id" binding:"required"`
	AttributeOptionID uint `json:"attribute_option_id" binding:"required"`
}

// UpdateProductVariantAttributeValueRequest đại diện cho yêu cầu cập nhật giá trị thuộc tính biến thể
type UpdateProductVariantAttributeValueRequest struct {
	AttributeOptionID uint `json:"attribute_option_id" binding:"required"`
}

// BatchCreateProductVariantAttributeValueRequest đại diện cho yêu cầu tạo nhiều giá trị thuộc tính biến thể
type BatchCreateProductVariantAttributeValueRequest struct {
	VariantID uint                                     `json:"variant_id" binding:"required"`
	Values    []ProductVariantAttributeValueCreateData `json:"values" binding:"required,dive"`
}

// ProductVariantAttributeValueCreateData đại diện cho dữ liệu của một giá trị thuộc tính biến thể trong batch
type ProductVariantAttributeValueCreateData struct {
	AttributeID       uint `json:"attribute_id" binding:"required"`
	AttributeOptionID uint `json:"attribute_option_id" binding:"required"`
}
