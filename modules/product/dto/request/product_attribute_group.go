package request

// CreateProductAttributeGroupRequest represents the request to create a new product attribute group
type CreateProductAttributeGroupRequest struct {
	Name         string `json:"name" binding:"required"`
	Code         string `json:"code" binding:"required"`
	DisplayOrder uint   `json:"display_order"`
}

// UpdateProductAttributeGroupRequest represents the request to update an existing product attribute group
type UpdateProductAttributeGroupRequest struct {
	Name         string `json:"name"`
	Code         string `json:"code"`
	DisplayOrder uint   `json:"display_order"`
}

// ListProductAttributeGroupRequest represents the request to list product attribute groups with pagination
type ListProductAttributeGroupRequest struct {
	Cursor string `form:"cursor"`
	Limit  int    `form:"limit"`
}
