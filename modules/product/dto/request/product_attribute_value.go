package request

import "time"

// CreateProductAttributeValueRequest đại diện cho yêu cầu tạo giá trị thuộc tính sản phẩm
type CreateProductAttributeValueRequest struct {
	ReferenceID       uint       `json:"reference_id" binding:"required"`
	ReferenceType     string     `json:"reference_type" binding:"required,oneof=PRODUCT"`
	AttributeID       uint       `json:"attribute_id" binding:"required"`
	ValueText         *string    `json:"value_text"`
	ValueNumeric      *float64   `json:"value_numeric"`
	ValueDate         *time.Time `json:"value_date"`
	ValueBoolean      *bool      `json:"value_boolean"`
	AttributeOptionID *uint      `json:"attribute_option_id"`
}

// UpdateProductAttributeValueRequest đại diện cho yêu cầu cập nhật giá trị thuộc tính sản phẩm
type UpdateProductAttributeValueRequest struct {
	ValueText         *string    `json:"value_text"`
	ValueNumeric      *float64   `json:"value_numeric"`
	ValueDate         *time.Time `json:"value_date"`
	ValueBoolean      *bool      `json:"value_boolean"`
	AttributeOptionID *uint      `json:"attribute_option_id"`
}

// BatchCreateProductAttributeValueRequest đại diện cho yêu cầu tạo nhiều giá trị thuộc tính sản phẩm
type BatchCreateProductAttributeValueRequest struct {
	ProductID       uint                        `json:"product_id" binding:"required"`
	AttributeValues []ProductAttributeValueData `json:"attribute_values" binding:"required,dive"`
}

// ProductAttributeValueData đại diện cho dữ liệu giá trị thuộc tính trong yêu cầu hàng loạt
type ProductAttributeValueData struct {
	AttributeID       uint       `json:"attribute_id" binding:"required"`
	ValueText         *string    `json:"value_text"`
	ValueNumeric      *float64   `json:"value_numeric"`
	ValueDate         *time.Time `json:"value_date"`
	ValueBoolean      *bool      `json:"value_boolean"`
	AttributeOptionID *uint      `json:"attribute_option_id"`
}

// ListProductAttributeValuesRequest đại diện cho yêu cầu lấy danh sách giá trị thuộc tính sản phẩm
type ListProductAttributeValuesRequest struct {
	ProductID   *uint  `form:"product_id"`
	AttributeID *uint  `form:"attribute_id"`
	Cursor      string `form:"cursor"`
	Limit       int    `form:"limit,default=10"`
}
