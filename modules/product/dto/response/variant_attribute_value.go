package response

import (
	"time"
)

// VariantAttributeValueResponse đại diện cho response của giá trị thuộc tính biến thể
type VariantAttributeValueResponse struct {
	ID          int       `json:"id"`
	TenantID    int       `json:"tenant_id"`
	VariantID   int       `json:"variant_id"`
	AttributeID int       `json:"attribute_id"`
	Value       string    `json:"value"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// VariantAttributeValueListResponse đại diện cho response danh sách giá trị thuộc tính biến thể có phân trang
type VariantAttributeValueListResponse struct {
	Values []*VariantAttributeValueResponse `json:"values"`
	Meta   CursorMeta                       `json:"meta"`
}
