package response

import (
	"time"
)

// ProductAttributeValueResponse đại diện cho giá trị thuộc tính sản phẩm trong response
type ProductAttributeValueResponse struct {
	ID                int        `json:"id"`
	TenantID          int        `json:"tenant_id"`
	ReferenceID       int        `json:"reference_id"`
	ReferenceType     string     `json:"reference_type"`
	AttributeID       int        `json:"attribute_id"`
	ValueText         *string    `json:"value_text"`
	ValueNumeric      *float64   `json:"value_numeric"`
	ValueDate         *time.Time `json:"value_date"`
	ValueBoolean      *bool      `json:"value_boolean"`
	AttributeOptionID *int       `json:"attribute_option_id"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`

	// Thông tin liên quan
	Attribute       *ProductAttributeResponse       `json:"attribute,omitempty"`
	AttributeOption *ProductAttributeOptionResponse `json:"attribute_option,omitempty"`
}

// ProductAttributeValueListResponse đại diện cho danh sách giá trị thuộc tính sản phẩm có phân trang
type ProductAttributeValueListResponse struct {
	AttributeValues []*ProductAttributeValueResponse `json:"attribute_values"`
	Meta            PaginationMeta                   `json:"meta"`
}

// ProductAttributeValuesByProduct đại diện cho giá trị thuộc tính sản phẩm được nhóm theo sản phẩm
type ProductAttributeValuesByProduct struct {
	ProductID       int                              `json:"product_id"`
	AttributeValues []*ProductAttributeValueResponse `json:"attribute_values"`
}
