package response

import (
	"time"

	"wnapi/modules/product/dto/common"
)

// ProductResponse represents a product in the response
type ProductResponse struct {
	ProductID            int                   `json:"product_id"`
	TenantID             int                   `json:"tenant_id"`
	CategoryID           *int                  `json:"category_id"`
	Name                 string                `json:"name"`
	Description          string                `json:"description,omitempty"`
	Content              string                `json:"content,omitempty"`
	Slug                 string                `json:"slug"`
	ImageURL             string                `json:"image_url,omitempty"`
	BasePrice            float64               `json:"base_price"`
	CostPrice            *float64              `json:"cost_price,omitempty"`
	ProductType          string                `json:"product_type"`
	Status               string                `json:"status"`
	ProductCode          string                `json:"product_code,omitempty"`
	IsTaxable            bool                  `json:"is_taxable"`
	TaxClassID           *int                  `json:"tax_class_id,omitempty"`
	IsVirtual            bool                  `json:"is_virtual"`
	IsDownloadable       bool                  `json:"is_downloadable"`
	Attributes           []common.AttributeDTO `json:"attributes,omitempty"`
	Variants             []common.VariantDTO   `json:"variants,omitempty"`
	ManageVariantPricing bool                  `json:"manage_variant_pricing"`
	CreatedAt            time.Time             `json:"created_at"`
	UpdatedAt            time.Time             `json:"updated_at"`
	CreatedBy            *int                  `json:"created_by,omitempty"`
	UpdatedBy            *int                  `json:"updated_by,omitempty"`
}

// ProductListResponse represents a paginated list of products
type ProductListResponse struct {
	Products []*ProductResponse `json:"products"`
	Meta     PaginationMeta     `json:"meta"`
}

// PaginationMeta represents metadata for pagination
type PaginationMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
}
