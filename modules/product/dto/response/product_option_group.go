package response

import (
	"time"

	"wnapi/modules/product/models"
	"wnapi/internal/pkg/pagination"
)

// ProductOptionGroupResponse đại diện cho đối tượng nhóm tùy chọn sản phẩm trong phản hồi
type ProductOptionGroupResponse struct {
	ID           uint      `json:"id"`
	TenantID     uint      `json:"tenant_id"`
	Name         string    `json:"name"`
	Description  *string   `json:"description"`
	Type         string    `json:"type"`
	Required     bool      `json:"required"`
	MinSelect    uint      `json:"min_select"`
	MaxSelect    uint      `json:"max_select"`
	DisplayOrder uint      `json:"display_order"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// ProductOptionGroupListResponse đại diện cho danh sách nhóm tùy chọn sản phẩm trong phản hồi
type ProductOptionGroupListResponse struct {
	Groups []ProductOptionGroupResponse `json:"groups"`
}

// ProductOptionGroupWithMeta đại diện cho danh sách nhóm tùy chọn sản phẩm có phân trang
type ProductOptionGroupWithMeta struct {
	Groups []ProductOptionGroupResponse `json:"groups"`
	Meta   CursorMeta                   `json:"meta"`
}

// NewProductOptionGroupResponse tạo một đối tượng ProductOptionGroupResponse từ model
func NewProductOptionGroupResponse(group *models.ProductOptionGroup) *ProductOptionGroupResponse {
	if group == nil {
		return nil
	}

	return &ProductOptionGroupResponse{
		ID:           group.GroupID,
		TenantID:     group.TenantID,
		Name:         group.Name,
		Description:  group.Description,
		Type:         group.Type,
		Required:     group.Required,
		MinSelect:    group.MinSelect,
		MaxSelect:    group.MaxSelect,
		DisplayOrder: group.DisplayOrder,
		CreatedAt:    group.CreatedAt,
		UpdatedAt:    group.UpdatedAt,
	}
}

// NewProductOptionGroupListResponse tạo một danh sách ProductOptionGroupResponse từ danh sách model
func NewProductOptionGroupListResponse(groups []*models.ProductOptionGroup) []ProductOptionGroupResponse {
	if len(groups) == 0 {
		return []ProductOptionGroupResponse{}
	}

	result := make([]ProductOptionGroupResponse, len(groups))
	for i, group := range groups {
		result[i] = *NewProductOptionGroupResponse(group)
	}

	return result
}

// NewProductOptionGroupWithMeta tạo một đối tượng ProductOptionGroupWithMeta từ danh sách model và thông tin phân trang
func NewProductOptionGroupWithMeta(groups []*models.ProductOptionGroup, cursorInfo *pagination.CursorInfo) *ProductOptionGroupWithMeta {
	return &ProductOptionGroupWithMeta{
		Groups: NewProductOptionGroupListResponse(groups),
		Meta:   *FromPaginationCursorInfo(cursorInfo),
	}
}
