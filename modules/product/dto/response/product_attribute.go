package response

import (
	"time"

	"wnapi/modules/product/models"
)

// ProductAttributeResponse đại diện cho response của thuộc t<PERSON>h sản phẩm
type ProductAttributeResponse struct {
	AttributeID     uint      `json:"attribute_id"`
	GroupID         *uint     `json:"group_id"`
	Name            string    `json:"name"`
	Code            string    `json:"code"`
	Type            string    `json:"type"`
	Unit            *string   `json:"unit"`
	ValidationRules *string   `json:"validation_rules"`
	IsConfigurable  bool      `json:"is_configurable"`
	IsFilterable    bool      `json:"is_filterable"`
	IsSearchable    bool      `json:"is_searchable"`
	IsComparable    bool      `json:"is_comparable"`
	IsRequired      bool      `json:"is_required"`
	FrontendInput   *string   `json:"frontend_input"`
	DisplayOrder    uint      `json:"display_order"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// FromModel chuyển đổi từ model sang response
func (r *ProductAttributeResponse) FromModel(model *models.ProductAttribute) {
	r.AttributeID = model.AttributeID
	r.GroupID = model.GroupID
	r.Name = model.Name
	r.Code = model.Code
	r.Type = model.Type
	r.Unit = model.Unit
	r.ValidationRules = model.ValidationRules
	r.IsConfigurable = model.IsConfigurable
	r.IsFilterable = model.IsFilterable
	r.IsSearchable = model.IsSearchable
	r.IsComparable = model.IsComparable
	r.IsRequired = model.IsRequired
	r.FrontendInput = model.FrontendInput
	r.DisplayOrder = model.DisplayOrder
	r.CreatedAt = model.CreatedAt
	r.UpdatedAt = model.UpdatedAt
}

// NewProductAttributeResponse tạo một response mới từ model
func NewProductAttributeResponse(model *models.ProductAttribute) *ProductAttributeResponse {
	if model == nil {
		return nil
	}
	response := &ProductAttributeResponse{}
	response.FromModel(model)
	return response
}

// NewProductAttributeResponseList tạo một danh sách response từ danh sách model
func NewProductAttributeResponseList(models []*models.ProductAttribute) []*ProductAttributeResponse {
	if len(models) == 0 {
		return []*ProductAttributeResponse{}
	}

	responseList := make([]*ProductAttributeResponse, len(models))
	for i, model := range models {
		responseList[i] = NewProductAttributeResponse(model)
	}
	return responseList
}
