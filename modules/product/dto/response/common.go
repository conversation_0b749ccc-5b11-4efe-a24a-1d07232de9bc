package response

import (
	"wnapi/internal/pkg/pagination"
)

// CursorMeta chứa thông tin phân trang kiểu cursor, phải tương thích với pagination.CursorInfo
type CursorMeta struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
}

// NewCursorMeta tạo một đối tượng CursorMeta mới từ pagination.CursorInfo
func NewCursorMeta(nextCursor string, hasMore bool) *CursorMeta {
	return &CursorMeta{
		NextCursor: nextCursor,
		HasMore:    hasMore,
	}
}

// FromPaginationCursorInfo chuyển đổi từ pagination.CursorInfo sang CursorMeta
func FromPaginationCursorInfo(cursorInfo *pagination.CursorInfo) *CursorMeta {
	if cursorInfo == nil {
		return &CursorMeta{
			NextCursor: "",
			HasMore:    false,
		}
	}

	return &CursorMeta{
		NextCursor: cursorInfo.NextCursor,
		HasMore:    cursorInfo.HasMore,
	}
}
