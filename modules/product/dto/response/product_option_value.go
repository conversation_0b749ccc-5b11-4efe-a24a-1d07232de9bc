package response

import (
	"time"

	"wnapi/modules/product/models"
	"wnapi/internal/pkg/pagination"
)

// ProductOptionValueResponse đại diện cho đối tượng giá trị tùy chọn sản phẩm trong phản hồi
type ProductOptionValueResponse struct {
	ID                  uint      `json:"id"`
	TenantID            uint      `json:"tenant_id"`
	GroupID             uint      `json:"group_id"`
	LinkedVariantID     *uint     `json:"linked_variant_id"`
	Name                string    `json:"name"`
	Description         *string   `json:"description"`
	ImageURL            *string   `json:"image_url"`
	PriceAdjustment     float64   `json:"price_adjustment"`
	PriceAdjustmentType string    `json:"price_adjustment_type"`
	IsDefault           bool      `json:"is_default"`
	DisplayOrder        uint      `json:"display_order"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

// ProductOptionValueListResponse đại diện cho danh sách giá trị tùy chọn sản phẩm trong phản hồi
type ProductOptionValueListResponse struct {
	Values []ProductOptionValueResponse `json:"values"`
}

// ProductOptionValueWithMeta đại diện cho danh sách giá trị tùy chọn sản phẩm có phân trang
type ProductOptionValueWithMeta struct {
	Values []ProductOptionValueResponse `json:"values"`
	Meta   CursorMeta                   `json:"meta"`
}

// NewProductOptionValueResponse tạo một đối tượng ProductOptionValueResponse từ model
func NewProductOptionValueResponse(value *models.ProductOptionValue) *ProductOptionValueResponse {
	if value == nil {
		return nil
	}

	return &ProductOptionValueResponse{
		ID:                  value.ValueID,
		TenantID:            value.TenantID,
		GroupID:             value.GroupID,
		LinkedVariantID:     value.LinkedVariantID,
		Name:                value.Name,
		Description:         value.Description,
		ImageURL:            value.ImageURL,
		PriceAdjustment:     value.PriceAdjustment,
		PriceAdjustmentType: value.PriceAdjustmentType,
		IsDefault:           value.IsDefault,
		DisplayOrder:        value.DisplayOrder,
		CreatedAt:           value.CreatedAt,
		UpdatedAt:           value.UpdatedAt,
	}
}

// NewProductOptionValueListResponse tạo một danh sách ProductOptionValueResponse từ danh sách model
func NewProductOptionValueListResponse(values []*models.ProductOptionValue) []ProductOptionValueResponse {
	if len(values) == 0 {
		return []ProductOptionValueResponse{}
	}

	result := make([]ProductOptionValueResponse, len(values))
	for i, value := range values {
		result[i] = *NewProductOptionValueResponse(value)
	}

	return result
}

// NewProductOptionValueWithMeta tạo một đối tượng ProductOptionValueWithMeta từ danh sách model và thông tin phân trang
func NewProductOptionValueWithMeta(values []*models.ProductOptionValue, cursorInfo *pagination.CursorInfo) *ProductOptionValueWithMeta {
	return &ProductOptionValueWithMeta{
		Values: NewProductOptionValueListResponse(values),
		Meta:   *FromPaginationCursorInfo(cursorInfo),
	}
}
