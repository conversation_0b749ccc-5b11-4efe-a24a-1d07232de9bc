package response

import (
	"time"
)

// ProductAttributeGroupResponse represents a product attribute group in the response
type ProductAttributeGroupResponse struct {
	ID           int       `json:"id"`
	TenantID     int       `json:"tenant_id"`
	Name         string    `json:"name"`
	Code         string    `json:"code"`
	DisplayOrder uint      `json:"display_order"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// ProductAttributeGroupListResponse represents a paginated list of product attribute groups
type ProductAttributeGroupListResponse struct {
	AttributeGroups []*ProductAttributeGroupResponse `json:"attribute_groups"`
	Meta            PaginationMeta                   `json:"meta"`
}
