package response

import (
	"strconv"
	"time"

	"wnapi/modules/product/models"
)

// ProductVariantAttributeValueResponse đại diện cho giá trị thuộc tính biến thể trong phản hồi
type ProductVariantAttributeValueResponse struct {
	ID                int       `json:"id"`
	TenantID          int       `json:"tenant_id"`
	VariantID         int       `json:"variant_id"`
	AttributeID       int       `json:"attribute_id"`
	AttributeOptionID int       `json:"attribute_option_id"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// ProductVariantAttributeValueListResponse đại diện cho danh sách giá trị thuộc tính biến thể
type ProductVariantAttributeValueListResponse struct {
	Values []ProductVariantAttributeValueResponse `json:"values"`
}

// FromModel chuyển đổi từ model sang response
func (r *ProductVariantAttributeValueResponse) FromModel(model *models.ProductVariantAttributeValue) {
	r.ID = int(model.ValueID)
	r.TenantID = int(model.TenantID)
	r.VariantID = int(model.VariantID)
	r.AttributeID = int(model.AttributeID)
	r.AttributeOptionID, _ = strconv.Atoi(model.Value)
	r.CreatedAt = model.CreatedAt
	r.UpdatedAt = model.UpdatedAt
}

// NewProductVariantAttributeValueResponse tạo một ProductVariantAttributeValueResponse từ model
func NewProductVariantAttributeValueResponse(model *models.ProductVariantAttributeValue) *ProductVariantAttributeValueResponse {
	if model == nil {
		return nil
	}
	response := &ProductVariantAttributeValueResponse{}
	response.FromModel(model)
	return response
}

// NewProductVariantAttributeValueListResponse tạo một danh sách ProductVariantAttributeValueResponse từ slice model
func NewProductVariantAttributeValueListResponse(models []*models.ProductVariantAttributeValue) []ProductVariantAttributeValueResponse {
	if len(models) == 0 {
		return []ProductVariantAttributeValueResponse{}
	}

	responseList := make([]ProductVariantAttributeValueResponse, len(models))
	for i, model := range models {
		response := ProductVariantAttributeValueResponse{}
		response.FromModel(model)
		responseList[i] = response
	}

	return responseList
}
