package response

import (
	"time"

	"wnapi/modules/product/models"
)

// ProductVariantResponse đại diện cho biến thể sản phẩm trong phản hồi
type ProductVariantResponse struct {
	ID        int       `json:"id"`
	TenantID  int       `json:"tenant_id"`
	ProductID int       `json:"product_id"`
	SKU       string    `json:"sku"`
	Price     *float64  `json:"price"`
	CostPrice *float64  `json:"cost_price"`
	ImageURL  *string   `json:"image_url"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ProductVariantListResponse đại diện cho danh sách biến thể sản phẩm có phân trang
type ProductVariantListResponse struct {
	Variants []ProductVariantResponse `json:"variants"`
	Meta     PaginationMeta           `json:"meta"`
}

// FromModel chuyển đổi từ model sang response
func (r *ProductVariantResponse) FromModel(model *models.ProductVariant) {
	r.ID = int(model.VariantID)
	r.TenantID = int(model.TenantID)
	r.ProductID = int(model.ProductID)
	r.SKU = model.SKU
	r.Price = model.Price
	r.CostPrice = model.CostPrice
	r.ImageURL = model.ImageURL
	r.IsActive = model.IsActive
	r.CreatedAt = model.CreatedAt
	r.UpdatedAt = model.UpdatedAt
}

// NewProductVariantResponse tạo một ProductVariantResponse từ model
func NewProductVariantResponse(model *models.ProductVariant) *ProductVariantResponse {
	if model == nil {
		return nil
	}

	response := &ProductVariantResponse{}
	response.FromModel(model)
	return response
}

// NewProductVariantListResponse tạo một danh sách ProductVariantResponse từ slice model
func NewProductVariantListResponse(models []*models.ProductVariant) []ProductVariantResponse {
	if len(models) == 0 {
		return []ProductVariantResponse{}
	}

	responseList := make([]ProductVariantResponse, len(models))
	for i, model := range models {
		response := ProductVariantResponse{}
		response.FromModel(model)
		responseList[i] = response
	}

	return responseList
}
