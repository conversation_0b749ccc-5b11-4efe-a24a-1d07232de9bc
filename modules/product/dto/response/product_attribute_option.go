package response

import (
	"time"

	"wnapi/modules/product/models"
)

// ProductAttributeOptionResponse đại diện cho response của tùy chọn thuộc t<PERSON>h sản phẩm
type ProductAttributeOptionResponse struct {
	OptionID     uint      `json:"option_id"`
	AttributeID  uint      `json:"attribute_id"`
	Value        string    `json:"value"`
	Label        *string   `json:"label"`
	SwatchType   *string   `json:"swatch_type"`
	SwatchValue  *string   `json:"swatch_value"`
	DisplayOrder uint      `json:"display_order"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// FromModel chuyển đổi từ model sang response
func (r *ProductAttributeOptionResponse) FromModel(model *models.ProductAttributeOption) {
	r.OptionID = model.OptionID
	r.AttributeID = model.AttributeID
	r.Value = model.Value
	r.Label = model.Label

	// Chuyển đổi SwatchType từ model sang chuỗi cho response
	if model.SwatchType != nil {
		swatchTypeStr := string(*model.SwatchType)
		r.SwatchType = &swatchTypeStr
	}

	r.SwatchValue = model.SwatchValue
	r.DisplayOrder = model.DisplayOrder
	r.CreatedAt = model.CreatedAt
	r.UpdatedAt = model.UpdatedAt
}

// NewProductAttributeOptionResponse tạo một response mới từ model
func NewProductAttributeOptionResponse(model *models.ProductAttributeOption) *ProductAttributeOptionResponse {
	if model == nil {
		return nil
	}
	response := &ProductAttributeOptionResponse{}
	response.FromModel(model)
	return response
}

// NewProductAttributeOptionResponseList tạo một danh sách response từ danh sách model
func NewProductAttributeOptionResponseList(models []*models.ProductAttributeOption) []*ProductAttributeOptionResponse {
	if len(models) == 0 {
		return []*ProductAttributeOptionResponse{}
	}

	responseList := make([]*ProductAttributeOptionResponse, len(models))
	for i, model := range models {
		responseList[i] = NewProductAttributeOptionResponse(model)
	}
	return responseList
}
