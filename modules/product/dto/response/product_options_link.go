package response

import (
	"time"

	"wnapi/modules/product/models"
	"wnapi/internal/pkg/pagination"
)

// ProductOptionsLinkResponse đại diện cho đối tượng liên kết sản phẩm với nhóm tùy chọn trong phản hồi
type ProductOptionsLinkResponse struct {
	ID           uint      `json:"id"`
	TenantID     uint      `json:"tenant_id"`
	ProductID    uint      `json:"product_id"`
	GroupID      uint      `json:"group_id"`
	DisplayOrder uint      `json:"display_order"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// ProductOptionsLinkListResponse đại diện cho danh sách liên kết sản phẩm với nhóm tùy chọn trong phản hồi
type ProductOptionsLinkListResponse struct {
	Links []ProductOptionsLinkResponse `json:"links"`
}

// ProductOptionsLinkWithMeta đại diện cho danh sách liên kết sản phẩm với nhóm tùy chọn có phân trang
type ProductOptionsLinkWithMeta struct {
	Links []ProductOptionsLinkResponse `json:"links"`
	Meta  CursorMeta                   `json:"meta"`
}

// NewProductOptionsLinkResponse tạo một đối tượng ProductOptionsLinkResponse từ model
func NewProductOptionsLinkResponse(link *models.ProductOptionsLink) *ProductOptionsLinkResponse {
	if link == nil {
		return nil
	}

	return &ProductOptionsLinkResponse{
		ID:           link.ProductOptionID,
		TenantID:     link.TenantID,
		ProductID:    link.ProductID,
		GroupID:      link.GroupID,
		DisplayOrder: link.DisplayOrder,
		CreatedAt:    link.CreatedAt,
		UpdatedAt:    link.UpdatedAt,
	}
}

// NewProductOptionsLinkListResponse tạo một danh sách ProductOptionsLinkResponse từ danh sách model
func NewProductOptionsLinkListResponse(links []*models.ProductOptionsLink) []ProductOptionsLinkResponse {
	if len(links) == 0 {
		return []ProductOptionsLinkResponse{}
	}

	result := make([]ProductOptionsLinkResponse, len(links))
	for i, link := range links {
		result[i] = *NewProductOptionsLinkResponse(link)
	}

	return result
}

// NewProductOptionsLinkWithMeta tạo một đối tượng ProductOptionsLinkWithMeta từ danh sách model và thông tin phân trang
func NewProductOptionsLinkWithMeta(links []*models.ProductOptionsLink, cursorInfo *pagination.CursorInfo) *ProductOptionsLinkWithMeta {
	return &ProductOptionsLinkWithMeta{
		Links: NewProductOptionsLinkListResponse(links),
		Meta:  *FromPaginationCursorInfo(cursorInfo),
	}
}
