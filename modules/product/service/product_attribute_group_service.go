package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/dto/response"
	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
)

// ProductAttributeGroupService handles business logic for product attribute groups
type ProductAttributeGroupService interface {
	CreateProductAttributeGroup(ctx context.Context, tenantID int, req *request.CreateProductAttributeGroupRequest) (*response.ProductAttributeGroupResponse, error)
	GetProductAttributeGroup(ctx context.Context, tenantID int, groupID int) (*response.ProductAttributeGroupResponse, error)
	GetProductAttributeGroupByCode(ctx context.Context, tenantID int, code string) (*response.ProductAttributeGroupResponse, error)
	UpdateProductAttributeGroup(ctx context.Context, tenantID int, groupID int, req *request.UpdateProductAttributeGroupRequest) (*response.ProductAttributeGroupResponse, error)
	DeleteProductAttributeGroup(ctx context.Context, tenantID int, groupID int) error
	ListProductAttributeGroups(ctx context.Context, tenantID int, req *request.ListProductAttributeGroupRequest) (*response.ProductAttributeGroupListResponse, error)
	GetAllProductAttributeGroups(ctx context.Context, tenantID int) ([]*response.ProductAttributeGroupResponse, error)
}

// productAttributeGroupService implements the ProductAttributeGroupService interface
type productAttributeGroupService struct {
	groupRepo repository.ProductAttributeGroupRepository
}

// NewProductAttributeGroupService creates a new product attribute group service instance
func NewProductAttributeGroupService(groupRepo repository.ProductAttributeGroupRepository) ProductAttributeGroupService {
	return &productAttributeGroupService{
		groupRepo: groupRepo,
	}
}

// CreateProductAttributeGroup creates a new product attribute group
func (s *productAttributeGroupService) CreateProductAttributeGroup(ctx context.Context, tenantID int, req *request.CreateProductAttributeGroupRequest) (*response.ProductAttributeGroupResponse, error) {
	// Check if a group with the same code already exists
	existingGroup, err := s.groupRepo.GetByCode(ctx, tenantID, req.Code)
	if err == nil && existingGroup != nil {
		return nil, fmt.Errorf("a product attribute group with code '%s' already exists", req.Code)
	}

	// Create group model
	group := &models.ProductAttributeGroup{
		TenantID:     uint(tenantID),
		Name:         req.Name,
		Code:         req.Code,
		DisplayOrder: req.DisplayOrder,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Save to repository
	if err := s.groupRepo.Create(ctx, group); err != nil {
		return nil, fmt.Errorf("failed to create product attribute group: %w", err)
	}

	// Map to response
	return mapProductAttributeGroupToResponse(group), nil
}

// GetProductAttributeGroup retrieves a product attribute group by ID
func (s *productAttributeGroupService) GetProductAttributeGroup(ctx context.Context, tenantID int, groupID int) (*response.ProductAttributeGroupResponse, error) {
	group, err := s.groupRepo.GetByID(ctx, tenantID, groupID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product attribute group: %w", err)
	}

	return mapProductAttributeGroupToResponse(group), nil
}

// GetProductAttributeGroupByCode retrieves a product attribute group by code
func (s *productAttributeGroupService) GetProductAttributeGroupByCode(ctx context.Context, tenantID int, code string) (*response.ProductAttributeGroupResponse, error) {
	group, err := s.groupRepo.GetByCode(ctx, tenantID, code)
	if err != nil {
		return nil, fmt.Errorf("failed to get product attribute group by code: %w", err)
	}

	return mapProductAttributeGroupToResponse(group), nil
}

// UpdateProductAttributeGroup updates an existing product attribute group
func (s *productAttributeGroupService) UpdateProductAttributeGroup(ctx context.Context, tenantID int, groupID int, req *request.UpdateProductAttributeGroupRequest) (*response.ProductAttributeGroupResponse, error) {
	// Get existing group
	group, err := s.groupRepo.GetByID(ctx, tenantID, groupID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product attribute group for update: %w", err)
	}

	// Check if code is being changed and if the new code already exists
	if req.Code != "" && req.Code != group.Code {
		existingGroup, err := s.groupRepo.GetByCode(ctx, tenantID, req.Code)
		if err == nil && existingGroup != nil && existingGroup.GroupID != group.GroupID {
			return nil, fmt.Errorf("a product attribute group with code '%s' already exists", req.Code)
		}
	}

	// Update fields if provided
	if req.Name != "" {
		group.Name = req.Name
	}
	
	if req.Code != "" {
		group.Code = req.Code
	}
	
	group.DisplayOrder = req.DisplayOrder
	
	// Update timestamp
	group.UpdatedAt = time.Now()

	// Save to repository
	if err := s.groupRepo.Update(ctx, group); err != nil {
		return nil, fmt.Errorf("failed to update product attribute group: %w", err)
	}

	return mapProductAttributeGroupToResponse(group), nil
}

// DeleteProductAttributeGroup removes a product attribute group
func (s *productAttributeGroupService) DeleteProductAttributeGroup(ctx context.Context, tenantID int, groupID int) error {
	if err := s.groupRepo.Delete(ctx, tenantID, groupID); err != nil {
		return fmt.Errorf("failed to delete product attribute group: %w", err)
	}

	return nil
}

// ListProductAttributeGroups retrieves a list of product attribute groups with pagination
func (s *productAttributeGroupService) ListProductAttributeGroups(ctx context.Context, tenantID int, req *request.ListProductAttributeGroupRequest) (*response.ProductAttributeGroupListResponse, error) {
	// Set default limit if not provided
	limit := req.Limit
	if limit <= 0 {
		limit = 10
	}

	// Get groups from repository
	groups, nextCursor, hasMore, err := s.groupRepo.List(ctx, tenantID, req.Cursor, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to list product attribute groups: %w", err)
	}

	// Map to response
	groupResponses := make([]*response.ProductAttributeGroupResponse, len(groups))
	for i, group := range groups {
		groupResponses[i] = mapProductAttributeGroupToResponse(group)
	}

	return &response.ProductAttributeGroupListResponse{
		AttributeGroups: groupResponses,
		Meta: response.PaginationMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}, nil
}

// GetAllProductAttributeGroups retrieves all product attribute groups for a tenant
func (s *productAttributeGroupService) GetAllProductAttributeGroups(ctx context.Context, tenantID int) ([]*response.ProductAttributeGroupResponse, error) {
	groups, err := s.groupRepo.GetAll(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get all product attribute groups: %w", err)
	}

	// Map to response
	groupResponses := make([]*response.ProductAttributeGroupResponse, len(groups))
	for i, group := range groups {
		groupResponses[i] = mapProductAttributeGroupToResponse(group)
	}

	return groupResponses, nil
}

// mapProductAttributeGroupToResponse maps a product attribute group model to a response DTO
func mapProductAttributeGroupToResponse(group *models.ProductAttributeGroup) *response.ProductAttributeGroupResponse {
	return &response.ProductAttributeGroupResponse{
		ID:           int(group.GroupID),
		TenantID:     int(group.TenantID),
		Name:         group.Name,
		Code:         group.Code,
		DisplayOrder: group.DisplayOrder,
		CreatedAt:    group.CreatedAt,
		UpdatedAt:    group.UpdatedAt,
	}
}
