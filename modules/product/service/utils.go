package service

import (
	"context"
	// TODO: Add auth import when available
	// "wnapi/internal/pkg/auth"
)

// getUserIDFromContext extracts the user ID from context
func getUserIDFromContext(ctx context.Context) int64 {
	// TODO: Implement proper user ID extraction when auth package is available
	/*
		// Get user ID from context if available
		userIDValue := ctx.Value(auth.UserIDKey)
		if userIDValue != nil {
			// Convert to int64 if possible
			if userID, ok := userIDValue.(uint); ok {
				return int64(userID)
			}
			if userID, ok := userIDValue.(int); ok {
				return int64(userID)
			}
		}
	*/

	// For testing purposes, return a default user ID
	return 1
}

// Các hàm tiện ích khác có thể được thêm vào đây trong tương lai
