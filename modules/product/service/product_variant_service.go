package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/dto/response"
	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
)

// ProductVariantService xử lý logic nghiệp vụ cho biến thể sản phẩm
type ProductVariantService interface {
	CreateVariant(ctx context.Context, tenantID int, req *request.CreateProductVariantRequest) (*response.ProductVariantResponse, error)
	BatchCreateVariants(ctx context.Context, tenantID int, req *request.BatchCreateProductVariantRequest) ([]response.ProductVariantResponse, error)
	GetVariant(ctx context.Context, tenantID int, variantID int) (*response.ProductVariantResponse, error)
	UpdateVariant(ctx context.Context, tenantID int, variantID int, req *request.UpdateProductVariantRequest) (*response.ProductVariantResponse, error)
	DeleteVariant(ctx context.Context, tenantID int, variantID int) error
	GetVariantsByProductID(ctx context.Context, tenantID int, productID int) ([]response.ProductVariantResponse, error)
	DeleteVariantsByProductID(ctx context.Context, tenantID int, productID int) error
	ListVariants(ctx context.Context, tenantID int, cursor string, limit int) (*response.ProductVariantListResponse, error)
}

// productVariantService thực hiện ProductVariantService interface
type productVariantService struct {
	variantRepo repository.ProductVariantRepository
	productRepo repository.ProductRepository
}

// NewProductVariantService tạo một instance mới của ProductVariantService
func NewProductVariantService(
	variantRepo repository.ProductVariantRepository,
	productRepo repository.ProductRepository,
) ProductVariantService {
	return &productVariantService{
		variantRepo: variantRepo,
		productRepo: productRepo,
	}
}

// CreateVariant tạo một biến thể sản phẩm mới
func (s *productVariantService) CreateVariant(
	ctx context.Context,
	tenantID int,
	req *request.CreateProductVariantRequest,
) (*response.ProductVariantResponse, error) {
	// Kiểm tra sản phẩm có tồn tại không
	_, err := s.productRepo.GetByID(ctx, tenantID, int(req.ProductID))
	if err != nil {
		return nil, fmt.Errorf("product not found: %w", err)
	}

	// Tạo model từ request
	variant := &models.ProductVariant{
		TenantID:  uint(tenantID),
		ProductID: req.ProductID,
		SKU:       req.SKU,
		Price:     req.Price,
		CostPrice: req.CostPrice,
		ImageURL:  req.ImageURL,
		IsActive:  req.IsActive,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Lưu vào repository
	if err := s.variantRepo.Create(ctx, variant); err != nil {
		return nil, fmt.Errorf("failed to create product variant: %w", err)
	}

	// Chuyển đổi sang response
	resp := response.NewProductVariantResponse(variant)
	return resp, nil
}

// BatchCreateVariants tạo nhiều biến thể sản phẩm cùng lúc
func (s *productVariantService) BatchCreateVariants(
	ctx context.Context,
	tenantID int,
	req *request.BatchCreateProductVariantRequest,
) ([]response.ProductVariantResponse, error) {
	// Kiểm tra sản phẩm có tồn tại không
	_, err := s.productRepo.GetByID(ctx, tenantID, int(req.ProductID))
	if err != nil {
		return nil, fmt.Errorf("product not found: %w", err)
	}

	// Tạo các model từ request
	variants := make([]*models.ProductVariant, 0, len(req.Variants))
	now := time.Now()

	for _, variantData := range req.Variants {
		variant := &models.ProductVariant{
			TenantID:  uint(tenantID),
			ProductID: req.ProductID,
			SKU:       variantData.SKU,
			Price:     variantData.Price,
			CostPrice: variantData.CostPrice,
			ImageURL:  variantData.ImageURL,
			IsActive:  variantData.IsActive,
			CreatedAt: now,
			UpdatedAt: now,
		}
		variants = append(variants, variant)
	}

	// Lưu các biến thể vào repository
	if err := s.variantRepo.CreateBatch(ctx, variants); err != nil {
		return nil, fmt.Errorf("failed to create product variants: %w", err)
	}

	// Lấy danh sách variants vừa tạo
	createdVariants, err := s.variantRepo.ListByProductID(ctx, tenantID, int(req.ProductID))
	if err != nil {
		return nil, fmt.Errorf("failed to get created product variants: %w", err)
	}

	// Chuyển đổi sang response
	return response.NewProductVariantListResponse(createdVariants), nil
}

// GetVariant lấy thông tin chi tiết một biến thể sản phẩm
func (s *productVariantService) GetVariant(
	ctx context.Context,
	tenantID int,
	variantID int,
) (*response.ProductVariantResponse, error) {
	variant, err := s.variantRepo.GetByID(ctx, tenantID, variantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product variant: %w", err)
	}

	return response.NewProductVariantResponse(variant), nil
}

// UpdateVariant cập nhật thông tin một biến thể sản phẩm
func (s *productVariantService) UpdateVariant(
	ctx context.Context,
	tenantID int,
	variantID int,
	req *request.UpdateProductVariantRequest,
) (*response.ProductVariantResponse, error) {
	// Lấy biến thể hiện tại
	variant, err := s.variantRepo.GetByID(ctx, tenantID, variantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product variant: %w", err)
	}

	// Cập nhật các trường
	variant.SKU = req.SKU
	variant.Price = req.Price
	variant.CostPrice = req.CostPrice
	variant.ImageURL = req.ImageURL
	variant.IsActive = req.IsActive
	variant.UpdatedAt = time.Now()

	// Lưu vào repository
	if err := s.variantRepo.Update(ctx, variant); err != nil {
		return nil, fmt.Errorf("failed to update product variant: %w", err)
	}

	return response.NewProductVariantResponse(variant), nil
}

// DeleteVariant xóa một biến thể sản phẩm
func (s *productVariantService) DeleteVariant(
	ctx context.Context,
	tenantID int,
	variantID int,
) error {
	if err := s.variantRepo.Delete(ctx, tenantID, variantID); err != nil {
		return fmt.Errorf("failed to delete product variant: %w", err)
	}

	return nil
}

// GetVariantsByProductID lấy tất cả biến thể của một sản phẩm
func (s *productVariantService) GetVariantsByProductID(
	ctx context.Context,
	tenantID int,
	productID int,
) ([]response.ProductVariantResponse, error) {
	variants, err := s.variantRepo.ListByProductID(ctx, tenantID, productID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product variants: %w", err)
	}

	return response.NewProductVariantListResponse(variants), nil
}

// DeleteVariantsByProductID xóa tất cả biến thể của một sản phẩm
func (s *productVariantService) DeleteVariantsByProductID(
	ctx context.Context,
	tenantID int,
	productID int,
) error {
	if err := s.variantRepo.DeleteByProductID(ctx, tenantID, productID); err != nil {
		return fmt.Errorf("failed to delete product variants: %w", err)
	}

	return nil
}

// ListVariants lấy danh sách biến thể sản phẩm có phân trang
func (s *productVariantService) ListVariants(
	ctx context.Context,
	tenantID int,
	cursor string,
	limit int,
) (*response.ProductVariantListResponse, error) {
	variants, cursorInfo, err := s.variantRepo.List(ctx, tenantID, cursor, limit, "variant_id", "ASC")
	if err != nil {
		return nil, fmt.Errorf("failed to list product variants: %w", err)
	}

	respVariants := response.NewProductVariantListResponse(variants)
	return &response.ProductVariantListResponse{
		Variants: respVariants,
		Meta: response.PaginationMeta{
			NextCursor: cursorInfo.NextCursor,
			HasMore:    cursorInfo.HasMore,
		},
	}, nil
}
