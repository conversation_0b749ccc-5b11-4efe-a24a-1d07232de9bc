package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/dto/response"
	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
	"wnapi/internal/pkg/pagination"
)

// ProductVariantAttributeValueService định nghĩa các phương thức làm việc với giá trị thuộc tính biến thể
type ProductVariantAttributeValueService interface {
	ListVariantAttributeValues(ctx context.Context, tenantID int, variantID uint, attributeID uint, cursor string, limit int) (*response.VariantAttributeValueListResponse, error)
	GetVariantAttributeValue(ctx context.Context, tenantID int, valueID uint) (*response.VariantAttributeValueResponse, error)
	CreateVariantAttributeValue(ctx context.Context, tenantID int, req *request.CreateVariantAttributeValueRequest) (*response.VariantAttributeValueResponse, error)
	UpdateVariantAttributeValue(ctx context.Context, tenantID int, valueID uint, req *request.UpdateVariantAttributeValueRequest) (*response.VariantAttributeValueResponse, error)
	DeleteVariantAttributeValue(ctx context.Context, tenantID int, valueID uint) error
	GetVariantAttributeValuesByVariantID(ctx context.Context, tenantID int, variantID uint) ([]*response.VariantAttributeValueResponse, error)
	BatchCreateVariantAttributeValues(ctx context.Context, tenantID int, req *request.BatchCreateVariantAttributeValueRequest) ([]*response.VariantAttributeValueResponse, error)
	DeleteVariantAttributeValuesByVariantID(ctx context.Context, tenantID int, variantID uint) error
}

// productVariantAttributeValueService triển khai ProductVariantAttributeValueService
type productVariantAttributeValueService struct {
	repo          repository.ProductVariantAttributeValueRepository
	variantRepo   repository.ProductVariantRepository
	attributeRepo repository.ProductAttributeRepository
}

// NewProductVariantAttributeValueService tạo instance mới của ProductVariantAttributeValueService
func NewProductVariantAttributeValueService(
	repo repository.ProductVariantAttributeValueRepository,
	variantRepo repository.ProductVariantRepository,
	attributeRepo repository.ProductAttributeRepository,
) ProductVariantAttributeValueService {
	return &productVariantAttributeValueService{
		repo:          repo,
		variantRepo:   variantRepo,
		attributeRepo: attributeRepo,
	}
}

// ListVariantAttributeValues lấy danh sách các giá trị thuộc tính biến thể
func (s *productVariantAttributeValueService) ListVariantAttributeValues(
	ctx context.Context,
	tenantID int,
	variantID uint,
	attributeID uint,
	cursor string,
	limit int,
) (*response.VariantAttributeValueListResponse, error) {
	// Thiết lập limit mặc định
	if limit <= 0 {
		limit = 10
	}

	// Lấy dữ liệu từ repository
	var values []*models.ProductVariantAttributeValue
	var cursorInfo *pagination.CursorInfo

	if attributeID > 0 {
		// Nếu có attribute ID, lấy theo attribute
		vals, err := s.repo.ListByAttributeID(ctx, tenantID, int(attributeID))
		if err != nil {
			return nil, fmt.Errorf("failed to list variant attribute values: %w", err)
		}
		values = vals
		cursorInfo = &pagination.CursorInfo{
			NextCursor: "",
			HasMore:    false,
		}
	} else if variantID > 0 {
		// Nếu có variant ID, lấy theo variant
		vals, err := s.repo.ListByVariantID(ctx, tenantID, int(variantID))
		if err != nil {
			return nil, fmt.Errorf("failed to list variant attribute values: %w", err)
		}
		values = vals
		cursorInfo = &pagination.CursorInfo{
			NextCursor: "",
			HasMore:    false,
		}
	} else {
		// Nếu không có bộ lọc cụ thể, hiện không thể lấy tất cả với phân trang
		return nil, fmt.Errorf("list without filter is not implemented")
	}

	// Chuyển đổi thành response
	respValues := make([]*response.VariantAttributeValueResponse, 0, len(values))
	for _, value := range values {
		resp, err := s.mapToResponse(value)
		if err != nil {
			return nil, err
		}
		respValues = append(respValues, resp)
	}

	return &response.VariantAttributeValueListResponse{
		Values: respValues,
		Meta: response.CursorMeta{
			NextCursor: cursorInfo.NextCursor,
			HasMore:    cursorInfo.HasMore,
		},
	}, nil
}

// GetVariantAttributeValue lấy chi tiết giá trị thuộc tính biến thể
func (s *productVariantAttributeValueService) GetVariantAttributeValue(
	ctx context.Context,
	tenantID int,
	valueID uint,
) (*response.VariantAttributeValueResponse, error) {
	// Lấy dữ liệu từ repository
	value, err := s.repo.GetByID(ctx, tenantID, int(valueID))
	if err != nil {
		return nil, fmt.Errorf("failed to get variant attribute value: %w", err)
	}

	// Chuyển đổi thành response
	return s.mapToResponse(value)
}

// CreateVariantAttributeValue tạo mới giá trị thuộc tính biến thể
func (s *productVariantAttributeValueService) CreateVariantAttributeValue(
	ctx context.Context,
	tenantID int,
	req *request.CreateVariantAttributeValueRequest,
) (*response.VariantAttributeValueResponse, error) {
	// Kiểm tra dữ liệu đầu vào
	if err := s.validateVariantAndAttribute(ctx, tenantID, req.VariantID, req.AttributeID); err != nil {
		return nil, err
	}

	// Tạo entity từ request
	value := &models.ProductVariantAttributeValue{
		TenantID:    uint(tenantID),
		VariantID:   req.VariantID,
		AttributeID: req.AttributeID,
		Value:       req.Value,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Lưu vào repository
	if err := s.repo.Create(ctx, value); err != nil {
		return nil, fmt.Errorf("failed to create variant attribute value: %w", err)
	}

	// Lấy dữ liệu đã tạo
	createdValue, err := s.repo.GetByID(ctx, tenantID, int(value.ValueID))
	if err != nil {
		return nil, fmt.Errorf("failed to get created variant attribute value: %w", err)
	}

	// Chuyển đổi thành response
	return s.mapToResponse(createdValue)
}

// UpdateVariantAttributeValue cập nhật giá trị thuộc tính biến thể
func (s *productVariantAttributeValueService) UpdateVariantAttributeValue(
	ctx context.Context,
	tenantID int,
	valueID uint,
	req *request.UpdateVariantAttributeValueRequest,
) (*response.VariantAttributeValueResponse, error) {
	// Lấy dữ liệu hiện tại
	value, err := s.repo.GetByID(ctx, tenantID, int(valueID))
	if err != nil {
		return nil, fmt.Errorf("failed to get variant attribute value: %w", err)
	}

	// Cập nhật thông tin
	value.Value = req.Value
	value.UpdatedAt = time.Now()

	// Lưu vào repository
	if err := s.repo.Update(ctx, value); err != nil {
		return nil, fmt.Errorf("failed to update variant attribute value: %w", err)
	}

	// Lấy dữ liệu đã cập nhật
	updatedValue, err := s.repo.GetByID(ctx, tenantID, int(valueID))
	if err != nil {
		return nil, fmt.Errorf("failed to get updated variant attribute value: %w", err)
	}

	// Chuyển đổi thành response
	return s.mapToResponse(updatedValue)
}

// DeleteVariantAttributeValue xóa giá trị thuộc tính biến thể
func (s *productVariantAttributeValueService) DeleteVariantAttributeValue(
	ctx context.Context,
	tenantID int,
	valueID uint,
) error {
	if err := s.repo.Delete(ctx, tenantID, int(valueID)); err != nil {
		return fmt.Errorf("failed to delete variant attribute value: %w", err)
	}
	return nil
}

// GetVariantAttributeValuesByVariantID lấy tất cả giá trị thuộc tính của một biến thể
func (s *productVariantAttributeValueService) GetVariantAttributeValuesByVariantID(
	ctx context.Context,
	tenantID int,
	variantID uint,
) ([]*response.VariantAttributeValueResponse, error) {
	// Lấy dữ liệu từ repository
	values, err := s.repo.ListByVariantID(ctx, tenantID, int(variantID))
	if err != nil {
		return nil, fmt.Errorf("failed to get variant attribute values: %w", err)
	}

	// Chuyển đổi thành response
	respValues := make([]*response.VariantAttributeValueResponse, 0, len(values))
	for _, value := range values {
		resp, err := s.mapToResponse(value)
		if err != nil {
			return nil, err
		}
		respValues = append(respValues, resp)
	}

	return respValues, nil
}

// BatchCreateVariantAttributeValues tạo nhiều giá trị thuộc tính biến thể cùng lúc
func (s *productVariantAttributeValueService) BatchCreateVariantAttributeValues(
	ctx context.Context,
	tenantID int,
	req *request.BatchCreateVariantAttributeValueRequest,
) ([]*response.VariantAttributeValueResponse, error) {
	// Kiểm tra biến thể
	_, err := s.variantRepo.GetByID(ctx, tenantID, int(req.VariantID))
	if err != nil {
		return nil, fmt.Errorf("variant not found: %w", err)
	}

	// Xóa các giá trị hiện tại nếu có
	if req.ReplaceExisting {
		if err := s.repo.DeleteByVariantID(ctx, tenantID, int(req.VariantID)); err != nil {
			return nil, fmt.Errorf("failed to delete existing variant attribute values: %w", err)
		}
	}

	// Tạo các entity từ request
	now := time.Now()
	values := make([]*models.ProductVariantAttributeValue, 0, len(req.Values))

	for _, valueData := range req.Values {
		// Kiểm tra thuộc tính
		if err := s.validateVariantAndAttribute(ctx, tenantID, req.VariantID, valueData.AttributeID); err != nil {
			return nil, err
		}

		value := &models.ProductVariantAttributeValue{
			TenantID:    uint(tenantID),
			VariantID:   req.VariantID,
			AttributeID: valueData.AttributeID,
			Value:       valueData.Value,
			CreatedAt:   now,
			UpdatedAt:   now,
		}
		values = append(values, value)
	}

	// Lưu vào repository
	if err := s.repo.CreateBatch(ctx, values); err != nil {
		return nil, fmt.Errorf("failed to batch create variant attribute values: %w", err)
	}

	// Lấy dữ liệu đã tạo
	createdValues, err := s.repo.ListByVariantID(ctx, tenantID, int(req.VariantID))
	if err != nil {
		return nil, fmt.Errorf("failed to get created variant attribute values: %w", err)
	}

	// Chuyển đổi thành response
	respValues := make([]*response.VariantAttributeValueResponse, 0, len(createdValues))
	for _, value := range createdValues {
		resp, err := s.mapToResponse(value)
		if err != nil {
			return nil, err
		}
		respValues = append(respValues, resp)
	}

	return respValues, nil
}

// DeleteVariantAttributeValuesByVariantID xóa tất cả giá trị thuộc tính của một biến thể
func (s *productVariantAttributeValueService) DeleteVariantAttributeValuesByVariantID(
	ctx context.Context,
	tenantID int,
	variantID uint,
) error {
	if err := s.repo.DeleteByVariantID(ctx, tenantID, int(variantID)); err != nil {
		return fmt.Errorf("failed to delete variant attribute values: %w", err)
	}
	return nil
}

// validateVariantAndAttribute kiểm tra tính hợp lệ của biến thể và thuộc tính
func (s *productVariantAttributeValueService) validateVariantAndAttribute(
	ctx context.Context,
	tenantID int,
	variantID uint,
	attributeID uint,
) error {
	// Kiểm tra biến thể
	_, err := s.variantRepo.GetByID(ctx, tenantID, int(variantID))
	if err != nil {
		return fmt.Errorf("variant not found: %w", err)
	}

	// Kiểm tra thuộc tính nếu repository được cung cấp
	if s.attributeRepo != nil {
		_, err = s.attributeRepo.GetByID(ctx, tenantID, int(attributeID))
		if err != nil {
			return fmt.Errorf("attribute not found: %w", err)
		}
	}

	return nil
}

// mapToResponse chuyển đổi entity thành response DTO
func (s *productVariantAttributeValueService) mapToResponse(
	value *models.ProductVariantAttributeValue,
) (*response.VariantAttributeValueResponse, error) {
	return &response.VariantAttributeValueResponse{
		ID:          int(value.ValueID),
		TenantID:    int(value.TenantID),
		VariantID:   int(value.VariantID),
		AttributeID: int(value.AttributeID),
		Value:       value.Value,
		CreatedAt:   value.CreatedAt,
		UpdatedAt:   value.UpdatedAt,
	}, nil
}
