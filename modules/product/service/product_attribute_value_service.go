package service

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"wnapi/modules/product/dto/request"
	"wnapi/modules/product/dto/response"
	"wnapi/modules/product/models"
	"wnapi/modules/product/repository"
)

// ProductAttributeValueService xử lý logic nghiệp vụ cho giá trị thuộc tính sản phẩm
type ProductAttributeValueService interface {
	CreateProductAttributeValue(ctx context.Context, tenantID int, req *request.CreateProductAttributeValueRequest) (*response.ProductAttributeValueResponse, error)
	BatchCreateProductAttributeValues(ctx context.Context, tenantID int, req *request.BatchCreateProductAttributeValueRequest) ([]*response.ProductAttributeValueResponse, error)
	GetProductAttributeValue(ctx context.Context, tenantID int, valueID int) (*response.ProductAttributeValueResponse, error)
	UpdateProductAttributeValue(ctx context.Context, tenantID int, valueID int, req *request.UpdateProductAttributeValueRequest) (*response.ProductAttributeValueResponse, error)
	DeleteProductAttributeValue(ctx context.Context, tenantID int, valueID int) error
	GetProductAttributeValuesByProductID(ctx context.Context, tenantID int, productID int) ([]*response.ProductAttributeValueResponse, error)
	GetProductAttributeValuesByAttributeID(ctx context.Context, tenantID int, attributeID int, cursor string, limit int) (*response.ProductAttributeValueListResponse, error)
	DeleteProductAttributeValuesByProductID(ctx context.Context, tenantID int, productID int) error
}

// productAttributeValueService thực hiện ProductAttributeValueService interface
type productAttributeValueService struct {
	valueRepo     repository.ProductAttributeValueRepository
	attributeRepo repository.ProductAttributeRepository
	optionRepo    repository.ProductAttributeOptionRepository
}

// NewProductAttributeValueService tạo một instance mới của ProductAttributeValueService
func NewProductAttributeValueService(
	valueRepo repository.ProductAttributeValueRepository,
	attributeRepo repository.ProductAttributeRepository,
	optionRepo repository.ProductAttributeOptionRepository,
) ProductAttributeValueService {
	return &productAttributeValueService{
		valueRepo:     valueRepo,
		attributeRepo: attributeRepo,
		optionRepo:    optionRepo,
	}
}

// CreateProductAttributeValue tạo mới một giá trị thuộc tính sản phẩm
func (s *productAttributeValueService) CreateProductAttributeValue(
	ctx context.Context,
	tenantID int,
	req *request.CreateProductAttributeValueRequest,
) (*response.ProductAttributeValueResponse, error) {
	// Validate dữ liệu
	if err := s.validateAttributeValue(ctx, tenantID, req.AttributeID, req.AttributeOptionID); err != nil {
		return nil, err
	}

	// Tạo model từ request
	attributeValue := &models.ProductAttributeValue{
		TenantID:      uint(tenantID),
		ReferenceID:   req.ReferenceID,
		ReferenceType: req.ReferenceType,
		AttributeID:   req.AttributeID,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Set các giá trị tùy chọn
	if req.ValueText != nil {
		attributeValue.ValueText = sql.NullString{String: *req.ValueText, Valid: true}
	}
	if req.ValueNumeric != nil {
		attributeValue.ValueNumeric = sql.NullFloat64{Float64: *req.ValueNumeric, Valid: true}
	}
	if req.ValueDate != nil {
		attributeValue.ValueDate = sql.NullTime{Time: *req.ValueDate, Valid: true}
	}
	if req.ValueBoolean != nil {
		attributeValue.ValueBoolean = sql.NullBool{Bool: *req.ValueBoolean, Valid: true}
	}
	if req.AttributeOptionID != nil {
		attributeValue.AttributeOptionID = req.AttributeOptionID
	}

	// Lưu vào repository
	if err := s.valueRepo.Create(ctx, attributeValue); err != nil {
		return nil, fmt.Errorf("failed to create product attribute value: %w", err)
	}

	// Lấy giá trị thuộc tính đã tạo để trả về
	createdValue, err := s.valueRepo.GetByID(ctx, tenantID, int(attributeValue.ValueID))
	if err != nil {
		return nil, fmt.Errorf("failed to get created product attribute value: %w", err)
	}

	// Ánh xạ sang response
	return s.mapAttributeValueToResponse(ctx, createdValue)
}

// BatchCreateProductAttributeValues tạo mới nhiều giá trị thuộc tính sản phẩm
func (s *productAttributeValueService) BatchCreateProductAttributeValues(
	ctx context.Context,
	tenantID int,
	req *request.BatchCreateProductAttributeValueRequest,
) ([]*response.ProductAttributeValueResponse, error) {
	// Tạo các model từ request
	attributeValues := make([]*models.ProductAttributeValue, 0, len(req.AttributeValues))

	for _, valueData := range req.AttributeValues {
		// Validate dữ liệu
		if err := s.validateAttributeValue(ctx, tenantID, valueData.AttributeID, valueData.AttributeOptionID); err != nil {
			return nil, err
		}

		// Tạo model
		attributeValue := &models.ProductAttributeValue{
			TenantID:      uint(tenantID),
			ReferenceID:   req.ProductID,
			ReferenceType: "PRODUCT",
			AttributeID:   valueData.AttributeID,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		// Set các giá trị tùy chọn
		if valueData.ValueText != nil {
			attributeValue.ValueText = sql.NullString{String: *valueData.ValueText, Valid: true}
		}
		if valueData.ValueNumeric != nil {
			attributeValue.ValueNumeric = sql.NullFloat64{Float64: *valueData.ValueNumeric, Valid: true}
		}
		if valueData.ValueDate != nil {
			attributeValue.ValueDate = sql.NullTime{Time: *valueData.ValueDate, Valid: true}
		}
		if valueData.ValueBoolean != nil {
			attributeValue.ValueBoolean = sql.NullBool{Bool: *valueData.ValueBoolean, Valid: true}
		}
		if valueData.AttributeOptionID != nil {
			attributeValue.AttributeOptionID = valueData.AttributeOptionID
		}

		attributeValues = append(attributeValues, attributeValue)
	}

	// Xóa các giá trị thuộc tính hiện tại của sản phẩm
	if err := s.valueRepo.DeleteByProductID(ctx, tenantID, int(req.ProductID)); err != nil {
		return nil, fmt.Errorf("failed to delete existing product attribute values: %w", err)
	}

	// Lưu các giá trị thuộc tính mới vào repository
	if err := s.valueRepo.CreateBatch(ctx, attributeValues); err != nil {
		return nil, fmt.Errorf("failed to create product attribute values: %w", err)
	}

	// Lấy các giá trị thuộc tính đã tạo để trả về
	createdValues, err := s.valueRepo.GetByProductID(ctx, tenantID, int(req.ProductID))
	if err != nil {
		return nil, fmt.Errorf("failed to get created product attribute values: %w", err)
	}

	// Ánh xạ sang response
	responses := make([]*response.ProductAttributeValueResponse, 0, len(createdValues))
	for _, value := range createdValues {
		resp, err := s.mapAttributeValueToResponse(ctx, value)
		if err != nil {
			return nil, err
		}
		responses = append(responses, resp)
	}

	return responses, nil
}

// GetProductAttributeValue lấy một giá trị thuộc tính sản phẩm theo ID
func (s *productAttributeValueService) GetProductAttributeValue(ctx context.Context, tenantID int, valueID int) (*response.ProductAttributeValueResponse, error) {
	// Lấy giá trị thuộc tính từ repository
	attributeValue, err := s.valueRepo.GetByID(ctx, tenantID, valueID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product attribute value: %w", err)
	}

	// Ánh xạ sang response
	return s.mapAttributeValueToResponse(ctx, attributeValue)
}

// UpdateProductAttributeValue cập nhật một giá trị thuộc tính sản phẩm đã tồn tại
func (s *productAttributeValueService) UpdateProductAttributeValue(
	ctx context.Context,
	tenantID int,
	valueID int,
	req *request.UpdateProductAttributeValueRequest,
) (*response.ProductAttributeValueResponse, error) {
	// Lấy giá trị thuộc tính hiện tại
	attributeValue, err := s.valueRepo.GetByID(ctx, tenantID, valueID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product attribute value for update: %w", err)
	}

	// Validate dữ liệu
	if req.AttributeOptionID != nil {
		if err := s.validateAttributeValue(ctx, tenantID, attributeValue.AttributeID, req.AttributeOptionID); err != nil {
			return nil, err
		}
	}

	// Cập nhật các trường nếu được cung cấp
	if req.ValueText != nil {
		attributeValue.ValueText = sql.NullString{String: *req.ValueText, Valid: true}
	}
	if req.ValueNumeric != nil {
		attributeValue.ValueNumeric = sql.NullFloat64{Float64: *req.ValueNumeric, Valid: true}
	}
	if req.ValueDate != nil {
		attributeValue.ValueDate = sql.NullTime{Time: *req.ValueDate, Valid: true}
	}
	if req.ValueBoolean != nil {
		attributeValue.ValueBoolean = sql.NullBool{Bool: *req.ValueBoolean, Valid: true}
	}
	if req.AttributeOptionID != nil {
		attributeValue.AttributeOptionID = req.AttributeOptionID
	}

	// Cập nhật timestamp
	attributeValue.UpdatedAt = time.Now()

	// Lưu vào repository
	if err := s.valueRepo.Update(ctx, attributeValue); err != nil {
		return nil, fmt.Errorf("failed to update product attribute value: %w", err)
	}

	// Ánh xạ sang response
	return s.mapAttributeValueToResponse(ctx, attributeValue)
}

// DeleteProductAttributeValue xóa một giá trị thuộc tính sản phẩm
func (s *productAttributeValueService) DeleteProductAttributeValue(ctx context.Context, tenantID int, valueID int) error {
	if err := s.valueRepo.Delete(ctx, tenantID, valueID); err != nil {
		return fmt.Errorf("failed to delete product attribute value: %w", err)
	}
	return nil
}

// GetProductAttributeValuesByProductID lấy tất cả giá trị thuộc tính của một sản phẩm
func (s *productAttributeValueService) GetProductAttributeValuesByProductID(ctx context.Context, tenantID int, productID int) ([]*response.ProductAttributeValueResponse, error) {
	// Lấy các giá trị thuộc tính từ repository
	attributeValues, err := s.valueRepo.GetByProductID(ctx, tenantID, productID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product attribute values: %w", err)
	}

	// Ánh xạ sang response
	responses := make([]*response.ProductAttributeValueResponse, 0, len(attributeValues))
	for _, value := range attributeValues {
		resp, err := s.mapAttributeValueToResponse(ctx, value)
		if err != nil {
			return nil, err
		}
		responses = append(responses, resp)
	}

	return responses, nil
}

// GetProductAttributeValuesByAttributeID lấy giá trị thuộc tính theo ID thuộc tính với phân trang
func (s *productAttributeValueService) GetProductAttributeValuesByAttributeID(
	ctx context.Context,
	tenantID int,
	attributeID int,
	cursor string,
	limit int,
) (*response.ProductAttributeValueListResponse, error) {
	// Lấy tất cả các giá trị thuộc tính từ repository
	attributeValues, err := s.valueRepo.GetByAttributeID(ctx, tenantID, attributeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get product attribute values by attribute ID: %w", err)
	}

	// Implement phân trang thủ công
	if limit <= 0 {
		limit = 10 // Giới hạn mặc định
	}

	// Tìm vị trí bắt đầu dựa trên cursor
	startIndex := 0
	if cursor != "" {
		cursorID, err := strconv.Atoi(cursor)
		if err == nil && cursorID > 0 {
			// Tìm vị trí của bản ghi có ID > cursorID
			for i, value := range attributeValues {
				if int(value.ValueID) > cursorID {
					startIndex = i
					break
				}
			}
		}
	}

	// Xác định số lượng bản ghi trả về và nextCursor
	endIndex := startIndex + limit
	hasMore := false
	var nextCursor string

	if endIndex < len(attributeValues) {
		hasMore = true
		nextCursor = fmt.Sprintf("%d", attributeValues[endIndex-1].ValueID)
	} else {
		endIndex = len(attributeValues)
	}

	// Lấy các bản ghi từ startIndex đến endIndex
	pagedValues := attributeValues[startIndex:endIndex]

	// Ánh xạ sang response
	responses := make([]*response.ProductAttributeValueResponse, 0, len(pagedValues))
	for _, value := range pagedValues {
		resp, err := s.mapAttributeValueToResponse(ctx, value)
		if err != nil {
			return nil, err
		}
		responses = append(responses, resp)
	}

	return &response.ProductAttributeValueListResponse{
		AttributeValues: responses,
		Meta: response.PaginationMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}, nil
}

// DeleteProductAttributeValuesByProductID xóa tất cả giá trị thuộc tính của một sản phẩm
func (s *productAttributeValueService) DeleteProductAttributeValuesByProductID(ctx context.Context, tenantID int, productID int) error {
	if err := s.valueRepo.DeleteByProductID(ctx, tenantID, productID); err != nil {
		return fmt.Errorf("failed to delete product attribute values by product ID: %w", err)
	}
	return nil
}

// validateAttributeValue kiểm tra tính hợp lệ của dữ liệu giá trị thuộc tính
func (s *productAttributeValueService) validateAttributeValue(ctx context.Context, tenantID int, attributeID uint, optionID *uint) error {
	// Kiểm tra xem thuộc tính có tồn tại không
	_, err := s.attributeRepo.GetByID(ctx, tenantID, int(attributeID))
	if err != nil {
		return fmt.Errorf("attribute not found: %w", err)
	}

	// Nếu có tùy chọn thuộc tính, kiểm tra xem nó có tồn tại không
	if optionID != nil {
		_, err := s.optionRepo.GetByID(ctx, tenantID, int(*optionID))
		if err != nil {
			return fmt.Errorf("attribute option not found: %w", err)
		}

		// Nên kiểm tra thêm xem tùy chọn có thuộc về thuộc tính không
		// Nhưng để đơn giản, chúng ta bỏ qua kiểm tra này
	}

	return nil
}

// mapAttributeValueToResponse ánh xạ model sang response DTO
func (s *productAttributeValueService) mapAttributeValueToResponse(ctx context.Context, attributeValue *models.ProductAttributeValue) (*response.ProductAttributeValueResponse, error) {
	resp := &response.ProductAttributeValueResponse{
		ID:            int(attributeValue.ValueID),
		TenantID:      int(attributeValue.TenantID),
		ReferenceID:   int(attributeValue.ReferenceID),
		ReferenceType: attributeValue.ReferenceType,
		AttributeID:   int(attributeValue.AttributeID),
		CreatedAt:     attributeValue.CreatedAt,
		UpdatedAt:     attributeValue.UpdatedAt,
	}

	// Xử lý các giá trị nullable
	if attributeValue.ValueText.Valid {
		text := attributeValue.ValueText.String
		resp.ValueText = &text
	}

	if attributeValue.ValueNumeric.Valid {
		numeric := attributeValue.ValueNumeric.Float64
		resp.ValueNumeric = &numeric
	}

	if attributeValue.ValueDate.Valid {
		date := attributeValue.ValueDate.Time
		resp.ValueDate = &date
	}

	if attributeValue.ValueBoolean.Valid {
		boolean := attributeValue.ValueBoolean.Bool
		resp.ValueBoolean = &boolean
	}

	if attributeValue.AttributeOptionID != nil {
		optionID := int(*attributeValue.AttributeOptionID)
		resp.AttributeOptionID = &optionID

		// Có thể thêm code ở đây để lấy thông tin chi tiết của tùy chọn thuộc tính nếu cần
	}

	return resp, nil
}
