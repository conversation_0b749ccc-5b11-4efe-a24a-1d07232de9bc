package integration

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"testing"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/spf13/viper"
)

var (
	testDB     *sql.DB
	apiBaseURL string
)

func TestMain(m *testing.M) {
	// Thiết lập môi trường test
	if err := setup(); err != nil {
		log.Fatalf("Lỗi thiết lập môi trường test: %v", err)
	}

	// Chạy tests
	exitCode := m.Run()

	// Dọn dẹp sau khi tests chạy xong
	if err := teardown(); err != nil {
		log.Printf("Lỗi dọn dẹp môi trường test: %v", err)
	}

	os.Exit(exitCode)
}

func setup() error {
	fmt.Println("Thiết lập môi trường test...")

	// Đọc cấu hình test
	if err := loadConfig(); err != nil {
		return fmt.Errorf("lỗi đọc cấu hình: %w", err)
	}

	// Chạy docker-compose để khởi động các dịch vụ phụ thuộc
	if err := startDockerServices(); err != nil {
		return fmt.Errorf("lỗi khởi động docker services: %w", err)
	}

	// Kết nối đến database
	if err := connectToDatabase(); err != nil {
		return fmt.Errorf("lỗi kết nối database: %w", err)
	}

	// Khởi động server API
	// Note: Đây là phần giả định, trong thực tế cần điều chỉnh theo cách project khởi động server
	// Có thể là khởi động server riêng hoặc sử dụng HTTP handler trực tiếp
	apiBaseURL = "http://localhost:8080"

	fmt.Println("Thiết lập môi trường test hoàn tất")
	return nil
}

func teardown() error {
	fmt.Println("Dọn dẹp môi trường test...")

	// Đóng kết nối database
	if testDB != nil {
		testDB.Close()
	}

	// Dừng docker services
	if err := stopDockerServices(); err != nil {
		return fmt.Errorf("lỗi dừng docker services: %w", err)
	}

	fmt.Println("Dọn dẹp môi trường test hoàn tất")
	return nil
}

func loadConfig() error {
	viper.SetConfigName("test-config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./setup")
	viper.AddConfigPath("../setup")
	viper.AddConfigPath("../../setup")
	viper.AddConfigPath("../../../setup")

	return viper.ReadInConfig()
}

func startDockerServices() error {
	cmd := exec.Command("docker", "compose", "-f", filepath.Join("setup", "docker-compose.yml"), "up", "-d")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return err
	}

	// Chờ services khởi động
	time.Sleep(5 * time.Second)
	return nil
}

func stopDockerServices() error {
	cmd := exec.Command("docker", "compose", "-f", filepath.Join("setup", "docker-compose.yml"), "down")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	return cmd.Run()
}

func connectToDatabase() error {
	dbHost := viper.GetString("database.host")
	dbPort := viper.GetInt("database.port")
	dbUser := viper.GetString("database.username")
	dbPass := viper.GetString("database.password")
	dbName := viper.GetString("database.name")

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true", dbUser, dbPass, dbHost, dbPort, dbName)
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return err
	}

	// Kiểm tra kết nối
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := db.PingContext(ctx); err != nil {
		return err
	}

	testDB = db
	return nil
}

// GetTestDB trả về đối tượng database để sử dụng trong tests
func GetTestDB(t *testing.T) *sql.DB {
	t.Helper()
	if testDB == nil {
		t.Fatal("Database chưa được khởi tạo")
	}
	return testDB
}

// GetAPIBaseURL trả về base URL của API để sử dụng trong tests
func GetAPIBaseURL() string {
	return apiBaseURL
}
