package api

import (
	"encoding/json"
	"net/http"
	"testing"

	"wnapi/modules/product/tests/integration"
	"wnapi/modules/product/tests/integration/helpers"
)

func TestListProducts(t *testing.T) {
	// Khởi tạo HTTP client
	client := helpers.NewTestClient(integration.GetAPIBaseURL())

	// Gọi API
	resp, err := client.Get("/api/v1/products")
	if err != nil {
		t.Fatalf("Không thể gọi API list products: %v", err)
	}

	// Kiểm tra status code
	helpers.AssertStatusCode(t, resp, http.StatusOK)

	// Kiểm tra cấu trúc response
	var response struct {
		Status struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
			Success bool   `json:"success"`
		} `json:"status"`
		Data []map[string]interface{} `json:"data"`
		Meta struct {
			NextCursor string `json:"next_cursor"`
			Has<PERSON><PERSON>    bool   `json:"has_more"`
		} `json:"meta"`
	}

	helpers.AssertJSONResponse(t, resp, &response)

	if !response.Status.Success {
		t.<PERSON>rf("API trả về trạng thái không thành công")
	}

	// Kiểm tra dữ liệu trả về
	if len(response.Data) == 0 {
		t.Log("API trả về danh sách sản phẩm trống")
	} else {
		// Kiểm tra cấu trúc của sản phẩm đầu tiên
		product := response.Data[0]
		requiredFields := []string{"id", "name", "price", "sku"}
		for _, field := range requiredFields {
			if _, exists := product[field]; !exists {
				t.Errorf("Thiếu trường bắt buộc '%s' trong sản phẩm", field)
			}
		}
	}
}

func TestGetProductDetail(t *testing.T) {
	// Khởi tạo HTTP client
	client := helpers.NewTestClient(integration.GetAPIBaseURL())

	// Gọi API với ID sản phẩm 1
	resp, err := client.Get("/api/v1/products/1")
	if err != nil {
		t.Fatalf("Không thể gọi API product detail: %v", err)
	}

	// Kiểm tra status code
	helpers.AssertStatusCode(t, resp, http.StatusOK)

	// Kiểm tra cấu trúc response
	var response struct {
		Status struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
			Success bool   `json:"success"`
		} `json:"status"`
		Data map[string]interface{} `json:"data"`
	}

	helpers.AssertJSONResponse(t, resp, &response)

	if !response.Status.Success {
		t.Errorf("API trả về trạng thái không thành công")
	}

	// Kiểm tra dữ liệu sản phẩm
	if response.Data["id"] != float64(1) {
		t.Errorf("ID sản phẩm không đúng: mong đợi %v, nhận được %v", 1, response.Data["id"])
	}

	requiredFields := []string{"name", "price", "sku", "description", "stock"}
	for _, field := range requiredFields {
		if _, exists := response.Data[field]; !exists {
			t.Errorf("Thiếu trường bắt buộc '%s' trong dữ liệu sản phẩm", field)
		}
	}
}

func TestCreateProduct(t *testing.T) {
	// Khởi tạo HTTP client
	client := helpers.NewTestClient(integration.GetAPIBaseURL())

	// Login để lấy token (giả định có API login)
	loginResp, err := client.PostJSON("/api/v1/auth/login", map[string]string{
		"email":    "<EMAIL>",
		"password": "password",
	})
	if err != nil {
		t.Fatalf("Không thể gọi API login: %v", err)
	}

	var loginData struct {
		Status struct {
			Success bool `json:"success"`
		} `json:"status"`
		Data struct {
			AccessToken string `json:"access_token"`
		} `json:"data"`
	}

	helpers.AssertJSONResponse(t, loginResp, &loginData)
	client.SetToken(loginData.Data.AccessToken)

	// Chuẩn bị dữ liệu sản phẩm
	newProduct := map[string]interface{}{
		"name":        "Sản phẩm test integration",
		"price":       199000.00,
		"sku":         "TEST-SKU-INT-001",
		"description": "Sản phẩm tạo từ integration test",
		"stock":       25,
	}

	// Gọi API tạo sản phẩm
	resp, err := client.PostJSON("/api/v1/products", newProduct)
	if err != nil {
		t.Fatalf("Không thể gọi API create product: %v", err)
	}

	// Kiểm tra status code
	helpers.AssertStatusCode(t, resp, http.StatusCreated)

	// Kiểm tra response
	var response struct {
		Status struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
			Success bool   `json:"success"`
		} `json:"status"`
		Data map[string]interface{} `json:"data"`
	}

	helpers.AssertJSONResponse(t, resp, &response)

	if !response.Status.Success {
		t.Errorf("API trả về trạng thái không thành công")
	}

	// Kiểm tra ID đã được tạo
	if _, exists := response.Data["id"]; !exists {
		t.Error("Không có ID trong dữ liệu sản phẩm trả về")
	}

	// Kiểm tra dữ liệu trả về
	for key, value := range newProduct {
		if response.Data[key] != value {
			t.Errorf("Trường '%s' không khớp: mong đợi %v, nhận được %v", key, value, response.Data[key])
		}
	}

	// Lưu ID để sử dụng ở test tiếp theo
	productID := int(response.Data["id"].(float64))

	// Kiểm tra sản phẩm đã được tạo thành công bằng cách lấy chi tiết
	getResp, err := client.Get("/api/v1/products/" + json.Number(productID).String())
	if err != nil {
		t.Fatalf("Không thể gọi API get product: %v", err)
	}

	helpers.AssertStatusCode(t, getResp, http.StatusOK)
}

func TestUpdateProduct(t *testing.T) {
	// Khởi tạo HTTP client
	client := helpers.NewTestClient(integration.GetAPIBaseURL())

	// Login để lấy token
	loginResp, err := client.PostJSON("/api/v1/auth/login", map[string]string{
		"email":    "<EMAIL>",
		"password": "password",
	})
	if err != nil {
		t.Fatalf("Không thể gọi API login: %v", err)
	}

	var loginData struct {
		Status struct {
			Success bool `json:"success"`
		} `json:"status"`
		Data struct {
			AccessToken string `json:"access_token"`
		} `json:"data"`
	}

	helpers.AssertJSONResponse(t, loginResp, &loginData)
	client.SetToken(loginData.Data.AccessToken)

	// Lấy sản phẩm cần cập nhật
	productID := "1"
	getResp, err := client.Get("/api/v1/products/" + productID)
	if err != nil {
		t.Fatalf("Không thể gọi API get product: %v", err)
	}

	var getProduct struct {
		Data map[string]interface{} `json:"data"`
	}

	helpers.AssertJSONResponse(t, getResp, &getProduct)

	// Chuẩn bị dữ liệu cập nhật
	updatedData := map[string]interface{}{
		"name":        getProduct.Data["name"],
		"price":       5990000.00, // Giá mới
		"description": "Mô tả đã được cập nhật từ integration test",
		"stock":       50, // Số lượng mới
	}

	// Gọi API cập nhật sản phẩm
	updateResp, err := client.PutJSON("/api/v1/products/"+productID, updatedData)
	if err != nil {
		t.Fatalf("Không thể gọi API update product: %v", err)
	}

	// Kiểm tra status code
	helpers.AssertStatusCode(t, updateResp, http.StatusOK)

	// Kiểm tra response
	var updateResponse struct {
		Status struct {
			Success bool `json:"success"`
		} `json:"status"`
		Data map[string]interface{} `json:"data"`
	}

	helpers.AssertJSONResponse(t, updateResp, &updateResponse)

	if !updateResponse.Status.Success {
		t.Errorf("API trả về trạng thái không thành công")
	}

	// Kiểm tra dữ liệu đã cập nhật
	for key, value := range updatedData {
		if updateResponse.Data[key] != value {
			t.Errorf("Trường '%s' không khớp sau khi cập nhật: mong đợi %v, nhận được %v", key, value, updateResponse.Data[key])
		}
	}
}

func TestDeleteProduct(t *testing.T) {
	// Khởi tạo HTTP client
	client := helpers.NewTestClient(integration.GetAPIBaseURL())

	// Login để lấy token
	loginResp, err := client.PostJSON("/api/v1/auth/login", map[string]string{
		"email":    "<EMAIL>",
		"password": "password",
	})
	if err != nil {
		t.Fatalf("Không thể gọi API login: %v", err)
	}

	var loginData struct {
		Status struct {
			Success bool `json:"success"`
		} `json:"status"`
		Data struct {
			AccessToken string `json:"access_token"`
		} `json:"data"`
	}

	helpers.AssertJSONResponse(t, loginResp, &loginData)
	client.SetToken(loginData.Data.AccessToken)

	// Tạo sản phẩm mới để xóa
	newProduct := map[string]interface{}{
		"name":        "Sản phẩm để xóa",
		"price":       99000.00,
		"sku":         "DELETE-TEST-001",
		"description": "Sản phẩm tạo để test xóa",
		"stock":       10,
	}

	createResp, err := client.PostJSON("/api/v1/products", newProduct)
	if err != nil {
		t.Fatalf("Không thể gọi API create product: %v", err)
	}

	var createData struct {
		Data map[string]interface{} `json:"data"`
	}

	helpers.AssertJSONResponse(t, createResp, &createData)
	productID := int(createData.Data["id"].(float64))

	// Xóa sản phẩm
	deleteResp, err := client.Delete("/api/v1/products/" + json.Number(productID).String())
	if err != nil {
		t.Fatalf("Không thể gọi API delete product: %v", err)
	}

	// Kiểm tra status code
	helpers.AssertStatusCode(t, deleteResp, http.StatusOK)

	// Kiểm tra response
	var deleteResponse struct {
		Status struct {
			Success bool `json:"success"`
		} `json:"status"`
	}

	helpers.AssertJSONResponse(t, deleteResp, &deleteResponse)

	if !deleteResponse.Status.Success {
		t.Errorf("API trả về trạng thái không thành công khi xóa")
	}

	// Kiểm tra sản phẩm đã bị xóa
	getResp, err := client.Get("/api/v1/products/" + json.Number(productID).String())
	if err != nil {
		t.Fatalf("Không thể gọi API get product: %v", err)
	}

	// Nên trả về 404 Not Found
	helpers.AssertStatusCode(t, getResp, http.StatusNotFound)
}
