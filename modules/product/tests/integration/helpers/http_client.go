package helpers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// TestClient là wrapper cho http.Client, cung cấp các tiện ích cho việc test
type TestClient struct {
	BaseURL    string
	HTTPClient *http.Client
	Token      string
}

// NewTestClient tạo client mới với base URL
func NewTestClient(baseURL string) *TestClient {
	return &TestClient{
		BaseURL: baseURL,
		HTTPClient: &http.Client{
			Timeout: time.Second * 10,
		},
	}
}

// SetToken đặt token để sử dụng cho các request tiếp theo
func (c *TestClient) SetToken(token string) {
	c.Token = token
}

// Get gửi HTTP GET request
func (c *TestClient) Get(path string) (*http.Response, error) {
	return c.Request(http.MethodGet, path, nil)
}

// Post gửi HTTP POST request với body
func (c *TestClient) Post(path string, body io.Reader) (*http.Response, error) {
	return c.Request(http.MethodPost, path, body)
}

// PostJSON gửi HTTP POST request với body dạng JSON
func (c *TestClient) PostJSON(path string, data interface{}) (*http.Response, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("lỗi marshal JSON: %w", err)
	}

	return c.Post(path, bytes.NewBuffer(jsonData))
}

// Put gửi HTTP PUT request với body
func (c *TestClient) Put(path string, body io.Reader) (*http.Response, error) {
	return c.Request(http.MethodPut, path, body)
}

// PutJSON gửi HTTP PUT request với body dạng JSON
func (c *TestClient) PutJSON(path string, data interface{}) (*http.Response, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("lỗi marshal JSON: %w", err)
	}

	return c.Put(path, bytes.NewBuffer(jsonData))
}

// Delete gửi HTTP DELETE request
func (c *TestClient) Delete(path string) (*http.Response, error) {
	return c.Request(http.MethodDelete, path, nil)
}

// Request gửi HTTP request với method, path và body
func (c *TestClient) Request(method, path string, body io.Reader) (*http.Response, error) {
	url := c.BaseURL + path
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, fmt.Errorf("lỗi tạo request: %w", err)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	if c.Token != "" {
		req.Header.Set("Authorization", "Bearer "+c.Token)
	}

	return c.HTTPClient.Do(req)
}
