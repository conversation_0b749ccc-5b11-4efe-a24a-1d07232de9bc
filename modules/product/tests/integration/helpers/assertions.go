package helpers

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
)

// AssertStatusCode kiểm tra xem status code có đúng như mong đợi
func AssertStatusCode(t *testing.T, resp *http.Response, expected int) {
	if resp.StatusCode != expected {
		t.Errorf("Mong đợi status code %d, nhận được %d", expected, resp.StatusCode)
	}
}

// AssertJSONResponse kiểm tra và parse JSON response
func AssertJSONResponse(t *testing.T, resp *http.Response, v interface{}) {
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Lỗi đọc response body: %v", err)
	}

	if err := json.Unmarshal(body, v); err != nil {
		t.Fatalf("Lỗi unmarshal JSON: %v, body: %s", err, string(body))
	}
}

// AssertSuccessResponse kiểm tra xem API có trả về success response
func AssertSuccessResponse(t *testing.T, resp *http.Response) {
	var response struct {
		Status struct {
			Code    int    `json:"code"`
			Message string `json:"message"`
			Success bool   `json:"success"`
		} `json:"status"`
	}

	AssertJSONResponse(t, resp, &response)

	if !response.Status.Success {
		t.Errorf("API trả về status.success = false, message: %s", response.Status.Message)
	}
}

// AssertErrorResponse kiểm tra xem API có trả về error response với error_code
func AssertErrorResponse(t *testing.T, resp *http.Response, expectedErrorCode string) {
	var response struct {
		Status struct {
			Code      int    `json:"code"`
			Message   string `json:"message"`
			Success   bool   `json:"success"`
			ErrorCode string `json:"error_code"`
		} `json:"status"`
	}

	AssertJSONResponse(t, resp, &response)

	if response.Status.Success {
		t.Error("API trả về status.success = true, nhưng mong đợi error response")
	}

	if response.Status.ErrorCode != expectedErrorCode {
		t.Errorf("Mong đợi error_code '%s', nhận được '%s'", expectedErrorCode, response.Status.ErrorCode)
	}
}

// AssertContainsJSON kiểm tra xem JSON response có chứa các giá trị mong đợi
func AssertContainsJSON(t *testing.T, resp *http.Response, expectedValues map[string]interface{}) {
	defer resp.Body.Close()

	var responseData map[string]interface{}
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Lỗi đọc response body: %v", err)
	}

	if err := json.Unmarshal(body, &responseData); err != nil {
		t.Fatalf("Lỗi unmarshal JSON: %v, body: %s", err, string(body))
	}

	for key, expectedValue := range expectedValues {
		if actualValue, exists := responseData[key]; exists {
			if fmt.Sprintf("%v", actualValue) != fmt.Sprintf("%v", expectedValue) {
				t.Errorf("Cho key '%s': mong đợi '%v', nhận được '%v'", key, expectedValue, actualValue)
			}
		} else {
			t.Errorf("Key '%s' không tồn tại trong response", key)
		}
	}
}
