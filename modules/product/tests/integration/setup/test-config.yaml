app:
  name: "ecom-test"
  env: "test"
  version: "1.0.0"
  
database:
  driver: "mysql"
  host: "localhost"
  port: 3306
  username: "root"
  password: "password"
  name: "ecom_test"
  max_idle_conn: 10
  max_open_conn: 100
  conn_max_lifetime: "1h"

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0

jwt:
  access_signing_key: "test-signing-key"
  refresh_signing_key: "test-refresh-key"
  access_token_expiration: "1h"
  refresh_token_expiration: "24h"
  issuer: "ecom-test"

log:
  level: "debug"
  format: "text" 