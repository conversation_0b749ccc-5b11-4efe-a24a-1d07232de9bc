-- <PERSON><PERSON><PERSON> các bảng cần thiết cho tests
CREATE TABLE IF NOT EXISTS ecom_products (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  price DECIMAL(18, 2) DEFAULT 0.00,
  sku VARCHAR(50) UNIQUE,
  description TEXT,
  stock INT UNSIGNED DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS ecom_orders (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  customer_id INT UNSIGNED NOT NULL,
  status VARCHAR(50) NOT NULL,
  total_amount DECIMAL(18, 2) DEFAULT 0.00,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS ecom_order_items (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  order_id INT UNSIGNED NOT NULL,
  product_id INT UNSIGNED NOT NULL,
  quantity INT UNSIGNED NOT NULL,
  price DECIMAL(18, 2) DEFAULT 0.00,
  FOREIGN KEY (order_id) REFERENCES ecom_orders(id),
  FOREIGN KEY (product_id) REFERENCES ecom_products(id)
);

CREATE TABLE IF NOT EXISTS ecom_carts (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  customer_id INT UNSIGNED NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS ecom_cart_items (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  cart_id INT UNSIGNED NOT NULL,
  product_id INT UNSIGNED NOT NULL,
  quantity INT UNSIGNED NOT NULL,
  FOREIGN KEY (cart_id) REFERENCES ecom_carts(id),
  FOREIGN KEY (product_id) REFERENCES ecom_products(id)
);

-- Dữ liệu mẫu cho tests
INSERT INTO ecom_products (name, price, sku, description, stock) VALUES
('Điện thoại Xiaomi Redmi Note 12', 4990000.00, 'XM-RN12-64GB', 'Điện thoại Xiaomi Redmi Note 12 64GB, RAM 4GB, Màn hình 6.67 inch', 100),
('Laptop Acer Nitro 5', 19990000.00, 'AC-NT5-I5', 'Laptop Acer Nitro 5 Gaming, Intel Core i5-11400H, RAM 8GB, SSD 512GB', 30),
('Tai nghe Bluetooth JBL T500BT', 1290000.00, 'JBL-T500BT', 'Tai nghe Bluetooth chụp tai JBL T500BT, thời gian sử dụng 16 giờ', 50); 