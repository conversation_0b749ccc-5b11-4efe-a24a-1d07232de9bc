package tracing

import (
	"context"
	"fmt"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

const (
	// Tracer name for product service
	tracerName = "product-service"
)

// Common product-specific attributes
const (
	AttributeOrderID       = attribute.Key("product.order_id")
	AttributeProductID     = attribute.Key("product.product_id")
	AttributeCartID        = attribute.Key("product.cart_id")
	AttributeCustomerID    = attribute.Key("product.customer_id")
	AttributePaymentStatus = attribute.Key("product.payment_status")
	AttributeOperation     = attribute.Key("product.operation")
	AttributeOrderAmount   = attribute.Key("product.order_amount")
)

// Operation types for product module
const (
	OperationProductBrowse  = "product_browse"
	OperationCartManage     = "cart_manage"
	OperationCheckout       = "checkout"
	OperationOrderCreate    = "order_create"
	OperationOrderUpdate    = "order_update"
	OperationInventoryCheck = "inventory_check"
	OperationPriceCalculate = "price_calculate"
	OperationPaymentProcess = "payment_process"
)

// StartSpan starts a new span with the given name
func StartSpan(ctx context.Context, spanName string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	tracer := otel.Tracer(tracerName)
	return tracer.Start(ctx, spanName, opts...)
}

// EndSpan ends the span with the given result
func EndSpan(span trace.Span, err error) {
	if err != nil {
		RecordError(span, err)
	}
	span.End()
}

// RecordError records an error in the span
func RecordError(span trace.Span, err error) {
	span.RecordError(err)
	span.SetStatus(codes.Error, err.Error())
}

// WithProductID adds a product ID attribute to the span
func WithProductID(span trace.Span, productID string) {
	span.SetAttributes(AttributeProductID.String(productID))
}

// WithOrderID adds an order ID attribute to the span
func WithOrderID(span trace.Span, orderID string) {
	span.SetAttributes(AttributeOrderID.String(orderID))
}

// WithCartID adds a cart ID attribute to the span
func WithCartID(span trace.Span, cartID string) {
	span.SetAttributes(AttributeCartID.String(cartID))
}

// WithCustomerID adds a customer ID attribute to the span (masked for privacy)
func WithCustomerID(span trace.Span, customerID string) {
	// Mask customer ID for privacy - keep only first and last 2 chars
	if len(customerID) > 5 {
		masked := customerID[:2] + "***" + customerID[len(customerID)-2:]
		span.SetAttributes(AttributeCustomerID.String(masked))
	} else {
		span.SetAttributes(AttributeCustomerID.String("***"))
	}
}

// WithPaymentStatus adds payment status to the span without sensitive details
func WithPaymentStatus(span trace.Span, status string) {
	span.SetAttributes(AttributePaymentStatus.String(status))
}

// WithOperation adds operation type to the span
func WithOperation(span trace.Span, operation string) {
	span.SetAttributes(AttributeOperation.String(operation))
}

// WithOrderAmount adds order amount with currency to the span
func WithOrderAmount(span trace.Span, amount float64, currency string) {
	span.SetAttributes(AttributeOrderAmount.String(fmt.Sprintf("%.2f %s", amount, currency)))
}

// Convenience functions for specific product operations

// TraceProductBrowsing creates a span for product browsing operations
func TraceProductBrowsing(ctx context.Context, productID string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, "ProductBrowsing")
	WithOperation(span, OperationProductBrowse)
	WithProductID(span, productID)
	return ctx, span
}

// TraceCartOperation creates a span for cart operations
func TraceCartOperation(ctx context.Context, cartID string, operation string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, "CartOperation_"+operation)
	WithOperation(span, OperationCartManage)
	WithCartID(span, cartID)
	return ctx, span
}

// TraceCheckoutProcess creates a span for the checkout process
func TraceCheckoutProcess(ctx context.Context, cartID, customerID string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, "CheckoutProcess")
	WithOperation(span, OperationCheckout)
	WithCartID(span, cartID)
	WithCustomerID(span, customerID)
	return ctx, span
}

// TraceOrderCreation creates a span for order creation
func TraceOrderCreation(ctx context.Context, orderID, customerID string, amount float64, currency string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, "OrderCreation")
	WithOperation(span, OperationOrderCreate)
	WithOrderID(span, orderID)
	WithCustomerID(span, customerID)
	WithOrderAmount(span, amount, currency)
	return ctx, span
}

// TraceOrderUpdate creates a span for order update operations
func TraceOrderUpdate(ctx context.Context, orderID string, status string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, "OrderUpdate")
	WithOperation(span, OperationOrderUpdate)
	WithOrderID(span, orderID)
	span.SetAttributes(attribute.String("order.status", status))
	return ctx, span
}

// TraceInventoryCheck creates a span for inventory check operations
func TraceInventoryCheck(ctx context.Context, productID string, quantity int) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, "InventoryCheck")
	WithOperation(span, OperationInventoryCheck)
	WithProductID(span, productID)
	span.SetAttributes(attribute.Int("inventory.quantity", quantity))
	return ctx, span
}

// TracePriceCalculation creates a span for price calculation operations
func TracePriceCalculation(ctx context.Context, cartID string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, "PriceCalculation")
	WithOperation(span, OperationPriceCalculate)
	WithCartID(span, cartID)
	return ctx, span
}

// TracePaymentProcessing creates a span for payment processing operations
func TracePaymentProcessing(ctx context.Context, orderID string, amount float64, currency string) (context.Context, trace.Span) {
	ctx, span := StartSpan(ctx, "PaymentProcessing")
	WithOperation(span, OperationPaymentProcess)
	WithOrderID(span, orderID)
	WithOrderAmount(span, amount, currency)
	return ctx, span
}
