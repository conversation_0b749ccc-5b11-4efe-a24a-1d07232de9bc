package notification

import (
	"context"
	"os"
	"path/filepath"

	"wnapi/internal/core"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/notification/api"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/models"
	"wnapi/modules/notification/repository"
	"wnapi/modules/notification/repository/mysql"
	"wnapi/modules/notification/service"
)

func init() {
	core.RegisterModuleFactory("notification", NewModule)
}

// Module implements the notification module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
}

// NewModule creates a new notification module
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Load configuration from environment variables
	notificationConfig, err := internal.LoadNotificationConfig()
	if err != nil {
		logger.Warn("Could not load configuration from environment variables, using defaults: %v", err)

		// Initialize default configuration if cannot read from env
		notificationConfig = &internal.NotificationConfig{
			EmailHost:     "smtp.gmail.com",
			EmailPort:     587,
			EmailFrom:     "<EMAIL>",
			SMSProvider:   "twilio",
			SMSEnabled:    false,
			PushEnabled:   false,
			QueueEnabled:  false,
			CacheEnabled:  false,
			RedisAddr:     "localhost:6379",
			CacheTTL:      300,
			RetryAttempts: 3,
		}
	}

	// Override configuration from config map if present (for backward compatibility)
	if emailHost, ok := config["email_host"].(string); ok && emailHost != "" {
		notificationConfig.EmailHost = emailHost
	}

	if smsProvider, ok := config["sms_provider"].(string); ok && smsProvider != "" {
		notificationConfig.SMSProvider = smsProvider
	}

	if smsEnabled, ok := config["sms_enabled"].(bool); ok {
		notificationConfig.SMSEnabled = smsEnabled
	}

	if pushEnabled, ok := config["push_enabled"].(bool); ok {
		notificationConfig.PushEnabled = pushEnabled
	}

	// Log configuration info
	logger.Info("Notification Config: EmailHost=%s, SMSProvider=%s, SMSEnabled=%v, PushEnabled=%v",
		notificationConfig.EmailHost, notificationConfig.SMSProvider, notificationConfig.SMSEnabled, notificationConfig.PushEnabled)

	// Initialize repositories using models
	notificationRepo := mysql.NewNotificationModelsRepository(app.GetDBManager().GetDB())
	channelRepo := mysql.NewChannelRepository(app.GetDBManager().GetDB())

	// Initialize services
	channelService := service.NewChannelService(channelRepo)

	// Create notification service
	notificationService := service.NewNotificationService(notificationRepo, *notificationConfig, logger)

	// Create handler with both services
	handler := api.NewHandler(notificationService, channelService)

	return &Module{
		name:    "notification",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
	}, nil
}

// notificationServiceAdapter adapter để chuyển đổi các method từ repository sang internal.NotificationService
type notificationServiceAdapter struct {
	repo   repository.NotificationRepository
	config internal.NotificationConfig
	logger logger.Logger
}

// CreateNotification tạo thông báo mới
func (a *notificationServiceAdapter) CreateNotification(ctx context.Context, notification interface{}) (int, error) {
	if n, ok := notification.(*models.Notification); ok {
		return a.repo.Create(ctx, n)
	}
	return 0, nil
}

// GetNotification lấy thông báo theo ID
func (a *notificationServiceAdapter) GetNotification(ctx context.Context, notificationID int) (interface{}, error) {
	return a.repo.GetByID(ctx, notificationID)
}

// GetUserNotifications lấy danh sách thông báo của người dùng
func (a *notificationServiceAdapter) GetUserNotifications(ctx context.Context, userID int, cursor string, limit int) ([]interface{}, string, error) {
	notifications, nextCursor, err := a.repo.GetByUserID(ctx, userID, cursor, limit, nil)
	if err != nil {
		return nil, "", err
	}

	// Chuyển đổi slice models.Notification sang slice interface{}
	result := make([]interface{}, len(notifications))
	for i, notification := range notifications {
		result[i] = notification
	}

	return result, nextCursor, nil
}

// MarkAsRead đánh dấu thông báo đã đọc
func (a *notificationServiceAdapter) MarkAsRead(ctx context.Context, notificationID int) error {
	return a.repo.MarkAsRead(ctx, notificationID)
}

// MarkAllAsRead đánh dấu tất cả thông báo của người dùng đã đọc
func (a *notificationServiceAdapter) MarkAllAsRead(ctx context.Context, userID int) error {
	return a.repo.MarkAllAsRead(ctx, userID)
}

// GetUnreadCount lấy số lượng thông báo chưa đọc
func (a *notificationServiceAdapter) GetUnreadCount(ctx context.Context, userID int) (int, error) {
	return a.repo.GetUnreadCount(ctx, userID)
}

// DeleteNotification xóa thông báo
func (a *notificationServiceAdapter) DeleteNotification(ctx context.Context, notificationID int) error {
	return a.repo.Delete(ctx, notificationID)
}

// Name returns the module name
func (m *Module) Name() string {
	return m.name
}

// Init initializes the module
func (m *Module) Init(ctx context.Context) error {
	m.logger.Info("Initializing notification module")
	return nil
}

// RegisterRoutes registers the module routes
func (m *Module) RegisterRoutes(server *core.Server) error {
	m.logger.Info("Registering notification module routes")

	if m.handler == nil {
		m.logger.Warn("Notification handler is not initialized, skipping route registration")
		return nil
	}

	err := m.handler.RegisterRoutes(server)
	if err != nil {
		return err
	}

	// Print routes list in development environment
	ginMode := os.Getenv("GIN_MODE")
	if ginMode != "release" {
		m.logger.Info("Available notification routes:")
		// Note: If handler has PrintRoutes method, uncomment the line below
		// m.handler.PrintRoutes()
	}

	return nil
}

// Cleanup cleans up module resources
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Cleaning up notification module")
	return nil
}

// GetMigrationPath returns the path containing migrations
func (m *Module) GetMigrationPath() string {
	return filepath.Join("modules", "notification", "migrations")
}

// GetMigrationOrder returns the priority order when running module migrations
func (m *Module) GetMigrationOrder() int {
	return 3 // Notification module can run after auth and rbac
}
