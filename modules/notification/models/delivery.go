package models

import (
	"time"
)

// Delivery represents a notification delivery attempt
type Delivery struct {
	QueueID        int       `db:"queue_id" json:"queue_id" gorm:"primaryKey"`
	NotificationID int       `db:"notification_id" json:"notification_id"`
	ChannelCode    string    `db:"channel_code" json:"channel_code"`
	Status         string    `db:"status" json:"status"`
	RetryCount     int       `db:"retry_count" json:"retry_count"`
	NextRetryAt    time.Time `db:"next_retry_at" json:"next_retry_at"`
	LastError      string    `db:"last_error" json:"last_error"`
	CreatedAt      time.Time `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time `db:"updated_at" json:"updated_at"`
}

// DeliveryLog represents a log entry for a notification delivery attempt
type DeliveryLog struct {
	LogID          int       `db:"log_id" json:"log_id" gorm:"primaryKey"`
	NotificationID int       `db:"notification_id" json:"notification_id"`
	ChannelCode    string    `db:"channel_code" json:"channel_code"`
	Status         string    `db:"status" json:"status"`
	ErrorMessage   string    `db:"error_message" json:"error_message"`
	Metadata       string    `db:"metadata" json:"metadata"`
	CreatedAt      time.Time `db:"created_at" json:"created_at"`
}

// Delivery status constants
const (
	DeliveryStatusPending    = "pending"
	DeliveryStatusProcessing = "processing"
	DeliveryStatusSent       = "sent"
	DeliveryStatusFailed     = "failed"
)

// TableName returns the table name for the Delivery model
func (Delivery) TableName() string {
	return "notification_queue"
}

// TableName returns the table name for the DeliveryLog model
func (DeliveryLog) TableName() string {
	return "notification_logs"
}
