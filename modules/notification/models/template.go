package models

import (
	"time"
)

// Template represents a notification template in the system
type Template struct {
	TemplateID       int       `db:"template_id" json:"template_id"`
	TemplateCode     string    `db:"template_code" json:"template_code"`
	TitleTemplate    string    `db:"title_template" json:"title_template"`       // Template for notification title, supports placeholders
	ContentTemplate  string    `db:"content_template" json:"content_template"`   // Template for notification content, supports placeholders
	NotificationType string    `db:"notification_type" json:"notification_type"` // Type of notification (e.g., system, account, content)
	IsActive         bool      `db:"is_active" json:"is_active"`
	CreatedAt        time.Time `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time `db:"updated_at" json:"updated_at"`
}

// TableName returns the table name for the Template model
func (Template) TableName() string {
	return "notification_templates"
}
