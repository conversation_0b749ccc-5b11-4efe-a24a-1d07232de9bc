// Package models defines domain models for the notification module
package models

import (
	"time"
)

// WebSocketConnection represents a WebSocket connection
type WebSocketConnection struct {
	ConnectionID string    `db:"connection_id" json:"connection_id"`
	UserID       int       `db:"user_id" json:"user_id"`
	ClientInfo   string    `db:"client_info" json:"client_info"`
	ConnectedAt  time.Time `db:"connected_at" json:"connected_at"`
	LastPingAt   time.Time `db:"last_ping_at" json:"last_ping_at"`
	IsActive     bool      `db:"is_active" json:"is_active"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
}

// TableName returns the table name for the WebSocketConnection model
func (WebSocketConnection) TableName() string {
	return "notification_websocket_connections"
}
