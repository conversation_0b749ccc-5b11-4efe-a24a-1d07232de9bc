package models

import (
	"time"
)

// Notification đại diện cho một thông báo trong hệ thống
type Notification struct {
	NotificationID   uint      `json:"notification_id" gorm:"primaryKey"`
	UserID           uint      `json:"user_id"`
	Title            string    `json:"title"`
	Content          string    `json:"content"`
	NotificationType string    `json:"notification_type"`
	ReferenceType    string    `json:"reference_type"`
	ReferenceID      string    `json:"reference_id"`
	IsRead           bool      `json:"is_read"`
	IsSent           bool      `json:"is_sent"`
	SentAt           time.Time `json:"sent_at,omitempty"`
	ReadAt           time.Time `json:"read_at,omitempty"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// NotificationType constants
const (
	NotificationTypeInfo      = "info"
	NotificationTypeWarning   = "warning"
	NotificationTypeAlert     = "alert"
	NotificationTypePromotion = "promotion"
	NotificationTypeSystem    = "system"
)

// TableName trả về tên bảng trong cơ sở dữ liệu
func (Notification) TableName() string {
	return "notification_notifications"
}
