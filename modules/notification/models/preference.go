package models

import (
	"time"
)

// Preference represents a user's notification preference
type Preference struct {
	PreferenceID     uint      `db:"preference_id" json:"preference_id"`
	UserID           uint      `db:"user_id" json:"user_id"`
	NotificationType string    `db:"notification_type" json:"notification_type"`
	ChannelCode      string    `db:"channel_code" json:"channel_code"`
	IsEnabled        bool      `db:"is_enabled" json:"is_enabled"`
	CreatedAt        time.Time `db:"created_at" json:"created_at"`
	UpdatedAt        time.Time `db:"updated_at" json:"updated_at"`
}

// TableName returns the table name for the Preference model
func (Preference) TableName() string {
	return "notification_user_preferences"
}
