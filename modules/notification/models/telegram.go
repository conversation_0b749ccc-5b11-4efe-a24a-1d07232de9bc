package models

import (
	"time"
)

// TelegramConnection represents a connection between a user and their Telegram account
type TelegramConnection struct {
	ID        int       `db:"id" json:"id" gorm:"primaryKey"`
	UserID    int       `db:"user_id" json:"user_id"`
	Chat<PERSON>    string    `db:"chat_id" json:"chat_id"`
	Username  string    `db:"username" json:"username"`
	FirstName string    `db:"first_name" json:"first_name"`
	LastName  string    `db:"last_name" json:"last_name"`
	IsActive  bool      `db:"is_active" json:"is_active"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
	UpdatedAt time.Time `db:"updated_at" json:"updated_at"`
}

// TableName returns the table name for the TelegramConnection model
func (TelegramConnection) TableName() string {
	return "telegram_connections"
}
