package dto

import (
	"time"
)

// Request DTOs

// CreateTemplateRequest represents a request to create a notification template
type CreateTemplateRequest struct {
	TemplateCode string `json:"template_code" binding:"required"`
	TemplateName string `json:"template_name" binding:"required"`
	Subject      string `json:"subject" binding:"required"`
	Content      string `json:"content" binding:"required"`
	ChannelID    uint   `json:"channel_id" binding:"required"`
	IsActive     bool   `json:"is_active"`
}

// UpdateTemplateRequest represents a request to update a notification template
type UpdateTemplateRequest struct {
	TemplateName string `json:"template_name,omitempty"`
	Subject      string `json:"subject,omitempty"`
	Content      string `json:"content,omitempty"`
	IsActive     *bool  `json:"is_active,omitempty"`
}

// ListTemplatesRequest represents a request to list notification templates
type ListTemplatesRequest struct {
	ChannelID uint   `form:"channel_id"`
	Cursor    string `form:"cursor"`
	Limit     int    `form:"limit,default=20"`
}

// Response DTOs

// TemplateResponse represents a notification template response
type TemplateResponse struct {
	TemplateID   uint      `json:"template_id"`
	TemplateCode string    `json:"template_code"`
	TemplateName string    `json:"template_name"`
	Subject      string    `json:"subject"`
	Content      string    `json:"content"`
	ChannelID    uint      `json:"channel_id"`
	ChannelCode  string    `json:"channel_code"`
	IsActive     bool      `json:"is_active"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TemplateListResponse represents a list of notification templates with pagination metadata
type TemplateListResponse struct {
	Templates []TemplateResponse `json:"templates"`
	Meta      PaginationMeta     `json:"meta"`
}

// TemplateCreatedResponse represents a response after creating a template
type TemplateCreatedResponse struct {
	TemplateID uint `json:"template_id"`
}

// TemplateUpdatedResponse represents a response after updating a template
type TemplateUpdatedResponse struct {
	Success bool `json:"success"`
}
