package dto

import (
	"time"
)

// Request DTOs

// SendNotificationRequest represents a request to send a notification
type SendNotificationRequest struct {
	TemplateCode  string                 `json:"template_code" binding:"required"`
	RecipientID   string                 `json:"recipient_id" binding:"required"`
	RecipientType string                 `json:"recipient_type" binding:"required"`
	Data          map[string]interface{} `json:"data" binding:"required"`
}

// ListNotificationsRequest represents a request to list notifications
type ListNotificationsRequest struct {
	RecipientID   string    `form:"recipient_id"`
	RecipientType string    `form:"recipient_type"`
	Status        string    `form:"status"`
	FromDate      time.Time `form:"from_date"`
	ToDate        time.Time `form:"to_date"`
	Cursor        string    `form:"cursor"`
	Limit         int       `form:"limit,default=20"`
}

// Response DTOs

// NotificationResponse represents a notification response
type NotificationResponse struct {
	NotificationID string                 `json:"notification_id"`
	TemplateCode   string                 `json:"template_code"`
	TemplateName   string                 `json:"template_name"`
	ChannelCode    string                 `json:"channel_code"`
	RecipientID    string                 `json:"recipient_id"`
	RecipientType  string                 `json:"recipient_type"`
	Subject        string                 `json:"subject"`
	Content        string                 `json:"content"`
	Data           map[string]interface{} `json:"data"`
	Status         string                 `json:"status"`
	SentAt         *time.Time             `json:"sent_at,omitempty"`
	ReadAt         *time.Time             `json:"read_at,omitempty"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
}

// NotificationListResponse represents a list of notifications with pagination metadata
type NotificationListResponse struct {
	Notifications []NotificationResponse `json:"notifications"`
	Meta          PaginationMeta         `json:"meta"`
}

// NotificationSentResponse represents a response after sending a notification
type NotificationSentResponse struct {
	NotificationID string `json:"notification_id"`
	Status         string `json:"status"`
}

// NotificationStatusResponse represents a notification status response
type NotificationStatusResponse struct {
	Status string `json:"status"`
}
