package dto

import (
	"time"
)

// Request DTOs

// CreateChannelRequest represents a request to create a notification channel
type CreateChannelRequest struct {
	ChannelCode string `json:"channel_code" binding:"required"`
	ChannelName string `json:"channel_name" binding:"required"`
	IsActive    bool   `json:"is_active"`
}

// UpdateChannelRequest represents a request to update a notification channel
type UpdateChannelRequest struct {
	ChannelName string `json:"channel_name,omitempty"`
	IsActive    *bool  `json:"is_active,omitempty"`
}

// ListChannelsRequest represents a request to list notification channels
type ListChannelsRequest struct {
	Cursor string `form:"cursor"`
	Limit  int    `form:"limit,default=20"`
}

// Response DTOs

// ChannelResponse represents a notification channel response
type ChannelResponse struct {
	ChannelID   uint      `json:"channel_id"`
	ChannelCode string    `json:"channel_code"`
	ChannelName string    `json:"channel_name"`
	IsActive    bool      `json:"is_active"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ChannelListResponse represents a list of notification channels with pagination metadata
type ChannelListResponse struct {
	Channels []ChannelResponse `json:"channels"`
	Meta     PaginationMeta    `json:"meta"`
}

// PaginationMeta is defined in common_dto.go

// ChannelCreatedResponse represents a response after creating a channel
type ChannelCreatedResponse struct {
	ChannelID uint `json:"channel_id"`
}

// ChannelUpdatedResponse represents a response after updating a channel
type ChannelUpdatedResponse struct {
	Success bool `json:"success"`
}
