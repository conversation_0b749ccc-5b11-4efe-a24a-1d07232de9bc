package middleware

import (
	"net/http"
	"time"

	"wnapi/internal/pkg/response"
	"wnapi/modules/notification/repository/redis"

	"github.com/gin-gonic/gin"
)

// RateLimitConfig holds configuration for rate limiting
type RateLimitConfig struct {
	Limit  int           // Number of requests allowed
	Window time.Duration // Time window for the limit
}

// RateLimitMiddleware creates a middleware that limits request rates
func RateLimitMiddleware(rateLimiter *redis.RateLimiter, config RateLimitConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get identifier for the client (IP address or user ID if authenticated)
		var identifier string
		userID, exists := c.Get("userID")
		if exists {
			identifier = userID.(string)
		} else {
			identifier = c.ClientIP()
		}

		// Create a unique key for this route and client
		key := c.FullPath() + ":" + identifier

		// Check if the request is allowed
		allowed, err := rateLimiter.Allow(c.Request.Context(), key, config.Limit, config.Window)
		if err != nil {
			// If there's an error with rate limiting, log it but allow the request
			// to proceed to avoid blocking legitimate traffic due to errors
			c.Next()
			return
		}

		if !allowed {
			// If the request exceeds the rate limit, return a 429 Too Many Requests
			remaining, _ := rateLimiter.RemainingRequests(c.Request.Context(), key, config.Limit)

			c.Header("X-RateLimit-Limit", string(config.Limit))
			c.Header("X-RateLimit-Remaining", string(remaining))
			c.Header("X-RateLimit-Reset", string(int(time.Now().Add(config.Window).Unix())))

			details := []interface{}{
				map[string]interface{}{
					"limit": config.Limit,
					"reset": int(time.Now().Add(config.Window).Unix()),
				},
			}
			response.ErrorWithDetails(c, http.StatusTooManyRequests, "Rate limit exceeded", "RATE_LIMIT_EXCEEDED", details)
			c.Abort()
			return
		}

		// If the request is allowed, proceed to the next handler
		c.Next()
	}
}
