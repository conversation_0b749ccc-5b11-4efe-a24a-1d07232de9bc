package handlers

import (
	"net/http"
	"strconv"

	"wnapi/modules/notification/dto"
	"wnapi/modules/notification/service"

	"github.com/gin-gonic/gin"
)

// ChannelHandler handles HTTP requests for notification channels
type ChannelHandler struct {
	channelService *service.ChannelService
}

// NewChannelHandler creates a new channel handler
func NewChannelHandler(channelService *service.ChannelService) *ChannelHandler {
	return &ChannelHandler{
		channelService: channelService,
	}
}

// CreateChannel handles creating a new notification channel
func (h *ChannelHandler) CreateChannel(c *gin.Context) {
	var req dto.CreateChannelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	channelID, err := h.channelService.CreateChannel(
		c.Request.Context(),
		req.<PERSON>,
		req.Channel<PERSON>ame,
		req.IsActive,
	)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to create channel",
			"error":   err.Error(),
		})
		return
	}

	response := dto.ChannelCreatedResponse{
		ChannelID: uint(channelID),
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "Channel created successfully",
		"data":    response,
	})
}

// GetChannel handles getting a channel by ID
func (h *ChannelHandler) GetChannel(c *gin.Context) {
	idStr := c.Param("id")
	channelID, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid channel ID",
			"error":   err.Error(),
		})
		return
	}

	channel, err := h.channelService.GetChannel(c.Request.Context(), channelID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Channel not found",
			"error":   err.Error(),
		})
		return
	}

	response := dto.ChannelResponse{
		ChannelID:   uint(channel.ChannelID),
		ChannelCode: channel.ChannelCode,
		ChannelName: channel.ChannelName,
		IsActive:    channel.IsActive,
		CreatedAt:   channel.CreatedAt,
		UpdatedAt:   channel.UpdatedAt,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Channel retrieved successfully",
		"data":    response,
	})
}

// ListChannels handles listing channels with pagination
func (h *ChannelHandler) ListChannels(c *gin.Context) {
	var req dto.ListChannelsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid query parameters",
			"error":   err.Error(),
		})
		return
	}

	// Set default limit if not provided
	if req.Limit <= 0 {
		req.Limit = 20
	}

	channels, nextCursor, err := h.channelService.ListChannels(
		c.Request.Context(),
		nil, // isActive filter not implemented in DTO yet
		req.Cursor,
		req.Limit,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to list channels",
			"error":   err.Error(),
		})
		return
	}

	// Convert to DTOs
	channelResponses := make([]dto.ChannelResponse, len(channels))
	for i, channel := range channels {
		channelResponses[i] = dto.ChannelResponse{
			ChannelID:   uint(channel.ChannelID),
			ChannelCode: channel.ChannelCode,
			ChannelName: channel.ChannelName,
			IsActive:    channel.IsActive,
			CreatedAt:   channel.CreatedAt,
			UpdatedAt:   channel.UpdatedAt,
		}
	}

	response := dto.ChannelListResponse{
		Channels: channelResponses,
		Meta: dto.PaginationMeta{
			NextCursor: nextCursor,
			HasMore:    nextCursor != "",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Channels retrieved successfully",
		"data":    response,
	})
}

// UpdateChannel handles updating an existing channel
func (h *ChannelHandler) UpdateChannel(c *gin.Context) {
	idStr := c.Param("id")
	channelID, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid channel ID",
			"error":   err.Error(),
		})
		return
	}

	var req dto.UpdateChannelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid request body",
			"error":   err.Error(),
		})
		return
	}

	// Get existing channel to preserve unchanged values
	existingChannel, err := h.channelService.GetChannel(c.Request.Context(), channelID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "Channel not found",
			"error":   err.Error(),
		})
		return
	}

	// Update only provided fields
	channelName := existingChannel.ChannelName
	if req.ChannelName != "" {
		channelName = req.ChannelName
	}

	isActive := existingChannel.IsActive
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	err = h.channelService.UpdateChannel(
		c.Request.Context(),
		channelID,
		channelName,
		isActive,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to update channel",
			"error":   err.Error(),
		})
		return
	}

	response := dto.ChannelUpdatedResponse{
		Success: true,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Channel updated successfully",
		"data":    response,
	})
}

// DeleteChannel handles deleting a channel
func (h *ChannelHandler) DeleteChannel(c *gin.Context) {
	idStr := c.Param("id")
	channelID, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "Invalid channel ID",
			"error":   err.Error(),
		})
		return
	}

	err = h.channelService.DeleteChannel(c.Request.Context(), channelID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "Failed to delete channel",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Channel deleted successfully",
	})
}
