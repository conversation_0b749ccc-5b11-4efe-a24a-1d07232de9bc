CREATE TABLE telegram_connections (
  id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  user_id INT UNSIGNED NOT NULL,
  chat_id VARCHAR(100) NOT NULL,
  username <PERSON><PERSON><PERSON><PERSON>(100),
  first_name <PERSON><PERSON><PERSON><PERSON>(100),
  last_name <PERSON><PERSON><PERSON><PERSON>(100),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_id (user_id),
  UNIQUE KEY unique_chat_id (chat_id)
);