CREATE TABLE notification_user_preferences (
  preference_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  user_id INT UNSIGNED NOT NULL,
  notification_type VARCHAR(50) NOT NULL COMMENT 'Type of notification (e.g., system, account, content)',
  channel_code VARCHAR(50) NOT NULL COMMENT 'Channel code (e.g., email, push, sms, in_app)',
  is_enabled BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_type_channel (user_id, notification_type, channel_code),
  INDEX idx_user_id (user_id)
); 