CREATE TABLE notification_templates (
  template_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  template_code VARCHAR(100) NOT NULL UNIQUE,
  title_template TEXT NOT NULL COMMENT 'Template for notification title, supports placeholders',
  content_template TEXT NOT NULL COMMENT 'Template for notification content, supports placeholders',
  notification_type VARCHAR(50) NOT NULL COMMENT 'Type of notification (e.g., system, account, content)',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_template_code (template_code),
  INDEX idx_notification_type (notification_type)
);