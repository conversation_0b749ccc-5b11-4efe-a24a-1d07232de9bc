CREATE TABLE notification_websocket_connections (
  connection_id VARCHAR(100) PRIMARY KEY,
  user_id INT UNSIGNED NOT NULL,
  client_info JSON NULL COMMENT 'Client information like device, browser, etc.',
  connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_ping_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  INDEX idx_user_id (user_id),
  INDEX idx_is_active (is_active),
  INDEX idx_last_ping_at (last_ping_at)
);