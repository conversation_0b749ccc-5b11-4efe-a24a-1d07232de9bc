CREATE TABLE notification_queue (
  queue_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  notification_id INT UNSIGNED NOT NULL,
  channel_code VARCHAR(50) NOT NULL,
  status ENUM('pending', 'processing', 'sent', 'failed') DEFAULT 'pending',
  retry_count INT UNSIGNED DEFAULT 0,
  next_retry_at TIMESTAMP NULL,
  last_error TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_notification_id (notification_id),
  INDEX idx_status (status),
  INDEX idx_next_retry_at (next_retry_at),
  FOREIGN KEY (notification_id) REFERENCES notifications(notification_id) ON DELETE CASCADE
);