CREATE TABLE notification_logs (
  log_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  notification_id INT UNSIGNED NOT NULL,
  channel_code VARCHAR(50) NOT NULL,
  status ENUM('success', 'failed') NOT NULL,
  error_message TEXT NULL,
  metadata JSON NULL COMMENT 'Additional data about the delivery',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_notification_id (notification_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (notification_id) REFERENCES notifications(notification_id) ON DELETE CASCADE
);