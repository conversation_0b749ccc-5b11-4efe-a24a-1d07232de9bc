package redis

import (
	"context"
	"fmt"
	"time"
)

// RateLimiter implements rate limiting using Redis
type RateLimiter struct {
	client *RedisClient
}

// NewRateLimiter creates a new rate limiter instance
func NewRateLimiter(client *RedisClient) *RateLimiter {
	return &RateLimiter{
		client: client,
	}
}

// Allow checks if a request is allowed based on a rate limit
func (r *RateLimiter) Allow(ctx context.Context, key string, limit int, window time.Duration) (bool, error) {
	// Current time in seconds
	now := time.Now().Unix()

	// Window bounds (in seconds)
	windowStart := now - int64(window.Seconds())

	// Key for the rate limit
	rateKey := fmt.Sprintf("rate:%s", key)

	// Add the current timestamp and remove timestamps outside the window
	// This would typically be implemented with Redis ZADD and ZREMRANGEBYSCORE
	// For simplicity, we'll use a basic check and increment approach

	// Get the current count
	value, err := r.client.Get(ctx, rateKey)

	var count int
	if err == nil {
		fmt.Sscanf(value, "%d", &count)
	}

	// Check if we're over the limit
	if count >= limit {
		return false, nil
	}

	// Increment the counter
	count++

	// Set with expiration at the end of the window
	expiry := time.Duration(window.Seconds()-float64(now-windowStart)) * time.Second
	err = r.client.Set(ctx, rateKey, count, expiry)
	if err != nil {
		return false, fmt.Errorf("failed to update rate limit: %w", err)
	}

	return true, nil
}

// Reset resets a rate limit counter
func (r *RateLimiter) Reset(ctx context.Context, key string) error {
	rateKey := fmt.Sprintf("rate:%s", key)
	return r.client.Del(ctx, rateKey)
}

// RemainingRequests gets the number of remaining requests for a key
func (r *RateLimiter) RemainingRequests(ctx context.Context, key string, limit int) (int, error) {
	rateKey := fmt.Sprintf("rate:%s", key)

	value, err := r.client.Get(ctx, rateKey)
	if err != nil {
		// If key doesn't exist, all requests are available
		return limit, nil
	}

	var count int
	fmt.Sscanf(value, "%d", &count)

	remaining := limit - count
	if remaining < 0 {
		remaining = 0
	}

	return remaining, nil
}
