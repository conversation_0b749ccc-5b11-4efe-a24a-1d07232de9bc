package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"wnapi/modules/notification/models"
)

const (
	templateKeyPrefix  = "template:"
	templateCodePrefix = "template_code:"
	templateExpiry     = 24 * time.Hour
)

// TemplateCache handles caching for notification templates
type TemplateCache struct {
	client *RedisClient
}

// NewTemplateCache creates a new template cache
func NewTemplateCache(client *RedisClient) *TemplateCache {
	return &TemplateCache{
		client: client,
	}
}

// SetTemplate caches a template
func (c *TemplateCache) SetTemplate(ctx context.Context, template *models.Template) error {
	// Cache by ID
	idKey := fmt.Sprintf("%s%d", templateKeyPrefix, template.TemplateID)
	value, err := json.Marshal(template)
	if err != nil {
		return fmt.Errorf("failed to marshal template: %w", err)
	}

	if err := c.client.Set(ctx, idKey, value, templateExpiry); err != nil {
		return err
	}

	// Also cache by code for faster lookups
	codeKey := fmt.Sprintf("%s%s", templateCodePrefix, template.TemplateCode)
	return c.client.Set(ctx, codeKey, value, templateExpiry)
}

// GetTemplateByID retrieves a cached template by ID
func (c *TemplateCache) GetTemplateByID(ctx context.Context, templateID int) (*models.Template, error) {
	key := fmt.Sprintf("%s%d", templateKeyPrefix, templateID)

	value, err := c.client.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get template from cache: %w", err)
	}

	var template models.Template
	if err := json.Unmarshal([]byte(value), &template); err != nil {
		return nil, fmt.Errorf("failed to unmarshal template: %w", err)
	}

	return &template, nil
}

// GetTemplateByCode retrieves a cached template by code
func (c *TemplateCache) GetTemplateByCode(ctx context.Context, templateCode string) (*models.Template, error) {
	key := fmt.Sprintf("%s%s", templateCodePrefix, templateCode)

	value, err := c.client.Get(ctx, key)
	if err != nil {
		return nil, fmt.Errorf("failed to get template from cache: %w", err)
	}

	var template models.Template
	if err := json.Unmarshal([]byte(value), &template); err != nil {
		return nil, fmt.Errorf("failed to unmarshal template: %w", err)
	}

	return &template, nil
}

// DeleteTemplate removes a template from the cache
func (c *TemplateCache) DeleteTemplate(ctx context.Context, templateID int) error {
	key := fmt.Sprintf("%s%d", templateKeyPrefix, templateID)
	return c.client.Del(ctx, key)
}

// InvalidateAllTemplates invalidates all template caches
func (c *TemplateCache) InvalidateAllTemplates(ctx context.Context) error {
	// In a real implementation, this would use Redis SCAN to find all keys with the prefix
	// and delete them. For simplicity, we'll just return nil.
	return nil
}

// InvalidateTemplate removes a template from cache by code
func (c *TemplateCache) InvalidateTemplate(ctx context.Context, templateCode string) error {
	// First try to get the template to get its ID
	template, err := c.GetTemplateByCode(ctx, templateCode)
	if err == nil && template != nil {
		// Delete by ID
		idKey := fmt.Sprintf("%s%d", templateKeyPrefix, template.TemplateID)
		if err := c.client.Del(ctx, idKey); err != nil {
			return err
		}
	}

	// Also delete by code
	codeKey := fmt.Sprintf("%s%s", templateCodePrefix, templateCode)
	return c.client.Del(ctx, codeKey)
}

// GetTemplate retrieves a template by code (simplified function for compatibility)
func (c *TemplateCache) GetTemplate(templateCode string) (*models.Template, bool) {
	key := fmt.Sprintf("%s%s", templateCodePrefix, templateCode)

	value, err := c.client.Get(context.Background(), key)
	if err != nil {
		return nil, false
	}

	var template models.Template
	if err := json.Unmarshal([]byte(value), &template); err != nil {
		return nil, false
	}

	return &template, true
}

// SetTemplateSimple caches a template (simplified function for compatibility)
func (c *TemplateCache) SetTemplateSimple(template *models.Template) error {
	return c.SetTemplate(context.Background(), template)
}

// InvalidateTemplateSimple removes a template by code (simplified function for compatibility)
func (c *TemplateCache) InvalidateTemplateSimple(templateCode string) error {
	return c.InvalidateTemplate(context.Background(), templateCode)
}
