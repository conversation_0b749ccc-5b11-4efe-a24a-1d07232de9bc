package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
)

const (
	// UserNotificationChannel is the channel for user-specific notifications
	UserNotificationChannel = "user_notification"
	// TopicChannel is the channel for topic-based notifications
	TopicChannel = "topic_notification"
)

// WebsocketMessage represents a message to be sent via WebSocket
type WebsocketMessage struct {
	UserID  int         `json:"user_id"`
	Type    string      `json:"type"`
	Payload interface{} `json:"payload"`
}

// UserMessage represents a message sent to a specific user
type UserMessage struct {
	UserID  int
	Message interface{}
}

// WebsocketPubSub handles WebSocket pub/sub operations
type WebsocketPubSub struct {
	client *RedisClient
}

// NewWebsocketPubSub creates a new WebSocket pub/sub instance
func NewWebsocketPubSub(client *RedisClient) *WebsocketPubSub {
	return &WebsocketPubSub{
		client: client,
	}
}

// PublishUserNotification publishes a notification message for a specific user
func (p *WebsocketPubSub) PublishUserNotification(ctx context.Context, userID int, messageType string, payload interface{}) error {
	message := WebsocketMessage{
		UserID:  userID,
		Type:    messageType,
		Payload: payload,
	}

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal websocket message: %w", err)
	}

	channel := fmt.Sprintf("%s:%d", UserNotificationChannel, userID)
	return p.client.Publish(ctx, channel, data)
}

// SubscribeToUserNotifications subscribes to notifications for a specific user
func (p *WebsocketPubSub) SubscribeToUserNotifications(ctx context.Context, userID int, handler func(message WebsocketMessage)) {
	channel := fmt.Sprintf("%s:%d", UserNotificationChannel, userID)
	pubsub := p.client.Subscribe(ctx, channel)

	// Handle messages in a goroutine
	go func() {
		defer pubsub.Close()

		for {
			msg, err := pubsub.ReceiveMessage(ctx)
			if err != nil {
				log.Printf("Error receiving message: %v", err)
				return
			}

			var message WebsocketMessage
			if err := json.Unmarshal([]byte(msg.Payload), &message); err != nil {
				log.Printf("Error unmarshaling message: %v", err)
				continue
			}

			handler(message)
		}
	}()
}

// PublishBroadcast publishes a broadcast message to all connected clients
func (p *WebsocketPubSub) PublishBroadcast(ctx context.Context, messageType string, payload interface{}) error {
	message := WebsocketMessage{
		UserID:  0, // 0 indicates broadcast
		Type:    messageType,
		Payload: payload,
	}

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal broadcast message: %w", err)
	}

	return p.client.Publish(ctx, "broadcast", data)
}

// PublishToUser publishes a message to a specific user
func (p *WebsocketPubSub) PublishToUser(userID int, message interface{}) error {
	ctx := context.Background()
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal user message: %w", err)
	}

	channel := fmt.Sprintf("%s:%d", UserNotificationChannel, userID)
	return p.client.Publish(ctx, channel, data)
}

// PublishToTopic publishes a message to a specific topic
func (p *WebsocketPubSub) PublishToTopic(topic string, message interface{}) error {
	ctx := context.Background()
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal topic message: %w", err)
	}

	channel := fmt.Sprintf("%s:%s", TopicChannel, topic)
	return p.client.Publish(ctx, channel, data)
}

// SubscribeToUsers subscribes to all user messages
func (p *WebsocketPubSub) SubscribeToUsers() <-chan UserMessage {
	ctx := context.Background()
	pattern := fmt.Sprintf("%s:*", UserNotificationChannel)
	pubsub := p.client.PSubscribe(ctx, pattern)

	messageCh := make(chan UserMessage)

	go func() {
		defer pubsub.Close()
		defer close(messageCh)

		for {
			msg, err := pubsub.ReceiveMessage(ctx)
			if err != nil {
				log.Printf("Error receiving user message: %v", err)
				return
			}

			var message interface{}
			if err := json.Unmarshal([]byte(msg.Payload), &message); err != nil {
				log.Printf("Error unmarshaling user message: %v", err)
				continue
			}

			// Extract user ID from channel name
			var userID int
			fmt.Sscanf(msg.Channel, UserNotificationChannel+":%d", &userID)

			messageCh <- UserMessage{
				UserID:  userID,
				Message: message,
			}
		}
	}()

	return messageCh
}
