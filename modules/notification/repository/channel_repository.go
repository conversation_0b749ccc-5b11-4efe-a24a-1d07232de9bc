package repository

import (
	"context"
	"wnapi/modules/notification/models"
)

// ChannelRepository handles channel-related database operations
type ChannelRepository interface {
	Create(ctx context.Context, channel *models.Channel) (int, error)
	GetByID(ctx context.Context, channelID int) (*models.Channel, error)
	GetByCode(ctx context.Context, code string) (*models.Channel, error)
	List(ctx context.Context, cursor string, limit int, filters map[string]interface{}) ([]*models.Channel, string, error)
	Update(ctx context.Context, channel *models.Channel) error
	Delete(ctx context.Context, channelID int) error
}
