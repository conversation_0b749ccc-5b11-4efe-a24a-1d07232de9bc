package repository

import (
	"context"
	"wnapi/modules/notification/models"
)

// NotificationRepository handles notification-related database operations
type NotificationRepository interface {
	Create(ctx context.Context, notification *models.Notification) (int, error)
	GetByID(ctx context.Context, notificationID int) (*models.Notification, error)
	GetByUserID(ctx context.Context, userID int, cursor string, limit int, filters map[string]interface{}) ([]*models.Notification, string, error)
	MarkAsRead(ctx context.Context, notificationID int) error
	MarkAllAsRead(ctx context.Context, userID int) error
	GetUnreadCount(ctx context.Context, userID int) (int, error)
	Update(ctx context.Context, notification *models.Notification) error
	Delete(ctx context.Context, notificationID int) error
}
