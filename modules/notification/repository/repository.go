// Repository interfaces for the notification module
package repository

import (
	"context"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/models"
)

// Repository là interface ch<PERSON>h cho tất cả repository
type Repository interface {
	Notification() NotificationRepositoryLegacy
	Template() TemplateRepositoryLegacy
	Channel() ChannelRepositoryLegacy
	Preference() PreferenceRepositoryLegacy
	Delivery() DeliveryRepositoryLegacy
	Telegram() TelegramRepositoryLegacy
	Websocket() WebsocketRepositoryLegacy
}

// NotificationRepository is defined in notification_repository.go

// TemplateRepositoryLegacy xử lý thao tác với bảng notification_templates
type TemplateRepositoryLegacy interface {
	GetTemplateByID(ctx context.Context, id int) (*internal.Template, error)
	CreateTemplate(ctx context.Context, template *internal.Template) (int, error)
	UpdateTemplate(ctx context.Context, template *internal.Template) error
	DeleteTemplate(ctx context.Context, id int) error
	ListTemplates(ctx context.Context, filter interface{}) ([]*internal.Template, error)
}

// ChannelRepositoryLegacy xử lý thao tác với bảng notification_channels
type ChannelRepositoryLegacy interface {
	GetChannelByID(ctx context.Context, id int) (*internal.Channel, error)
	CreateChannel(ctx context.Context, channel *internal.Channel) (int, error)
	UpdateChannel(ctx context.Context, channel *internal.Channel) error
	DeleteChannel(ctx context.Context, id int) error
	ListChannels(ctx context.Context, filter interface{}) ([]*internal.Channel, error)
}

// NotificationRepositoryLegacy is the legacy interface using internal types
type NotificationRepositoryLegacy interface {
	GetNotificationByID(ctx context.Context, id int) (*internal.Notification, error)
	CreateNotification(ctx context.Context, notification *internal.Notification) (int, error)
	UpdateNotification(ctx context.Context, notification *internal.Notification) error
	DeleteNotification(ctx context.Context, id int) error
	ListNotifications(ctx context.Context, filter interface{}) ([]*internal.Notification, error)
	MarkAsRead(ctx context.Context, id int) error
}

// PreferenceRepositoryLegacy xử lý thao tác với bảng notification_preferences
type PreferenceRepositoryLegacy interface {
	GetPreferenceByID(ctx context.Context, id int) (*internal.Preference, error)
	GetPreferenceByUserID(ctx context.Context, userID int) (*internal.Preference, error)
	CreatePreference(ctx context.Context, preference *internal.Preference) (int, error)
	UpdatePreference(ctx context.Context, preference *internal.Preference) error
	DeletePreference(ctx context.Context, id int) error
	ListPreferences(ctx context.Context, filter interface{}) ([]*internal.Preference, error)
}

// DeliveryRepositoryLegacy xử lý thao tác với bảng notification_deliveries
type DeliveryRepositoryLegacy interface {
	GetDeliveryByID(ctx context.Context, id int) (*internal.Delivery, error)
	CreateDelivery(ctx context.Context, delivery *internal.Delivery) (int, error)
	UpdateDelivery(ctx context.Context, delivery *internal.Delivery) error
	DeleteDelivery(ctx context.Context, id int) error
	ListDeliveries(ctx context.Context, filter interface{}) ([]*internal.Delivery, error)
}

// TelegramRepositoryLegacy xử lý thao tác với bảng notification_telegram
type TelegramRepositoryLegacy interface {
	GetTelegramByID(ctx context.Context, id int) (*internal.Telegram, error)
	GetTelegramByUserID(ctx context.Context, userID int) (*internal.Telegram, error)
	CreateTelegram(ctx context.Context, telegram *internal.Telegram) (int, error)
	UpdateTelegram(ctx context.Context, telegram *internal.Telegram) error
	DeleteTelegram(ctx context.Context, id int) error
	ListTelegrams(ctx context.Context, filter interface{}) ([]*internal.Telegram, error)
}

// WebsocketRepositoryLegacy xử lý thao tác với bảng notification_websockets
type WebsocketRepositoryLegacy interface {
	GetWebsocketByID(ctx context.Context, id string) (*internal.Websocket, error)
	CreateWebsocket(ctx context.Context, websocket *internal.Websocket) (string, error)
	UpdateWebsocket(ctx context.Context, websocket *internal.Websocket) error
	DeleteWebsocket(ctx context.Context, id string) error
	ListWebsockets(ctx context.Context, filter interface{}) ([]*internal.Websocket, error)
}

// Modern repository interfaces using models types

// TemplateRepository handles operations with notification templates using models
type TemplateRepository interface {
	GetByID(ctx context.Context, id int) (*models.Template, error)
	Create(ctx context.Context, template *models.Template) (int, error)
	Update(ctx context.Context, template *models.Template) error
	Delete(ctx context.Context, id int) error
	List(ctx context.Context, cursor string, limit int, filters map[string]interface{}) ([]*models.Template, string, error)
}

// PreferenceRepository handles operations with notification preferences using models
type PreferenceRepository interface {
	GetByID(ctx context.Context, id int) (*models.Preference, error)
	GetByUserID(ctx context.Context, userID int) (*models.Preference, error)
	Create(ctx context.Context, preference *models.Preference) (int, error)
	Update(ctx context.Context, preference *models.Preference) error
	Delete(ctx context.Context, id int) error
	List(ctx context.Context, filters map[string]interface{}) ([]*models.Preference, error)
}

// WebsocketRepository handles operations with websocket connections using models
type WebsocketRepository interface {
	GetByID(ctx context.Context, id string) (*models.WebSocketConnection, error)
	Create(ctx context.Context, websocket *models.WebSocketConnection) (string, error)
	Update(ctx context.Context, websocket *models.WebSocketConnection) error
	Delete(ctx context.Context, id string) error
	List(ctx context.Context, filters map[string]interface{}) ([]*models.WebSocketConnection, error)
}
