package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"wnapi/modules/notification/models"
	"wnapi/modules/notification/repository"

	"github.com/jmoiron/sqlx"
)

// NotificationModelsRepository implements the new NotificationRepository interface using models.Notification
type NotificationModelsRepository struct {
	db *sqlx.DB
}

// NewNotificationModelsRepository creates a new notification repository using models
func NewNotificationModelsRepository(db *sqlx.DB) repository.NotificationRepository {
	return &NotificationModelsRepository{
		db: db,
	}
}

// Create adds a new notification to the database
func (r *NotificationModelsRepository) Create(ctx context.Context, notification *models.Notification) (int, error) {
	query := `
		INSERT INTO notifications (
			user_id, title, content, notification_type,
			reference_type, reference_id, is_read, is_sent,
			created_at, updated_at
		) VALUES (
			:user_id, :title, :content, :notification_type,
			:reference_type, :reference_id, :is_read, :is_sent,
			:created_at, :updated_at
		)
	`

	notification.CreatedAt = time.Now()
	notification.UpdatedAt = time.Now()

	result, err := r.db.NamedExecContext(ctx, query, notification)
	if err != nil {
		return 0, fmt.Errorf("failed to create notification: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get last insert ID: %w", err)
	}

	return int(id), nil
}

// GetByID retrieves a notification by its ID
func (r *NotificationModelsRepository) GetByID(ctx context.Context, notificationID int) (*models.Notification, error) {
	query := `
		SELECT * FROM notifications WHERE notification_id = ?
	`

	var notification models.Notification
	err := r.db.GetContext(ctx, &notification, query, notificationID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("notification not found")
		}
		return nil, fmt.Errorf("failed to get notification: %w", err)
	}

	return &notification, nil
}

// GetByUserID retrieves notifications for a specific user with pagination
func (r *NotificationModelsRepository) GetByUserID(ctx context.Context, userID int, cursor string, limit int, filters map[string]interface{}) ([]*models.Notification, string, error) {
	query := `
		SELECT * FROM notifications 
		WHERE user_id = ?
	`
	args := []interface{}{userID}

	// Add cursor pagination
	if cursor != "" {
		if cursorID, err := strconv.Atoi(cursor); err == nil {
			query += " AND notification_id < ?"
			args = append(args, cursorID)
		}
	}

	// Apply filters
	if notificationType, ok := filters["notification_type"]; ok {
		query += " AND notification_type = ?"
		args = append(args, notificationType)
	}
	if isRead, ok := filters["is_read"]; ok {
		query += " AND is_read = ?"
		args = append(args, isRead)
	}

	query += " ORDER BY notification_id DESC LIMIT ?"
	args = append(args, limit+1) // Get one extra to check if there's more

	var notifications []*models.Notification
	err := r.db.SelectContext(ctx, &notifications, query, args...)
	if err != nil {
		return nil, "", fmt.Errorf("failed to get notifications: %w", err)
	}

	var nextCursor string
	if len(notifications) > limit {
		// Remove the extra record and set next cursor
		notifications = notifications[:limit]
		nextCursor = strconv.Itoa(int(notifications[len(notifications)-1].NotificationID))
	}

	return notifications, nextCursor, nil
}

// MarkAsRead marks a notification as read
func (r *NotificationModelsRepository) MarkAsRead(ctx context.Context, notificationID int) error {
	query := `
		UPDATE notifications 
		SET is_read = true, read_at = NOW(), updated_at = NOW()
		WHERE notification_id = ?
	`

	result, err := r.db.ExecContext(ctx, query, notificationID)
	if err != nil {
		return fmt.Errorf("failed to mark notification as read: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("notification not found")
	}

	return nil
}

// MarkAllAsRead marks all notifications for a user as read
func (r *NotificationModelsRepository) MarkAllAsRead(ctx context.Context, userID int) error {
	query := `
		UPDATE notifications 
		SET is_read = true, read_at = NOW(), updated_at = NOW()
		WHERE user_id = ? AND is_read = false
	`

	_, err := r.db.ExecContext(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to mark all notifications as read: %w", err)
	}

	return nil
}

// GetUnreadCount gets the count of unread notifications for a user
func (r *NotificationModelsRepository) GetUnreadCount(ctx context.Context, userID int) (int, error) {
	query := `
		SELECT COUNT(*) FROM notifications 
		WHERE user_id = ? AND is_read = false
	`

	var count int
	err := r.db.GetContext(ctx, &count, query, userID)
	if err != nil {
		return 0, fmt.Errorf("failed to get unread count: %w", err)
	}

	return count, nil
}

// Update updates a notification
func (r *NotificationModelsRepository) Update(ctx context.Context, notification *models.Notification) error {
	query := `
		UPDATE notifications 
		SET 
			user_id = :user_id,
			title = :title,
			content = :content,
			notification_type = :notification_type,
			reference_type = :reference_type,
			reference_id = :reference_id,
			is_read = :is_read,
			is_sent = :is_sent,
			sent_at = :sent_at,
			read_at = :read_at,
			updated_at = NOW()
		WHERE notification_id = :notification_id
	`

	result, err := r.db.NamedExecContext(ctx, query, notification)
	if err != nil {
		return fmt.Errorf("failed to update notification: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("notification not found")
	}

	return nil
}

// Delete deletes a notification
func (r *NotificationModelsRepository) Delete(ctx context.Context, notificationID int) error {
	query := `DELETE FROM notifications WHERE notification_id = ?`

	result, err := r.db.ExecContext(ctx, query, notificationID)
	if err != nil {
		return fmt.Errorf("failed to delete notification: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("notification not found")
	}

	return nil
}
