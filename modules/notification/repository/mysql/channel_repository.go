package mysql

import (
	"context"
	"database/sql"
	"fmt"

	"wnapi/modules/notification/models"
	"wnapi/modules/notification/repository"

	"github.com/jmoiron/sqlx"
)

// ChannelRepository implements the ChannelRepository interface
type ChannelRepository struct {
	db *sqlx.DB
}

// NewChannelRepository creates a new channel repository
func NewChannelRepository(db *sqlx.DB) repository.ChannelRepository {
	return &ChannelRepository{
		db: db,
	}
}

// Create adds a new notification channel to the database
func (r *ChannelRepository) Create(ctx context.Context, channel *models.Channel) (int, error) {
	query := `
		INSERT INTO notification_channels (
			channel_code, channel_name, is_active
		) VALUES (
			:channel_code, :channel_name, :is_active
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, channel)
	if err != nil {
		return 0, fmt.Errorf("failed to create channel: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get last insert ID: %w", err)
	}

	return int(id), nil
}

// GetByID retrieves a channel by its ID
func (r *ChannelRepository) GetByID(ctx context.Context, channelID int) (*models.Channel, error) {
	query := `SELECT * FROM notification_channels WHERE channel_id = ?`

	var channel models.Channel
	err := r.db.GetContext(ctx, &channel, query, channelID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get channel by ID: %w", err)
	}

	return &channel, nil
}

// GetByCode retrieves a channel by its code
func (r *ChannelRepository) GetByCode(ctx context.Context, code string) (*models.Channel, error) {
	query := `SELECT * FROM notification_channels WHERE channel_code = ?`

	var channel models.Channel
	err := r.db.GetContext(ctx, &channel, query, code)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get channel by code: %w", err)
	}

	return &channel, nil
}

// List retrieves a list of channels with pagination
func (r *ChannelRepository) List(ctx context.Context, cursor string, limit int, filters map[string]interface{}) ([]*models.Channel, string, error) {
	if limit <= 0 {
		limit = 20
	}

	query := `SELECT * FROM notification_channels WHERE 1=1`
	args := []interface{}{}

	// Add filters
	if filters != nil {
		if val, ok := filters["is_active"]; ok {
			query += " AND is_active = ?"
			args = append(args, val)
		}
	}

	// Add cursor pagination
	if cursor != "" {
		query += " AND channel_id < ?"
		args = append(args, cursor)
	}

	query += " ORDER BY channel_id DESC LIMIT ?"
	args = append(args, limit+1) // Get one extra to determine if there are more

	var channels []*models.Channel
	err := r.db.SelectContext(ctx, &channels, query, args...)
	if err != nil {
		return nil, "", fmt.Errorf("failed to list channels: %w", err)
	}

	var nextCursor string
	if len(channels) > limit {
		lastChannel := channels[limit]
		nextCursor = fmt.Sprintf("%d", lastChannel.ChannelID)
		channels = channels[:limit]
	}

	return channels, nextCursor, nil
}

// Update updates a channel
func (r *ChannelRepository) Update(ctx context.Context, channel *models.Channel) error {
	query := `
		UPDATE notification_channels 
		SET 
			channel_code = :channel_code, 
			channel_name = :channel_name, 
			is_active = :is_active,
			updated_at = NOW()
		WHERE channel_id = :channel_id
	`

	_, err := r.db.NamedExecContext(ctx, query, channel)
	if err != nil {
		return fmt.Errorf("failed to update channel: %w", err)
	}

	return nil
}

// Delete deletes a channel
func (r *ChannelRepository) Delete(ctx context.Context, channelID int) error {
	query := `DELETE FROM notification_channels WHERE channel_id = ?`

	_, err := r.db.ExecContext(ctx, query, channelID)
	if err != nil {
		return fmt.Errorf("failed to delete channel: %w", err)
	}

	return nil
}
