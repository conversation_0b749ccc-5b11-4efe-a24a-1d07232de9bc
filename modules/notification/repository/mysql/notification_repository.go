package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/notification/dto"
	"wnapi/modules/notification/internal"

	"github.com/jmoiron/sqlx"
	"go.opentelemetry.io/otel/attribute"
)

// NotificationRepository triển khai NotificationRepository interface
type NotificationRepository struct {
	db     *sqlx.DB
	logger logger.Logger
}

// NewNotificationRepository tạo repository mới cho notification
func NewNotificationRepository(db *sqlx.DB, logger logger.Logger) *NotificationRepository {
	return &NotificationRepository{
		db:     db,
		logger: logger,
	}
}

// GetNotificationByID lấy thông tin notification theo ID
func (r *NotificationRepository) GetNotificationByID(ctx context.Context, id int) (*internal.Notification, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "notifications")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "notifications"),
		attribute.String("db.operation", "get_notification_by_id"),
		attribute.Int("notification.id", id),
	)

	var notification internal.Notification
	query := `SELECT id, tenant_id, user_id, title, content, type, status, is_read, sent_at, created_at, updated_at 
              FROM notifications WHERE id = ?`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	err := r.db.GetContext(ctx, &notification, query, id)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrNotificationNotFound
		}
		r.logger.Error("Không thể lấy thông tin notification", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("notification.id", notification.ID),
	)

	return &notification, nil
}

// CreateNotification tạo notification mới
func (r *NotificationRepository) CreateNotification(ctx context.Context, notification *internal.Notification) (int, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "notifications")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "notifications"),
		attribute.String("db.operation", "create_notification"),
	)

	query := `INSERT INTO notifications (tenant_id, user_id, title, content, type, status, is_read, sent_at, created_at, updated_at) 
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	now := time.Now()
	notification.CreatedAt = now
	notification.UpdatedAt = now

	result, err := r.db.ExecContext(ctx, query,
		notification.TenantID,
		notification.UserID,
		notification.Title,
		notification.Content,
		notification.Type,
		notification.Status,
		notification.IsRead,
		notification.SentAt,
		notification.CreatedAt,
		notification.UpdatedAt,
	)

	if err != nil {
		r.logger.Error("Không thể tạo notification", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return 0, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		r.logger.Error("Không thể lấy ID notification vừa tạo", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return 0, err
	}

	tracing.AddSpanAttributes(ctx, attribute.Int64("notification.id", id))
	return int(id), nil
}

// UpdateNotification cập nhật thông tin notification
func (r *NotificationRepository) UpdateNotification(ctx context.Context, notification *internal.Notification) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "notifications")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "notifications"),
		attribute.String("db.operation", "update_notification"),
		attribute.Int("notification.id", notification.ID),
	)

	query := `UPDATE notifications 
              SET title = ?, content = ?, type = ?, status = ?, is_read = ?, sent_at = ?, updated_at = ? 
              WHERE id = ?`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	notification.UpdatedAt = time.Now()

	result, err := r.db.ExecContext(ctx, query,
		notification.Title,
		notification.Content,
		notification.Type,
		notification.Status,
		notification.IsRead,
		notification.SentAt,
		notification.UpdatedAt,
		notification.ID,
	)

	if err != nil {
		r.logger.Error("Không thể cập nhật notification", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Không thể xác định số hàng ảnh hưởng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	if rowsAffected == 0 {
		return internal.ErrNotificationNotFound
	}

	return nil
}

// DeleteNotification xóa notification
func (r *NotificationRepository) DeleteNotification(ctx context.Context, id int) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "notifications")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "notifications"),
		attribute.String("db.operation", "delete_notification"),
		attribute.Int("notification.id", id),
	)

	query := `DELETE FROM notifications WHERE id = ?`
	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	result, err := r.db.ExecContext(ctx, query, id)
	if err != nil {
		r.logger.Error("Không thể xóa notification", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Không thể xác định số hàng ảnh hưởng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	if rowsAffected == 0 {
		return internal.ErrNotificationNotFound
	}

	return nil
}

// ListNotifications lấy danh sách notifications theo filter
func (r *NotificationRepository) ListNotifications(ctx context.Context, filter interface{}) ([]*internal.Notification, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "notifications")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "notifications"),
		attribute.String("db.operation", "list_notifications"),
	)

	// Chuyển đổi filter từ interface{} sang dto.NotificationFilter
	notificationFilter, ok := filter.(dto.NotificationFilter)
	if !ok {
		r.logger.Error("Filter không đúng định dạng", logger.String("expected", "dto.NotificationFilter"))
		return nil, fmt.Errorf("filter không đúng định dạng")
	}

	var params []interface{}
	query := `SELECT id, tenant_id, user_id, title, content, type, status, is_read, sent_at, created_at, updated_at 
              FROM notifications WHERE 1=1`

	// Thêm điều kiện lọc
	if notificationFilter.TenantID > 0 {
		query += " AND tenant_id = ?"
		params = append(params, notificationFilter.TenantID)
	}

	if notificationFilter.UserID > 0 {
		query += " AND user_id = ?"
		params = append(params, notificationFilter.UserID)
	}

	if notificationFilter.Type != "" {
		query += " AND type = ?"
		params = append(params, notificationFilter.Type)
	}

	if notificationFilter.Status != "" {
		query += " AND status = ?"
		params = append(params, notificationFilter.Status)
	}

	if notificationFilter.IsRead != nil {
		query += " AND is_read = ?"
		params = append(params, *notificationFilter.IsRead)
	}

	// Mặc định limit và offset nếu không có
	limit := 10
	offset := 0

	if notificationFilter.Limit > 0 {
		limit = notificationFilter.Limit
	}

	if notificationFilter.Offset > 0 {
		offset = notificationFilter.Offset
	}

	// Thêm order by và limit
	query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
	params = append(params, limit, offset)

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	var notifications []*internal.Notification
	err := r.db.SelectContext(ctx, &notifications, query, params...)
	if err != nil {
		r.logger.Error("Không thể lấy danh sách notifications", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Int("notification.count", len(notifications)),
	)

	return notifications, nil
}

// MarkAsRead đánh dấu notification đã đọc
func (r *NotificationRepository) MarkAsRead(ctx context.Context, id int) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "notifications")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "notifications"),
		attribute.String("db.operation", "mark_as_read"),
		attribute.Int("notification.id", id),
	)

	query := `UPDATE notifications SET is_read = true, updated_at = ? WHERE id = ?`
	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	now := time.Now()
	result, err := r.db.ExecContext(ctx, query, now, id)
	if err != nil {
		r.logger.Error("Không thể đánh dấu đã đọc notification", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		r.logger.Error("Không thể xác định số hàng ảnh hưởng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return err
	}

	if rowsAffected == 0 {
		return internal.ErrNotificationNotFound
	}

	return nil
}
