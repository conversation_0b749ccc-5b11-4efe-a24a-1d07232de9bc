package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"wnapi/modules/notification/models"

	"github.com/jmoiron/sqlx"
)

// TelegramRepository handles database operations for Telegram connections
type TelegramRepository struct {
	db *sqlx.DB
}

// NewTelegramRepository creates a new TelegramRepository
func NewTelegramRepository(db *sqlx.DB) *TelegramRepository {
	return &TelegramRepository{
		db: db,
	}
}

// Create adds a new Telegram connection for a user
func (r *TelegramRepository) Create(ctx context.Context, connection *models.TelegramConnection) (int, error) {
	query := `
		INSERT INTO telegram_connections 
		(user_id, chat_id, username, first_name, last_name, is_active, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`

	now := time.Now()
	connection.CreatedAt = now
	connection.UpdatedAt = now

	result, err := r.db.ExecContext(
		ctx,
		query,
		connection.UserID,
		connection.ChatID,
		connection.Username,
		connection.FirstName,
		connection.LastName,
		connection.IsActive,
		connection.CreatedAt,
		connection.UpdatedAt,
	)

	if err != nil {
		return 0, fmt.Errorf("failed to create telegram connection: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get last insert id: %w", err)
	}

	return int(id), nil
}

// GetByUserID retrieves a Telegram connection by user ID
func (r *TelegramRepository) GetByUserID(ctx context.Context, userID int) (*models.TelegramConnection, error) {
	query := `
		SELECT * FROM telegram_connections
		WHERE user_id = ? AND is_active = true
		LIMIT 1
	`

	var connection models.TelegramConnection
	err := r.db.GetContext(ctx, &connection, query, userID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get telegram connection: %w", err)
	}

	return &connection, nil
}

// GetByChatID retrieves a Telegram connection by chat ID
func (r *TelegramRepository) GetByChatID(ctx context.Context, chatID string) (*models.TelegramConnection, error) {
	query := `
		SELECT * FROM telegram_connections
		WHERE chat_id = ?
		LIMIT 1
	`

	var connection models.TelegramConnection
	err := r.db.GetContext(ctx, &connection, query, chatID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get telegram connection by chat ID: %w", err)
	}

	return &connection, nil
}

// Update updates an existing Telegram connection
func (r *TelegramRepository) Update(ctx context.Context, connection *models.TelegramConnection) error {
	query := `
		UPDATE telegram_connections
		SET chat_id = ?, username = ?, first_name = ?, last_name = ?, is_active = ?, updated_at = ?
		WHERE user_id = ?
	`

	connection.UpdatedAt = time.Now()

	_, err := r.db.ExecContext(
		ctx,
		query,
		connection.ChatID,
		connection.Username,
		connection.FirstName,
		connection.LastName,
		connection.IsActive,
		connection.UpdatedAt,
		connection.UserID,
	)

	if err != nil {
		return fmt.Errorf("failed to update telegram connection: %w", err)
	}

	return nil
}

// Delete deactivates a Telegram connection
func (r *TelegramRepository) Delete(ctx context.Context, userID int) error {
	query := `
		UPDATE telegram_connections
		SET is_active = false, updated_at = ?
		WHERE user_id = ?
	`

	_, err := r.db.ExecContext(ctx, query, time.Now(), userID)
	if err != nil {
		return fmt.Errorf("failed to deactivate telegram connection: %w", err)
	}

	return nil
}
