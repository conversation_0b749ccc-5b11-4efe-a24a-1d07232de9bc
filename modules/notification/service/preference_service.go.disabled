// Package service implements the business logic for the notification module
package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/notification/models"
	"wnapi/modules/notification/repository"
)

// PreferenceService handles user notification preferences business logic
type PreferenceService struct {
	preferenceRepo repository.PreferenceRepository
}

// NewPreferenceService creates a new preference service
func NewPreferenceService(preferenceRepo repository.PreferenceRepository) *PreferenceService {
	return &PreferenceService{
		preferenceRepo: preferenceRepo,
	}
}

// CreatePreference creates a new notification preference for a user
func (s *PreferenceService) CreatePreference(
	ctx context.Context,
	userID int,
	notificationType string,
	channelCode string,
	isEnabled bool,
) (int, error) {
	preference := &models.Preference{
		UserID:           uint(userID),
		NotificationType: notificationType,
		ChannelCode:      channelCode,
		IsEnabled:        isEnabled,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	preferenceID, err := s.preferenceRepo.Create(ctx, preference)
	if err != nil {
		return 0, fmt.Errorf("failed to create preference: %w", err)
	}

	return preferenceID, nil
}

// GetPreference gets a preference by ID
func (s *PreferenceService) GetPreference(ctx context.Context, preferenceID int) (*models.Preference, error) {
	preference, err := s.preferenceRepo.GetByID(ctx, preferenceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get preference: %w", err)
	}
	return preference, nil
}

// GetUserPreferences gets all preferences for a user with optional filtering
func (s *PreferenceService) GetUserPreferences(
	ctx context.Context,
	userID int,
	notificationType string,
	channelCode string,
	cursor string,
	limit int,
) ([]*models.Preference, string, error) {
	filters := make(map[string]interface{})

	if notificationType != "" {
		filters["notification_type"] = notificationType
	}

	if channelCode != "" {
		filters["channel_code"] = channelCode
	}

	preferences, nextCursor, err := s.preferenceRepo.GetUserPreferences(ctx, userID, cursor, limit, filters)
	if err != nil {
		return nil, "", fmt.Errorf("failed to get user preferences: %w", err)
	}

	return preferences, nextCursor, nil
}

// UpdatePreference updates an existing preference
func (s *PreferenceService) UpdatePreference(
	ctx context.Context,
	preferenceID int,
	isEnabled bool,
) error {
	// Get existing preference
	preference, err := s.preferenceRepo.GetByID(ctx, preferenceID)
	if err != nil {
		return fmt.Errorf("failed to get preference: %w", err)
	}

	// Update fields
	preference.IsEnabled = isEnabled
	preference.UpdatedAt = time.Now()

	if err := s.preferenceRepo.Update(ctx, preference); err != nil {
		return fmt.Errorf("failed to update preference: %w", err)
	}

	return nil
}

// DeletePreference deletes a preference by ID
func (s *PreferenceService) DeletePreference(ctx context.Context, preferenceID int) error {
	if err := s.preferenceRepo.Delete(ctx, preferenceID); err != nil {
		return fmt.Errorf("failed to delete preference: %w", err)
	}
	return nil
}

// SetDefaultPreferences sets default preferences for a user
func (s *PreferenceService) SetDefaultPreferences(ctx context.Context, userID int) error {
	if err := s.preferenceRepo.SetDefaultPreferences(ctx, userID); err != nil {
		return fmt.Errorf("failed to set default preferences: %w", err)
	}
	return nil
}
