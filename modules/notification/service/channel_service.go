// Package service implements the business logic for the notification module
package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/modules/notification/models"
	"wnapi/modules/notification/repository"
)

// ChannelService handles notification channel business logic
type ChannelService struct {
	channelRepo repository.ChannelRepository
}

// NewChannelService creates a new channel service
func NewChannelService(channelRepo repository.ChannelRepository) *ChannelService {
	return &ChannelService{
		channelRepo: channelRepo,
	}
}

// CreateChannel creates a new notification channel
func (s *ChannelService) CreateChannel(
	ctx context.Context,
	channelCode, channelName string,
	isActive bool,
) (int, error) {
	// Check if channel with same code already exists
	existingChannel, err := s.channelRepo.GetByCode(ctx, channelCode)
	if err == nil && existingChannel != nil {
		return 0, fmt.Errorf("channel with code %s already exists", channelCode)
	}

	channel := &models.Channel{
		ChannelCode: channelCode,
		ChannelName: channelName,
		IsActive:    isActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	channelID, err := s.channelRepo.Create(ctx, channel)
	if err != nil {
		return 0, fmt.Errorf("failed to create channel: %w", err)
	}

	return channelID, nil
}

// GetChannel gets a channel by ID
func (s *ChannelService) GetChannel(ctx context.Context, channelID int) (*models.Channel, error) {
	channel, err := s.channelRepo.GetByID(ctx, channelID)
	if err != nil {
		return nil, fmt.Errorf("failed to get channel: %w", err)
	}
	return channel, nil
}

// GetChannelByCode gets a channel by its code
func (s *ChannelService) GetChannelByCode(ctx context.Context, channelCode string) (*models.Channel, error) {
	channel, err := s.channelRepo.GetByCode(ctx, channelCode)
	if err != nil {
		return nil, fmt.Errorf("failed to get channel by code: %w", err)
	}
	return channel, nil
}

// ListChannels lists channels with filtering and pagination
func (s *ChannelService) ListChannels(
	ctx context.Context,
	isActive *bool,
	cursor string,
	limit int,
) ([]*models.Channel, string, error) {
	filters := make(map[string]interface{})

	if isActive != nil {
		filters["is_active"] = *isActive
	}

	channels, nextCursor, err := s.channelRepo.List(ctx, cursor, limit, filters)
	if err != nil {
		return nil, "", fmt.Errorf("failed to list channels: %w", err)
	}

	return channels, nextCursor, nil
}

// UpdateChannel updates an existing channel
func (s *ChannelService) UpdateChannel(
	ctx context.Context,
	channelID int,
	channelName string,
	isActive bool,
) error {
	// Get existing channel
	channel, err := s.channelRepo.GetByID(ctx, channelID)
	if err != nil {
		return fmt.Errorf("failed to get channel: %w", err)
	}

	// Update fields
	channel.ChannelName = channelName
	channel.IsActive = isActive
	channel.UpdatedAt = time.Now()

	if err := s.channelRepo.Update(ctx, channel); err != nil {
		return fmt.Errorf("failed to update channel: %w", err)
	}

	return nil
}

// DeleteChannel deletes a channel by ID
func (s *ChannelService) DeleteChannel(ctx context.Context, channelID int) error {
	if err := s.channelRepo.Delete(ctx, channelID); err != nil {
		return fmt.Errorf("failed to delete channel: %w", err)
	}
	return nil
}
