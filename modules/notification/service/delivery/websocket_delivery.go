package delivery

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"wnapi/modules/notification/models"
)

// WebsocketDelivery implements websocket notification delivery
type WebsocketDelivery struct {
	websocketService interface {
		SendToUser(ctx context.Context, userID int, topic string, payload string) error
	}
}

// NewWebsocketDelivery creates a new websocket delivery service
func NewWebsocketDelivery(websocketService interface {
	SendToUser(ctx context.Context, userID int, topic string, payload string) error
}) *WebsocketDelivery {
	return &WebsocketDelivery{
		websocketService: websocketService,
	}
}

// Send sends a notification via WebSocket
func (s *WebsocketDelivery) Send(ctx context.Context, notification *models.Notification, userID uint) error {
	// Create a message payload
	payload := map[string]interface{}{
		"id":                notification.NotificationID,
		"title":             notification.Title,
		"content":           notification.Content,
		"notification_type": notification.NotificationType,
		"reference_type":    notification.ReferenceType,
		"reference_id":      notification.ReferenceID,
		"is_read":           notification.IsRead,
		"created_at":        notification.CreatedAt.Format(time.RFC3339),
	}

	// Convert to JSON
	payloadJson, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal notification payload: %w", err)
	}

	// Send to user's websocket connections
	err = s.websocketService.SendToUser(ctx, int(notification.UserID), "notification", string(payloadJson))
	if err != nil {
		return fmt.Errorf("failed to send notification to websocket: %w", err)
	}

	return nil
}

// GetChannelCode returns the channel code for websocket notifications
func (s *WebsocketDelivery) GetChannelCode() string {
	return "websocket"
}
