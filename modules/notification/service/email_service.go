package service

import (
	"time"

	"wnapi/modules/notification/queue"
)

// EmailService quản lý gửi email dựa trên queue
type EmailService struct {
	queueClient *queue.SimpleClient
}

// NewEmailService tạo một service mới để gửi email qua queue
func NewEmailService(redisAddr string) *EmailService {
	client := queue.NewSimpleClient(redisAddr)

	return &EmailService{
		queueClient: client,
	}
}

// SendEmail đưa email vào hàng đợi để gửi ngay lập tức
func (s *EmailService) SendEmail(to string, subject string, templateID string, variables map[string]string, tenantID int, userID int) error {
	payload := queue.EmailPayload{
		TenantID:   tenantID,
		UserID:     userID,
		To:         to,
		Subject:    subject,
		TemplateID: templateID,
		Variables:  variables,
	}

	return s.queueClient.SendEmail(payload)
}

// SendEmailWithCCAndBCC đưa email có CC và BCC vào hàng đợi để gửi
func (s *EmailService) SendEmailWithCCAndBCC(to string, cc []string, bcc []string, subject string, templateID string, variables map[string]string, tenantID int, userID int) error {
	payload := queue.EmailPayload{
		TenantID:   tenantID,
		UserID:     userID,
		To:         to,
		CC:         cc,
		BCC:        bcc,
		Subject:    subject,
		TemplateID: templateID,
		Variables:  variables,
	}

	return s.queueClient.SendEmail(payload)
}

// SendEmailWithAttachments đưa email có đính kèm vào hàng đợi để gửi
func (s *EmailService) SendEmailWithAttachments(to string, subject string, templateID string, variables map[string]string, attachments []string, tenantID int, userID int) error {
	payload := queue.EmailPayload{
		TenantID:    tenantID,
		UserID:      userID,
		To:          to,
		Subject:     subject,
		TemplateID:  templateID,
		Variables:   variables,
		Attachments: attachments,
	}

	return s.queueClient.SendEmail(payload)
}

// ScheduleEmail lập lịch gửi email vào thời điểm cụ thể
func (s *EmailService) ScheduleEmail(to string, subject string, templateID string, variables map[string]string, sendAt time.Time, tenantID int, userID int) error {
	payload := queue.EmailPayload{
		TenantID:   tenantID,
		UserID:     userID,
		To:         to,
		Subject:    subject,
		TemplateID: templateID,
		Variables:  variables,
	}

	return s.queueClient.SendEmailWithSchedule(payload, sendAt)
}

// SendEmailBatch gửi hàng loạt email với cùng nội dung đến nhiều người
func (s *EmailService) SendEmailBatch(recipients []string, subject string, templateID string, variables map[string]string, tenantID int, userID int) error {
	var payloads []queue.EmailPayload

	for _, recipient := range recipients {
		payloads = append(payloads, queue.EmailPayload{
			TenantID:   tenantID,
			UserID:     userID,
			To:         recipient,
			Subject:    subject,
			TemplateID: templateID,
			Variables:  variables,
		})
	}

	return s.queueClient.ScheduleBatchEmails(payloads)
}

// SendEmailLater gửi email sau một khoảng thời gian
func (s *EmailService) SendEmailLater(to string, subject string, templateID string, variables map[string]string, delay time.Duration, tenantID int, userID int) error {
	payload := queue.EmailPayload{
		TenantID:   tenantID,
		UserID:     userID,
		To:         to,
		Subject:    subject,
		TemplateID: templateID,
		Variables:  variables,
	}

	return s.queueClient.SendEmailIn(payload, delay)
}

// Close đóng kết nối client
func (s *EmailService) Close() error {
	return s.queueClient.Close()
}
