package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/notification/dto"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/repository"

	"github.com/google/uuid"
)

// Service implements the NotificationService interface
type Service struct {
	repo   repository.Repository
	config internal.NotificationConfig
	logger logger.Logger
}

// NewService creates a new notification service
func NewService(repo repository.Repository, config internal.NotificationConfig, logger logger.Logger) NotificationService {
	return &Service{
		repo:   repo,
		config: config,
		logger: logger,
	}
}

// SendEmail sends an email notification
func (s *Service) SendEmail(ctx context.Context, req dto.EmailRequest) (*dto.NotificationResponse, error) {
	s.logger.Info("Sending email to: %s, subject: %s", req.To, req.Subject)

	// Generate notification ID
	notificationID := uuid.New().String()

	// Create notification history record
	content := req.Content
	if content == "" && req.Body != "" {
		content = req.Body // Support both Content and Body fields
	}

	history := &dto.NotificationHistory{
		ID:        notificationID,
		UserID:    req.UserID,
		Type:      "email",
		Title:     req.Subject,
		Message:   content,
		Recipient: strings.Join(req.To, ","),
		Status:    "pending",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Save to database
	if err := s.repo.SaveEmailNotification(ctx, history); err != nil {
		s.logger.Error("Failed to save email notification: %v", err)
		return nil, fmt.Errorf("failed to save notification: %w", err)
	}

	// TODO: Implement actual email sending logic here
	// This would integrate with SMTP, SendGrid, AWS SES, etc.
	success := s.simulateEmailSending(req)

	// Update status based on result
	status := "sent"
	if !success {
		status = "failed"
	}

	if err := s.repo.UpdateNotificationStatus(ctx, notificationID, status); err != nil {
		s.logger.Error("Failed to update notification status: %v", err)
	}

	return &dto.NotificationResponse{
		ID:      notificationID,
		Status:  status,
		Message: "Email notification processed",
		SentAt:  time.Now(),
	}, nil
}

// SendSMS sends an SMS notification
func (s *Service) SendSMS(ctx context.Context, req dto.SMSRequest) (*dto.NotificationResponse, error) {
	s.logger.Info("Sending SMS to: %s", req.To)

	if !s.config.SMSEnabled {
		return nil, fmt.Errorf("SMS notifications are disabled")
	}

	// Generate notification ID
	notificationID := uuid.New().String()

	// Create notification history record
	history := &dto.NotificationHistory{
		ID:        notificationID,
		UserID:    req.UserID,
		Type:      "sms",
		Title:     "SMS Notification",
		Message:   req.Message,
		Recipient: strings.Join(req.To, ","),
		Status:    "pending",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Save to database
	if err := s.repo.SaveSMSNotification(ctx, history); err != nil {
		s.logger.Error("Failed to save SMS notification: %v", err)
		return nil, fmt.Errorf("failed to save notification: %w", err)
	}

	// TODO: Implement actual SMS sending logic here
	// This would integrate with Twilio, AWS SNS, etc.
	success := s.simulateSMSSending(req)

	// Update status based on result
	status := "sent"
	if !success {
		status = "failed"
	}

	if err := s.repo.UpdateNotificationStatus(ctx, notificationID, status); err != nil {
		s.logger.Error("Failed to update notification status: %v", err)
	}

	return &dto.NotificationResponse{
		ID:      notificationID,
		Status:  status,
		Message: "SMS notification processed",
		SentAt:  time.Now(),
	}, nil
}

// SendPushNotification sends a push notification
func (s *Service) SendPushNotification(ctx context.Context, req dto.PushRequest) (*dto.NotificationResponse, error) {
	userID := req.UserID
	if userID == "" && len(req.To) > 0 {
		userID = req.To[0] // Use first recipient as user ID if not specified
	}
	s.logger.Info("Sending push notification to user: %s, title: %s", userID, req.Title)

	if !s.config.PushEnabled {
		return nil, fmt.Errorf("push notifications are disabled")
	}

	// Generate notification ID
	notificationID := uuid.New().String()

	// Create notification history record
	recipient := userID
	if len(req.To) > 0 {
		recipient = strings.Join(req.To, ",")
	}

	history := &dto.NotificationHistory{
		ID:        notificationID,
		UserID:    userID,
		Type:      "push",
		Title:     req.Title,
		Message:   req.Body,
		Recipient: recipient,
		Status:    "pending",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Save to database
	if err := s.repo.SavePushNotification(ctx, history); err != nil {
		s.logger.Error("Failed to save push notification: %v", err)
		return nil, fmt.Errorf("failed to save notification: %w", err)
	}

	// TODO: Implement actual push notification sending logic here
	// This would integrate with Firebase FCM, Apple Push Notification Service, etc.
	success := s.simulatePushSending(req)

	// Update status based on result
	status := "sent"
	if !success {
		status = "failed"
	}

	if err := s.repo.UpdateNotificationStatus(ctx, notificationID, status); err != nil {
		s.logger.Error("Failed to update notification status: %v", err)
	}

	return &dto.NotificationResponse{
		ID:      notificationID,
		Status:  status,
		Message: "Push notification processed",
		SentAt:  time.Now(),
	}, nil
}

// GetNotificationHistory retrieves notification history for a user
func (s *Service) GetNotificationHistory(ctx context.Context, userID string) ([]dto.NotificationHistory, error) {
	s.logger.Info("Getting notification history for user: %s", userID)

	// Get history from repository with default pagination
	history, err := s.repo.GetNotificationHistory(ctx, userID, 50, 0)
	if err != nil {
		s.logger.Error("Failed to get notification history: %v", err)
		return nil, fmt.Errorf("failed to get notification history: %w", err)
	}

	return history, nil
}

// Simulation methods (to be replaced with actual implementations)

func (s *Service) simulateEmailSending(req dto.EmailRequest) bool {
	s.logger.Info("Simulating email sending to %s with subject '%s'", req.To, req.Subject)
	// Simulate 90% success rate
	return time.Now().UnixNano()%10 != 0
}

func (s *Service) simulateSMSSending(req dto.SMSRequest) bool {
	s.logger.Info("Simulating SMS sending to %s with message '%s'", req.To, req.Message)
	// Simulate 90% success rate
	return time.Now().UnixNano()%10 != 0
}

func (s *Service) simulatePushSending(req dto.PushRequest) bool {
	userID := req.UserID
	if userID == "" && len(req.To) > 0 {
		userID = req.To[0]
	}
	s.logger.Info("Simulating push notification to user %s with title '%s'", userID, req.Title)
	// Simulate 90% success rate
	return time.Now().UnixNano()%10 != 0
}
