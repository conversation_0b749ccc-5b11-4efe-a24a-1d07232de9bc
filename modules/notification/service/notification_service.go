package service

import (
	"context"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/models"
	"wnapi/modules/notification/repository"
	"wnapi/modules/notification/repository/redis"
)

// NotificationService triển khai interface NotificationService
type NotificationService struct {
	repo   repository.NotificationRepository
	config internal.NotificationConfig
	logger logger.Logger
	cache  *redis.NotificationCache
}

// NewNotificationService tạo một notification service mới
func NewNotificationService(repo repository.NotificationRepository, config internal.NotificationConfig, log logger.Logger) internal.NotificationService {
	// Khởi tạo cache nếu cần
	var cache *redis.NotificationCache
	if config.CacheEnabled {
		cache = redis.NewNotificationCache(config.RedisAddr, time.Duration(config.CacheTTL)*time.Second)
	}

	return &NotificationService{
		repo:   repo,
		config: config,
		logger: log,
		cache:  cache,
	}
}

// CreateNotification tạo một thông báo mới
func (s *NotificationService) CreateNotification(ctx context.Context, notification interface{}) (int, error) {
	notif, ok := notification.(*models.Notification)
	if !ok {
		return 0, fmt.Errorf("invalid notification type")
	}

	s.logger.Debug("Creating notification", logger.String("title", notif.Title))

	notificationID, err := s.repo.Create(ctx, notif)
	if err != nil {
		s.logger.Error("Failed to create notification", logger.String("error", err.Error()))
		return 0, fmt.Errorf("failed to create notification: %w", err)
	}

	// Invalidate cache for user's unread count
	if s.cache != nil {
		s.cache.InvalidateUserUnreadCount(ctx, int(notif.UserID))
	}

	return notificationID, nil
}

// GetNotification lấy thông tin thông báo theo ID
func (s *NotificationService) GetNotification(ctx context.Context, notificationID int) (interface{}, error) {
	s.logger.Debug("Getting notification", logger.Int("notification_id", notificationID))

	notification, err := s.repo.GetByID(ctx, notificationID)
	if err != nil {
		s.logger.Error("Failed to get notification",
			logger.Int("notification_id", notificationID),
			logger.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get notification: %w", err)
	}

	return notification, nil
}

// GetUserNotifications lấy danh sách thông báo của người dùng
func (s *NotificationService) GetUserNotifications(ctx context.Context, userID int, cursor string, limit int) ([]interface{}, string, error) {
	s.logger.Debug("Getting user notifications",
		logger.Int("user_id", userID),
		logger.String("cursor", cursor),
		logger.Int("limit", limit))

	filters := make(map[string]interface{})

	notifications, nextCursor, err := s.repo.GetByUserID(ctx, userID, cursor, limit, filters)
	if err != nil {
		s.logger.Error("Failed to get user notifications",
			logger.Int("user_id", userID),
			logger.String("error", err.Error()))
		return nil, "", fmt.Errorf("failed to get user notifications: %w", err)
	}

	// Chuyển đổi từ []*models.Notification sang []interface{}
	result := make([]interface{}, len(notifications))
	for i, notification := range notifications {
		result[i] = notification
	}

	return result, nextCursor, nil
}

// MarkAsRead đánh dấu thông báo đã đọc
func (s *NotificationService) MarkAsRead(ctx context.Context, notificationID int) error {
	s.logger.Debug("Marking notification as read", logger.Int("notification_id", notificationID))

	notification, err := s.repo.GetByID(ctx, notificationID)
	if err != nil {
		s.logger.Error("Failed to get notification",
			logger.Int("notification_id", notificationID),
			logger.String("error", err.Error()))
		return fmt.Errorf("failed to get notification: %w", err)
	}

	if err := s.repo.MarkAsRead(ctx, notificationID); err != nil {
		s.logger.Error("Failed to mark notification as read",
			logger.Int("notification_id", notificationID),
			logger.String("error", err.Error()))
		return fmt.Errorf("failed to mark notification as read: %w", err)
	}

	// Invalidate cache for user's unread count
	if s.cache != nil {
		s.cache.InvalidateUserUnreadCount(ctx, int(notification.UserID))
	}

	return nil
}

// MarkAllAsRead đánh dấu tất cả thông báo của người dùng đã đọc
func (s *NotificationService) MarkAllAsRead(ctx context.Context, userID int) error {
	s.logger.Debug("Marking all notifications as read", logger.Int("user_id", userID))

	if err := s.repo.MarkAllAsRead(ctx, userID); err != nil {
		s.logger.Error("Failed to mark all notifications as read",
			logger.Int("user_id", userID),
			logger.String("error", err.Error()))
		return fmt.Errorf("failed to mark all notifications as read: %w", err)
	}

	// Invalidate cache for user's unread count
	if s.cache != nil {
		s.cache.InvalidateUserUnreadCount(ctx, userID)
	}

	return nil
}

// GetUnreadCount lấy số lượng thông báo chưa đọc của người dùng
func (s *NotificationService) GetUnreadCount(ctx context.Context, userID int) (int, error) {
	s.logger.Debug("Getting unread count", logger.Int("user_id", userID))

	// Try to get from cache first
	if s.cache != nil {
		count, err := s.cache.GetUserUnreadCount(ctx, userID)
		if err == nil {
			return count, nil
		}
		// Log cache miss but continue to get from database
		s.logger.Debug("Cache miss for unread count", logger.Int("user_id", userID))
	}

	// Get from database
	count, err := s.repo.GetUnreadCount(ctx, userID)
	if err != nil {
		s.logger.Error("Failed to get unread count",
			logger.Int("user_id", userID),
			logger.String("error", err.Error()))
		return 0, fmt.Errorf("failed to get unread count: %w", err)
	}

	// Cache the result
	if s.cache != nil {
		s.cache.SetUserUnreadCount(ctx, userID, count)
	}

	return count, nil
}

// DeleteNotification xóa thông báo
func (s *NotificationService) DeleteNotification(ctx context.Context, notificationID int) error {
	s.logger.Debug("Deleting notification", logger.Int("notification_id", notificationID))

	notification, err := s.repo.GetByID(ctx, notificationID)
	if err != nil {
		s.logger.Error("Failed to get notification",
			logger.Int("notification_id", notificationID),
			logger.String("error", err.Error()))
		return fmt.Errorf("failed to get notification: %w", err)
	}

	if err := s.repo.Delete(ctx, notificationID); err != nil {
		s.logger.Error("Failed to delete notification",
			logger.Int("notification_id", notificationID),
			logger.String("error", err.Error()))
		return fmt.Errorf("failed to delete notification: %w", err)
	}

	// Invalidate cache for user's unread count if notification was unread
	if !notification.IsRead && s.cache != nil {
		s.cache.InvalidateUserUnreadCount(ctx, int(notification.UserID))
	}

	return nil
}
