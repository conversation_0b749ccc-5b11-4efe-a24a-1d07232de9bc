CREATE TABLE IF NOT EXISTS customers (
  customer_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  user_id INT UNSIGNED NULL, -- <PERSON><PERSON><PERSON> kết với users nếu khách hàng đăng ký tài khoản
  group_id INT UNSIGNED,
  full_name VARCHAR(200) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  status ENUM('active', 'inactive', 'blocked', 'pending') DEFAULT 'pending',
  is_verified BOOLEAN DEFAULT FALSE,
  avatar_url VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_login_at TIMESTAMP NULL,
  created_by INT UNSIGNED,
  updated_by INT UNSIGNED,
  UNIQUE KEY uq_tenant_customer_email (tenant_id, email),
  INDEX idx_customer_tenant_id (tenant_id),
  INDEX idx_customer_user_id (user_id),
  INDEX idx_customer_group_id (group_id),
  INDEX idx_customer_status (status),
  INDEX idx_customer_full_name (full_name),
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
  FOREIGN KEY (group_id) REFERENCES customer_groups(group_id) ON DELETE SET NULL
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 