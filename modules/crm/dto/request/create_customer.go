package request

type CreateCustomerRequest struct {
	TenantID  uint   `json:"tenant_id" binding:"required,min=1"`
	UserID    *uint  `json:"user_id"`
	GroupID   *uint  `json:"group_id"`
	FullName  string `json:"full_name" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Phone     string `json:"phone"`
	Status    string `json:"status" binding:"omitempty,oneof=active inactive blocked pending"`
	AvatarURL string `json:"avatar_url"`
	CreatedBy *uint  `json:"created_by"`
}
