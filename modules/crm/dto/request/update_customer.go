package request

type UpdateCustomerRequest struct {
	GroupID    *uint   `json:"group_id"`
	FullName   *string `json:"full_name"`
	Email      *string `json:"email" binding:"omitempty,email"`
	Phone      *string `json:"phone"`
	Status     *string `json:"status" binding:"omitempty,oneof=active inactive blocked pending"`
	IsVerified *bool   `json:"is_verified"`
	AvatarURL  *string `json:"avatar_url"`
	UpdatedBy  *uint   `json:"updated_by"`
}
