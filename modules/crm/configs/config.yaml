db:
  host: localhost
  port: 3307
  username: root
  password: root
  database: blog_v4

crm:
  port: 9053
  grpc_port: 9054

jwt:
  access_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  refresh_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  access_token_expiration: "168h"
  refresh_token_expiration: "168h"
  issuer: wn-backend

tracing:
  enabled: true
  service_name: "crm-service"
  exporter_type: "jaeger"  # Options: "signoz" or "jaeger"
  sample_ratio: 1.0
  signoz:
    endpoint: "localhost:4317"
  jaeger:
    host: "localhost"
    port: "6831"