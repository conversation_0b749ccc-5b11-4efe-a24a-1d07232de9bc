package configs

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/spf13/viper"
)

type Config struct {
	Database struct {
		Host     string `mapstructure:"host"`
		Port     int    `mapstructure:"port"`
		Username string `mapstructure:"username"`
		Password string `mapstructure:"password"`
		Name     string `mapstructure:"database"`
	} `mapstructure:"db"`

	CRM struct {
		Host     string `mapstructure:"host"`
		Port     int    `mapstructure:"port"`
		GRPCPort int    `mapstructure:"grpc_port"`
	} `mapstructure:"crm"`

	JWT struct {
		AccessSigningKey       string        `mapstructure:"access_signing_key"`
		RefreshSigningKey      string        `mapstructure:"refresh_signing_key"`
		AccessTokenExpiration  time.Duration `mapstructure:"access_token_expiration"`
		RefreshTokenExpiration time.Duration `mapstructure:"refresh_token_expiration"`
		Issuer                 string        `mapstructure:"issuer"`
	} `mapstructure:"jwt"`

	Tracing struct {
		Enabled      bool    `mapstructure:"enabled"`
		ServiceName  string  `mapstructure:"service_name"`
		ExporterType string  `mapstructure:"exporter_type"` // "signoz" or "jaeger"
		SampleRatio  float64 `mapstructure:"sample_ratio"`
		Signoz       struct {
			Endpoint string `mapstructure:"endpoint"`
		} `mapstructure:"signoz"`
		Jaeger struct {
			Host string `mapstructure:"host"`
			Port string `mapstructure:"port"`
		} `mapstructure:"jaeger"`
	} `mapstructure:"tracing"`
}

func LoadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("../configs")
	viper.AddConfigPath("../../configs")

	if err := viper.ReadInConfig(); err != nil {
		return nil, err
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	// Override with environment variables if set
	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.Database.Host = dbHost
	}

	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		if port, err := strconv.Atoi(dbPort); err == nil {
			config.Database.Port = port
		}
	}

	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		config.Database.Username = dbUser
	}

	if dbPass := os.Getenv("DB_PASSWORD"); dbPass != "" {
		config.Database.Password = dbPass
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.Database.Name = dbName
	}

	if httpPort := os.Getenv("HTTP_PORT"); httpPort != "" {
		if port, err := strconv.Atoi(httpPort); err == nil {
			config.CRM.Port = port
		}
	}

	if grpcPort := os.Getenv("GRPC_PORT"); grpcPort != "" {
		if port, err := strconv.Atoi(grpcPort); err == nil {
			config.CRM.GRPCPort = port
		}
	}

	// Handle tracing configuration from environment variables
	if tracingEnabled := os.Getenv("TRACING_ENABLED"); tracingEnabled != "" {
		config.Tracing.Enabled = tracingEnabled == "true"
	}

	if serviceName := os.Getenv("TRACING_SERVICE_NAME"); serviceName != "" {
		config.Tracing.ServiceName = serviceName
	} else if config.Tracing.ServiceName == "" {
		config.Tracing.ServiceName = "crm-service" // Default service name from task requirements
	}

	if exporterType := os.Getenv("TRACING_EXPORTER_TYPE"); exporterType != "" {
		config.Tracing.ExporterType = exporterType
	} else if config.Tracing.ExporterType == "" {
		config.Tracing.ExporterType = "signoz" // Default to SignOz
	}

	if sampleRatio := os.Getenv("TRACING_SAMPLE_RATIO"); sampleRatio != "" {
		if ratio, err := strconv.ParseFloat(sampleRatio, 64); err == nil {
			config.Tracing.SampleRatio = ratio
		}
	} else if config.Tracing.SampleRatio == 0 {
		config.Tracing.SampleRatio = 1.0 // Default sample ratio
	}

	// Handle SignOz endpoint
	if signozEndpoint := os.Getenv("SIGNOZ_ENDPOINT"); signozEndpoint != "" {
		config.Tracing.Signoz.Endpoint = signozEndpoint
	} else if config.Tracing.Signoz.Endpoint == "" {
		config.Tracing.Signoz.Endpoint = "localhost:4317" // Default endpoint
	}

	// Handle Jaeger configuration
	jaegerHost := os.Getenv("JAEGER_AGENT_HOST")
	jaegerPort := os.Getenv("JAEGER_AGENT_PORT")
	if jaegerHost != "" {
		config.Tracing.Jaeger.Host = jaegerHost
	} else if config.Tracing.Jaeger.Host == "" {
		config.Tracing.Jaeger.Host = "localhost"
	}

	if jaegerPort != "" {
		config.Tracing.Jaeger.Port = jaegerPort
	} else if config.Tracing.Jaeger.Port == "" {
		config.Tracing.Jaeger.Port = "6831"
	}

	// Set default values if needed
	if config.Database.Host == "" {
		config.Database.Host = "mysql"
	}
	if config.CRM.Host == "" {
		config.CRM.Host = "0.0.0.0"
	}

	// Print config in Docker environment
	if os.Getenv("DOCKER_ENV") == "true" {
		PrintConfig(&config)
	}

	return &config, nil
}

// PrintConfig prints all configuration values in a formatted JSON
func PrintConfig(config *Config) {
	// Create a copy of the config with the password masked for security
	configCopy := *config
	configCopy.Database.Password = "********"

	// Marshal config to JSON for pretty printing
	configJSON, err := json.MarshalIndent(configCopy, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling config: %v\n", err)
		return
	}

	fmt.Printf("=== CRM MODULE CONFIGURATION ===\n%s\n==============================\n", string(configJSON))
}
