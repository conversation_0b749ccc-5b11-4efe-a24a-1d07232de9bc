package models

import (
	"time"
)

type CustomerStatus string

const (
	CustomerStatusActive   CustomerStatus = "active"
	CustomerStatusInactive CustomerStatus = "inactive"
	CustomerStatusBlocked  CustomerStatus = "blocked"
	CustomerStatusPending  CustomerStatus = "pending"
)

type Customer struct {
	CustomerID  uint           `db:"customer_id" json:"customer_id" gorm:"primaryKey"`
	TenantID    uint           `db:"tenant_id" json:"tenant_id"`
	UserID      *uint          `db:"user_id" json:"user_id"`
	GroupID     *uint          `db:"group_id" json:"group_id"`
	FullName    string         `db:"full_name" json:"full_name"`
	Email       string         `db:"email" json:"email"`
	Phone       string         `db:"phone" json:"phone"`
	Status      CustomerStatus `db:"status" json:"status"`
	IsVerified  bool           `db:"is_verified" json:"is_verified"`
	AvatarURL   string         `db:"avatar_url" json:"avatar_url"`
	CreatedAt   time.Time      `db:"created_at" json:"created_at"`
	UpdatedAt   time.Time      `db:"updated_at" json:"updated_at"`
	LastLoginAt *time.Time     `db:"last_login_at" json:"last_login_at"`
	CreatedBy   *uint          `db:"created_by" json:"created_by"`
	UpdatedBy   *uint          `db:"updated_by" json:"updated_by"`
}
