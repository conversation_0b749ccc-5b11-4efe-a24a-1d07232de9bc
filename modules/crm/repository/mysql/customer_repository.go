package mysql

import (
	"context"
	"fmt"
	"log"

	"github.com/webnew/wn-backend-v2/modules/crm/models"
	"github.com/webnew/wn-backend-v2/pkg/pagination"
	"gorm.io/gorm"
)

type customerRepository struct {
	db *gorm.DB
}

func NewCustomerRepository(db *gorm.DB) *customerRepository {
	return &customerRepository{
		db: db,
	}
}

func (r *customerRepository) Create(ctx context.Context, customer *models.Customer) error {
	log.Printf("[DEBUG SQL] - Create customer: %+v", customer)
	result := r.db.WithContext(ctx).Create(customer)
	if result.Error != nil {
		return fmt.Errorf("failed to create customer: %w", result.Error)
	}
	return nil
}

func (r *customerRepository) Update(ctx context.Context, customer *models.Customer) error {
	log.Printf("[DEBUG SQL] - Update customer: %+v", customer)
	result := r.db.WithContext(ctx).Save(customer)
	if result.Error != nil {
		return fmt.Errorf("failed to update customer: %w", result.Error)
	}
	return nil
}

func (r *customerRepository) Delete(ctx context.Context, customerID uint) error {
	log.Printf("[DEBUG SQL] - Delete customer with ID: %d", customerID)
	result := r.db.WithContext(ctx).Delete(&models.Customer{}, customerID)
	if result.Error != nil {
		return fmt.Errorf("failed to delete customer: %w", result.Error)
	}
	return nil
}

func (r *customerRepository) FindByID(ctx context.Context, customerID uint) (*models.Customer, error) {
	query := `
		SELECT customer_id, tenant_id, user_id, group_id, full_name, email, phone, status, 
		       is_verified, avatar_url, created_at, updated_at, last_login_at, created_by, updated_by
		FROM customers 
		WHERE customer_id = ?`
	log.Printf("[DEBUG SQL] - FindByID: %s, params: [%d]", query, customerID)

	var customer models.Customer
	result := r.db.WithContext(ctx).Raw(query, customerID).Scan(&customer)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to find customer by id: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return nil, nil
	}
	return &customer, nil
}

func (r *customerRepository) FindByEmail(ctx context.Context, email string, tenantID uint) (*models.Customer, error) {
	query := `
		SELECT customer_id, tenant_id, user_id, group_id, full_name, email, phone, status, 
		       is_verified, avatar_url, created_at, updated_at, last_login_at, created_by, updated_by
		FROM customers 
		WHERE email = ? AND tenant_id = ?`
	log.Printf("[DEBUG SQL] - FindByEmail: %s, params: [%s, %d]", query, email, tenantID)

	var customer models.Customer
	result := r.db.WithContext(ctx).Raw(query, email, tenantID).Scan(&customer)

	if result.Error != nil {
		return nil, fmt.Errorf("failed to find customer by email: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return nil, nil
	}
	return &customer, nil
}

func (r *customerRepository) List(ctx context.Context, cursor string, limit int, tenantID uint, search string) ([]*models.Customer, string, error) {
	var customers []*models.Customer
	decodedCursor, err := pagination.DecodeCursor(cursor)
	if err != nil {
		return nil, "", fmt.Errorf("failed to decode cursor: %w", err)
	}

	query := `
		SELECT customer_id, tenant_id, user_id, group_id, full_name, email, phone, status, 
		       is_verified, avatar_url, created_at, updated_at, last_login_at, created_by, updated_by
		FROM customers 
		WHERE tenant_id = ?`

	args := []interface{}{tenantID}

	if decodedCursor != nil && decodedCursor.ID > 0 {
		query += ` AND customer_id > ?`
		args = append(args, decodedCursor.ID)
	}

	if search != "" {
		query += ` AND (full_name LIKE ? OR email LIKE ? OR phone LIKE ?)`
		searchParam := "%" + search + "%"
		args = append(args, searchParam, searchParam, searchParam)
	}

	query += ` ORDER BY customer_id LIMIT ?`
	args = append(args, limit+1)

	logParams := fmt.Sprintf("tenantID: %d", tenantID)
	if decodedCursor != nil && decodedCursor.ID > 0 {
		logParams += fmt.Sprintf(", cursor: %v", decodedCursor.ID)
	}
	if search != "" {
		logParams += fmt.Sprintf(", search: %s", search)
	}
	logParams += fmt.Sprintf(", limit: %d", limit+1)

	log.Printf("[DEBUG SQL] - List customers: %s, params: [%s]", query, logParams)

	result := r.db.WithContext(ctx).Raw(query, args...).Scan(&customers)

	if result.Error != nil {
		return nil, "", fmt.Errorf("failed to list customers: %w", result.Error)
	}

	var nextCursor string
	if len(customers) > limit {
		nextCursor = pagination.EncodeCursor(fmt.Sprintf("%d", customers[len(customers)-1].CustomerID))
		customers = customers[:limit]
	}

	return customers, nextCursor, nil
}
