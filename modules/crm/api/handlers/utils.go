package handlers

import (
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/pkg/auth"
	"github.com/webnew/wn-backend-v2/pkg/response"
)

// Constants for error codes
const (
	ErrCodeInvalidRequestFormat = "INVALID_REQUEST_FORMAT"
	ErrCodeUnauthorized         = "UNAUTHORIZED"
	ErrCodeInternalServerError  = "INTERNAL_SERVER_ERROR"
	ErrCodeCustomerNotFound     = "CUSTOMER_NOT_FOUND"
	ErrCodeCustomerEmailExists  = "CUSTOMER_EMAIL_EXISTS"
	ErrCodeInvalidTenantData    = "INVALID_TENANT_DATA"
)

// apiSuccess sends a successful API response
func apiSuccess(c *gin.Context, statusCode int, message string, data interface{}) {
	response.Success(c, statusCode, message, data)
}

// apiSuccessWithMeta sends a successful API response with metadata
func apiSuccessWithMeta(c *gin.Context, statusCode int, message string, data interface{}, meta map[string]interface{}) {
	response.SuccessWithMeta(c, statusCode, message, data, meta)
}

// apiError sends an error API response
func apiError(c *gin.Context, statusCode int, errorCode string, message string) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, nil)
}

// apiErrorWithDetails sends an error API response with detailed information
func apiErrorWithDetails(c *gin.Context, statusCode int, errorCode string, message string, details interface{}) {
	response.ErrorWithDetails(c, statusCode, message, errorCode, details)
}

func getTenantIDFromContext(c *gin.Context) (string, error) {
	// Get claims from context
	claims, exists := auth.GetClaimsFromContext(c)
	if !exists {
		return "", errors.New("không tìm thấy thông tin xác thực")
	}

	return strconv.FormatUint(uint64(claims.TenantID), 10), nil
}

func getUserIDFromContext(c *gin.Context) (string, error) {
	// Get user ID from context
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		return "", errors.New("không tìm thấy thông tin người dùng")
	}

	return strconv.FormatUint(uint64(userID), 10), nil
}
