package handlers

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/crm/dto/request"
	"github.com/webnew/wn-backend-v2/modules/crm/service"
)

type CustomerHandler struct {
	customerService *service.CustomerService
}

func NewCustomerHandler(customerService *service.CustomerService) *CustomerHandler {
	return &CustomerHandler{
		customerService: customerService,
	}
}

func (h *CustomerHandler) CreateCustomer(c *gin.Context) {
	var req request.CreateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Invalid request data")
		return
	}

	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeUnauthorized, err.Error())
		return
	}

	// Convert tenantID from string to uint
	tID, err := strconv.ParseUint(tenantID, 10, 64)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Invalid tenant ID")
		return
	}
	req.TenantID = uint(tID)

	// Get user ID from context for created_by
	userID, err := getUserIDFromContext(c)
	if err == nil && userID != "" {
		uID, err := strconv.ParseUint(userID, 10, 64)
		if err == nil {
			createdBy := uint(uID)
			req.CreatedBy = &createdBy
		}
	}

	customerResp, err := h.customerService.Create(c, &req)
	if err != nil {
		status := http.StatusInternalServerError
		errorCode := ErrCodeInternalServerError
		message := "Failed to create customer"

		if errors.Is(err, service.ErrCustomerEmailExists) {
			status = http.StatusConflict
			errorCode = ErrCodeCustomerEmailExists
			message = "Email already exists for this tenant"
		} else if errors.Is(err, service.ErrTenantIDRequired) {
			status = http.StatusBadRequest
			errorCode = ErrCodeInvalidTenantData
			message = "Tenant ID is required"
		}

		apiError(c, status, errorCode, message)
		return
	}

	apiSuccess(c, http.StatusCreated, "Customer created successfully", customerResp)
}

func (h *CustomerHandler) UpdateCustomer(c *gin.Context) {
	// Get customer ID from path parameter
	customerID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Invalid customer ID")
		return
	}

	var req request.UpdateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Invalid request data")
		return
	}

	// Get user ID from context for updated_by
	userID, err := getUserIDFromContext(c)
	if err == nil && userID != "" {
		uID, err := strconv.ParseUint(userID, 10, 64)
		if err == nil {
			updatedBy := uint(uID)
			req.UpdatedBy = &updatedBy
		}
	}

	customerResp, err := h.customerService.Update(c, uint(customerID), &req)
	if err != nil {
		status := http.StatusInternalServerError
		errorCode := ErrCodeInternalServerError
		message := "Failed to update customer"

		if errors.Is(err, service.ErrCustomerNotFound) {
			status = http.StatusNotFound
			errorCode = ErrCodeCustomerNotFound
			message = "Customer not found"
		} else if errors.Is(err, service.ErrCustomerEmailExists) {
			status = http.StatusConflict
			errorCode = ErrCodeCustomerEmailExists
			message = "Email already exists for this tenant"
		}

		apiError(c, status, errorCode, message)
		return
	}

	apiSuccess(c, http.StatusOK, "Customer updated successfully", customerResp)
}

func (h *CustomerHandler) DeleteCustomer(c *gin.Context) {
	// Get customer ID from path parameter
	customerID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Invalid customer ID")
		return
	}

	err = h.customerService.Delete(c, uint(customerID))
	if err != nil {
		status := http.StatusInternalServerError
		errorCode := ErrCodeInternalServerError
		message := "Failed to delete customer"

		if errors.Is(err, service.ErrCustomerNotFound) {
			status = http.StatusNotFound
			errorCode = ErrCodeCustomerNotFound
			message = "Customer not found"
		}

		apiError(c, status, errorCode, message)
		return
	}

	apiSuccess(c, http.StatusOK, "Customer deleted successfully", nil)
}

func (h *CustomerHandler) GetCustomer(c *gin.Context) {
	// Get customer ID from path parameter
	customerID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidRequestFormat, "Invalid customer ID")
		return
	}

	customerResp, err := h.customerService.GetByID(c, uint(customerID))
	if err != nil {
		status := http.StatusInternalServerError
		errorCode := ErrCodeInternalServerError
		message := "Failed to get customer"

		if errors.Is(err, service.ErrCustomerNotFound) {
			status = http.StatusNotFound
			errorCode = ErrCodeCustomerNotFound
			message = "Customer not found"
		}

		apiError(c, status, errorCode, message)
		return
	}

	apiSuccess(c, http.StatusOK, "Customer found", customerResp)
}

func (h *CustomerHandler) ListCustomers(c *gin.Context) {
	// Get tenant ID from context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeUnauthorized, err.Error())
		return
	}

	// Convert tenantID from string to uint
	tID, err := strconv.ParseUint(tenantID, 10, 64)
	if err != nil {
		apiError(c, http.StatusInternalServerError, ErrCodeInternalServerError, "Invalid tenant ID")
		return
	}

	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 10
	}
	search := c.Query("search")

	customers, nextCursor, err := h.customerService.List(c, cursor, limit, uint(tID), search)
	if err != nil {
		status := http.StatusInternalServerError
		errorCode := ErrCodeInternalServerError
		message := "Failed to list customers"

		if errors.Is(err, service.ErrTenantIDRequired) {
			status = http.StatusBadRequest
			errorCode = ErrCodeInvalidTenantData
			message = "Tenant ID is required"
		}

		apiError(c, status, errorCode, message)
		return
	}

	meta := map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    nextCursor != "",
	}

	apiSuccessWithMeta(c, http.StatusOK, "Customers retrieved successfully", customers, meta)
}
