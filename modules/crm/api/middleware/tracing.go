package middleware

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
)

// TracingMiddleware creates a middleware that adds OpenTracing to HTTP requests
func TracingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Skip tracing for health check or metrics endpoints
		if c.Request.URL.Path == "/health" || c.Request.URL.Path == "/metrics" {
			c.Next()
			return
		}

		// Create a span name based on the request
		spanName := fmt.Sprintf("%s %s", c.Request.Method, c.FullPath())
		if spanName == "" {
			spanName = fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path)
		}

		// Get the global tracer
		tracer := opentracing.GlobalTracer()

		// Extract any existing spans from request headers (for distributed tracing)
		var spanCtx opentracing.SpanContext
		wireContext, err := tracer.Extract(
			opentracing.HTTPHeaders,
			opentracing.HTTPHeadersCarrier(c.Request.Header),
		)
		if err != nil && err != opentracing.ErrSpanContextNotFound {
			// Log error if extraction fails (but not for "not found" which is normal)
			fmt.Printf("Error extracting span context: %v\n", err)
		}

		if wireContext != nil {
			spanCtx = wireContext
		}

		// Create a new span
		var span opentracing.Span
		if spanCtx != nil {
			// We have a parent span from the request headers
			span = tracer.StartSpan(
				spanName,
				opentracing.ChildOf(spanCtx),
			)
		} else {
			// No parent span, this is the root span
			span = tracer.StartSpan(spanName)
		}
		defer span.Finish()

		// Set standard HTTP span tags
		ext.HTTPMethod.Set(span, c.Request.Method)
		ext.HTTPUrl.Set(span, c.Request.URL.String())
		ext.Component.Set(span, "gin")
		ext.SpanKindRPCServer.Set(span)

		// Add request ID if available
		if requestID := c.GetHeader("X-Request-ID"); requestID != "" {
			span.SetTag("http.request_id", requestID)
		}

		// Add client IP
		if clientIP := c.ClientIP(); clientIP != "" {
			span.SetTag("http.client_ip", clientIP)
		}

		// Store the span in the context
		c.Set("span", span)

		// Use the context with the span
		c.Request = c.Request.WithContext(
			opentracing.ContextWithSpan(c.Request.Context(), span),
		)

		// Process the request
		c.Next()

		// Add response status code to span
		status := c.Writer.Status()
		ext.HTTPStatusCode.Set(span, uint16(status))

		// Tag error if status code is 4xx or 5xx
		if status >= 400 {
			ext.Error.Set(span, true)
			span.SetTag("error.message", fmt.Sprintf("HTTP %d", status))
		}
	}
}
