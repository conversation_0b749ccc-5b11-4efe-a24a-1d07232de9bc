CREATE TABLE IF NOT EXISTS media_folders (
  folder_id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  tenant_id INT UNSIGNED NOT NULL,
  parent_id INT UNSIGNED NULL,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  slug VARCHAR(255) NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL DEFAULT NULL,
  created_by INT UNSIGNED,
  updated_by INT UNSIGNED,
  
  -- Constraints
  INDEX idx_media_folder_tenant (tenant_id),
  INDEX idx_media_folder_parent (parent_id),
  INDEX idx_media_folder_created_at (created_at),
  INDEX idx_media_folder_deleted_at (deleted_at),
  INDEX idx_media_folder_public (is_public)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 