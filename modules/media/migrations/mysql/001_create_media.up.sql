CREATE TABLE media (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    media_type ENUM('image', 'video', 'audio', 'document', 'other') NOT NULL,
    filename VA<PERSON>HAR(255) NOT NULL,
    original_filename VA<PERSON>HAR(255) NOT NULL,
    object_key VARCHAR(512) NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    size BIGINT UNSIGNED NOT NULL,
    status ENUM('pending', 'processing', 'ready', 'failed') NOT NULL DEFAULT 'pending',
    public_url VARCHAR(1024),
    description TEXT,
    uploaded_by VARCHAR(36),
    checksum VARCHAR(64),
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- JSON fields for flexible metadata and properties
    metadata JSON COMMENT 'Type-specific metadata (dimensions, duration, etc.)',
    properties JSON COMMENT 'Extended dynamic properties',
    
    -- Schema versioning for future migrations
    schema_version INT NOT NULL DEFAULT 1,
    
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL DEFAULT NULL,
    
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_media_type (media_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at),
    INDEX idx_checksum (checksum),
    INDEX idx_is_public (is_public),
    
    -- Index for JSON fields if supported by MySQL version
    -- INDEX idx_metadata (metadata->'$.width', metadata->'$.height', metadata->'$.duration') USING JSON
    
    FULLTEXT INDEX ft_description (description),
    FULLTEXT INDEX ft_filename (filename)
) ENGINE=InnoDB;