CREATE TABLE media_versions (
    id VARCHAR(36) NOT NULL PRIMARY KEY,
    media_id VARCHAR(36) NOT NULL,
    version_type VARCHAR(50) NOT NULL,
    width INT UNSIGNED,
    height INT UNSIGNED,
    object_key VARCHAR(512) NOT NULL,
    content_type VARCHAR(100) NOT NULL,
    size BIGINT UNSIGNED NOT NULL,
    quality TINYINT UNSIGNED,
    public_url VARCHAR(1024),
    properties JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_media_versions_media_id
        FOREIGN KEY (media_id)
        REFERENCES media(id)
        ON DELETE CASCADE
) ENGINE=InnoDB; 