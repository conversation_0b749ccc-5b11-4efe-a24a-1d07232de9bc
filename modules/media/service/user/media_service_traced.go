package user

import (
	"context"
	"mime/multipart"

	"wnapi/internal/pkg/tracing"
	"wnapi/modules/media/dto/media"
	"wnapi/modules/media/models"
	"wnapi/modules/media/service/common"
)

// TracedMediaService is a wrapper around MediaService that adds tracing
type TracedMediaService struct {
	service *MediaService
}

// Ensure TracedMediaService implements common.MediaService
var _ common.MediaService = (*TracedMediaService)(nil)

// NewTracedMediaService creates a new traced media service
func NewTracedMediaService(service *MediaService) *TracedMediaService {
	return &TracedMediaService{
		service: service,
	}
}

// Upload traces the upload operation
func (s *TracedMediaService) Upload(ctx context.Context, tenantID string, userID string, req *media.UploadMediaRequest) (*media.UploadMediaResponse, error) {
	// Create a new span for the upload operation
	ctx, span := tracing.ModuleMiddleware(ctx, "media", "upload")
	defer span.End()

	// Add additional attributes
	tracing.AddSpanAttributes(ctx,
		tracing.Function("Upload"),
		tracing.ContentType(req.File.Header.Get("Content-Type")),
		tracing.FileSize(int(req.File.Size)),
		tracing.TenantID(tenantID),
		tracing.UserID(userID),
		tracing.Filename(req.File.Filename),
	)

	// Call the original service method
	result, err := s.service.Upload(ctx, tenantID, userID, req)

	// Record error if any
	if err != nil {
		tracing.RecordError(ctx, err)
	} else if result != nil {
		// Add result attributes
		tracing.AddSpanAttributes(ctx, tracing.FileID(result.ID))
	}

	return result, err
}

// Get traces the get operation
func (s *TracedMediaService) Get(ctx context.Context, tenantID string, id string) (*media.MediaResponse, error) {
	return tracing.WithSpanResult(ctx, "media", "get", func(ctx context.Context) (*media.MediaResponse, error) {
		// Add span attributes
		tracing.AddSpanAttributes(ctx,
			tracing.Function("Get"),
			tracing.FileID(id),
			tracing.TenantID(tenantID),
		)

		// Call the original service method
		return s.service.Get(ctx, tenantID, id)
	})
}

// List traces the list operation
func (s *TracedMediaService) List(ctx context.Context, tenantID string, req *media.ListMediaRequest) ([]*media.MediaResponse, string, error) {
	ctx, span := tracing.StartSpan(ctx, "media", "list")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		tracing.Function("List"),
		tracing.TenantID(tenantID),
	)

	if req.MediaType != "" {
		tracing.AddSpanAttributes(ctx, tracing.MediaType(req.MediaType))
	}

	if req.Search != "" {
		tracing.AddSpanAttributes(ctx, tracing.SearchQuery(req.Search))
	}

	result, nextCursor, err := s.service.List(ctx, tenantID, req)
	if err != nil {
		tracing.RecordError(ctx, err)
	} else {
		tracing.AddSpanAttributes(ctx,
			tracing.ResultCount(len(result)),
			tracing.HasMore(nextCursor != ""),
		)
	}

	return result, nextCursor, err
}

// Update traces the update operation
func (s *TracedMediaService) Update(ctx context.Context, tenantID string, id string, req *media.UpdateMediaRequest) (*media.MediaResponse, error) {
	return tracing.WithSpanResult(ctx, "media", "update", func(ctx context.Context) (*media.MediaResponse, error) {
		// Add span attributes
		tracing.AddSpanAttributes(ctx,
			tracing.Function("Update"),
			tracing.FileID(id),
			tracing.TenantID(tenantID),
		)

		// Call the original service method
		return s.service.Update(ctx, tenantID, id, req)
	})
}

// Delete traces the delete operation
func (s *TracedMediaService) Delete(ctx context.Context, tenantID string, id string, permanent bool) error {
	return tracing.WithSpan(ctx, "media", "delete", func(ctx context.Context) error {
		// Add span attributes
		tracing.AddSpanAttributes(ctx,
			tracing.Function("Delete"),
			tracing.FileID(id),
			tracing.TenantID(tenantID),
			tracing.Permanent(permanent),
		)

		// Call the original service method
		return s.service.Delete(ctx, tenantID, id, permanent)
	})
}

// GetFile traces the get file operation
func (s *TracedMediaService) GetFile(ctx context.Context, tenantID string, id string) ([]byte, string, error) {
	ctx, span := tracing.StartSpan(ctx, "media", "get_file")
	defer span.End()

	tracing.AddSpanAttributes(ctx,
		tracing.Function("GetFile"),
		tracing.FileID(id),
		tracing.TenantID(tenantID),
	)

	data, contentType, err := s.service.GetFile(ctx, tenantID, id)
	if err != nil {
		tracing.RecordError(ctx, err)
	} else {
		tracing.AddSpanAttributes(ctx,
			tracing.ContentType(contentType),
			tracing.DataSize(len(data)),
		)
	}

	return data, contentType, err
}

// ProcessFile traces the process file operation
func (s *TracedMediaService) ProcessFile(ctx context.Context, mediaEntity *models.Media) error {
	return tracing.WithSpan(ctx, "media", "process_file", func(ctx context.Context) error {
		// Add span attributes
		tracing.AddSpanAttributes(ctx,
			tracing.Function("ProcessFile"),
			tracing.FileID(mediaEntity.ID),
			tracing.TenantID(mediaEntity.TenantID),
			tracing.MediaType(string(mediaEntity.MediaType)),
		)

		// Call the original service method
		return s.service.ProcessFile(ctx, mediaEntity)
	})
}

// SaveFile traces the save file operation
func (s *TracedMediaService) SaveFile(ctx context.Context, tenantID string, file *multipart.FileHeader, mediaType models.MediaType) (*models.Media, error) {
	ctx, span := tracing.StartSpan(ctx, "media", "save_file")
	defer span.End()

	// Add span attributes
	tracing.AddSpanAttributes(ctx,
		tracing.Function("SaveFile"),
		tracing.TenantID(tenantID),
		tracing.MediaType(string(mediaType)),
		tracing.Filename(file.Filename),
		tracing.FileSize(int(file.Size)),
	)

	// Call the original service method
	result, err := s.service.SaveFile(ctx, tenantID, file, mediaType)
	if err != nil {
		tracing.RecordError(ctx, err)
	}

	return result, err
}

// GetSpanFromContext is a helper function to get the current span from context
func (s *TracedMediaService) GetSpanFromContext(ctx context.Context) interface{} {
	return tracing.SpanFromContext(ctx)
}
