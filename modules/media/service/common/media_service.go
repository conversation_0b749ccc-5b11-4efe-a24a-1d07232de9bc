package common

import (
	"context"
	"mime/multipart"

	"wnapi/modules/media/dto/media"
	"wnapi/modules/media/models"
)

// MediaService định nghĩa các phương thức chung cho media service
type MediaService interface {
	// Upload tải lên một media mới
	Upload(ctx context.Context, tenantID string, userID string, req *media.UploadMediaRequest) (*media.UploadMediaResponse, error)

	// Get lấy thông tin chi tiết về một media
	Get(ctx context.Context, tenantID string, id string) (*media.MediaResponse, error)

	// List lấy danh sách media
	List(ctx context.Context, tenantID string, req *media.ListMediaRequest) ([]*media.MediaResponse, string, error)

	// Update cập nhật thông tin media
	Update(ctx context.Context, tenantID string, id string, req *media.UpdateMediaRequest) (*media.MediaResponse, error)

	// Delete xóa media
	Delete(ctx context.Context, tenantID string, id string, permanent bool) error

	// GetFile lấy nội dung file của media
	GetFile(ctx context.Context, tenantID string, id string) ([]byte, string, error)

	// ProcessFile xử lý file media (tạo các phiên bản, optimize, v.v.)
	ProcessFile(ctx context.Context, mediaEntity *models.Media) error

	// SaveFile lưu file vào storage
	SaveFile(ctx context.Context, tenantID string, file *multipart.FileHeader, mediaType models.MediaType) (*models.Media, error)
}
