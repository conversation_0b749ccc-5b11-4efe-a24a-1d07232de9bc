package service

import (
	"context"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/media/internal"
)

// localStorageRepository triển khai StorageRepository interface cho local storage
type localStorageRepository struct {
	basePath string
	logger   logger.Logger
}

// s3StorageRepository triển khai StorageRepository interface cho S3
type s3StorageRepository struct {
	bucket    string
	region    string
	accessKey string
	secretKey string
	logger    logger.Logger
}

// NewStorageRepository tạo storage repository dựa trên cấu hình
func NewStorageRepository(config internal.MediaConfig, logger logger.Logger) (internal.StorageRepository, error) {
	switch config.StorageType {
	case "s3":
		return &s3StorageRepository{
			bucket:    config.S3Bucket,
			region:    config.S3Region,
			accessKey: config.S3Access<PERSON>ey,
			secretKey: config.S3Secret<PERSON>ey,
			logger:    logger,
		}, nil
	case "local":
		fallthrough
	default:
		// Default to local storage
		basePath := "/tmp/media" // Default path, should be configurable
		if err := os.MkdirAll(basePath, 0755); err != nil {
			return nil, fmt.Errorf("failed to create storage directory: %w", err)
		}
		return &localStorageRepository{
			basePath: basePath,
			logger:   logger,
		}, nil
	}
}

// Local Storage Implementation

func (r *localStorageRepository) Upload(ctx context.Context, objectKey string, data []byte, contentType string) error {
	filePath := filepath.Join(r.basePath, objectKey)
	
	// Create directory if it doesn't exist
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// Write file
	err := ioutil.WriteFile(filePath, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write file: %w", err)
	}

	r.logger.Info("File uploaded successfully", logger.String("objectKey", objectKey), logger.String("path", filePath))
	return nil
}

func (r *localStorageRepository) Download(ctx context.Context, objectKey string) ([]byte, error) {
	filePath := filepath.Join(r.basePath, objectKey)
	
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, internal.ErrMediaNotFound
		}
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	return data, nil
}

func (r *localStorageRepository) Delete(ctx context.Context, objectKey string) error {
	filePath := filepath.Join(r.basePath, objectKey)
	
	err := os.Remove(filePath)
	if err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	r.logger.Info("File deleted successfully", logger.String("objectKey", objectKey), logger.String("path", filePath))
	return nil
}

func (r *localStorageRepository) GetURL(ctx context.Context, objectKey string) (string, error) {
	// For local storage, return a file:// URL or a web-accessible URL
	// This is a simplified implementation
	return fmt.Sprintf("/media/%s", objectKey), nil
}

// S3 Storage Implementation (placeholder)

func (r *s3StorageRepository) Upload(ctx context.Context, objectKey string, data []byte, contentType string) error {
	// TODO: Implement S3 upload using AWS SDK
	r.logger.Warn("S3 storage not implemented yet, falling back to local storage")
	
	// For now, create a local storage fallback
	localRepo := &localStorageRepository{
		basePath: "/tmp/media",
		logger:   r.logger,
	}
	return localRepo.Upload(ctx, objectKey, data, contentType)
}

func (r *s3StorageRepository) Download(ctx context.Context, objectKey string) ([]byte, error) {
	// TODO: Implement S3 download using AWS SDK
	r.logger.Warn("S3 storage not implemented yet, falling back to local storage")
	
	// For now, create a local storage fallback
	localRepo := &localStorageRepository{
		basePath: "/tmp/media",
		logger:   r.logger,
	}
	return localRepo.Download(ctx, objectKey)
}

func (r *s3StorageRepository) Delete(ctx context.Context, objectKey string) error {
	// TODO: Implement S3 delete using AWS SDK
	r.logger.Warn("S3 storage not implemented yet, falling back to local storage")
	
	// For now, create a local storage fallback
	localRepo := &localStorageRepository{
		basePath: "/tmp/media",
		logger:   r.logger,
	}
	return localRepo.Delete(ctx, objectKey)
}

func (r *s3StorageRepository) GetURL(ctx context.Context, objectKey string) (string, error) {
	// TODO: Implement S3 URL generation using AWS SDK
	r.logger.Warn("S3 storage not implemented yet, falling back to local storage")
	
	// For now, create a local storage fallback
	localRepo := &localStorageRepository{
		basePath: "/tmp/media",
		logger:   r.logger,
	}
	return localRepo.GetURL(ctx, objectKey)
}
