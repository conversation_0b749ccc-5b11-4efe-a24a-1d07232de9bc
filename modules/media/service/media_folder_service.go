package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/media/dto"
	"wnapi/modules/media/internal"
)

// mediaFolderService triển khai MediaFolderService interface
type mediaFolderService struct {
	repo   internal.Repository
	config internal.MediaConfig
	logger logger.Logger
}

// NewMediaFolderService tạo một media folder service mới
func NewMediaFolderService(repo internal.Repository, config internal.MediaConfig, logger logger.Logger) internal.MediaFolderService {
	return &mediaFolderService{
		repo:   repo,
		config: config,
		logger: logger,
	}
}

// Create tạo folder mới
func (s *mediaFolderService) Create(ctx context.Context, tenantID uint, userID uint, req dto.CreateFolderRequest) (*dto.FolderResponse, error) {
	// Generate slug from name
	slug := s.generateSlug(req.Name)

	// Check if slug already exists
	existingFolder, err := s.repo.GetFolderBySlug(ctx, tenantID, slug)
	if err != nil && err != internal.ErrFolderNotFound {
		return nil, err
	}
	if existingFolder != nil {
		// Generate unique slug
		slug = s.generateUniqueSlug(ctx, tenantID, slug)
	}

	// Validate parent folder if specified
	if req.ParentID != nil {
		_, err := s.repo.GetFolderByID(ctx, tenantID, *req.ParentID)
		if err != nil {
			if err == internal.ErrFolderNotFound {
				return nil, internal.ErrInvalidFolder
			}
			return nil, err
		}
	}

	// Create folder entity
	folder := &internal.MediaFolder{
		TenantID:    tenantID,
		Name:        req.Name,
		Slug:        slug,
		Description: req.Description,
		ParentID:    req.ParentID,
		IsPublic:    req.IsPublic,
		CreatedBy:   userID,
	}

	// Save to database
	err = s.repo.CreateFolder(ctx, tenantID, folder)
	if err != nil {
		s.logger.Error("Failed to create folder", logger.String("error", err.Error()))
		return nil, err
	}

	return s.toFolderResponse(folder), nil
}

// GetByID lấy thông tin folder theo ID
func (s *mediaFolderService) GetByID(ctx context.Context, tenantID uint, id uint) (*dto.FolderResponse, error) {
	folder, err := s.repo.GetFolderByID(ctx, tenantID, id)
	if err != nil {
		return nil, err
	}

	return s.toFolderResponse(folder), nil
}

// List lấy danh sách folder
func (s *mediaFolderService) List(ctx context.Context, tenantID uint, req dto.ListFolderRequest) (*dto.ListFolderResponse, error) {
	params := internal.ListFolderParams{
		ParentID: req.ParentID,
		IsPublic: req.IsPublic,
		Search:   req.Search,
		Cursor:   req.Cursor,
		Limit:    req.Limit,
	}

	folderList, nextCursor, err := s.repo.ListFolders(ctx, tenantID, params)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	responses := make([]*dto.FolderResponse, len(folderList))
	for i, folder := range folderList {
		responses[i] = s.toFolderResponse(folder)
	}

	return &dto.ListFolderResponse{
		Data:       responses,
		NextCursor: nextCursor,
		HasMore:    nextCursor != "",
	}, nil
}

// Update cập nhật thông tin folder
func (s *mediaFolderService) Update(ctx context.Context, tenantID uint, id uint, req dto.UpdateFolderRequest) (*dto.FolderResponse, error) {
	// Get existing folder
	folder, err := s.repo.GetFolderByID(ctx, tenantID, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Name != nil {
		folder.Name = *req.Name
		// Regenerate slug if name changed
		newSlug := s.generateSlug(*req.Name)
		if newSlug != folder.Slug {
			// Check if new slug already exists
			existingFolder, err := s.repo.GetFolderBySlug(ctx, tenantID, newSlug)
			if err != nil && err != internal.ErrFolderNotFound {
				return nil, err
			}
			if existingFolder != nil && existingFolder.ID != folder.ID {
				// Generate unique slug
				newSlug = s.generateUniqueSlug(ctx, tenantID, newSlug)
			}
			folder.Slug = newSlug
		}
	}

	if req.Description != nil {
		folder.Description = *req.Description
	}

	if req.ParentID != nil {
		// Validate parent folder
		if *req.ParentID != 0 {
			_, err := s.repo.GetFolderByID(ctx, tenantID, *req.ParentID)
			if err != nil {
				if err == internal.ErrFolderNotFound {
					return nil, internal.ErrInvalidFolder
				}
				return nil, err
			}
		}
		folder.ParentID = req.ParentID
	}

	if req.IsPublic != nil {
		folder.IsPublic = *req.IsPublic
	}

	// Save changes
	err = s.repo.UpdateFolder(ctx, tenantID, folder)
	if err != nil {
		return nil, err
	}

	return s.toFolderResponse(folder), nil
}

// Delete xóa folder
func (s *mediaFolderService) Delete(ctx context.Context, tenantID uint, id uint) error {
	// Check if folder exists
	_, err := s.repo.GetFolderByID(ctx, tenantID, id)
	if err != nil {
		return err
	}

	// TODO: Check if folder has children or media files
	// For now, just delete the folder

	return s.repo.DeleteFolder(ctx, tenantID, id)
}

// Helper methods

func (s *mediaFolderService) generateSlug(name string) string {
	// Convert to lowercase
	slug := strings.ToLower(name)

	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	// Limit length
	if len(slug) > 50 {
		slug = slug[:50]
	}

	return slug
}

func (s *mediaFolderService) generateUniqueSlug(ctx context.Context, tenantID uint, baseSlug string) string {
	counter := 1
	for {
		newSlug := fmt.Sprintf("%s-%d", baseSlug, counter)
		_, err := s.repo.GetFolderBySlug(ctx, tenantID, newSlug)
		if err == internal.ErrFolderNotFound {
			return newSlug
		}
		counter++
		if counter > 100 { // Prevent infinite loop
			return fmt.Sprintf("%s-%d", baseSlug, time.Now().Unix())
		}
	}
}

func (s *mediaFolderService) toFolderResponse(folder *internal.MediaFolder) *dto.FolderResponse {
	return &dto.FolderResponse{
		ID:          folder.ID,
		TenantID:    folder.TenantID,
		Name:        folder.Name,
		Slug:        folder.Slug,
		Description: folder.Description,
		ParentID:    folder.ParentID,
		IsPublic:    folder.IsPublic,
		CreatedBy:   folder.CreatedBy,
		CreatedAt:   folder.CreatedAt.Format(time.RFC3339),
		UpdatedAt:   folder.UpdatedAt.Format(time.RFC3339),
	}
}
