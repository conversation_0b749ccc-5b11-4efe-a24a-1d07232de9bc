package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"wnapi/internal/pkg/tracing"
	media "wnapi/modules/media"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// Khởi tạo module
	if err := media.Init(); err != nil {
		log.Fatalf("Failed to initialize media module: %v", err)
	}

	// Khởi tạo router
	router := gin.Default()

	router.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"*"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	// Đăng ký middleware
	media.RegisterMiddlewares(router)

	// Đăng ký routes
	media.RegisterHTTPRoutes(router)

	// Khởi động server HTTP
	go func() {
		if err := router.Run(":9046"); err != nil {
			log.Fatalf("Failed to run server: %v", err)
		}
	}()

	log.Println("Media module server running on :9046")

	// Xử lý tín hiệu tắt server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Shutdown tracing (sử dụng internal/pkg/tracing)
	if provider := tracing.GetProvider(); provider != nil {
		provider.Shutdown()
	}
}
