package repository

import "errors"

// <PERSON><PERSON><PERSON> định nghĩa lỗi cho repository
var (
	// ErrMediaNotFound lỗi khi không tìm thấy media
	ErrMediaNotFound = errors.New("media not found")

	// ErrInvalidObjectKey lỗi khi object key không hợp lệ
	ErrInvalidObjectKey = errors.New("invalid object key")

	// ErrDatabaseOperation lỗi khi thao tác với database
	ErrDatabaseOperation = errors.New("database operation failed")

	// ErrStorageOperation lỗi khi thao tác với storage
	ErrStorageOperation = errors.New("storage operation failed")

	// ErrInvalidMedia lỗi khi thông tin media không hợp lệ
	ErrInvalidMedia = errors.New("invalid media information")

	// ErrFileUpload lỗi khi upload file
	ErrFileUpload = errors.New("file upload failed")

	// ErrFileDownload lỗi khi download file
	ErrFileDownload = errors.New("file download failed")

	// ErrInvalidFileType lỗi khi loại file không hợp lệ
	ErrInvalidFileType = errors.New("invalid file type")

	// ErrFileTooLarge lỗi khi file quá lớn
	ErrFileTooLarge = errors.New("file is too large")
)
