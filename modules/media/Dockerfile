FROM golang:1.23-alpine AS builder

# Create the working directory
WORKDIR /app

# Copy go mod and sum files to download dependencies
COPY /modules/media/go.mod /modules/media/go.sum ./

# Copy the pkg module for shared code
COPY /pkg /pkg

# Download dependencies
RUN go mod download

# Copy the source code
COPY /modules/media ./

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o /media-api ./cmd/main.go

# Use a minimal alpine image for the final container
FROM alpine:3.17

# Add necessary packages
RUN apk --no-cache add ca-certificates tzdata

# Set timezone
ENV TZ=UTC

# Create a non-root user and group
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Create the storage directory
RUN mkdir -p /storage/media && chown -R appuser:appgroup /storage/media

# Copy the binary from builder
COPY --from=builder /media-api /app/media-api

# Set working directory
WORKDIR /app

# Use the non-root user
USER appuser

# Expose the application port
EXPOSE 9046

# Command to run the application
CMD ["/app/media-api"]

# For development with air hot reload
FROM golang:1.23-alpine AS development

WORKDIR /app/modules/media

# Install air for hot reloading
RUN go install github.com/air-verse/air@latest

# Install required packages
RUN apk --no-cache add ca-certificates tzdata git

# Create the storage directory
RUN mkdir -p /storage/media

# Entry point for development
CMD ["air", "-c", ".air.toml"] 