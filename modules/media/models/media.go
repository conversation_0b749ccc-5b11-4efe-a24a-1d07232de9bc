package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// MediaType định nghĩa loại media
type MediaType string

// MediaStatus định nghĩa trạng thái của media
type MediaStatus string

const (
	// MediaTypeImage loại media hình ảnh
	MediaTypeImage MediaType = "image"
	// MediaTypeVideo loại media video
	MediaTypeVideo MediaType = "video"
	// MediaTypeAudio loại media âm thanh
	MediaTypeAudio MediaType = "audio"
	// MediaTypeDocument loại media tài liệu
	MediaTypeDocument MediaType = "document"
	// MediaTypeOther loại media khác
	MediaTypeOther MediaType = "other"

	// MediaStatusPending trạng thái chờ xử lý
	MediaStatusPending MediaStatus = "pending"
	// MediaStatusProcessing trạng thái đang xử lý
	MediaStatusProcessing MediaStatus = "processing"
	// MediaStatusReady trạng thái sẵn sàng sử dụng
	MediaStatusReady MediaStatus = "ready"
	// MediaStatusFailed trạng thái xử lý thất bại
	MediaStatusFailed MediaStatus = "failed"
)

// Media đại diện cho bản ghi trong bảng media
type Media struct {
	ID               string      `json:"id" db:"id" gorm:"primaryKey"`
	TenantID         string      `json:"tenant_id" db:"tenant_id"`
	MediaType        MediaType   `json:"media_type" db:"media_type"`
	Filename         string      `json:"filename" db:"filename"`
	OriginalFilename string      `json:"original_filename" db:"original_filename"`
	ObjectKey        string      `json:"object_key" db:"object_key"`
	ContentType      string      `json:"content_type" db:"content_type"`
	Size             int64       `json:"size" db:"size"`
	Status           MediaStatus `json:"status" db:"status"`
	PublicURL        string      `json:"public_url,omitempty" db:"public_url"`
	Description      string      `json:"description,omitempty" db:"description"`
	UploadedBy       string      `json:"uploaded_by,omitempty" db:"uploaded_by"`
	Checksum         string      `json:"checksum,omitempty" db:"checksum"`
	IsPublic         bool        `json:"is_public" db:"is_public"`
	Metadata         Metadata    `json:"metadata,omitempty" db:"metadata"`
	Properties       Properties  `json:"properties,omitempty" db:"properties"`
	SchemaVersion    int         `json:"schema_version" db:"schema_version"`
	CreatedAt        time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time   `json:"updated_at" db:"updated_at"`
	DeletedAt        *time.Time  `json:"deleted_at,omitempty" db:"deleted_at"`
}

// Metadata là một cấu trúc lưu trữ metadata của media trong JSON
type Metadata map[string]interface{}

// Value chuyển đổi Metadata sang JSON để lưu vào cơ sở dữ liệu
func (m Metadata) Value() (driver.Value, error) {
	if m == nil {
		return nil, nil
	}
	return json.Marshal(m)
}

// Scan chuyển đổi giá trị từ cơ sở dữ liệu sang Metadata
func (m *Metadata) Scan(value interface{}) error {
	if value == nil {
		*m = make(Metadata)
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return errors.New("invalid type for Metadata")
	}

	return json.Unmarshal(data, m)
}

// Properties là một cấu trúc lưu trữ thuộc tính bổ sung của media trong JSON
type Properties map[string]interface{}

// Value chuyển đổi Properties sang JSON để lưu vào cơ sở dữ liệu
func (p Properties) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return json.Marshal(p)
}

// Scan chuyển đổi giá trị từ cơ sở dữ liệu sang Properties
func (p *Properties) Scan(value interface{}) error {
	if value == nil {
		*p = make(Properties)
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return errors.New("invalid type for Properties")
	}

	return json.Unmarshal(data, p)
}

// MediaVersion đại diện cho phiên bản của media
type MediaVersion struct {
	ID          string     `json:"id" db:"id" gorm:"primaryKey"`
	MediaID     string     `json:"media_id" db:"media_id"`
	VersionType string     `json:"version_type" db:"version_type"`
	Width       *int       `json:"width,omitempty" db:"width"`
	Height      *int       `json:"height,omitempty" db:"height"`
	ObjectKey   string     `json:"object_key" db:"object_key"`
	ContentType string     `json:"content_type" db:"content_type"`
	Size        int64      `json:"size" db:"size"`
	Quality     *int       `json:"quality,omitempty" db:"quality"`
	PublicURL   string     `json:"public_url,omitempty" db:"public_url"`
	Properties  Properties `json:"properties,omitempty" db:"properties"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
}

// MediaTag đại diện cho tag của media
type MediaTag struct {
	MediaID string `json:"media_id" db:"media_id"`
	Tag     string `json:"tag" db:"tag"`
}
