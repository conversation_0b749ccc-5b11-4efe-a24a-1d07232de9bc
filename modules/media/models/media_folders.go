package models

import (
	"time"
)

// MediaFolder đại diện cho bản ghi trong bảng media_folders
type MediaFolder struct {
	FolderID    int        `json:"folder_id" db:"folder_id" gorm:"primaryKey"`
	TenantID    int        `json:"tenant_id" db:"tenant_id"`
	ParentID    *int       `json:"parent_id,omitempty" db:"parent_id"`
	Name        string     `json:"name" db:"name"`
	Slug        string     `json:"slug" db:"slug"`
	Description string     `json:"description,omitempty" db:"description"`
	IsPublic    bool       `json:"is_public" db:"is_public"`
	CreatedAt   time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at" db:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" db:"deleted_at"`
	CreatedBy   *int       `json:"created_by,omitempty" db:"created_by"`
	UpdatedBy   *int       `json:"updated_by,omitempty" db:"updated_by"`
}
