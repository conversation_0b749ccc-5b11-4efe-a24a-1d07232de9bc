package internal

import (
	"context"
	"mime/multipart"
	"net/http"
	"time"
	"wnapi/modules/media/dto"
)

// MediaConfig chứa cấu hình media service
type MediaConfig struct {
	StorageType      string   `yaml:"storage_type" env:"MEDIA_STORAGE_TYPE" envDefault:"s3"`
	S3Bucket         string   `yaml:"s3_bucket" env:"MEDIA_S3_BUCKET"`
	S3Region         string   `yaml:"s3_region" env:"MEDIA_S3_REGION"`
	S3AccessKey      string   `yaml:"s3_access_key" env:"MEDIA_S3_ACCESS_KEY"`
	S3SecretKey      string   `yaml:"s3_secret_key" env:"MEDIA_S3_SECRET_KEY"`
	MaxFileSize      int64    `yaml:"max_file_size" env:"MEDIA_MAX_FILE_SIZE" envDefault:"104857600"` // 100MB
	AllowedTypes     []string `yaml:"allowed_types" env:"MEDIA_ALLOWED_TYPES"`
	ImageQuality     int      `yaml:"image_quality" env:"MEDIA_IMAGE_QUALITY" envDefault:"85"`
	EnableProcessing bool     `yaml:"enable_processing" env:"MEDIA_ENABLE_PROCESSING" envDefault:"true"`
	Message          string   `env:"MESSAGE" envDefault:"Xin chào từ module Media!"`
}

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrMediaNotFound là lỗi khi không tìm thấy media
	ErrMediaNotFound ServiceError = "media_not_found"
	// ErrInvalidFileType là lỗi khi loại file không được hỗ trợ
	ErrInvalidFileType ServiceError = "invalid_file_type"
	// ErrFileTooLarge là lỗi khi file quá lớn
	ErrFileTooLarge ServiceError = "file_too_large"
	// ErrUploadFailed là lỗi khi upload thất bại
	ErrUploadFailed ServiceError = "upload_failed"
	// ErrProcessingFailed là lỗi khi xử lý file thất bại
	ErrProcessingFailed ServiceError = "processing_failed"
	// ErrInvalidTenant là lỗi khi tenant không hợp lệ
	ErrInvalidTenant ServiceError = "invalid_tenant"
	// ErrUnauthorized là lỗi khi không có quyền truy cập
	ErrUnauthorized ServiceError = "unauthorized"
	// ErrFolderNotFound là lỗi khi không tìm thấy folder
	ErrFolderNotFound ServiceError = "folder_not_found"
	// ErrInvalidFolder là lỗi khi folder không hợp lệ
	ErrInvalidFolder ServiceError = "invalid_folder"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// GetErrorResponse trả về ErrorResponse tương ứng với ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if response, exists := errorResponseMap[serviceErr]; exists {
			return response
		}
	}
	// Lỗi mặc định
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Đã xảy ra lỗi không xác định",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// errorResponseMap ánh xạ ServiceError với ErrorResponse
var errorResponseMap = map[ServiceError]ErrorResponse{
	ErrMediaNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy media",
		ErrorCode:  "MEDIA_NOT_FOUND",
	},
	ErrInvalidFileType: {
		StatusCode: http.StatusBadRequest,
		Message:    "Loại file không được hỗ trợ",
		ErrorCode:  "INVALID_FILE_TYPE",
	},
	ErrFileTooLarge: {
		StatusCode: http.StatusBadRequest,
		Message:    "File quá lớn",
		ErrorCode:  "FILE_TOO_LARGE",
	},
	ErrUploadFailed: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Upload file thất bại",
		ErrorCode:  "UPLOAD_FAILED",
	},
	ErrProcessingFailed: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Xử lý file thất bại",
		ErrorCode:  "PROCESSING_FAILED",
	},
	ErrInvalidTenant: {
		StatusCode: http.StatusBadRequest,
		Message:    "Tenant không hợp lệ",
		ErrorCode:  "INVALID_TENANT",
	},
	ErrUnauthorized: {
		StatusCode: http.StatusUnauthorized,
		Message:    "Không có quyền truy cập",
		ErrorCode:  "UNAUTHORIZED",
	},
	ErrFolderNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy thư mục",
		ErrorCode:  "FOLDER_NOT_FOUND",
	},
	ErrInvalidFolder: {
		StatusCode: http.StatusBadRequest,
		Message:    "Thư mục không hợp lệ",
		ErrorCode:  "INVALID_FOLDER",
	},
}

func (e ServiceError) Error() string {
	return string(e)
}

// MediaType and MediaStatus are now defined in dto package to avoid import cycles
// Use dto.MediaType and dto.MediaStatus instead

// Media đại diện cho media entity
type Media struct {
	ID               uint            `json:"id" gorm:"primaryKey;type:int unsigned"`
	TenantID         uint            `json:"tenant_id" gorm:"type:int unsigned;not null;index"`
	MediaType        dto.MediaType   `json:"media_type" gorm:"type:enum('image','video','audio','document','other');not null"`
	Filename         string          `json:"filename" gorm:"type:varchar(255);not null"`
	OriginalFilename string          `json:"original_filename" gorm:"type:varchar(255);not null"`
	ObjectKey        string          `json:"object_key" gorm:"type:varchar(512);not null"`
	ContentType      string          `json:"content_type" gorm:"type:varchar(100);not null"`
	Size             int64           `json:"size" gorm:"type:bigint unsigned;not null"`
	Status           dto.MediaStatus `json:"status" gorm:"type:enum('pending','processing','ready','failed');not null;default:pending"`
	PublicURL        string          `json:"public_url,omitempty" gorm:"type:varchar(1024)"`
	Description      string          `json:"description,omitempty" gorm:"type:text"`
	UploadedBy       uint            `json:"uploaded_by,omitempty" gorm:"type:int unsigned"`
	Checksum         string          `json:"checksum,omitempty" gorm:"type:varchar(64)"`
	IsPublic         bool            `json:"is_public" gorm:"default:false"`
	FolderID         *uint           `json:"folder_id,omitempty" gorm:"type:int unsigned"`
	SchemaVersion    int             `json:"schema_version" gorm:"default:1"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`
	DeletedAt        *time.Time      `json:"deleted_at,omitempty" gorm:"index"`
}

// MediaFolder đại diện cho media folder entity
type MediaFolder struct {
	ID          uint       `json:"id" gorm:"primaryKey;type:int unsigned"`
	TenantID    uint       `json:"tenant_id" gorm:"type:int unsigned;not null;index"`
	Name        string     `json:"name" gorm:"type:varchar(255);not null"`
	Slug        string     `json:"slug" gorm:"type:varchar(255);not null;index"`
	Description string     `json:"description,omitempty" gorm:"type:text"`
	ParentID    *uint      `json:"parent_id,omitempty" gorm:"type:int unsigned"`
	IsPublic    bool       `json:"is_public" gorm:"default:false"`
	CreatedBy   uint       `json:"created_by" gorm:"type:int unsigned;not null"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   *time.Time `json:"deleted_at,omitempty" gorm:"index"`
}

// MediaService định nghĩa interface cho media service
type MediaService interface {
	Upload(ctx context.Context, tenantID uint, userID uint, file *multipart.FileHeader, req dto.UploadMediaRequest) (*dto.UploadMediaResponse, error)
	GetByID(ctx context.Context, tenantID uint, id uint) (*dto.MediaResponse, error)
	List(ctx context.Context, tenantID uint, req dto.ListMediaRequest) (*dto.ListMediaResponse, error)
	Update(ctx context.Context, tenantID uint, id uint, req dto.UpdateMediaRequest) (*dto.MediaResponse, error)
	Delete(ctx context.Context, tenantID uint, id uint, permanent bool) error
	GetFile(ctx context.Context, tenantID uint, id uint) ([]byte, string, error)
}

// MediaFolderService định nghĩa interface cho media folder service
type MediaFolderService interface {
	Create(ctx context.Context, tenantID uint, userID uint, req dto.CreateFolderRequest) (*dto.FolderResponse, error)
	GetByID(ctx context.Context, tenantID uint, id uint) (*dto.FolderResponse, error)
	List(ctx context.Context, tenantID uint, req dto.ListFolderRequest) (*dto.ListFolderResponse, error)
	Update(ctx context.Context, tenantID uint, id uint, req dto.UpdateFolderRequest) (*dto.FolderResponse, error)
	Delete(ctx context.Context, tenantID uint, id uint) error
}

// Repository định nghĩa interface cho media repository
type Repository interface {
	// Media operations
	CreateMedia(ctx context.Context, tenantID uint, media *Media) error
	GetMediaByID(ctx context.Context, tenantID uint, id uint) (*Media, error)
	UpdateMedia(ctx context.Context, tenantID uint, media *Media) error
	DeleteMedia(ctx context.Context, tenantID uint, id uint) error
	ListMedia(ctx context.Context, tenantID uint, params ListMediaParams) ([]*Media, string, error)

	// Folder operations
	CreateFolder(ctx context.Context, tenantID uint, folder *MediaFolder) error
	GetFolderByID(ctx context.Context, tenantID uint, id uint) (*MediaFolder, error)
	GetFolderBySlug(ctx context.Context, tenantID uint, slug string) (*MediaFolder, error)
	UpdateFolder(ctx context.Context, tenantID uint, folder *MediaFolder) error
	DeleteFolder(ctx context.Context, tenantID uint, id uint) error
	ListFolders(ctx context.Context, tenantID uint, params ListFolderParams) ([]*MediaFolder, string, error)
}

// StorageRepository định nghĩa interface cho storage operations
type StorageRepository interface {
	Upload(ctx context.Context, objectKey string, data []byte, contentType string) error
	Download(ctx context.Context, objectKey string) ([]byte, error)
	Delete(ctx context.Context, objectKey string) error
	GetURL(ctx context.Context, objectKey string) (string, error)
}

// ListMediaParams chứa các tham số để lọc và phân trang danh sách media
type ListMediaParams struct {
	MediaType *dto.MediaType
	Status    *dto.MediaStatus
	IsPublic  *bool
	FolderID  *uint
	Search    string
	Cursor    string
	Limit     int
}

// ListFolderParams chứa các tham số để lọc và phân trang danh sách folder
type ListFolderParams struct {
	ParentID *uint
	IsPublic *bool
	Search   string
	Cursor   string
	Limit    int
}
