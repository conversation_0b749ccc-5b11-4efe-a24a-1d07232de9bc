package configs

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/spf13/viper"
)

// Config đại diện cho cấu hình module media
type Config struct {
	Server     ServerConfig    `yaml:"server" mapstructure:"server"`
	DB         DBConfig        `yaml:"db" mapstructure:"db"`
	Storage    StorageConfig   `yaml:"storage" mapstructure:"storage"`
	Upload     UploadConfig    `yaml:"upload" mapstructure:"upload"`
	JWT        JWTConfig       `yaml:"jwt" mapstructure:"jwt"`
	Tracing    *TracingConfig  `yaml:"tracing" mapstructure:"tracing"`
	Processors ProcessorConfig `yaml:"processors" mapstructure:"processors"`
}

// ServerConfig đại diện cho cấu hình server của module media
type ServerConfig struct {
	Host string `yaml:"host" mapstructure:"host"`
	Port int    `yaml:"port" mapstructure:"port"`
}

// DBConfig đại diện cho cấu hình database của module media
type DBConfig struct {
	Host     string `yaml:"host" mapstructure:"host"`
	Port     int    `yaml:"port" mapstructure:"port"`
	Username string `yaml:"username" mapstructure:"username"`
	Password string `yaml:"password" mapstructure:"password"`
	Database string `yaml:"database" mapstructure:"database"`
}

// JWTConfig đại diện cho cấu hình JWT
type JWTConfig struct {
	AccessSecret         string        `yaml:"access_signing_key" mapstructure:"access_signing_key"`
	RefreshSecret        string        `yaml:"refresh_signing_key" mapstructure:"refresh_signing_key"`
	AccessExpiryMinutes  string        `yaml:"access_token_expiration" mapstructure:"access_token_expiration"`
	RefreshExpiryHours   string        `yaml:"refresh_token_expiration" mapstructure:"refresh_token_expiration"`
	AccessTokenDuration  time.Duration `yaml:"-" mapstructure:"-"` // Thời gian hết hạn đã chuyển đổi
	RefreshTokenDuration time.Duration `yaml:"-" mapstructure:"-"` // Thời gian hết hạn đã chuyển đổi
	Issuer               string        `yaml:"issuer" mapstructure:"issuer"`
}

// StorageConfig đại diện cho cấu hình storage
type StorageConfig struct {
	Type  string       `yaml:"type" mapstructure:"type"` // "local" hoặc "minio"
	Local LocalStorage `yaml:"local" mapstructure:"local"`
	Minio MinioStorage `yaml:"minio" mapstructure:"minio"`
}

// LocalStorage đại diện cho cấu hình lưu trữ cục bộ
type LocalStorage struct {
	BasePath string `yaml:"base_path" mapstructure:"base_path"`
	BaseURL  string `yaml:"base_url" mapstructure:"base_url"`
}

// MinioStorage đại diện cho cấu hình Minio
type MinioStorage struct {
	Endpoint        string        `yaml:"endpoint" mapstructure:"endpoint"`
	AccessKeyID     string        `yaml:"access_key_id" mapstructure:"access_key_id"`
	SecretAccessKey string        `yaml:"secret_access_key" mapstructure:"secret_access_key"`
	UseSSL          bool          `yaml:"use_ssl" mapstructure:"use_ssl"`
	BucketName      string        `yaml:"bucket_name" mapstructure:"bucket_name"`
	Region          string        `yaml:"region" mapstructure:"region"`
	PresignedExpiry time.Duration `yaml:"presigned_expiry" mapstructure:"presigned_expiry"`
}

// UploadConfig đại diện cho cấu hình upload
type UploadConfig struct {
	MaxFileSize  int64               `yaml:"max_file_size" mapstructure:"max_file_size"`
	AllowedTypes map[string][]string `yaml:"allowed_types" mapstructure:"allowed_types"`
}

// ProcessorConfig đại diện cho cấu hình xử lý media
type ProcessorConfig struct {
	Image ImageProcessorConfig `yaml:"image" mapstructure:"image"`
	Video VideoProcessorConfig `yaml:"video" mapstructure:"video"`
}

// ImageProcessorConfig đại diện cho cấu hình xử lý hình ảnh
type ImageProcessorConfig struct {
	Enabled   bool              `yaml:"enabled" mapstructure:"enabled"`
	Thumbnail ImageResizeConfig `yaml:"thumbnail" mapstructure:"thumbnail"`
	Medium    ImageResizeConfig `yaml:"medium" mapstructure:"medium"`
	Large     ImageResizeConfig `yaml:"large" mapstructure:"large"`
}

// ImageResizeConfig đại diện cho cấu hình kích thước hình ảnh
type ImageResizeConfig struct {
	Width  int `yaml:"width" mapstructure:"width"`
	Height int `yaml:"height" mapstructure:"height"`
}

// VideoProcessorConfig đại diện cho cấu hình xử lý video
type VideoProcessorConfig struct {
	Enabled     bool                   `yaml:"enabled" mapstructure:"enabled"`
	Thumbnail   VideoThumbnailConfig   `yaml:"thumbnail" mapstructure:"thumbnail"`
	Transcoding VideoTranscodingConfig `yaml:"transcoding" mapstructure:"transcoding"`
}

// VideoThumbnailConfig đại diện cho cấu hình thumbnail video
type VideoThumbnailConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	Time    string `yaml:"time" mapstructure:"time"`
}

// VideoTranscodingConfig đại diện cho cấu hình chuyển đổi video
type VideoTranscodingConfig struct {
	Enabled bool `yaml:"enabled" mapstructure:"enabled"`
}

// TracingConfig đại diện cho cấu hình tracing
type TracingConfig struct {
	Enabled      bool   `yaml:"enabled" mapstructure:"enabled"`
	ServiceName  string `yaml:"service_name" mapstructure:"service_name"`
	ExporterType string `yaml:"exporter_type" mapstructure:"exporter_type"`
	Signoz       struct {
		Endpoint string `yaml:"endpoint" mapstructure:"endpoint"`
	} `yaml:"signoz" mapstructure:"signoz"`
	Jaeger struct {
		Host string `yaml:"host" mapstructure:"host"`
		Port string `yaml:"port" mapstructure:"port"`
	} `yaml:"jaeger" mapstructure:"jaeger"`
	SampleRatio float64 `yaml:"sample_ratio" mapstructure:"sample_ratio"`
}

// LoadConfig loads the configuration from config files
func LoadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("../configs")
	viper.AddConfigPath("../../configs")

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Chuyển đổi chuỗi thời gian thành time.Duration
	accessDuration, err := time.ParseDuration(config.JWT.AccessExpiryMinutes)
	if err == nil {
		config.JWT.AccessTokenDuration = accessDuration
	}

	refreshDuration, err := time.ParseDuration(config.JWT.RefreshExpiryHours)
	if err == nil {
		config.JWT.RefreshTokenDuration = refreshDuration
	}

	// Override with environment variables if set
	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		config.DB.Host = dbHost
	}

	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		if port, err := strconv.Atoi(dbPort); err == nil {
			config.DB.Port = port
		}
	}

	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		config.DB.Username = dbUser
	}

	if dbPass := os.Getenv("DB_PASSWORD"); dbPass != "" {
		config.DB.Password = dbPass
	}

	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		config.DB.Database = dbName
	}

	if httpPort := os.Getenv("HTTP_PORT"); httpPort != "" {
		if port, err := strconv.Atoi(httpPort); err == nil {
			config.Server.Port = port
		}
	}

	// JWT env vars
	if jwtAccessSecret := os.Getenv("JWT_ACCESS_SIGNING_KEY"); jwtAccessSecret != "" {
		config.JWT.AccessSecret = jwtAccessSecret
	}

	if jwtRefreshSecret := os.Getenv("JWT_REFRESH_SIGNING_KEY"); jwtRefreshSecret != "" {
		config.JWT.RefreshSecret = jwtRefreshSecret
	}

	// Ưu tiên lấy issuer từ AUTH_JWT_ISSUER nếu có
	if jwtIssuer := os.Getenv("AUTH_JWT_ISSUER"); jwtIssuer != "" {
		config.JWT.Issuer = jwtIssuer
	} else if jwtIssuer := os.Getenv("JWT_ISSUER"); jwtIssuer != "" {
		config.JWT.Issuer = jwtIssuer
	}

	if config.JWT.Issuer == "" {
		return nil, fmt.Errorf("Thiếu biến môi trường AUTH_JWT_ISSUER hoặc JWT_ISSUER cho issuer")
	}

	if jwtAccessExpiry := os.Getenv("JWT_ACCESS_TOKEN_EXPIRATION"); jwtAccessExpiry != "" {
		config.JWT.AccessExpiryMinutes = jwtAccessExpiry
	}

	if jwtRefreshExpiry := os.Getenv("JWT_REFRESH_TOKEN_EXPIRATION"); jwtRefreshExpiry != "" {
		config.JWT.RefreshExpiryHours = jwtRefreshExpiry
	}

	// Storage env vars
	if storageType := os.Getenv("STORAGE_TYPE"); storageType != "" {
		config.Storage.Type = storageType
	}

	// Minio env vars
	if minioEndpoint := os.Getenv("MINIO_ENDPOINT"); minioEndpoint != "" {
		config.Storage.Minio.Endpoint = minioEndpoint
	}

	if accessKey := os.Getenv("MINIO_ACCESS_KEY"); accessKey != "" {
		config.Storage.Minio.AccessKeyID = accessKey
	}

	if secretKey := os.Getenv("MINIO_SECRET_KEY"); secretKey != "" {
		config.Storage.Minio.SecretAccessKey = secretKey
	}

	if bucketName := os.Getenv("MINIO_BUCKET"); bucketName != "" {
		config.Storage.Minio.BucketName = bucketName
	}

	// Local storage env vars
	if basePath := os.Getenv("STORAGE_LOCAL_PATH"); basePath != "" {
		config.Storage.Local.BasePath = basePath
	}

	if baseURL := os.Getenv("STORAGE_LOCAL_URL"); baseURL != "" {
		config.Storage.Local.BaseURL = baseURL
	}

	// Initialize tracing config if not set
	if config.Tracing == nil {
		config.Tracing = &TracingConfig{
			Enabled:      false,
			ServiceName:  "media-service",
			ExporterType: "signoz",
			SampleRatio:  1.0,
		}
	}

	// Handle tracing configuration from environment variables
	if tracingEnabled := os.Getenv("TRACING_ENABLED"); tracingEnabled != "" {
		config.Tracing.Enabled = tracingEnabled == "true"
	}

	if serviceName := os.Getenv("TRACING_SERVICE_NAME"); serviceName != "" {
		config.Tracing.ServiceName = serviceName
	}

	if exporterType := os.Getenv("TRACING_EXPORTER_TYPE"); exporterType != "" {
		config.Tracing.ExporterType = exporterType
	}

	if sampleRatio := os.Getenv("TRACING_SAMPLE_RATIO"); sampleRatio != "" {
		if ratio, err := strconv.ParseFloat(sampleRatio, 64); err == nil {
			config.Tracing.SampleRatio = ratio
		}
	}

	if signozEndpoint := os.Getenv("SIGNOZ_ENDPOINT"); signozEndpoint != "" {
		config.Tracing.Signoz.Endpoint = signozEndpoint
	}

	// Handle Jaeger agent host and port
	jaegerHost := os.Getenv("JAEGER_AGENT_HOST")
	jaegerPort := os.Getenv("JAEGER_AGENT_PORT")
	if jaegerHost != "" {
		config.Tracing.Jaeger.Host = jaegerHost
	}
	if jaegerPort != "" {
		config.Tracing.Jaeger.Port = jaegerPort
	}

	// Enable tracing if Jaeger or SigNoz is configured
	if (jaegerHost != "" && jaegerPort != "") || config.Tracing.Signoz.Endpoint != "" {
		config.Tracing.Enabled = true
	}

	// Kiểm tra các trường cấu hình bắt buộc
	if err := validateConfig(&config); err != nil {
		return nil, err
	}

	// Print config in Docker environment
	if os.Getenv("DOCKER_ENV") == "true" {
		PrintConfig(&config)
	}

	return &config, nil
}

// validateConfig kiểm tra các trường cấu hình bắt buộc
func validateConfig(config *Config) error {
	// Kiểm tra cấu hình Server
	if config.Server.Host == "" {
		return fmt.Errorf("server.host không được cấu hình")
	}
	if config.Server.Port == 0 {
		return fmt.Errorf("server.port không được cấu hình")
	}

	// Kiểm tra cấu hình Database
	if config.DB.Host == "" {
		return fmt.Errorf("db.host không được cấu hình")
	}
	if config.DB.Port == 0 {
		return fmt.Errorf("db.port không được cấu hình")
	}
	if config.DB.Username == "" {
		return fmt.Errorf("db.username không được cấu hình")
	}
	if config.DB.Database == "" {
		return fmt.Errorf("db.database không được cấu hình")
	}

	// Kiểm tra cấu hình JWT
	if config.JWT.AccessSecret == "" {
		return fmt.Errorf("jwt.access_signing_key không được cấu hình")
	}
	if config.JWT.RefreshSecret == "" {
		return fmt.Errorf("jwt.refresh_signing_key không được cấu hình")
	}
	if config.JWT.AccessExpiryMinutes == "" {
		return fmt.Errorf("jwt.access_token_expiration không được cấu hình")
	}
	if config.JWT.RefreshExpiryHours == "" {
		return fmt.Errorf("jwt.refresh_token_expiration không được cấu hình")
	}
	if config.JWT.Issuer == "" {
		return fmt.Errorf("jwt.issuer không được cấu hình")
	}

	// Kiểm tra kết quả chuyển đổi thời gian token
	if config.JWT.AccessTokenDuration == 0 {
		return fmt.Errorf("jwt.access_token_expiration không hợp lệ, không thể chuyển đổi '%s' thành time.Duration", config.JWT.AccessExpiryMinutes)
	}
	if config.JWT.RefreshTokenDuration == 0 {
		return fmt.Errorf("jwt.refresh_token_expiration không hợp lệ, không thể chuyển đổi '%s' thành time.Duration", config.JWT.RefreshExpiryHours)
	}

	// Kiểm tra cấu hình Storage
	if config.Storage.Type == "" {
		return fmt.Errorf("storage.type không được cấu hình")
	}

	// Kiểm tra cấu hình dựa trên loại Storage
	if config.Storage.Type == "local" {
		if config.Storage.Local.BasePath == "" {
			return fmt.Errorf("storage.local.base_path không được cấu hình")
		}
		if config.Storage.Local.BaseURL == "" {
			return fmt.Errorf("storage.local.base_url không được cấu hình")
		}
	} else if config.Storage.Type == "minio" {
		if config.Storage.Minio.Endpoint == "" {
			return fmt.Errorf("storage.minio.endpoint không được cấu hình")
		}
		if config.Storage.Minio.AccessKeyID == "" {
			return fmt.Errorf("storage.minio.access_key_id không được cấu hình")
		}
		if config.Storage.Minio.SecretAccessKey == "" {
			return fmt.Errorf("storage.minio.secret_access_key không được cấu hình")
		}
		if config.Storage.Minio.BucketName == "" {
			return fmt.Errorf("storage.minio.bucket_name không được cấu hình")
		}
	} else {
		return fmt.Errorf("storage.type không hợp lệ, phải là 'local' hoặc 'minio'")
	}

	return nil
}

// PrintConfig prints all configuration values in a formatted JSON
func PrintConfig(config *Config) {
	// Create a copy of the config with the password masked for security
	configCopy := *config
	configCopy.DB.Password = "********"
	configCopy.Storage.Minio.SecretAccessKey = "********"

	// Marshal config to JSON for pretty printing
	configJSON, err := json.MarshalIndent(configCopy, "", "  ")
	if err != nil {
		fmt.Printf("Error marshaling config: %v\n", err)
		return
	}

	fmt.Printf("=== MEDIA MODULE CONFIGURATION ===\n%s\n==============================\n", string(configJSON))
}
