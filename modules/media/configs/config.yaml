server:
  host: "0.0.0.0"
  port: 9046

db:
  host: "localhost"
  port: 3307
  username: "root"
  password: "root"
  database: "blog_v4"

jwt:
  access_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  refresh_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  access_token_expiration: "168h"
  refresh_token_expiration: "168h"
  issuer: wn-backend

storage:
  type: "minio" # <PERSON><PERSON>i từ "local" thành "minio"

  # Cấu hình cho local storage
  local:
    base_path: "./storage/media"
    base_url: "http://cdn.wn-api.local/media"

  # Cấu hình cho minio
  minio:
    endpoint: "localhost:9071"
    access_key_id: "minio"
    secret_access_key: "minio123"
    use_ssl: false
    bucket_name: "media"
    region: "us-east-1"
    presigned_expiry: 5m # 5 phút

upload:
  max_file_size: 10485760 # 10MB
  allowed_types:
    image:
      - "image/jpeg"
      - "image/png"
      - "image/gif"
      - "image/webp"
    video:
      - "video/mp4"
      - "video/webm"
    audio:
      - "audio/mpeg"
      - "audio/wav"
    document:
      - "application/pdf"
      - "application/msword"
      - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"

processors:
  image:
    enabled: true
    thumbnail:
      width: 150
      height: 150
    medium:
      width: 800
      height: 600
    large:
      width: 1920
      height: 1080
  video:
    enabled: true
    thumbnail:
      enabled: true
      time: "00:00:03"
    transcoding:
      enabled: true

tracing:
  enabled: true
  service_name: "media-service"
  exporter_type: "jaeger"
  signoz:
    endpoint: "localhost:4317"
  jaeger:
    host: "localhost"
    port: "6831"
  sample_ratio: 1.0