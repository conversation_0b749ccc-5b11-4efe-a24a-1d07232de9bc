package common

// CursorPaginationRequest định nghĩa request parameters cho phân trang dựa trên cursor
type CursorPaginationRequest struct {
	Cursor string `form:"cursor" json:"cursor"`
	Limit  int    `form:"limit" json:"limit"`
}

// CursorPaginationResponse định nghĩa response metadata cho phân trang
type CursorPaginationResponse struct {
	NextCursor string `json:"next_cursor,omitempty"`
	HasMore    bool   `json:"has_more"`
}
