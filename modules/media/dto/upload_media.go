package dto

// MediaType định nghĩa loại media
type MediaType string

const (
	MediaTypeImage    MediaType = "image"
	MediaTypeVideo    MediaType = "video"
	MediaTypeAudio    MediaType = "audio"
	MediaTypeDocument MediaType = "document"
	MediaTypeOther    MediaType = "other"
)

// MediaStatus định nghĩa trạng thái của media
type MediaStatus string

const (
	MediaStatusPending    MediaStatus = "pending"
	MediaStatusProcessing MediaStatus = "processing"
	MediaStatusReady      MediaStatus = "ready"
	MediaStatusFailed     MediaStatus = "failed"
)

// UploadMediaRequest định nghĩa request để upload media
type UploadMediaRequest struct {
	Description string `json:"description,omitempty" form:"description"`
	IsPublic    bool   `json:"is_public" form:"is_public"`
	FolderID    *uint  `json:"folder_id,omitempty" form:"folder_id"`
}

// UploadMediaResponse định nghĩa response sau khi upload media thành công
type UploadMediaResponse struct {
	ID               uint        `json:"id"`
	TenantID         uint        `json:"tenant_id"`
	MediaType        MediaType   `json:"media_type"`
	Filename         string      `json:"filename"`
	OriginalFilename string      `json:"original_filename"`
	ContentType      string      `json:"content_type"`
	Size             int64       `json:"size"`
	Status           MediaStatus `json:"status"`
	PublicURL        string      `json:"public_url,omitempty"`
	Description      string      `json:"description,omitempty"`
	IsPublic         bool        `json:"is_public"`
	FolderID         *uint       `json:"folder_id,omitempty"`
	CreatedAt        string      `json:"created_at"`
	UpdatedAt        string      `json:"updated_at"`
}
