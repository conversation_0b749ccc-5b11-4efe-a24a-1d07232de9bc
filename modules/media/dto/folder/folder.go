package folder

import (
	"time"
)

// CreateFolderRequest yêu cầu tạo thư mục mới
type CreateFolderRequest struct {
	ParentID    *int   `json:"parent_id,omitempty"`
	Name        string `json:"name" binding:"required"`
	Description string `json:"description,omitempty"`
	IsPublic    bool   `json:"is_public"`
}

// UpdateFolderRequest yêu cầu cập nhật thư mục
type UpdateFolderRequest struct {
	ParentID    *int    `json:"parent_id,omitempty"`
	Name        string  `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
	IsPublic    *bool   `json:"is_public,omitempty"`
}

// ListFolderRequest yêu cầu lấy danh sách thư mục
type ListFolderRequest struct {
	ParentID *int   `form:"parent_id,omitempty"`
	IsPublic *bool  `form:"is_public,omitempty"`
	Search   string `form:"search,omitempty"`
	Cursor   string `form:"cursor,omitempty"`
	Limit    int    `form:"limit,omitempty"`
}

// FolderResponse đối tượng trả về cho thư mục
type FolderResponse struct {
	FolderID    int       `json:"folder_id"`
	ParentID    *int      `json:"parent_id,omitempty"`
	Name        string    `json:"name"`
	Slug        string    `json:"slug"`
	Description string    `json:"description,omitempty"`
	IsPublic    bool      `json:"is_public"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}
