package dto

// ListFolderRequest định nghĩa request để lấy danh sách folder
type ListFolderRequest struct {
	ParentID *uint  `json:"parent_id,omitempty" form:"parent_id"`
	IsPublic *bool  `json:"is_public,omitempty" form:"is_public"`
	Search   string `json:"search,omitempty" form:"search"`
	Cursor   string `json:"cursor,omitempty" form:"cursor"`
	Limit    int    `json:"limit,omitempty" form:"limit" validate:"min=1,max=100"`
}

// ListFolderResponse định nghĩa response cho danh sách folder
type ListFolderResponse struct {
	Data       []*FolderResponse `json:"data"`
	NextCursor string            `json:"next_cursor,omitempty"`
	HasMore    bool              `json:"has_more"`
	Total      int               `json:"total,omitempty"`
}
