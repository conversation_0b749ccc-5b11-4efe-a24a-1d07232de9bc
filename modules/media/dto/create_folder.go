package dto

// CreateFolderRequest định nghĩa request để tạo folder
type CreateFolderRequest struct {
	Name        string `json:"name" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty"`
	ParentID    *uint  `json:"parent_id,omitempty"`
	IsPublic    bool   `json:"is_public"`
}

// FolderResponse định nghĩa response cho folder
type FolderResponse struct {
	ID          uint   `json:"id"`
	TenantID    uint   `json:"tenant_id"`
	Name        string `json:"name"`
	Slug        string `json:"slug"`
	Description string `json:"description,omitempty"`
	ParentID    *uint  `json:"parent_id,omitempty"`
	IsPublic    bool   `json:"is_public"`
	CreatedBy   uint   `json:"created_by"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}
