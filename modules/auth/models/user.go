package models

import (
	"database/sql/driver"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type UserStatus string

const (
	UserStatusActive                    UserStatus = "active"
	UserStatusInactive                  UserStatus = "inactive"
	UserStatusSuspended                 UserStatus = "suspended"
	UserStatusPendingVerification       UserStatus = "pending_verification"
	UserStatusEmailVerificationRequired UserStatus = "email_verification_required"
	UserStatusBanned                    UserStatus = "banned"
	UserStatusLocked                    UserStatus = "locked"
	UserStatusDeleted                   UserStatus = "deleted"
)

type UserType int

const (
	UserTypeUnknown UserType = iota
	UserTypeAdmin
	UserTypeTenant
	UserTypeCustomer
)

// Value implements the driver.Valuer interface
func (ut UserType) Value() (driver.Value, error) {
	return int64(ut), nil
}

// Scan implements the sql.Scanner interface
func (ut *UserType) Scan(value interface{}) error {
	if value == nil {
		*ut = UserTypeUnknown
		return nil
	}

	switch v := value.(type) {
	case int64:
		*ut = UserType(v)
	case int:
		*ut = UserType(v)
	case uint:
		*ut = UserType(v)
	case []byte:
		// Xử lý trường hợp giá trị là chuỗi
		var err error
		var intVal int
		switch string(v) {
		case "admin":
			*ut = UserTypeAdmin
		case "tenant":
			*ut = UserTypeTenant
		case "customer":
			*ut = UserTypeCustomer
		default:
			fmt.Sscanf(string(v), "%d", &intVal)
			*ut = UserType(intVal)
		}
		if err != nil {
			return err
		}
	default:
		return fmt.Errorf("không thể chuyển đổi %T thành UserType", value)
	}

	return nil
}

// String chuyển đổi UserType sang chuỗi để hiển thị
func (ut UserType) String() string {
	switch ut {
	case UserTypeAdmin:
		return "admin"
	case UserTypeTenant:
		return "tenant"
	case UserTypeCustomer:
		return "customer"
	default:
		return "unknown"
	}
}

// ParseUserType chuyển đổi từ string sang UserType
func ParseUserType(s string) UserType {
	switch s {
	case "admin":
		return UserTypeAdmin
	case "tenant":
		return UserTypeTenant
	case "customer":
		return UserTypeCustomer
	default:
		return UserTypeUnknown
	}
}

// Hằng số giúp so sánh enum từ client code
const (
	UserTypeAdminStr    = "admin"
	UserTypeTenantStr   = "tenant"
	UserTypeCustomerStr = "customer"
)

// IsAdmin kiểm tra UserType có phải admin không
func (ut UserType) IsAdmin() bool {
	return ut == UserTypeAdmin
}

// IsTenant kiểm tra UserType có phải tenant không
func (ut UserType) IsTenant() bool {
	return ut == UserTypeTenant
}

// IsCustomer kiểm tra UserType có phải customer không
func (ut UserType) IsCustomer() bool {
	return ut == UserTypeCustomer
}

// User là model GORM cho bảng users
type User struct {
	UserID          uint       `gorm:"column:user_id;primaryKey;autoIncrement" json:"user_id"`
	TenantID        uint       `gorm:"column:tenant_id;index" json:"tenant_id"`
	Username        string     `gorm:"column:username;uniqueIndex" json:"username"`
	Email           string     `gorm:"column:email;uniqueIndex" json:"email"`
	PasswordHash    string     `gorm:"column:password_hash" json:"-"`
	FullName        string     `gorm:"column:full_name" json:"full_name"`
	CreatedAt       time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt       time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	LastLogin       *time.Time `gorm:"column:last_login" json:"last_login"`
	Status          UserStatus `gorm:"column:status;type:ENUM('active', 'inactive', 'suspended', 'pending_verification', 'email_verification_required', 'banned', 'locked', 'deleted');default:pending_verification" json:"status"`
	IsEmailVerified bool       `gorm:"column:is_email_verified;default:0" json:"is_email_verified"`
	UserType        UserType   `gorm:"column:user_type;type:ENUM('admin', 'tenant', 'customer')" json:"user_type"`
	Profile         *Profile   `gorm:"foreignKey:UserID" json:"profile,omitempty"`
}

// TableName chỉ định tên bảng cho model User
func (User) TableName() string {
	return "users"
}

// BeforeCreate là hook được gọi trước khi tạo bản ghi
func (u *User) BeforeCreate(tx *gorm.DB) (err error) {
	return nil
}

// BeforeUpdate là hook được gọi trước khi cập nhật bản ghi
func (u *User) BeforeUpdate(tx *gorm.DB) (err error) {
	return nil
}
