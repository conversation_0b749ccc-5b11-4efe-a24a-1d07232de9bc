package models

import (
	"time"
)

// EmailVerification đại diện cho một token xác thực email
type EmailVerification struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID    int64     `gorm:"column:user_id;index" json:"user_id"`
	Email     string    `gorm:"column:email;index" json:"email"`
	Token     string    `gorm:"column:token;uniqueIndex" json:"token"`
	Verified  bool      `gorm:"column:verified;default:false" json:"verified"`
	ExpiresAt time.Time `gorm:"column:expires_at;index" json:"expires_at"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName xác định tên bảng trong cơ sở dữ liệu
func (EmailVerification) TableName() string {
	return "auth_email_verifications"
}
