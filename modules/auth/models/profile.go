package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

type NotificationPreferences struct {
	EmailNotifications bool `json:"email_notifications"`
	PushNotifications  bool `json:"push_notifications"`
	SMSNotifications   bool `json:"sms_notifications"`
}

func (n NotificationPreferences) Value() (driver.Value, error) {
	return json.Marshal(n)
}

func (n *NotificationPreferences) Scan(value interface{}) error {
	b, ok := value.([]byte)
	if !ok {
		return nil
	}
	return json.Unmarshal(b, &n)
}

// Profile là model GORM cho bảng user_profiles
type Profile struct {
	UserID                  uint                    `gorm:"column:user_id;primaryKey" json:"user_id"`
	AvatarURL               string                  `gorm:"column:avatar_url" json:"avatar_url"`
	Phone                   string                  `gorm:"column:phone" json:"phone"`
	Address                 string                  `gorm:"column:address" json:"address"`
	City                    string                  `gorm:"column:city" json:"city"`
	State                   string                  `gorm:"column:state" json:"state"`
	Country                 string                  `gorm:"column:country" json:"country"`
	PostalCode              string                  `gorm:"column:postal_code" json:"postal_code"`
	DateOfBirth             *time.Time              `gorm:"column:date_of_birth" json:"date_of_birth"`
	Gender                  string                  `gorm:"column:gender" json:"gender"`
	PreferredLanguage       string                  `gorm:"column:preferred_language" json:"preferred_language"`
	Timezone                string                  `gorm:"column:timezone" json:"timezone"`
	NotificationPreferences NotificationPreferences `gorm:"column:notification_preferences;type:json" json:"notification_preferences"`
	SocialLinkedin          string                  `gorm:"column:social_linkedin" json:"social_linkedin"`
	SocialTwitter           string                  `gorm:"column:social_twitter" json:"social_twitter"`
	SocialFacebook          string                  `gorm:"column:social_facebook" json:"social_facebook"`
	SocialInstagram         string                  `gorm:"column:social_instagram" json:"social_instagram"`
	Skills                  string                  `gorm:"column:skills" json:"skills"`
	Interests               string                  `gorm:"column:interests" json:"interests"`
	ProfessionalTitle       string                  `gorm:"column:professional_title" json:"professional_title"`
	Department              string                  `gorm:"column:department" json:"department"`
	HireDate                *time.Time              `gorm:"column:hire_date" json:"hire_date"`
	EmergencyContactName    string                  `gorm:"column:emergency_contact_name" json:"emergency_contact_name"`
	EmergencyContactPhone   string                  `gorm:"column:emergency_contact_phone" json:"emergency_contact_phone"`
	CreatedAt               time.Time               `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt               time.Time               `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
	User                    *User                   `gorm:"foreignKey:UserID" json:"-"`
}

// TableName chỉ định tên bảng cho model Profile
func (Profile) TableName() string {
	return "user_profiles"
}
