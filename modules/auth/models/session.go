package models

import (
	"time"
)

// AuthSession đại diện cho phiên đăng nhập
type AuthSession struct {
	ID           string    `gorm:"column:id;primaryKey" json:"id"`
	UserID       uint      `gorm:"column:user_id;index" json:"user_id"`
	AccessToken  string    `gorm:"column:access_token" json:"access_token"`
	RefreshToken string    `gorm:"column:refresh_token" json:"refresh_token"`
	ExpiresAt    time.Time `gorm:"column:expires_at;index" json:"expires_at"`
	CreatedAt    time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName xác định tên bảng trong cơ sở dữ liệu
func (AuthSession) TableName() string {
	return "auth_sessions"
}
