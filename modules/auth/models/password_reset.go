package models

import (
	"time"
)

// PasswordReset là model cho bảng auth_password_resets
type PasswordReset struct {
	ID        int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Email     string    `gorm:"column:email;index" json:"email"`
	Token     string    `gorm:"column:token;uniqueIndex" json:"token"`
	Used      bool      `gorm:"column:used;default:false" json:"used"`
	ExpiresAt time.Time `gorm:"column:expires_at;index" json:"expires_at"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName xác định tên bảng trong cơ sở dữ liệu
func (PasswordReset) TableName() string {
	return "auth_password_resets"
}
