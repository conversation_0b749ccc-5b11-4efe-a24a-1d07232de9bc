package models

import (
	"time"
)

// SocialLogin represents a social media account linked to a user
type SocialLogin struct {
	ID             int64     `db:"id"`
	UserID         int64     `db:"user_id"`
	Provider       string    `db:"provider"` // "facebook" or "google"
	ProviderUserID string    `db:"provider_user_id"`
	AccessToken    string    `db:"access_token"`
	RefreshToken   string    `db:"refresh_token"`
	TokenExpiresAt time.Time `db:"token_expires_at"`
	CreatedAt      time.Time `db:"created_at"`
	UpdatedAt      time.Time `db:"updated_at"`
}
