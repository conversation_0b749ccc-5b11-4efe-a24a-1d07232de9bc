package service

import (
	"context"
	"fmt"
	"log"
	"time"

	"wnapi/modules/auth/dto"
	autherrors "wnapi/modules/auth/internal"
	"wnapi/modules/auth/repository"
	"wnapi/modules/notification/service"

	"github.com/google/uuid"
)

const (
	// Thời gian hết hạn của token reset password (24 giờ)
	tokenExpiration = 24 * time.Hour
)

// PasswordResetService xử lý các thao tác liên quan đến reset password
type PasswordResetService struct {
	passwordResetRepo repository.PasswordResetRepository
	emailService      *service.EmailService
	baseURL           string
	webURL            string
}

// NewPasswordResetService tạo một service mới để xử lý reset password
func NewPasswordResetService(
	passwordResetRepo repository.PasswordResetRepository,
	emailService *service.EmailService,
	baseURL string,
	webURL string,
) *PasswordResetService {
	return &PasswordResetService{
		passwordResetRepo: passwordResetRepo,
		emailService:      emailService,
		baseURL:           baseURL,
		webURL:            webURL,
	}
}

// ForgotPassword xử lý yêu cầu quên mật khẩu
func (s *PasswordResetService) ForgotPassword(ctx context.Context, req *dto.ForgotPasswordRequest) error {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Trong trường hợp này, giả sử chúng ta đã xác thực email tồn tại
	// Trong triển khai thực tế, sẽ gọi userClient.GetUserByEmail ở đây

	// Tạo token đặt lại mật khẩu
	token := uuid.New().String()
	expiresAt := time.Now().Add(tokenExpiration)

	// Vô hiệu hóa tất cả token cũ của email này
	if err := s.passwordResetRepo.InvalidateOldTokens(ctxWithTimeout, req.Email); err != nil {
		log.Printf("Warning: không thể vô hiệu hóa token cũ: %v", err)
	}

	// Tạo token mới
	if err := s.passwordResetRepo.Create(ctxWithTimeout, req.Email, token, expiresAt); err != nil {
		return autherrors.Wrap(autherrors.ErrCodePasswordResetFailed, "Không thể tạo token đặt lại mật khẩu", err)
	}

	// Tạo URL đặt lại mật khẩu
	resetURL := fmt.Sprintf("%s/auth/reset-password?token=%s", s.webURL, token)

	// Gửi email đặt lại mật khẩu qua notification service
	if s.emailService != nil {
		variables := map[string]string{
			"reset_url": resetURL,
			"token":     token,
			"email":     req.Email,
		}

		if err := s.emailService.SendEmail(
			req.Email,
			"Đặt lại mật khẩu của bạn",
			"password_reset", // Template ID
			variables,
			1, // Tenant ID - có thể lấy từ context
			0, // User ID - chưa có vì đây là forgot password
		); err != nil {
			log.Printf("Failed to send password reset email: %v", err)
			// Không return error để không fail request, chỉ log
		} else {
			log.Printf("Password reset email sent successfully to: %s", req.Email)
		}
	} else {
		// Fallback to log if email service is not available
		log.Printf("Email service not available. Reset URL for %s: %s", req.Email, resetURL)
	}

	return nil
}

// VerifyResetToken xác thực token đặt lại mật khẩu
func (s *PasswordResetService) VerifyResetToken(ctx context.Context, req *dto.VerifyResetTokenRequest) (*dto.VerifyTokenResponse, error) {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	// Tìm token
	resetToken, err := s.passwordResetRepo.FindByToken(ctxWithTimeout, req.Token)
	if err != nil {
		return nil, autherrors.Wrap(autherrors.ErrCodeInvalidToken, "Không thể xác thực token", err)
	}

	// Kiểm tra xem token có tồn tại và còn hiệu lực
	if resetToken == nil || resetToken.Used || resetToken.ExpiresAt.Before(time.Now()) {
		return nil, autherrors.New(autherrors.ErrCodeInvalidToken, "vi")
	}

	return &dto.VerifyTokenResponse{
		Email: resetToken.Email,
		Valid: true,
	}, nil
}

// ResetPassword đặt lại mật khẩu
func (s *PasswordResetService) ResetPassword(ctx context.Context, req *dto.ResetPasswordRequest) error {
	// Tạo context với timeout
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// Kiểm tra mật khẩu và xác nhận mật khẩu
	if req.Password != req.ConfirmPassword {
		return autherrors.New(autherrors.ErrCodePasswordsNotMatch, "vi")
	}

	// Tìm token
	resetToken, err := s.passwordResetRepo.FindByToken(ctxWithTimeout, req.Token)
	if err != nil {
		return autherrors.Wrap(autherrors.ErrCodeInvalidToken, "Không thể xác thực token", err)
	}

	// Kiểm tra xem token có tồn tại và còn hiệu lực
	if resetToken == nil || resetToken.Used || resetToken.ExpiresAt.Before(time.Now()) {
		return autherrors.New(autherrors.ErrCodeInvalidToken, "vi")
	}

	// Trong triển khai thực tế, sẽ gọi userClient.GetUserByEmail và userClient.ResetPassword ở đây
	log.Printf("Đặt lại mật khẩu cho email: %s", resetToken.Email)

	// Đánh dấu token đã sử dụng
	if err := s.passwordResetRepo.MarkAsUsed(ctxWithTimeout, req.Token); err != nil {
		log.Printf("Warning: không thể đánh dấu token đã sử dụng: %v", err)
	}

	// Gửi email xác nhận đặt lại mật khẩu thành công
	if s.emailService != nil {
		variables := map[string]string{
			"email": resetToken.Email,
		}

		if err := s.emailService.SendEmail(
			resetToken.Email,
			"Mật khẩu đã được đặt lại thành công",
			"password_reset_success", // Template ID
			variables,
			1, // Tenant ID - có thể lấy từ context
			0, // User ID - chưa có
		); err != nil {
			log.Printf("Failed to send password reset confirmation email: %v", err)
			// Không return error vì password đã được reset thành công
		} else {
			log.Printf("Password reset confirmation email sent successfully to: %s", resetToken.Email)
		}
	} else {
		// Fallback to log if email service is not available
		log.Printf("Email service not available. Password reset successful for: %s", resetToken.Email)
	}

	return nil
}
