package mysql

import (
	"context"
	"errors"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/models"
	"wnapi/modules/auth/repository"

	"gorm.io/gorm"
)

// GormEmailVerificationRepository là cài đặt MySQL cho EmailVerificationRepository sử dụng GORM
type GormEmailVerificationRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewEmailVerificationRepository tạo instance mới của GormEmailVerificationRepository
func NewEmailVerificationRepository(db *gorm.DB, logger logger.Logger) repository.EmailVerificationRepository {
	return &GormEmailVerificationRepository{
		db:     db,
		logger: logger,
	}
}

// Create lưu token xác thực email vào database
func (r *GormEmailVerificationRepository) Create(ctx context.Context, verification *models.EmailVerification) error {
	now := time.Now()
	verification.CreatedAt = now
	verification.UpdatedAt = now

	result := r.db.WithContext(ctx).Create(verification)
	if result.Error != nil {
		r.logger.Error("Không thể tạo xác thực email", logger.String("error", result.Error.Error()))
		return fmt.Errorf("failed to create email verification: %w", result.Error)
	}

	return nil
}

// GetByToken lấy token xác thực email bằng token
func (r *GormEmailVerificationRepository) GetByToken(ctx context.Context, token string) (*models.EmailVerification, error) {
	var verification models.EmailVerification

	result := r.db.WithContext(ctx).
		Where("token = ? AND verified = ?", token, false).
		First(&verification)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		r.logger.Error("Không thể lấy thông tin xác thực email", logger.String("error", result.Error.Error()))
		return nil, fmt.Errorf("failed to get email verification by token: %w", result.Error)
	}

	return &verification, nil
}

// MarkAsVerified đánh dấu token đã được sử dụng để xác thực
func (r *GormEmailVerificationRepository) MarkAsVerified(ctx context.Context, token string) error {
	result := r.db.WithContext(ctx).Model(&models.EmailVerification{}).
		Where("token = ?", token).
		Updates(map[string]interface{}{
			"verified":   true,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		r.logger.Error("Không thể cập nhật trạng thái xác thực email", logger.String("error", result.Error.Error()))
		return fmt.Errorf("failed to mark email verification as verified: %w", result.Error)
	}

	return nil
}

// GetByUserID lấy tất cả các token xác thực email của một người dùng
func (r *GormEmailVerificationRepository) GetByUserID(ctx context.Context, userID int64) ([]*models.EmailVerification, error) {
	var verifications []*models.EmailVerification

	result := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&verifications)

	if result.Error != nil {
		r.logger.Error("Không thể lấy danh sách xác thực email", logger.String("error", result.Error.Error()))
		return nil, fmt.Errorf("failed to get email verifications by user ID: %w", result.Error)
	}

	return verifications, nil
}

// DeleteExpired xóa tất cả các token đã hết hạn
func (r *GormEmailVerificationRepository) DeleteExpired(ctx context.Context) error {
	now := time.Now()

	result := r.db.WithContext(ctx).
		Where("expires_at < ? AND verified = ?", now, false).
		Delete(&models.EmailVerification{})

	if result.Error != nil {
		r.logger.Error("Không thể xóa xác thực email hết hạn", logger.String("error", result.Error.Error()))
		return fmt.Errorf("failed to delete expired email verifications: %w", result.Error)
	}

	return nil
}
