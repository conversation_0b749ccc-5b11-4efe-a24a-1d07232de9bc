package mysql

import (
	"context"
	"errors"
	"fmt"
	"time"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/models"
	"wnapi/modules/auth/repository"

	"gorm.io/gorm"
)

type gormPasswordResetRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewPasswordResetRepository tạo một instance mới của gormPasswordResetRepository
func NewPasswordResetRepository(db *gorm.DB, logger logger.Logger) repository.PasswordResetRepository {
	return &gormPasswordResetRepository{
		db:     db,
		logger: logger,
	}
}

// Create tạo một bản ghi đặt lại mật khẩu mới
func (r *gormPasswordResetRepository) Create(ctx context.Context, email string, token string, expiresAt time.Time) error {
	passwordReset := models.PasswordReset{
		Email:     email,
		Token:     token,
		Used:      false,
		ExpiresAt: expiresAt,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	result := r.db.WithContext(ctx).Create(&passwordReset)
	if result.Error != nil {
		r.logger.Error("Không thể tạo token đặt lại mật khẩu", logger.String("error", result.Error.Error()))
		return fmt.Errorf("failed to create password reset: %w", result.Error)
	}
	return nil
}

// FindByToken tìm bản ghi đặt lại mật khẩu theo token
func (r *gormPasswordResetRepository) FindByToken(ctx context.Context, token string) (*models.PasswordReset, error) {
	var reset models.PasswordReset
	now := time.Now()

	result := r.db.WithContext(ctx).
		Where("token = ? AND used = ? AND expires_at > ?", token, false, now).
		First(&reset)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		r.logger.Error("Không thể tìm token đặt lại mật khẩu", logger.String("error", result.Error.Error()))
		return nil, fmt.Errorf("failed to find password reset token: %w", result.Error)
	}

	return &reset, nil
}

// MarkAsUsed đánh dấu token đã được sử dụng
func (r *gormPasswordResetRepository) MarkAsUsed(ctx context.Context, token string) error {
	result := r.db.WithContext(ctx).Model(&models.PasswordReset{}).
		Where("token = ?", token).
		Update("used", true)

	if result.Error != nil {
		r.logger.Error("Không thể đánh dấu token đã sử dụng", logger.String("error", result.Error.Error()))
		return fmt.Errorf("failed to mark token as used: %w", result.Error)
	}

	return nil
}

// InvalidateOldTokens vô hiệu hóa tất cả token cũ cho một email
func (r *gormPasswordResetRepository) InvalidateOldTokens(ctx context.Context, email string) error {
	result := r.db.WithContext(ctx).Model(&models.PasswordReset{}).
		Where("email = ? AND used = ?", email, false).
		Update("used", true)

	if result.Error != nil {
		r.logger.Error("Không thể vô hiệu hóa token cũ", logger.String("error", result.Error.Error()))
		return fmt.Errorf("failed to invalidate old tokens: %w", result.Error)
	}

	return nil
}

// DeleteExpiredTokens xóa các token đã hết hạn
func (r *gormPasswordResetRepository) DeleteExpiredTokens(ctx context.Context) error {
	now := time.Now()
	oneDayAgo := now.Add(-24 * time.Hour)

	// Xóa token đã hết hạn
	expiredResult := r.db.WithContext(ctx).
		Where("expires_at < ?", now).
		Delete(&models.PasswordReset{})

	if expiredResult.Error != nil {
		r.logger.Error("Không thể xóa token hết hạn", logger.String("error", expiredResult.Error.Error()))
		return fmt.Errorf("failed to delete expired tokens: %w", expiredResult.Error)
	}

	// Xóa token đã sử dụng và cũ
	usedResult := r.db.WithContext(ctx).
		Where("used = ? AND created_at < ?", true, oneDayAgo).
		Delete(&models.PasswordReset{})

	if usedResult.Error != nil {
		r.logger.Error("Không thể xóa token đã sử dụng", logger.String("error", usedResult.Error.Error()))
		return fmt.Errorf("failed to delete used tokens: %w", usedResult.Error)
	}

	return nil
}
