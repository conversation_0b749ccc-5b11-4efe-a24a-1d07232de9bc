package repository

import (
	"context"
	"time"
	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/models"
)

// Repository định nghĩa interface cho authentication repository
type Repository interface {
	// User
	CreateUser(ctx context.Context, user *internal.User, password string) error
	GetUserByID(ctx context.Context, id int) (*internal.User, error)
	GetUserByUsername(ctx context.Context, username string) (*internal.User, error)
	GetUserByEmail(ctx context.Context, email string) (*internal.User, error)
	UpdateUser(ctx context.Context, user *internal.User) error
	DeleteUser(ctx context.Context, id int) error

	// Token
	CreateToken(ctx context.Context, token *internal.Token) error
	GetTokenByValue(ctx context.Context, tokenValue string, tokenType internal.TokenType) (*internal.Token, error)
	DeleteToken(ctx context.Context, id string) error
	DeleteExpiredTokens(ctx context.Context) error
}

// NewAuthRepository tạo repository sử dụng MySQL
func NewAuthRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	// Cài đặt cụ thể sẽ được thực hiện trong các package con
	// Do import cycle, chúng ta không thể import trực tiếp mysql package ở đây
	return nil, nil
}

// SessionRepository interface for session management
// TODO: Implement when session functionality is needed
type SessionRepository interface {
	CreateSession(ctx context.Context, session *models.AuthSession) error
	GetSession(ctx context.Context, sessionID string) (*models.AuthSession, error)
	DeleteSession(ctx context.Context, sessionID string) error
	DeleteExpiredSessions(ctx context.Context) error
}

// EmailVerificationRepository interface for email verification management
// TODO: Implement when email verification functionality is needed
type EmailVerificationRepository interface {
	Create(ctx context.Context, verification *models.EmailVerification) error
	GetByToken(ctx context.Context, token string) (*models.EmailVerification, error)
	GetByUserID(ctx context.Context, userID int64) ([]*models.EmailVerification, error)
	MarkAsVerified(ctx context.Context, token string) error
	DeleteExpired(ctx context.Context) error
}

// PasswordResetRepository interface cho quản lý đặt lại mật khẩu
type PasswordResetRepository interface {
	Create(ctx context.Context, email string, token string, expiresAt time.Time) error
	FindByToken(ctx context.Context, token string) (*models.PasswordReset, error)
	MarkAsUsed(ctx context.Context, token string) error
	InvalidateOldTokens(ctx context.Context, email string) error
	DeleteExpiredTokens(ctx context.Context) error
}
