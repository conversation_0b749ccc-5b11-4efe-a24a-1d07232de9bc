package internal

import (
	pkgErrors "wnapi/internal/pkg/errors"
)

// ErrorCode mở rộng từ mã lỗi chung
type ErrorCode = pkgErrors.ErrorCode

// Authentication error codes - <PERSON><PERSON> thừa lỗi chung
const (
	// Lỗi chung - sử dụng từ package errors
	ErrCodeUnknown          = pkgErrors.ErrCodeUnknown
	ErrCodeValidationFailed = pkgErrors.ErrCodeValidationFailed
	ErrCodeBadRequest       = pkgErrors.ErrCodeBadRequest
	ErrCodeInternalServer   = pkgErrors.ErrCodeInternalServer
	ErrCodeNotFound         = pkgErrors.ErrCodeNotFound
	ErrCodeForbidden        = pkgErrors.ErrCodeForbidden
	ErrCodeConflict         = pkgErrors.ErrCodeConflict
	ErrCodeUnauthorized     = pkgErrors.ErrCodeUnauthorized
	ErrCodeTimeout          = pkgErrors.ErrCodeTimeout
)

// Mã lỗi riêng cho module auth
const (
	// Lỗi xác thực người dùng
	ErrCodeUserNotFound       ErrorCode = "USER_NOT_FOUND"
	ErrCodeUserAlreadyExists  ErrorCode = "USER_ALREADY_EXISTS"
	ErrCodeEmailAlreadyExists ErrorCode = "EMAIL_ALREADY_EXISTS"
	ErrCodeInvalidCredentials ErrorCode = "INVALID_CREDENTIALS"
	ErrCodeInvalidPassword    ErrorCode = "INVALID_PASSWORD"
	ErrCodeWeakPassword       ErrorCode = "WEAK_PASSWORD"

	// Lỗi trạng thái tài khoản
	ErrCodeAccountLocked    ErrorCode = "ACCOUNT_LOCKED"
	ErrCodeAccountSuspended ErrorCode = "ACCOUNT_SUSPENDED"
	ErrCodeAccountInactive  ErrorCode = "ACCOUNT_INACTIVE"
	ErrCodeAccountDeleted   ErrorCode = "ACCOUNT_DELETED"

	// Lỗi xác minh email
	ErrCodeEmailNotVerified         ErrorCode = "EMAIL_NOT_VERIFIED"
	ErrCodeEmailVerificationExpired ErrorCode = "EMAIL_VERIFICATION_EXPIRED"
	ErrCodeInvalidVerificationToken ErrorCode = "INVALID_VERIFICATION_TOKEN"
	ErrCodeEmailVerificationFailed  ErrorCode = "EMAIL_VERIFICATION_FAILED"
	ErrCodeEmailAlreadyVerified     ErrorCode = "EMAIL_ALREADY_VERIFIED"

	// Lỗi token
	ErrCodeSessionExpired      ErrorCode = "SESSION_EXPIRED"
	ErrCodeSessionNotFound     ErrorCode = "SESSION_NOT_FOUND"
	ErrCodeInvalidToken        ErrorCode = "INVALID_TOKEN"
	ErrCodeTokenExpired        ErrorCode = "TOKEN_EXPIRED"
	ErrCodeInvalidRefreshToken ErrorCode = "INVALID_REFRESH_TOKEN"

	// Lỗi đặt lại mật khẩu
	ErrCodePasswordsNotMatch   ErrorCode = "PASSWORDS_NOT_MATCH"
	ErrCodePasswordResetFailed ErrorCode = "PASSWORD_RESET_FAILED"
	ErrCodeTokenNotFound       ErrorCode = "TOKEN_NOT_FOUND"
)

// Ánh xạ mã lỗi thành thông báo lỗi theo ngôn ngữ
var ErrorMessages = map[ErrorCode]map[string]string{
	// Lỗi xác thực người dùng
	ErrCodeUserNotFound: {
		"en": "User not found",
		"vi": "Không tìm thấy người dùng",
	},
	ErrCodeUserAlreadyExists: {
		"en": "User already exists",
		"vi": "Người dùng đã tồn tại",
	},
	ErrCodeEmailAlreadyExists: {
		"en": "Email already exists",
		"vi": "Email đã tồn tại",
	},
	ErrCodeInvalidCredentials: {
		"en": "Invalid credentials",
		"vi": "Thông tin đăng nhập không hợp lệ",
	},
	ErrCodeInvalidPassword: {
		"en": "Invalid password",
		"vi": "Mật khẩu không hợp lệ",
	},
	ErrCodeWeakPassword: {
		"en": "Password does not meet security requirements",
		"vi": "Mật khẩu không đáp ứng yêu cầu bảo mật",
	},

	// Lỗi trạng thái tài khoản
	ErrCodeAccountLocked: {
		"en": "Account is locked",
		"vi": "Tài khoản đã bị khóa",
	},
	ErrCodeAccountSuspended: {
		"en": "Account is suspended",
		"vi": "Tài khoản đã bị tạm ngưng",
	},
	ErrCodeAccountInactive: {
		"en": "Account is inactive",
		"vi": "Tài khoản không hoạt động",
	},
	ErrCodeAccountDeleted: {
		"en": "Account is deleted",
		"vi": "Tài khoản đã bị xóa",
	},

	// Lỗi xác minh email
	ErrCodeEmailNotVerified: {
		"en": "Email is not verified",
		"vi": "Email chưa được xác minh",
	},
	ErrCodeEmailVerificationExpired: {
		"en": "Email verification link has expired",
		"vi": "Liên kết xác minh email đã hết hạn",
	},
	ErrCodeInvalidVerificationToken: {
		"en": "Invalid verification token",
		"vi": "Token xác minh không hợp lệ",
	},
	ErrCodeEmailVerificationFailed: {
		"en": "Email verification failed",
		"vi": "Xác minh email thất bại",
	},
	ErrCodeEmailAlreadyVerified: {
		"en": "Email is already verified",
		"vi": "Email đã được xác minh",
	},

	// Lỗi token
	ErrCodeSessionExpired: {
		"en": "Session has expired",
		"vi": "Phiên làm việc đã hết hạn",
	},
	ErrCodeSessionNotFound: {
		"en": "Session not found",
		"vi": "Không tìm thấy phiên làm việc",
	},
	ErrCodeInvalidToken: {
		"en": "Invalid token",
		"vi": "Token không hợp lệ",
	},
	ErrCodeTokenExpired: {
		"en": "Token has expired",
		"vi": "Token đã hết hạn",
	},
	ErrCodeInvalidRefreshToken: {
		"en": "Invalid refresh token",
		"vi": "Refresh token không hợp lệ",
	},

	// Lỗi đặt lại mật khẩu
	ErrCodePasswordsNotMatch: {
		"en": "Passwords do not match",
		"vi": "Mật khẩu và xác nhận mật khẩu không khớp",
	},
	ErrCodePasswordResetFailed: {
		"en": "Password reset failed",
		"vi": "Đặt lại mật khẩu thất bại",
	},
	ErrCodeTokenNotFound: {
		"en": "Token not found",
		"vi": "Không tìm thấy token",
	},
}

// GetMessage trả về thông báo lỗi theo ngôn ngữ
func GetMessage(code ErrorCode, lang string) string {
	// Kiểm tra trong bản đồ lỗi riêng của auth
	if messages, exists := ErrorMessages[code]; exists {
		if message, exists := messages[lang]; exists {
			return message
		}
		// Fallback to English trong ErrorMessages
		if message, exists := messages["en"]; exists {
			return message
		}
	}

	// Kiểm tra trong bản đồ lỗi chung
	return pkgErrors.GetCommonMessage(code, lang)
}

// AppError kế thừa từ pkgErrors
// Sử dụng cho các hàm tạo lỗi đặc thù cho module auth
type AppError = pkgErrors.AppError

func Wrap(code ErrorCode, message string, err error) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: getHTTPStatus(code),
		Internal:   err,
	}
}

func New(code ErrorCode, lang string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetMessage(code, lang),
		HTTPStatus: getHTTPStatus(code),
	}
}

func NewWithDetails(code ErrorCode, lang string, details string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetMessage(code, lang),
		Details:    details,
		HTTPStatus: getHTTPStatus(code),
	}
}

func NewValidation(code ErrorCode, lang string, fields map[string]string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetMessage(code, lang),
		Fields:     fields,
		HTTPStatus: 400,
	}
}

func getHTTPStatus(code ErrorCode) int {
	switch code {
	case ErrCodeUserNotFound, ErrCodeTokenNotFound, ErrCodeSessionNotFound:
		return 404
	case ErrCodeInvalidCredentials, ErrCodeInvalidToken, ErrCodeTokenExpired, ErrCodeInvalidRefreshToken, ErrCodeSessionExpired:
		return 401
	case ErrCodeUserAlreadyExists, ErrCodeEmailAlreadyExists:
		return 409
	case ErrCodeWeakPassword, ErrCodeValidationFailed, ErrCodeBadRequest, ErrCodePasswordsNotMatch:
		return 400
	case ErrCodeAccountLocked, ErrCodeAccountSuspended, ErrCodeAccountInactive, ErrCodeAccountDeleted:
		return 403
	default:
		return pkgErrors.GetDefaultHTTPStatus(code)
	}
}
