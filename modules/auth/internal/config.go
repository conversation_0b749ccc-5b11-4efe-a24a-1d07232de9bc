package internal

import (
	"fmt"
	"log"
	"os"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// LoadAuthConfig đọc cấu hình auth từ biến môi trường
func LoadAuthConfig() (*AuthConfig, error) {
	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	cfg := &AuthConfig{}

	// Đọc cấu hình từ biến môi trường với prefix AUTH_
	opts := env.Options{
		Prefix: "AUTH_",
	}
	if err := env.ParseWithOptions(cfg, opts); err != nil {
		return nil, fmt.Errorf("lỗi đọc cấu hình auth từ biến môi trường: %w", err)
	}

	if cfg.JWTSecret == "" {
		return nil, fmt.Errorf("Thiếu biến môi trường AUTH_JWTSECRET trong .env hoặc hệ thống")
	}

	if cfg.Issuer == "" {
		return nil, fmt.Errorf("Thiếu biến môi trường AUTH_JWT_ISSUER trong .env hoặc hệ thống")
	}

	return cfg, nil
}
