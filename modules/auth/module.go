package auth

import (
	"context"
	"fmt"
	"path/filepath"
	"time"

	"wnapi/internal/core"
	pkgauth "wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/api"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/repository/mysql"
	"wnapi/modules/auth/service"
	notificationService "wnapi/modules/notification/service"

	gormMysql "gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func init() {
	core.RegisterModuleFactory("auth", NewModule)
}

// Module triển khai auth module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Khởi tạo repository
	repo, err := mysql.NewMySQLRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	// Đọc cấu hình từ biến môi trường (bắt buộc phải có)
	authConfig, err := internal.LoadAuthConfig()
	if err != nil {
		return nil, err
	}

	// Ghi đè cấu hình từ config map nếu có (để tương thích ngược)
	if jwtSecret, ok := config["jwt_secret"].(string); ok && jwtSecret != "" {
		authConfig.JWTSecret = jwtSecret
	}

	if accessExpiry, ok := config["access_token_expiry"].(string); ok && accessExpiry != "" {
		if duration, err := time.ParseDuration(accessExpiry); err == nil {
			authConfig.AccessTokenExpiry = duration
		}
	}

	if refreshExpiry, ok := config["refresh_token_expiry"].(string); ok && refreshExpiry != "" {
		if duration, err := time.ParseDuration(refreshExpiry); err == nil {
			authConfig.RefreshTokenExpiry = duration
		}
	}

	// Log thông tin cấu hình (che giấu secret)
	logger.Info("Cấu hình Auth: AccessTokenExpiry=%v, RefreshTokenExpiry=%v",
		authConfig.AccessTokenExpiry, authConfig.RefreshTokenExpiry)

	// Khởi tạo email service từ notification module
	// Lấy Redis address từ config hoặc sử dụng default
	redisAddr := "localhost:6379" // default
	if redisConfig, ok := config["redis_addr"].(string); ok && redisConfig != "" {
		redisAddr = redisConfig
	}
	emailService := notificationService.NewEmailService(redisAddr)

	// Tạo GORM DB từ sqlx DB để sử dụng cho email verification repository
	sqlxDB := app.GetDBManager().GetDB()
	gormDB, err := gorm.Open(gormMysql.New(gormMysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM for email verification: %w", err)
	}

	// Khởi tạo email verification repository
	emailVerificationRepo := mysql.NewEmailVerificationRepository(gormDB, logger)

	// Tạo AuthService với notification support
	webURL := "http://localhost:3000" // hoặc lấy từ config nếu có
	authService := service.NewServiceWithNotification(repo, *authConfig, logger, emailService, emailVerificationRepo, webURL)

	// Tạo JWTConfig từ AuthConfig
	jwtConfig := pkgauth.JWTConfig{
		AccessSigningKey:       authConfig.JWTSecret,
		RefreshSigningKey:      authConfig.JWTSecret,
		AccessTokenExpiration:  authConfig.AccessTokenExpiry,
		RefreshTokenExpiration: authConfig.RefreshTokenExpiry,
		Issuer:                 authConfig.Issuer,
	}

	// Nếu cần, có thể lấy repository thực tế ở đây
	baseURL := "http://localhost:8080" // hoặc lấy từ config nếu có

	// Tạo passwordResetService với email service
	passwordResetService := service.NewPasswordResetService(
		nil,          // repository thực tế nếu có
		emailService, // email service để gửi email
		baseURL,
		webURL,
	)

	// Tạo handler với AuthService, PasswordResetService và JWTConfig
	handler := api.NewHandler(authService, passwordResetService, jwtConfig)

	return &Module{
		name:    "auth",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
	}, nil
}

// Name trả về tên của module
func (m *Module) Name() string {
	return m.name
}

// Init khởi tạo module
func (m *Module) Init(ctx context.Context) error {
	m.logger.Info("Initializing auth module")
	return nil
}

// RegisterRoutes đăng ký các route của module
func (m *Module) RegisterRoutes(server *core.Server) error {
	if m.handler == nil {
		m.logger.Warn("Auth handler is not initialized, skipping route registration")
		return nil
	}

	err := registerRoutes(server, m.handler)
	if err != nil {
		return err
	}

	return nil
}

// Cleanup dọn dẹp tài nguyên của module
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Cleaning up auth module")
	return nil
}

// GetMigrationPath trả về đường dẫn chứa migrations
func (m *Module) GetMigrationPath() string {
	return filepath.Join("modules", "auth", "migrations")
}

// GetMigrationOrder trả về thứ tự ưu tiên khi chạy migration của module
func (m *Module) GetMigrationOrder() int {
	return 1 // Auth module cần chạy đầu tiên
}
