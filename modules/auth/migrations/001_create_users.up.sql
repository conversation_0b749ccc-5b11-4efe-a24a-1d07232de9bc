CREATE TABLE IF NOT EXISTS users (
  user_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(100),
  user_type <PERSON><PERSON><PERSON>( 'admin', 'tenant', 'customer') NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  last_login TIMESTAMP NULL,
  status ENUM('active', 'inactive', 'suspended', 'pending_verification', 'email_verification_required', 'banned', 'locked', 'deleted') DEFAULT 'pending_verification',
  is_email_verified TINYINT(1) NOT NULL DEFAULT 0,
  INDEX idx_users_email (email),
  INDEX idx_users_tenant_id (tenant_id),
  INDEX idx_users_status (status)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 