CREATE TABLE IF NOT EXISTS user_profiles (
  user_id INT UNSIGNED PRIMARY KEY,
  avatar_url VARCHAR(255),
  phone VARCHAR(20),
  address TEXT,
  city VARCHAR(100),
  state VARCHAR(100),
  country VARCHAR(100),
  postal_code VARCHAR(20),
  date_of_birth DATE,
  gender VARCHAR(20),
  preferred_language VARCHAR(10),
  timezone VARCHAR(50) DEFAULT 'UTC',
  notification_preferences JSON,
  social_linkedin VARCHAR(255),
  social_twitter VARCHAR(255),
  social_facebook VARCHAR(255),
  social_instagram VARCHAR(255),
  skills TEXT,
  interests TEXT,
  professional_title VARCHAR(100),
  department VARCHAR(100),
  hire_date DATE,
  emergency_contact_name VARCHAR(100),
  emergency_contact_phone VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT fk_profile_user FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  INDEX idx_profile_country (country)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 