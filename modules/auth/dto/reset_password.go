package dto

// ResetPasswordRequest đại diện cho yêu cầu đặt lại mật khẩu
type ResetPasswordRequest struct {
	Token           string `json:"token" binding:"required"`
	Password        string `json:"password" binding:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" binding:"required,eqfield=Password"`
}

// ResetPasswordResponse chứa thông tin phản hồi cho việc đặt lại mật khẩu
type ResetPasswordResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}
