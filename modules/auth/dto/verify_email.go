package dto

// VerifyEmailRequest chứa token để xác thực email
type VerifyEmailRequest struct {
	Token string `json:"token" form:"token" binding:"required"`
}

// ResendVerificationEmailRequest chứa email để gửi lại email xác thực
type ResendVerificationEmailRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// VerifyEmailResponse chứa thông tin phản hồi cho việc xác thực email
type VerifyEmailResponse struct {
	Email    string `json:"email"`
	Verified bool   `json:"verified"`
	Message  string `json:"message"`
}
