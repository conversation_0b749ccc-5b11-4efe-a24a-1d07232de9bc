package dto

// SocialLoginRequest represents a request to authenticate using a social token
type SocialLoginRequest struct {
	Provider string `json:"provider" binding:"required"` // "facebook" or "google"
	Token    string `json:"token" binding:"required"`    // OAuth access token from the provider
	Email    string `json:"email"`                       // Optional email for verification
	UserID   string `json:"user_id"`                     // Provider's user ID (optional)
}

// SocialLinkRequest represents a request to link a social account to an existing user
type SocialLinkRequest struct {
	Provider string `json:"provider" binding:"required"` // "facebook" or "google"
	Token    string `json:"token" binding:"required"`    // OAuth access token from the provider
}

// LinkedSocialAccount represents a social account that is linked to a user
type LinkedSocialAccount struct {
	ID              int64  `json:"id"`
	Provider        string `json:"provider"` // "facebook" or "google"
	ProviderUserID  string `json:"provider_user_id"`
	LinkedAt        string `json:"linked_at"`
	LastUsed        string `json:"last_used,omitempty"`
	Email           string `json:"email,omitempty"`
	Name            string `json:"name,omitempty"`
	ProfileImageURL string `json:"profile_image_url,omitempty"`
}

// SocialUserInfo represents user information retrieved from a social provider
type SocialUserInfo struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	Name          string `json:"name"`
	FirstName     string `json:"first_name,omitempty"`
	LastName      string `json:"last_name,omitempty"`
	ProfileImage  string `json:"profile_image,omitempty"`
	VerifiedEmail bool   `json:"verified_email,omitempty"`
}
