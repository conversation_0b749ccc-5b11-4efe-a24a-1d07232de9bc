package dto

import "wnapi/modules/auth/models"

// CreateUserRequest định ngh<PERSON>a cấu trúc yêu cầu tạo người dùng
type CreateUserRequest struct {
	TenantID uint            `json:"tenant_id" binding:"required,min=1"`
	Username string          `json:"username" binding:"required,min=3,max=50"`
	Email    string          `json:"email" binding:"required,email"`
	Password string          `json:"password" binding:"required,min=8"`
	FullName string          `json:"full_name" binding:"required"`
	UserType models.UserType `json:"user_type" binding:"required"`
}

// CreateUserResponse chứa thông tin phản hồi cho việc tạo người dùng
type CreateUserResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message"`
	User    *UserResponse `json:"user,omitempty"`
}
