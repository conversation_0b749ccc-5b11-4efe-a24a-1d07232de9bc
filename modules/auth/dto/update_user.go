package dto

// UpdateUserRequest định nghĩa cấu trúc yêu cầu cập nhật người dùng
type UpdateUserRequest struct {
	Username *string `json:"username" binding:"omitempty,min=3,max=50"`
	Email    *string `json:"email" binding:"omitempty,email"`
	FullName *string `json:"full_name"`
	Status   *string `json:"status" binding:"omitempty,oneof=active inactive suspended banned locked"`
	TenantID *uint   `json:"tenant_id" binding:"omitempty,min=1"`
	UserType *string `json:"user_type" binding:"omitempty,oneof=admin tenant customer"`
}

// UpdateUserResponse chứa thông tin phản hồi cho việc cập nhật người dùng
type UpdateUserResponse struct {
	Success bool         `json:"success"`
	Message string       `json:"message"`
	User    *UserResponse `json:"user,omitempty"`
}
