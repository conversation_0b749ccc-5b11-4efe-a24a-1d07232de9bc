package dto

// RefreshTokenRequest là request body cho API refresh token
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"` // Using snake_case for JSON
}

// RefreshTokenResponse là response cho API refresh token
type RefreshTokenResponse struct {
	AccessToken           string `json:"access_token"`
	AccessTokenExpiresIn  int    `json:"access_token_expires_in"`
	RefreshToken          string `json:"refresh_token"`
	RefreshTokenExpiresIn int    `json:"refresh_token_expires_in"`
	TokenType             string `json:"token_type"`
}
