package dto

// LoginRequest là request body cho API login
type LoginRequest struct {
	Email      string `json:"email" binding:"required,email"`
	Password   string `json:"password" binding:"required,min=8"`
	AdminLogin bool   `json:"-"` // Field used internally, not from JSON
}

// LoginResponse là response cho API login
type LoginResponse struct {
	AccessToken           string `json:"access_token"`
	AccessTokenExpiresIn  int    `json:"access_token_expires_in"`
	RefreshToken          string `json:"refresh_token"`
	RefreshTokenExpiresIn int    `json:"refresh_token_expires_in"`
	TokenType             string `json:"token_type"`
	UserID                int64  `json:"user_id"`
	Email                 string `json:"email"`
	TenantID              int    `json:"tenant_id"`
}
