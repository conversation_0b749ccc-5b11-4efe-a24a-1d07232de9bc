package dto

import (
	"time"

	"wnapi/modules/auth/models"
)

// RegisterRequest là request body cho API register
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
	FullName string `json:"full_name" binding:"required"`
	UserType string `json:"user_type" binding:"omitempty,oneof=admin tenant customer"`
}

// RegisterResponse là response cho API register
type RegisterResponse struct {
	UserID int    `json:"user_id"`
	Status string `json:"status"`
	Email  string `json:"email"`
}

// UserResponse chứa thông tin chi tiết người dùng
type UserResponse struct {
	UserID          uint            `json:"user_id"`
	TenantID        uint            `json:"tenant_id"`
	Username        string          `json:"username"`
	Email           string          `json:"email"`
	FullName        string          `json:"full_name"`
	Phone           string          `json:"phone,omitempty"`
	Role            string          `json:"role,omitempty"`
	Status          string          `json:"status"`
	LastLogin       *time.Time      `json:"last_login,omitempty"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
	IsEmailVerified bool            `json:"is_email_verified"`
	UserType        models.UserType `json:"user_type"`
}

func NewUserResponse(user *models.User) *UserResponse {
	if user == nil {
		return nil
	}

	return &UserResponse{
		UserID:          user.UserID,
		TenantID:        user.TenantID,
		Username:        user.Username,
		Email:           user.Email,
		FullName:        user.FullName,
		Status:          string(user.Status),
		LastLogin:       user.LastLogin,
		CreatedAt:       user.CreatedAt,
		UpdatedAt:       user.UpdatedAt,
		IsEmailVerified: user.IsEmailVerified,
		UserType:        user.UserType,
	}
}
