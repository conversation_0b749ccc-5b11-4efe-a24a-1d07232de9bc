package api

import (
	"os"
	"wnapi/internal/core"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/api/handlers"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/service"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module Auth
type Handler struct {
	adminAuthHandler              *handlers.AdminAuthHandler
	adminPasswordResetHandler     *handlers.AdminPasswordResetHandler
	adminEmailVerificationHandler *handlers.AdminEmailVerificationHandler
	jwtService                    *auth.JWTService
}

// NewHandler tạo một handler mới
func NewHandler(authService internal.AuthService, passwordResetService *service.PasswordResetService, jwtConfig auth.JWTConfig) *Handler {
	// Thiết lập mode cho Gin dựa vào biến môi trường
	appEnv := os.Getenv("APP_ENV")
	if appEnv != "production" {
		gin.SetMode(gin.DebugMode)
	}

	jwtService := auth.NewJWTService(jwtConfig)
	return &Handler{
		adminAuthHandler:              handlers.NewAdminAuthHandler(authService),
		adminPasswordResetHandler:     handlers.NewAdminPasswordResetHandler(passwordResetService),
		adminEmailVerificationHandler: handlers.NewAdminEmailVerificationHandler(authService),
		jwtService:                    jwtService,
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Auth
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/v1/auth")

	// Thêm middleware tracing cho tất cả các route auth
	apiGroup.Use(tracing.GinMiddleware("auth"))

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)

	// ===== BASIC AUTH ROUTES =====
	// Không yêu cầu xác thực
	apiGroup.POST("/login", h.adminAuthHandler.Login)
	apiGroup.POST("/signin", h.adminAuthHandler.Login) // Alias for login
	apiGroup.POST("/register", h.adminAuthHandler.Register)
	apiGroup.POST("/signup", h.adminAuthHandler.Register) // Alias for register
	apiGroup.POST("/refresh-token", h.adminAuthHandler.RefreshToken)

	// ===== PASSWORD RESET ROUTES =====
	apiGroup.POST("/forgot-password", h.adminPasswordResetHandler.ForgotPassword)
	apiGroup.GET("/verify-reset-token", h.adminPasswordResetHandler.VerifyResetToken)
	apiGroup.POST("/reset-password", h.adminPasswordResetHandler.ResetPassword)

	// ===== EMAIL VERIFICATION ROUTES =====
	apiGroup.GET("/verify-email", h.adminEmailVerificationHandler.VerifyEmail)
	apiGroup.POST("/resend-verification", h.adminEmailVerificationHandler.ResendVerification)

	// Yêu cầu xác thực - sử dụng JWT middleware
	authenticated := apiGroup.Group("/")
	authenticated.Use(h.jwtService.JWTAuthMiddleware())
	{
		authenticated.POST("/logout", h.adminAuthHandler.Logout)
		authenticated.PUT("/change-password", h.adminAuthHandler.ChangePassword)
		authenticated.GET("/profile", h.adminAuthHandler.GetProfile)
	}

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "auth",
		"message": "Auth module is running",
	})
}

// AuthHandler is an alias for Handler to maintain backward compatibility
type AuthHandler = Handler

// NewAuthHandler creates a new AuthHandler (alias for NewHandler)
func NewAuthHandler(authService internal.AuthService, passwordResetService *service.PasswordResetService, jwtConfig auth.JWTConfig) *AuthHandler {
	return NewHandler(authService, passwordResetService, jwtConfig)
}
