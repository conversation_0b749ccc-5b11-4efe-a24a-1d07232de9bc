package tracing

import (
	"fmt"
	"log"

	"github.com/webnew/wn-backend-v2/modules/site/configs"
	"github.com/webnew/wn-backend-v2/pkg/tracing"
)

// Global cleanup function
var shutdownFunc func()

// InitTracing initializes the OpenTelemetry tracer based on configuration
func InitTracing(cfg *configs.TracingConfig) error {
	if cfg == nil || !cfg.Enabled {
		log.Println("Tracing is disabled")
		return nil
	}

	serviceName := cfg.ServiceName
	if serviceName == "" {
		serviceName = "site-service"
		log.Println("[WARN] Service name not provided, using default:", serviceName)
	}

	// Initialize tracer based on exporter type
	switch cfg.ExporterType {
	case "jaeger":
		log.Println("Initializing Jaeger tracer")
		if cfg.Jaeger.Host == "" || cfg.Jaeger.Port == "" {
			return fmt.Errorf("jaeger host or port is not configured")
		}
		
		jaegerTracer, jaegerCloser, err := tracing.InitJaeger(serviceName, cfg.Jaeger.Host, cfg.Jaeger.Port)
		if err != nil {
			return fmt.Errorf("failed to initialize Jaeger tracer: %w", err)
		}
		
		// Store the Jaeger tracer for later use
		SetJaegerTracer(jaegerTracer)
		
		// Create a cleanup function
		shutdownFunc = func() {
			if jaegerCloser != nil {
				if err := jaegerCloser.Close(); err != nil {
					log.Printf("Error closing Jaeger tracer: %v", err)
				} else {
					log.Println("Jaeger tracer closed successfully")
				}
			}
		}
		
		log.Printf("Jaeger tracer initialized for service %s with agent at %s:%s", 
			serviceName, cfg.Jaeger.Host, cfg.Jaeger.Port)
		
	case "signoz":
		log.Println("Initializing SigNoz tracer")
		if cfg.Signoz.Endpoint == "" {
			return fmt.Errorf("signoz endpoint is not configured")
		}
		
		signozCleanup, err := tracing.InitSignozTracer(serviceName, cfg.Signoz.Endpoint)
		if err != nil {
			return fmt.Errorf("failed to initialize SigNoz tracer: %w", err)
		}
		
		shutdownFunc = signozCleanup
		log.Printf("SigNoz tracer initialized for service %s with endpoint %s", 
			serviceName, cfg.Signoz.Endpoint)
		
	default:
		return fmt.Errorf("unsupported exporter type: %s", cfg.ExporterType)
	}

	return nil
}

// Shutdown cleans up the tracer
func Shutdown() {
	if shutdownFunc != nil {
		shutdownFunc()
		log.Println("Tracer shutdown completed")
	}
}
