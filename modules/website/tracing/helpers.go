package tracing

import (
	"context"
	"fmt"
	"log"

	"github.com/opentracing/opentracing-go"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"github.com/webnew/wn-backend-v2/modules/site/configs"
	projectTracing "github.com/webnew/wn-backend-v2/pkg/tracing"
)

// Global variables for tracers
var (
	jaegerTracer opentracing.Tracer
)

// SetJaegerTracer sets the global Jaeger tracer
func SetJaegerTracer(tracer opentracing.Tracer) {
	jaegerTracer = tracer
}

// StartSpan starts a new span based on the exporter type
func StartSpan(ctx context.Context, cfg *configs.TracingConfig, operationName string) (context.Context, interface{}) {
	if cfg == nil || !cfg.Enabled {
		return ctx, nil
	}

	switch cfg.ExporterType {
	case "jaeger":
		if jaegerTracer == nil {
			log.Println("[WARN] Jaeger tracer not initialized")
			return ctx, nil
		}
		span := jaegerTracer.StartSpan(operationName)
		ctxWithSpan := opentracing.ContextWithSpan(ctx, span)
		return ctxWithSpan, span
	case "signoz":
		ctxWithSpan, span := projectTracing.StartSpan(ctx, operationName)
		return ctxWithSpan, span
	default:
		log.Printf("[WARN] Unsupported exporter type: %s", cfg.ExporterType)
		return ctx, nil
	}
}

// EndSpan ends a span
func EndSpan(span interface{}) {
	if span == nil {
		return
	}

	switch s := span.(type) {
	case opentracing.Span:
		s.Finish()
	case trace.Span:
		s.End()
	default:
		log.Printf("[WARN] Unknown span type: %T", span)
	}
}

// AddAttribute adds an attribute to a span
func AddAttribute(span interface{}, key string, value interface{}) {
	if span == nil {
		return
	}

	switch s := span.(type) {
	case opentracing.Span:
		s.SetTag(key, value)
	case trace.Span:
		switch v := value.(type) {
		case string:
			s.SetAttributes(attribute.String(key, v))
		case int:
			s.SetAttributes(attribute.Int(key, v))
		case int64:
			s.SetAttributes(attribute.Int64(key, v))
		case float64:
			s.SetAttributes(attribute.Float64(key, v))
		case bool:
			s.SetAttributes(attribute.Bool(key, v))
		default:
			s.SetAttributes(attribute.String(key, fmt.Sprintf("%v", v)))
		}
	default:
		log.Printf("[WARN] Unknown span type: %T", span)
	}
}

// RecordError records an error in a span
func RecordError(span interface{}, err error) {
	if span == nil || err == nil {
		return
	}

	switch s := span.(type) {
	case opentracing.Span:
		s.SetTag("error", true)
		s.LogKV("error.message", err.Error())
	case trace.Span:
		s.RecordError(err)
		s.SetStatus(codes.Error, err.Error())
	default:
		log.Printf("[WARN] Unknown span type: %T", span)
	}
}

// GetSpanFromContext gets the current span from context
func GetSpanFromContext(ctx context.Context) interface{} {
	// For Jaeger
	if jaegerTracer != nil {
		span := opentracing.SpanFromContext(ctx)
		if span != nil {
			return span
		}
	}

	// For OpenTelemetry
	span := trace.SpanFromContext(ctx)
	if span.SpanContext().IsValid() {
		return span
	}

	return nil
}

// Site-specific tracing functions

// TracePageRendering traces a page rendering operation
func TracePageRendering(ctx context.Context, cfg *configs.TracingConfig, pageID int, templateName string) (context.Context, interface{}) {
	ctx, span := StartSpan(ctx, cfg, "site.page_rendering")
	if span != nil {
		AddAttribute(span, "site.page_id", pageID)
		AddAttribute(span, "site.template", templateName)
		AddAttribute(span, "site.operation", "render")
	}
	return ctx, span
}

// TraceThemeManagement traces a theme management operation
func TraceThemeManagement(ctx context.Context, cfg *configs.TracingConfig, themeID int, operation string) (context.Context, interface{}) {
	ctx, span := StartSpan(ctx, cfg, "site.theme_management")
	if span != nil {
		AddAttribute(span, "site.theme_id", themeID)
		AddAttribute(span, "site.operation", operation)
	}
	return ctx, span
}

// TraceSiteSettings traces a site settings operation
func TraceSiteSettings(ctx context.Context, cfg *configs.TracingConfig, siteID int, operation string) (context.Context, interface{}) {
	ctx, span := StartSpan(ctx, cfg, "site.settings")
	if span != nil {
		AddAttribute(span, "site.id", siteID)
		AddAttribute(span, "site.operation", operation)
	}
	return ctx, span
}

// TraceNavigation traces a navigation structure operation
func TraceNavigation(ctx context.Context, cfg *configs.TracingConfig, siteID int, operation string) (context.Context, interface{}) {
	ctx, span := StartSpan(ctx, cfg, "site.navigation")
	if span != nil {
		AddAttribute(span, "site.id", siteID)
		AddAttribute(span, "site.operation", operation)
	}
	return ctx, span
}

// TraceLayoutManagement traces a layout management operation
func TraceLayoutManagement(ctx context.Context, cfg *configs.TracingConfig, pageID int, operation string) (context.Context, interface{}) {
	ctx, span := StartSpan(ctx, cfg, "site.layout_management")
	if span != nil {
		AddAttribute(span, "site.page_id", pageID)
		AddAttribute(span, "site.operation", operation)
	}
	return ctx, span
}

// TraceAssetHandling traces an asset handling operation
func TraceAssetHandling(ctx context.Context, cfg *configs.TracingConfig, assetType string, operation string) (context.Context, interface{}) {
	ctx, span := StartSpan(ctx, cfg, "site.asset_handling")
	if span != nil {
		AddAttribute(span, "site.component", assetType)
		AddAttribute(span, "site.operation", operation)
	}
	return ctx, span
}

// TraceTemplateProcessing traces a template processing operation
func TraceTemplateProcessing(ctx context.Context, cfg *configs.TracingConfig, templateID int, templateName string) (context.Context, interface{}) {
	ctx, span := StartSpan(ctx, cfg, "site.template_processing")
	if span != nil {
		AddAttribute(span, "site.template", templateName)
		AddAttribute(span, "site.operation", "process")
	}
	return ctx, span
}

// TraceDBOperation traces a database operation
func TraceDBOperation(ctx context.Context, cfg *configs.TracingConfig, operation string, table string) (context.Context, interface{}) {
	ctx, span := StartSpan(ctx, cfg, "site.database")
	if span != nil {
		AddAttribute(span, "site.operation", operation)
		AddAttribute(span, "db.table", table)
	}
	return ctx, span
}
