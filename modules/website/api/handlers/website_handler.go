package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"github.com/webnew/wn-backend-v2/modules/site/dto/request"
	"github.com/webnew/wn-backend-v2/modules/site/service"
)

// WebsiteHandler xử lý các HTTP requests liên quan đến website
type WebsiteHandler struct {
	websiteService service.WebsiteService
}

// NewWebsiteHandler tạo một instance mới của WebsiteHandler
func NewWebsiteHandler(websiteService service.WebsiteService) *WebsiteHandler {
	return &WebsiteHandler{
		websiteService: websiteService,
	}
}

// CreateWebsite xử lý request tạo website mới
func (h *WebsiteHandler) CreateWebsite(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Parse request
	var req request.CreateWebsiteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleInvalidInput(c, err)
		return
	}

	// Gọi service để tạo website
	website, err := h.websiteService.CreateWebsite(c.Request.Context(), tenantID, req)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusCreated, "Website đã được tạo thành công", website)
}

// GetWebsite xử lý request lấy thông tin website theo ID
func (h *WebsiteHandler) GetWebsite(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Lấy website ID từ URL path
	websiteID, err := getIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Gọi service để lấy thông tin website
	website, err := h.websiteService.GetWebsite(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusOK, "Thông tin website đã được lấy thành công", website)
}

// UpdateWebsite xử lý request cập nhật website
func (h *WebsiteHandler) UpdateWebsite(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Lấy website ID từ URL path
	websiteID, err := getIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Parse request
	var req request.UpdateWebsiteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handleInvalidInput(c, err)
		return
	}

	// Gọi service để cập nhật website
	website, err := h.websiteService.UpdateWebsite(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusOK, "Website đã được cập nhật thành công", website)
}

// DeleteWebsite xử lý request xóa website
func (h *WebsiteHandler) DeleteWebsite(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Lấy website ID từ URL path
	websiteID, err := getIDParam(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Gọi service để xóa website
	err = h.websiteService.DeleteWebsite(c.Request.Context(), tenantID, websiteID)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	apiSuccess(c, http.StatusOK, "Website đã được xóa thành công", nil)
}

// ListWebsites xử lý request liệt kê danh sách website
func (h *WebsiteHandler) ListWebsites(c *gin.Context) {
	// Lấy tenant ID từ context
	tenantID, err := getTenantIDFromContext(c)
	if err != nil {
		apiError(c, http.StatusBadRequest, ErrCodeInvalidInput, err.Error())
		return
	}

	// Parse query parameters
	var req request.ListWebsiteRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		handleInvalidInput(c, err)
		return
	}

	// Gọi service để lấy danh sách website
	result, err := h.websiteService.ListWebsites(c.Request.Context(), tenantID, req)
	if err != nil {
		handleDBError(c, err)
		return
	}

	// Trả về response thành công
	meta := map[string]interface{}{
		"next_cursor": result.NextCursor,
		"has_more":    result.HasMore,
	}
	apiSuccessWithMeta(c, http.StatusOK, "Danh sách website đã được lấy thành công", result.Websites, meta)
}
