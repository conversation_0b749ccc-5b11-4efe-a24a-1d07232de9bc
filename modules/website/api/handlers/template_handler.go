package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/site/models"
	"github.com/webnew/wn-backend-v2/modules/site/service"
)

// TemplateHandler xử lý các request liên quan đến template
type TemplateHandler struct {
	templateService service.TemplateService
}

// NewTemplateHandler tạo mới template handler
func NewTemplateHandler(templateService service.TemplateService) *TemplateHandler {
	return &TemplateHandler{
		templateService: templateService,
	}
}

// ListTemplates lấy danh sách templates của một theme
func (h *TemplateHandler) ListTemplates(c *gin.Context) {
	themeIDStr := c.Param("theme_id")
	themeID, err := strconv.Atoi(themeIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Theme ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "Theme ID không hợp lệ", "INVALID_THEME_ID", details)
		return
	}

	limit := 10
	cursor := c.Query("cursor")

	templates, nextCursor, hasMore, err := h.templateService.GetTemplates(themeID, limit, cursor)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "Không thể lấy danh sách templates", "TEMPLATE_LIST_ERROR", details)
		return
	}

	meta := map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    hasMore,
	}
	apiSuccessWithMeta(c, http.StatusOK, "Lấy danh sách templates thành công", templates, meta)
}

// ListTemplatesByType lấy danh sách templates theo loại của một theme
func (h *TemplateHandler) ListTemplatesByType(c *gin.Context) {
	themeIDStr := c.Param("theme_id")
	themeID, err := strconv.Atoi(themeIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Theme ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "Theme ID không hợp lệ", "INVALID_THEME_ID", details)
		return
	}

	templateType := c.Param("type")
	if templateType == "" {
		details := []interface{}{map[string]string{"message": "Loại template không thể trống"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "Loại template không hợp lệ", "INVALID_TEMPLATE_TYPE", details)
		return
	}

	limit := 10
	cursor := c.Query("cursor")

	templates, nextCursor, hasMore, err := h.templateService.GetTemplatesByType(themeID, templateType, limit, cursor)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "Không thể lấy danh sách templates theo loại", "TEMPLATE_LIST_BY_TYPE_ERROR", details)
		return
	}

	meta := map[string]interface{}{
		"next_cursor": nextCursor,
		"has_more":    hasMore,
	}
	apiSuccessWithMeta(c, http.StatusOK, "Lấy danh sách templates theo loại thành công", templates, meta)
}

// GetTemplate lấy template theo ID
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID", details)
		return
	}

	template, err := h.templateService.GetTemplateByID(id)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusNotFound, "Template không tồn tại", "TEMPLATE_NOT_FOUND", details)
		return
	}

	apiSuccess(c, http.StatusOK, "Lấy template thành công", template)
}

// CreateTemplate tạo mới template
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	themeIDStr := c.Param("theme_id")
	themeID, err := strconv.Atoi(themeIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Theme ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "Theme ID không hợp lệ", "INVALID_THEME_ID", details)
		return
	}

	var template models.Template
	if err := c.ShouldBindJSON(&template); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_DATA", details)
		return
	}

	// Đảm bảo template thuộc về theme được chỉ định
	template.ThemeID = themeID

	newTemplate, err := h.templateService.CreateTemplate(&template)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "Không thể tạo template", "TEMPLATE_CREATE_ERROR", details)
		return
	}

	apiSuccess(c, http.StatusCreated, "Tạo template thành công", newTemplate)
}

// UpdateTemplate cập nhật template
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	themeIDStr := c.Param("theme_id")
	themeID, err := strconv.Atoi(themeIDStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Theme ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "Theme ID không hợp lệ", "INVALID_THEME_ID", details)
		return
	}

	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID", details)
		return
	}

	var template models.Template
	if err := c.ShouldBindJSON(&template); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusBadRequest, "Dữ liệu không hợp lệ", "INVALID_DATA", details)
		return
	}

	// Đảm bảo template thuộc về theme được chỉ định
	template.ThemeID = themeID

	updatedTemplate, err := h.templateService.UpdateTemplate(id, &template)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "Không thể cập nhật template", "TEMPLATE_UPDATE_ERROR", details)
		return
	}

	apiSuccess(c, http.StatusOK, "Cập nhật template thành công", updatedTemplate)
}

// DeleteTemplate xóa template
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		details := []interface{}{map[string]string{"message": "ID phải là số nguyên"}}
		apiErrorWithDetails(c, http.StatusBadRequest, "ID không hợp lệ", "INVALID_ID", details)
		return
	}

	err = h.templateService.DeleteTemplate(id)
	if err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		apiErrorWithDetails(c, http.StatusInternalServerError, "Không thể xóa template", "TEMPLATE_DELETE_ERROR", details)
		return
	}

	apiSuccess(c, http.StatusOK, "Xóa template thành công", nil)
}
