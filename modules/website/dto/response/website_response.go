package response

import (
	"time"
)

// WebsiteResponse đại diện cho response website
type WebsiteResponse struct {
	ID           int            `json:"id"`
	TenantID     int            `json:"tenant_id"`
	Name         string         `json:"name"`
	Subdomain    *string        `json:"subdomain,omitempty"`
	CustomDomain *string        `json:"custom_domain,omitempty"`
	Description  *string        `json:"description,omitempty"`
	Status       string         `json:"status"`
	ThemeID      *int           `json:"theme_id,omitempty"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	Theme        *ThemeResponse `json:"theme,omitempty"`
}

// WebsiteListResponse đại diện cho response danh sách website
type WebsiteListResponse struct {
	Websites   []WebsiteResponse `json:"websites"`
	NextCursor string            `json:"next_cursor"`
	HasMore    bool              `json:"has_more"`
}
