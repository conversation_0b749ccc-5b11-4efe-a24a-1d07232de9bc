package models

import (
	"time"
)

// Website mô tả một website trong hệ thống
type Website struct {
	WebsiteID    int       `db:"website_id" json:"website_id" gorm:"primaryKey"`
	TenantID     int       `db:"tenant_id" json:"tenant_id"`
	Name         string    `db:"name" json:"name"`
	Subdomain    *string   `db:"subdomain" json:"subdomain,omitempty"`
	CustomDomain *string   `db:"custom_domain" json:"custom_domain,omitempty"`
	Description  *string   `db:"description" json:"description,omitempty"`
	Status       string    `db:"status" json:"status"` // active, inactive, maintenance, building, deleted
	ThemeID      *int      `db:"theme_id" json:"theme_id,omitempty"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`

	// Virtual fields (không lưu trong DB)
	Theme *Theme `db:"-" json:"theme,omitempty"`
}
