package models

import (
	"time"
)

// Theme mô tả một theme trong hệ thống
type Theme struct {
	ThemeID      int       `db:"theme_id" json:"theme_id" gorm:"primaryKey"`
	Name         string    `db:"name" json:"name"`
	Description  *string   `db:"description" json:"description,omitempty"`
	ThumbnailURL *string   `db:"thumbnail_url" json:"thumbnail_url,omitempty"`
	IsPublic     bool      `db:"is_public" json:"is_public"`
	CreatedBy    *int      `db:"created_by" json:"created_by,omitempty"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`

	// Virtual fields (không lưu trong DB)
	Templates []*Template `db:"-" json:"templates,omitempty"`
}
