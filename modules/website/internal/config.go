package internal

import (
	"fmt"
	"log"
	"os"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// WebsiteConfig chứa cấu hình website service
type WebsiteConfig struct {
	SiteName            string `env:"SITE_NAME" envDefault:"My Website"`
	SiteDescription     string `env:"SITE_DESCRIPTION" envDefault:"Website description"`
	ContactEmail        string `env:"CONTACT_EMAIL" envDefault:"<EMAIL>"`
	MaxMenuItems        int    `env:"MAX_MENU_ITEMS" envDefault:"10"`
	MaxBanners          int    `env:"MAX_BANNERS" envDefault:"5"`
	EnableContactForm   bool   `env:"ENABLE_CONTACT_FORM" envDefault:"true"`
	CacheTimeoutMinutes int    `env:"CACHE_TIMEOUT_MINUTES" envDefault:"10"`
	MaxPageSize         int    `env:"MAX_PAGE_SIZE" envDefault:"100"`
	DefaultPageSize     int    `env:"DEFAULT_PAGE_SIZE" envDefault:"10"`
	Message             string `env:"MESSAGE" envDefault:"Xin chào từ module Website!"`
}

// LoadWebsiteConfig đọc cấu hình website từ biến môi trường
func LoadWebsiteConfig() (*WebsiteConfig, error) {
	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	// Khởi tạo config mặc định
	cfg := GetDefaultConfig()

	// Đọc cấu hình từ biến môi trường với prefix WEBSITE_
	opts := env.Options{
		Prefix: "WEBSITE_",
	}
	if err := env.ParseWithOptions(cfg, opts); err != nil {
		return nil, fmt.Errorf("lỗi đọc cấu hình website từ biến môi trường: %w", err)
	}

	return cfg, nil
}

// GetDefaultConfig trả về cấu hình mặc định
func GetDefaultConfig() *WebsiteConfig {
	return &WebsiteConfig{
		SiteName:            "My Website",
		SiteDescription:     "Website description",
		ContactEmail:        "<EMAIL>",
		MaxMenuItems:        10,
		MaxBanners:          5,
		EnableContactForm:   true,
		CacheTimeoutMinutes: 10,
		MaxPageSize:         100,
		DefaultPageSize:     10,
		Message:             "Xin chào từ module Website!",
	}
}
