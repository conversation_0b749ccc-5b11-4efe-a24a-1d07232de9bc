package internal

import (
	"context"
	"net/http"
	"time"
	"wnapi/modules/website/dto"
)

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrPageNotFound là lỗi khi không tìm thấy trang
	ErrPageNotFound ServiceError = "page_not_found"
	// ErrMenuNotFound là lỗi khi không tìm thấy menu
	ErrMenuNotFound ServiceError = "menu_not_found"
	// ErrBannerNotFound là lỗi khi không tìm thấy banner
	ErrBannerNotFound ServiceError = "banner_not_found"
	// ErrInvalidPageData là lỗi khi dữ liệu trang không hợp lệ
	ErrInvalidPageData ServiceError = "invalid_page_data"
	// ErrMaxMenuItemsExceeded là lỗi khi vượt quá số lượng menu items tối đa
	ErrMaxMenuItemsExceeded ServiceError = "max_menu_items_exceeded"
	// ErrMaxBannersExceeded là lỗi khi vượt quá số lượng banners tối đa
	ErrMaxBannersExceeded ServiceError = "max_banners_exceeded"
	// ErrDatabaseError là lỗi khi tương tác với database
	ErrDatabaseError ServiceError = "database_error"
	// ErrInvalidSortField là lỗi khi trường sắp xếp không hợp lệ
	ErrInvalidSortField ServiceError = "invalid_sort_field"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrPageNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy trang",
		ErrorCode:  "PAGE_NOT_FOUND",
	},
	ErrMenuNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy menu",
		ErrorCode:  "MENU_NOT_FOUND",
	},
	ErrBannerNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy banner",
		ErrorCode:  "BANNER_NOT_FOUND",
	},
	ErrInvalidPageData: {
		StatusCode: http.StatusBadRequest,
		Message:    "Dữ liệu trang không hợp lệ",
		ErrorCode:  "INVALID_PAGE_DATA",
	},
	ErrMaxMenuItemsExceeded: {
		StatusCode: http.StatusBadRequest,
		Message:    "Đã vượt quá số lượng menu items tối đa",
		ErrorCode:  "MAX_MENU_ITEMS_EXCEEDED",
	},
	ErrMaxBannersExceeded: {
		StatusCode: http.StatusBadRequest,
		Message:    "Đã vượt quá số lượng banners tối đa",
		ErrorCode:  "MAX_BANNERS_EXCEEDED",
	},
	ErrDatabaseError: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi cơ sở dữ liệu",
		ErrorCode:  "DATABASE_ERROR",
	},
	ErrInvalidSortField: {
		StatusCode: http.StatusBadRequest,
		Message:    "Trường sắp xếp không hợp lệ",
		ErrorCode:  "INVALID_SORT_FIELD",
	},
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}

	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// Page định nghĩa cấu trúc dữ liệu cho trang
type Page struct {
	ID            int64      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Title         string     `gorm:"column:title;size:200;not null" json:"title"`
	Slug          string     `gorm:"column:slug;size:255;uniqueIndex" json:"slug"`
	Content       string     `gorm:"column:content;type:text" json:"content"`
	Description   string     `gorm:"column:description;size:500" json:"description"`
	Published     bool       `gorm:"column:published;default:false" json:"published"`
	PublishedAt   *time.Time `gorm:"column:published_at" json:"published_at"`
	FeaturedImage string     `gorm:"column:featured_image;size:255" json:"featured_image"`
	CreatedAt     time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (Page) TableName() string {
	return "website_pages"
}

// Menu định nghĩa cấu trúc dữ liệu cho menu
type Menu struct {
	ID        int64      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name      string     `gorm:"column:name;size:100;not null" json:"name"`
	Position  string     `gorm:"column:position;size:50;not null" json:"position"`
	IsActive  bool       `gorm:"column:is_active;default:true" json:"is_active"`
	Items     []MenuItem `gorm:"-" json:"items"` // Được nạp riêng, không lưu trực tiếp trong DB
	CreatedAt time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (Menu) TableName() string {
	return "website_menus"
}

// MenuItem định nghĩa cấu trúc dữ liệu cho mục menu
type MenuItem struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	MenuID    int64     `gorm:"column:menu_id;index" json:"menu_id"`
	ParentID  *int64    `gorm:"column:parent_id;index" json:"parent_id"`
	Title     string    `gorm:"column:title;size:100;not null" json:"title"`
	URL       string    `gorm:"column:url;size:255" json:"url"`
	Target    string    `gorm:"column:target;size:20;default:_self" json:"target"`
	Order     int       `gorm:"column:order;default:0" json:"order"`
	IsActive  bool      `gorm:"column:is_active;default:true" json:"is_active"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (MenuItem) TableName() string {
	return "website_menu_items"
}

// Banner định nghĩa cấu trúc dữ liệu cho banner
type Banner struct {
	ID        int64      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Title     string     `gorm:"column:title;size:200;not null" json:"title"`
	ImageURL  string     `gorm:"column:image_url;size:255;not null" json:"image_url"`
	LinkURL   string     `gorm:"column:link_url;size:255" json:"link_url"`
	Position  string     `gorm:"column:position;size:50;not null" json:"position"`
	Order     int        `gorm:"column:order;default:0" json:"order"`
	IsActive  bool       `gorm:"column:is_active;default:true" json:"is_active"`
	StartDate *time.Time `gorm:"column:start_date" json:"start_date"`
	EndDate   *time.Time `gorm:"column:end_date" json:"end_date"`
	CreatedAt time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time  `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (Banner) TableName() string {
	return "website_banners"
}

// Repository định nghĩa interface cho website repository
type Repository interface {
	// Page methods
	CreatePage(ctx context.Context, page *Page) error
	GetPageByID(ctx context.Context, id int64) (*Page, error)
	GetPageBySlug(ctx context.Context, slug string) (*Page, error)
	UpdatePage(ctx context.Context, page *Page) error
	DeletePage(ctx context.Context, id int64) error
	ListPages(ctx context.Context, params dto.ListPagesParams) ([]Page, int64, error)

	// Menu methods
	CreateMenu(ctx context.Context, menu *Menu) error
	GetMenuByID(ctx context.Context, id int64) (*Menu, error)
	GetMenuByPosition(ctx context.Context, position string) (*Menu, error)
	UpdateMenu(ctx context.Context, menu *Menu) error
	DeleteMenu(ctx context.Context, id int64) error
	ListMenus(ctx context.Context, params dto.ListMenusParams) ([]Menu, int64, error)

	// Menu item methods
	CreateMenuItem(ctx context.Context, item *MenuItem) error
	GetMenuItemsByMenuID(ctx context.Context, menuID int64) ([]MenuItem, error)
	UpdateMenuItem(ctx context.Context, item *MenuItem) error
	DeleteMenuItem(ctx context.Context, id int64) error

	// Banner methods
	CreateBanner(ctx context.Context, banner *Banner) error
	GetBannerByID(ctx context.Context, id int64) (*Banner, error)
	UpdateBanner(ctx context.Context, banner *Banner) error
	DeleteBanner(ctx context.Context, id int64) error
	ListBanners(ctx context.Context, params dto.ListBannersParams) ([]Banner, int64, error)
	GetActiveBannersByPosition(ctx context.Context, position string) ([]Banner, error)
}

// WebsiteService định nghĩa interface cho website service
type WebsiteService interface {
	// Page methods
	CreatePage(ctx context.Context, req dto.CreatePageRequest) (*dto.PageResponse, error)
	GetPage(ctx context.Context, id int64) (*dto.PageResponse, error)
	GetPageBySlug(ctx context.Context, slug string) (*dto.PageResponse, error)
	UpdatePage(ctx context.Context, id int64, req dto.UpdatePageRequest) (*dto.PageResponse, error)
	DeletePage(ctx context.Context, id int64) error
	ListPages(ctx context.Context, params dto.ListPagesParams) (*dto.ListPagesResponse, error)

	// Menu methods
	CreateMenu(ctx context.Context, req dto.CreateMenuRequest) (*dto.MenuResponse, error)
	GetMenu(ctx context.Context, id int64) (*dto.MenuResponse, error)
	GetMenuByPosition(ctx context.Context, position string) (*dto.MenuResponse, error)
	UpdateMenu(ctx context.Context, id int64, req dto.UpdateMenuRequest) (*dto.MenuResponse, error)
	DeleteMenu(ctx context.Context, id int64) error
	ListMenus(ctx context.Context, params dto.ListMenusParams) (*dto.ListMenusResponse, error)

	// Menu item methods
	CreateMenuItem(ctx context.Context, req dto.CreateMenuItemRequest) (*dto.MenuItemResponse, error)
	UpdateMenuItem(ctx context.Context, id int64, req dto.UpdateMenuItemRequest) (*dto.MenuItemResponse, error)
	DeleteMenuItem(ctx context.Context, id int64) error

	// Banner methods
	CreateBanner(ctx context.Context, req dto.CreateBannerRequest) (*dto.BannerResponse, error)
	GetBanner(ctx context.Context, id int64) (*dto.BannerResponse, error)
	UpdateBanner(ctx context.Context, id int64, req dto.UpdateBannerRequest) (*dto.BannerResponse, error)
	DeleteBanner(ctx context.Context, id int64) error
	ListBanners(ctx context.Context, params dto.ListBannersParams) (*dto.ListBannersResponse, error)
	GetActiveBannersByPosition(ctx context.Context, position string) (*dto.ActiveBannersResponse, error)
}
