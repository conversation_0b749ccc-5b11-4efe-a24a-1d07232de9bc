package website

import (
	"context"
	"path/filepath"
	"runtime"

	"wnapi/internal/core"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/website/api"
	"wnapi/modules/website/internal"
	"wnapi/modules/website/repository"
	"wnapi/modules/website/service"
)

func init() {
	core.RegisterModuleFactory("website", NewModule)
}

// Module triển khai website module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Khởi tạo repository
	repo, err := repository.NewMySQLRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	// Đọc cấu hình từ biến môi trường
	websiteConfig, err := internal.LoadWebsiteConfig()
	if err != nil {
		logger.Warn("<PERSON>hông thể đọc cấu hình từ biến môi trường, sử dụng giá trị mặc định: %v", err)
		// Sử dụng cấu hình mặc định
		websiteConfig = internal.GetDefaultConfig()
	}

	// Khởi tạo service và handler
	websiteService := service.NewService(repo, *websiteConfig, logger)
	handler := api.NewHandler(websiteService, logger)

	return &Module{
		name:    "website",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
	}, nil
}

// Name trả về tên của module
func (m *Module) Name() string {
	return m.name
}

// Init khởi tạo module
func (m *Module) Init() error {
	m.logger.Info("Khởi tạo module website")
	return nil
}

// RegisterRoutes đăng ký routes của module
func (m *Module) RegisterRoutes(server *core.Server) error {
	m.logger.Info("Đăng ký routes cho module website")
	return registerRoutes(server, m.handler)
}

// Cleanup giải phóng tài nguyên khi shutdown
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Giải phóng tài nguyên module website")
	return nil
}

// GetMigrationPath trả về đường dẫn đến thư mục migrations
func (m *Module) GetMigrationPath() string {
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		return ""
	}
	return filepath.Join(filepath.Dir(filename), "migrations")
}

// GetMigrationOrder trả về thứ tự ưu tiên cho migration
func (m *Module) GetMigrationOrder() int {
	return 20 // Ưu tiên thấp hơn so với module cốt lõi và blog
}
