package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"

	"github.com/webnew/wn-backend-v2/modules/site/dto/request"
	"github.com/webnew/wn-backend-v2/modules/site/models"
	"github.com/webnew/wn-backend-v2/modules/site/repository"
)

// Các errors
var (
	ErrNotFound = errors.New("không tìm thấy website")
	ErrConflict = errors.New("website đã tồn tại")
)

type websiteRepository struct {
	db *sqlx.DB
}

// NewWebsiteRepository tạo một instance mới của WebsiteRepository
func NewWebsiteRepository(db *sqlx.DB) repository.WebsiteRepository {
	return &websiteRepository{
		db: db,
	}
}

// Create tạo một website mới
func (r *websiteRepository) Create(ctx context.Context, website *models.Website) error {
	query := `
		INSERT INTO site_websites (tenant_id, name, subdomain, custom_domain, description, status, theme_id)
		VALUES (?, ?, ?, ?, ?, ?, ?)
	`

	result, err := r.db.ExecContext(
		ctx,
		query,
		website.TenantID,
		website.Name,
		website.Subdomain,
		website.CustomDomain,
		website.Description,
		website.Status,
		website.ThemeID,
	)
	if err != nil {
		// Kiểm tra lỗi unique constraint
		if strings.Contains(err.Error(), "Duplicate entry") {
			return ErrConflict
		}
		return err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return err
	}

	// Set ID cho website
	website.WebsiteID = int(id)

	// Lấy dữ liệu mới nhất
	return r.getWebsiteByID(ctx, website.TenantID, website.WebsiteID, website)
}

// GetByID lấy website theo ID
func (r *websiteRepository) GetByID(ctx context.Context, tenantID, websiteID int) (*models.Website, error) {
	website := &models.Website{}
	err := r.getWebsiteByID(ctx, tenantID, websiteID, website)
	if err != nil {
		return nil, err
	}
	return website, nil
}

// GetBySubdomain lấy website theo subdomain
func (r *websiteRepository) GetBySubdomain(ctx context.Context, subdomain string) (*models.Website, error) {
	query := `
		SELECT * FROM site_websites 
		WHERE subdomain = ?
	`

	website := &models.Website{}
	err := r.db.GetContext(ctx, website, query, subdomain)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, err
	}

	return website, nil
}

// GetByCustomDomain lấy website theo custom domain
func (r *websiteRepository) GetByCustomDomain(ctx context.Context, customDomain string) (*models.Website, error) {
	query := `
		SELECT * FROM site_websites 
		WHERE custom_domain = ?
	`

	website := &models.Website{}
	err := r.db.GetContext(ctx, website, query, customDomain)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrNotFound
		}
		return nil, err
	}

	return website, nil
}

// Update cập nhật website
func (r *websiteRepository) Update(ctx context.Context, website *models.Website) error {
	query := `
		UPDATE site_websites 
		SET name = ?, subdomain = ?, custom_domain = ?, description = ?, status = ?, theme_id = ?
		WHERE website_id = ? AND tenant_id = ?
	`

	result, err := r.db.ExecContext(
		ctx,
		query,
		website.Name,
		website.Subdomain,
		website.CustomDomain,
		website.Description,
		website.Status,
		website.ThemeID,
		website.WebsiteID,
		website.TenantID,
	)
	if err != nil {
		// Kiểm tra lỗi unique constraint
		if strings.Contains(err.Error(), "Duplicate entry") {
			return ErrConflict
		}
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrNotFound
	}

	// Lấy dữ liệu mới nhất
	return r.getWebsiteByID(ctx, website.TenantID, website.WebsiteID, website)
}

// Delete xóa website
func (r *websiteRepository) Delete(ctx context.Context, tenantID, websiteID int) error {
	query := `
		DELETE FROM site_websites 
		WHERE website_id = ? AND tenant_id = ?
	`

	result, err := r.db.ExecContext(ctx, query, websiteID, tenantID)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return ErrNotFound
	}

	return nil
}

// List lấy danh sách website với cursor pagination
func (r *websiteRepository) List(ctx context.Context, tenantID int, req request.ListWebsiteRequest) ([]*models.Website, string, bool, error) {
	// Mặc định limit nếu không được cung cấp
	limit := 10
	if req.Limit > 0 {
		limit = req.Limit
	}
	if limit > 100 {
		limit = 100 // Giới hạn tối đa
	}

	// Xây dựng where clause
	whereClause := "WHERE tenant_id = ?"
	args := []interface{}{tenantID}

	// Tìm kiếm theo từ khóa
	if req.Search != "" {
		whereClause += " AND (name LIKE ? OR subdomain LIKE ? OR custom_domain LIKE ?)"
		searchTerm := "%" + req.Search + "%"
		args = append(args, searchTerm, searchTerm, searchTerm)
	}

	// Lọc theo status
	if req.Status != "" {
		whereClause += " AND status = ?"
		args = append(args, req.Status)
	}

	// Xử lý cursor
	if req.Cursor != "" {
		// Giả sử cursor là ID của website cuối cùng từ lần fetch trước
		websiteID, err := decodeCursor(req.Cursor)
		if err == nil {
			whereClause += " AND website_id > ?"
			args = append(args, websiteID)
		}
	}

	// Xây dựng order clause
	orderClause := "ORDER BY website_id ASC"
	if req.SortBy != "" {
		orderBy := req.SortBy
		// Chỉ cho phép sort theo các trường an toàn
		allowedFields := map[string]bool{
			"website_id": true,
			"name":       true,
			"created_at": true,
			"updated_at": true,
		}
		if allowedFields[orderBy] {
			orderClause = fmt.Sprintf("ORDER BY %s", orderBy)
			if req.SortDir == "desc" {
				orderClause += " DESC"
			} else {
				orderClause += " ASC"
			}
		}
	}

	// Truy vấn danh sách
	query := fmt.Sprintf(`
		SELECT * FROM site_websites 
		%s
		%s
		LIMIT ?
	`, whereClause, orderClause)

	// Thêm limit vào args
	args = append(args, limit+1) // Fetch one more to check if there are more results

	// Thực hiện truy vấn
	websites := []*models.Website{}
	err := r.db.SelectContext(ctx, &websites, query, args...)
	if err != nil {
		return nil, "", false, err
	}

	// Kiểm tra xem có kết quả tiếp theo không
	hasMore := false
	nextCursor := ""

	if len(websites) > limit {
		hasMore = true
		websites = websites[:limit]
	}

	// Tạo cursor cho lần truy vấn tiếp theo
	if len(websites) > 0 && hasMore {
		lastWebsite := websites[len(websites)-1]
		nextCursor = encodeCursor(lastWebsite.WebsiteID)
	}

	return websites, nextCursor, hasMore, nil
}

// Helper functions

// getWebsiteByID helper function để lấy website theo ID
func (r *websiteRepository) getWebsiteByID(ctx context.Context, tenantID, websiteID int, website *models.Website) error {
	query := `
		SELECT * FROM site_websites 
		WHERE website_id = ? AND tenant_id = ?
	`

	err := r.db.GetContext(ctx, website, query, websiteID, tenantID)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrNotFound
		}
		return err
	}

	return nil
}

// decodeCursor giải mã cursor
func decodeCursor(cursor string) (int, error) {
	// Đơn giản hóa bằng cách giả định cursor là websiteID
	var id int
	_, err := fmt.Sscanf(cursor, "%d", &id)
	return id, err
}

// encodeCursor mã hóa cursor
func encodeCursor(id int) string {
	return fmt.Sprintf("%d", id)
}
