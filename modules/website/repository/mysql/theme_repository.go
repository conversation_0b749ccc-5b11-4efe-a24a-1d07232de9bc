package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/site/dto/request"
	"github.com/webnew/wn-backend-v2/modules/site/models"
	"github.com/webnew/wn-backend-v2/modules/site/repository"
)

// ThemeRepository là MySQL implementation của ThemeRepository interface
type ThemeRepository struct {
	db *sqlx.DB
}

// NewThemeRepository tạo mới instance của ThemeRepository
func NewThemeRepository(db *sqlx.DB) repository.ThemeRepository {
	return &ThemeRepository{
		db: db,
	}
}

// Create thêm một theme mới vào database
func (r *ThemeRepository) Create(ctx context.Context, theme *models.Theme) error {
	query := `
		INSERT INTO themes (
			name, description, thumbnail, version, author, is_public, 
			created_at, updated_at
		) VALUES (
			:name, :description, :thumbnail, :version, :author, :is_public, 
			:created_at, :updated_at
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, theme)
	if err != nil {
		return fmt.Errorf("không thể tạo theme: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("không thể lấy ID của theme vừa tạo: %w", err)
	}

	theme.ThemeID = int(id)
	return nil
}

// GetByID lấy theme theo ID
func (r *ThemeRepository) GetByID(ctx context.Context, themeID int) (*models.Theme, error) {
	query := `
		SELECT * FROM themes
		WHERE theme_id = ?
	`

	var theme models.Theme
	err := r.db.GetContext(ctx, &theme, query, themeID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("không thể lấy theme: %w", err)
	}

	return &theme, nil
}

// Update cập nhật theme
func (r *ThemeRepository) Update(ctx context.Context, theme *models.Theme) error {
	query := `
		UPDATE themes
		SET name = :name, 
			description = :description, 
			thumbnail = :thumbnail, 
			version = :version, 
			author = :author, 
			is_public = :is_public, 
			updated_at = :updated_at
		WHERE theme_id = :theme_id
	`

	result, err := r.db.NamedExecContext(ctx, query, theme)
	if err != nil {
		return fmt.Errorf("không thể cập nhật theme: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("lỗi khi kiểm tra số dòng bị ảnh hưởng: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("không tìm thấy theme để cập nhật")
	}

	return nil
}

// Delete xóa theme
func (r *ThemeRepository) Delete(ctx context.Context, themeID int) error {
	query := `
		DELETE FROM themes
		WHERE theme_id = ?
	`

	result, err := r.db.ExecContext(ctx, query, themeID)
	if err != nil {
		return fmt.Errorf("không thể xóa theme: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("lỗi khi kiểm tra số dòng bị ảnh hưởng: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("không tìm thấy theme để xóa")
	}

	return nil
}

// List lấy danh sách themes với phân trang
func (r *ThemeRepository) List(ctx context.Context, req request.ListThemeRequest) ([]*models.Theme, string, bool, error) {
	// Set default sort nếu không được chỉ định
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortDir == "" {
		req.SortDir = "DESC"
	}

	// Setup query cơ bản
	baseQuery := `
		SELECT * FROM themes
		WHERE 1=1
	`

	// Thêm điều kiện tìm kiếm nếu có
	var params []interface{}
	if req.Search != "" {
		baseQuery += " AND (name LIKE ? OR description LIKE ?)"
		searchPattern := "%" + req.Search + "%"
		params = append(params, searchPattern, searchPattern)
	}

	// Thêm điều kiện lọc theo is_public nếu có
	if req.IsPublic != nil {
		baseQuery += " AND is_public = ?"
		params = append(params, *req.IsPublic)
	}

	// Thêm điều kiện phân trang
	if req.Cursor != "" {
		// Xác định điều kiện so sánh dựa trên hướng sắp xếp
		operator := ">"
		if req.SortDir == "DESC" {
			operator = "<"
		}

		// Thêm điều kiện để lấy các bản ghi sau cursor
		baseQuery += fmt.Sprintf(" AND %s %s ?", req.SortBy, operator)
		params = append(params, req.Cursor)
	}

	// Thêm sắp xếp và giới hạn
	baseQuery += fmt.Sprintf(" ORDER BY %s %s LIMIT ?", req.SortBy, req.SortDir)
	params = append(params, req.Limit+1) // +1 để kiểm tra hasMore

	// Thực thi query
	var themes []*models.Theme
	err := r.db.SelectContext(ctx, &themes, baseQuery, params...)
	if err != nil {
		return nil, "", false, fmt.Errorf("không thể lấy danh sách themes: %w", err)
	}

	// Xác định nếu còn dữ liệu
	hasMore := false
	if len(themes) > req.Limit {
		hasMore = true
		themes = themes[:req.Limit] // Cắt bỏ phần tử cuối
	}

	// Xác định nextCursor
	var nextCursor string
	if len(themes) > 0 && hasMore {
		lastTheme := themes[len(themes)-1]
		switch req.SortBy {
		case "created_at":
			nextCursor = lastTheme.CreatedAt.Format(time.RFC3339)
		case "updated_at":
			nextCursor = lastTheme.UpdatedAt.Format(time.RFC3339)
		case "name":
			nextCursor = lastTheme.Name
		default:
			nextCursor = fmt.Sprintf("%v", lastTheme.ThemeID)
		}
	}

	return themes, nextCursor, hasMore, nil
}

// ListPublic lấy danh sách public themes với phân trang
func (r *ThemeRepository) ListPublic(ctx context.Context, req request.ListThemeRequest) ([]*models.Theme, string, bool, error) {
	isPublic := true
	req.IsPublic = &isPublic
	return r.List(ctx, req)
}
