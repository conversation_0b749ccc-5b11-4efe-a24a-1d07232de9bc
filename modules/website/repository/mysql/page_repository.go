package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/site/dto/request"
	"github.com/webnew/wn-backend-v2/modules/site/models"
	"github.com/webnew/wn-backend-v2/modules/site/repository"
)

// PageRepository là MySQL implementation của PageRepository interface
type PageRepository struct {
	db *sqlx.DB
}

// NewPageRepository tạo mới instance của PageRepository
func NewPageRepository(db *sqlx.DB) repository.PageRepository {
	return &PageRepository{
		db: db,
	}
}

// Create thêm một page mới vào database
func (r *PageRepository) Create(ctx context.Context, page *models.Page) error {
	query := `
		INSERT INTO pages (
			website_id, title, slug, content, layout, meta_title, meta_description, 
			is_homepage, status, published_at, created_at, updated_at
		) VALUES (
			:website_id, :title, :slug, :content, :layout, :meta_title, :meta_description, 
			:is_homepage, :status, :published_at, :created_at, :updated_at
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, page)
	if err != nil {
		return fmt.Errorf("không thể tạo page: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("không thể lấy ID của page vừa tạo: %w", err)
	}

	page.PageID = int(id)
	return nil
}

// GetByID lấy page theo ID
func (r *PageRepository) GetByID(ctx context.Context, websiteID, pageID int) (*models.Page, error) {
	query := `
		SELECT * FROM pages
		WHERE page_id = ? AND website_id = ?
	`

	var page models.Page
	err := r.db.GetContext(ctx, &page, query, pageID, websiteID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("không thể lấy page: %w", err)
	}

	return &page, nil
}

// GetBySlug lấy page theo slug
func (r *PageRepository) GetBySlug(ctx context.Context, websiteID int, slug string) (*models.Page, error) {
	query := `
		SELECT * FROM pages
		WHERE slug = ? AND website_id = ?
	`

	var page models.Page
	err := r.db.GetContext(ctx, &page, query, slug, websiteID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("không thể lấy page theo slug: %w", err)
	}

	return &page, nil
}

// GetHomepage lấy trang chủ của website
func (r *PageRepository) GetHomepage(ctx context.Context, websiteID int) (*models.Page, error) {
	query := `
		SELECT * FROM pages
		WHERE website_id = ? AND is_homepage = true
		LIMIT 1
	`

	var page models.Page
	err := r.db.GetContext(ctx, &page, query, websiteID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("không thể lấy trang chủ: %w", err)
	}

	return &page, nil
}

// Update cập nhật page
func (r *PageRepository) Update(ctx context.Context, page *models.Page) error {
	query := `
		UPDATE pages
		SET title = :title, 
			slug = :slug, 
			content = :content, 
			layout = :layout, 
			meta_title = :meta_title, 
			meta_description = :meta_description, 
			is_homepage = :is_homepage, 
			status = :status, 
			published_at = :published_at, 
			updated_at = :updated_at
		WHERE page_id = :page_id AND website_id = :website_id
	`

	result, err := r.db.NamedExecContext(ctx, query, page)
	if err != nil {
		return fmt.Errorf("không thể cập nhật page: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("lỗi khi kiểm tra số dòng bị ảnh hưởng: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("không tìm thấy page để cập nhật")
	}

	return nil
}

// Delete xóa page
func (r *PageRepository) Delete(ctx context.Context, websiteID, pageID int) error {
	query := `
		DELETE FROM pages
		WHERE page_id = ? AND website_id = ?
	`

	result, err := r.db.ExecContext(ctx, query, pageID, websiteID)
	if err != nil {
		return fmt.Errorf("không thể xóa page: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("lỗi khi kiểm tra số dòng bị ảnh hưởng: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("không tìm thấy page để xóa")
	}

	return nil
}

// List lấy danh sách pages với phân trang
func (r *PageRepository) List(ctx context.Context, websiteID int, req request.ListPageRequest) ([]*models.Page, string, bool, error) {
	// Set default sort nếu không được chỉ định
	if req.SortBy == "" {
		req.SortBy = "created_at"
	}
	if req.SortDir == "" {
		req.SortDir = "DESC"
	}

	// Setup query cơ bản
	baseQuery := `
		SELECT * FROM pages
		WHERE website_id = ?
	`

	// Thêm điều kiện tìm kiếm nếu có
	params := []interface{}{websiteID}
	if req.Search != "" {
		baseQuery += " AND (title LIKE ? OR slug LIKE ?)"
		searchPattern := "%" + req.Search + "%"
		params = append(params, searchPattern, searchPattern)
	}

	// Thêm điều kiện lọc theo status nếu có
	if req.Status != "" {
		baseQuery += " AND status = ?"
		params = append(params, req.Status)
	}

	// Thêm điều kiện phân trang
	if req.Cursor != "" {
		// Xác định điều kiện so sánh dựa trên hướng sắp xếp
		operator := ">"
		if req.SortDir == "DESC" {
			operator = "<"
		}

		// Thêm điều kiện để lấy các bản ghi sau cursor
		baseQuery += fmt.Sprintf(" AND %s %s ?", req.SortBy, operator)
		params = append(params, req.Cursor)
	}

	// Thêm sắp xếp và giới hạn
	baseQuery += fmt.Sprintf(" ORDER BY %s %s LIMIT ?", req.SortBy, req.SortDir)
	params = append(params, req.Limit+1) // +1 để kiểm tra hasMore

	// Thực thi query
	var pages []*models.Page
	err := r.db.SelectContext(ctx, &pages, baseQuery, params...)
	if err != nil {
		return nil, "", false, fmt.Errorf("không thể lấy danh sách pages: %w", err)
	}

	// Xác định nếu còn dữ liệu
	hasMore := false
	if len(pages) > req.Limit {
		hasMore = true
		pages = pages[:req.Limit] // Cắt bỏ phần tử cuối
	}

	// Xác định nextCursor
	var nextCursor string
	if len(pages) > 0 && hasMore {
		lastPage := pages[len(pages)-1]
		switch req.SortBy {
		case "created_at":
			nextCursor = lastPage.CreatedAt.Format(time.RFC3339)
		case "updated_at":
			nextCursor = lastPage.UpdatedAt.Format(time.RFC3339)
		case "title":
			nextCursor = lastPage.Title
		default:
			nextCursor = fmt.Sprintf("%v", lastPage.PageID)
		}
	}

	return pages, nextCursor, hasMore, nil
}
