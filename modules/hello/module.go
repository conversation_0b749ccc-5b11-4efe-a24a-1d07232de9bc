package hello

import (
	"context"
	"net/http"
	"path/filepath"

	"wnapi/internal/core"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

func init() {
	core.RegisterModuleFactory("hello", NewModule)
}

// Module triển khai hello module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	message string
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	// Lấy message từ config hoặc sử dụng giá trị mặc định
	message := "Hello, World!"
	if msgConfig, ok := config["message"]; ok {
		if msg, ok := msgConfig.(string); ok {
			message = msg
		}
	}

	return &Module{
		name:    "hello",
		logger:  app.GetLogger(),
		config:  config,
		app:     app,
		message: message,
	}, nil
}

// Name trả về tên của module
func (m *Module) Name() string {
	return m.name
}

// Init khởi tạo module
func (m *Module) Init(ctx context.Context) error {
	m.logger.Info("Initializing hello module")
	return nil
}

// RegisterRoutes đăng ký các route của module
func (m *Module) RegisterRoutes(server *core.Server) error {
	m.logger.Info("Registering hello module routes")
	return registerRoutes(server, m)
}

// Cleanup dọn dẹp tài nguyên của module
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Cleaning up hello module")
	return nil
}

// GetMigrationPath trả về đường dẫn chứa migrations
func (m *Module) GetMigrationPath() string {
	return filepath.Join("modules", "hello", "migrations")
}

// GetMigrationOrder trả về thứ tự ưu tiên khi chạy migration của module
func (m *Module) GetMigrationOrder() int {
	return 10 // Hello module có thể chạy sau các module cốt lõi khác
}

// helloHandler trả về thông điệp chào đơn giản
func (m *Module) helloHandler(c *gin.Context) {
	c.String(http.StatusOK, m.message)
}

// helloJSONHandler trả về thông điệp chào dạng JSON
func (m *Module) helloJSONHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": m.message,
	})
}
