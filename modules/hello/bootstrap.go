package hello

import (
	"wnapi/internal/core"

	"github.com/gin-gonic/gin"
)

// registerRoutes đăng ký các route của module hello
func registerRoutes(server *core.Server, m *Module) error {
	// Tạo router group cho module
	group := server.Group("/hello")

	// Đăng ký các route
	group.GET("", m.helloHandler)
	group.GET("/json", m.helloJSONHandler)
	group.GET("/ping", func(c *gin.Context) {
		c.String(200, "pong")
	})

	return nil
}
