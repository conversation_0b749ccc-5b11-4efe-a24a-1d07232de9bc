package models

import (
	"time"
)

// CartItem represents an item in a shopping cart
type CartItem struct {
	ID            uint64    `db:"id" json:"id"`
	CartID        uint64    `db:"cart_id" json:"cart_id"`
	ItemType      string    `db:"item_type" json:"item_type"` // 'ecom_product', 'dinein_product', etc.
	ReferenceID   string    `db:"reference_id" json:"reference_id"`
	ReferenceType string    `db:"reference_type" json:"reference_type"`
	Quantity      uint      `db:"quantity" json:"quantity"`
	UnitPrice     float64   `db:"unit_price" json:"unit_price"`
	TotalPrice    float64   `db:"total_price" json:"total_price"`
	ItemData      JSON      `db:"item_data" json:"item_data"`
	Options       JSON      `db:"options" json:"options,omitempty"`
	Notes         string    `db:"notes" json:"notes,omitempty"`
	CreatedAt     time.Time `db:"created_at" json:"created_at"`
	UpdatedAt     time.Time `db:"updated_at" json:"updated_at"`

	// Fields not in DB, populated when needed
	Addons []CartItemAddon `db:"-" json:"addons,omitempty"`
}
