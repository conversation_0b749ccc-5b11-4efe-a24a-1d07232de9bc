package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// CartStatus represents the status of a cart
type CartStatus string

const (
	// CartStatusActive represents an active cart
	CartStatusActive CartStatus = "active"
	// CartStatusAbandoned represents an abandoned cart
	CartStatusAbandoned CartStatus = "abandoned"
	// CartStatusConverted represents a cart that has been converted to an order
	CartStatusConverted CartStatus = "converted"
	// CartStatusExpired represents an expired cart
	CartStatusExpired CartStatus = "expired"
)

// JSON is a custom type for handling JSON data in the database
type JSON map[string]interface{}

// Value implements the driver.Valuer interface for JSON
func (j JSON) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// <PERSON>an implements the sql.Scanner interface for JSON
func (j *JSON) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, j)
}

// <PERSON>t represents a shopping cart
type Cart struct {
	ID        uint64     `db:"id" json:"id"`
	TenantID  uint64     `db:"tenant_id" json:"tenant_id"`
	UserID    *uint64    `db:"user_id" json:"user_id,omitempty"`
	SessionID *string    `db:"session_id" json:"session_id,omitempty"`
	CartType  string     `db:"cart_type" json:"cart_type"` // 'ecommerce', 'dinein', etc.
	Status    CartStatus `db:"status" json:"status"`
	Currency  string     `db:"currency" json:"currency"`
	Notes     string     `db:"notes" json:"notes,omitempty"`
	Metadata  JSON       `db:"metadata" json:"metadata,omitempty"`
	ExpiresAt *time.Time `db:"expires_at" json:"expires_at,omitempty"`
	CreatedAt time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt time.Time  `db:"updated_at" json:"updated_at"`

	// Fields not in DB, populated when needed
	Items   []CartItem   `db:"-" json:"items,omitempty"`
	Summary *CartSummary `db:"-" json:"summary,omitempty"`
}
