package models

import (
	"time"
)

// CartSummary represents a summary of a shopping cart
type CartSummary struct {
	ID             uint64    `db:"id" json:"id"`
	CartID         uint64    `db:"cart_id" json:"cart_id"`
	Subtotal       float64   `db:"subtotal" json:"subtotal"`
	DiscountAmount float64   `db:"discount_amount" json:"discount_amount"`
	TaxAmount      float64   `db:"tax_amount" json:"tax_amount"`
	ShippingAmount float64   `db:"shipping_amount" json:"shipping_amount"`
	ServiceFee     float64   `db:"service_fee" json:"service_fee"`
	OtherFees      JSON      `db:"other_fees" json:"other_fees,omitempty"`
	TotalAmount    float64   `db:"total_amount" json:"total_amount"`
	CouponCodes    JSON      `db:"coupon_codes" json:"coupon_codes,omitempty"`
	CreatedAt      time.Time `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time `db:"updated_at" json:"updated_at"`
}
