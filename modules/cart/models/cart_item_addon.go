package models

import (
	"time"
)

// CartItemAddon represents an addon (topping, option, etc.) for a cart item
type CartItemAddon struct {
	ID            uint64    `db:"id" json:"id"`
	CartItemID    uint64    `db:"cart_item_id" json:"cart_item_id"`
	AddonType     string    `db:"addon_type" json:"addon_type"` // 'topping', 'option', 'customization', etc.
	ReferenceID   string    `db:"reference_id" json:"reference_id"`
	ReferenceType string    `db:"reference_type" json:"reference_type"`
	Quantity      uint      `db:"quantity" json:"quantity"`
	UnitPrice     float64   `db:"unit_price" json:"unit_price"`
	TotalPrice    float64   `db:"total_price" json:"total_price"`
	GroupID       *string   `db:"group_id" json:"group_id,omitempty"`
	AddonData     JSON      `db:"addon_data" json:"addon_data"`
	CreatedAt     time.Time `db:"created_at" json:"created_at"`
}
