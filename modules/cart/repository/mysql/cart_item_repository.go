package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/jmoiron/sqlx"

	"github.com/webnew/wn-backend-v2/modules/cart/models"
	"github.com/webnew/wn-backend-v2/modules/cart/repository"
)

// CartItemRepository implements repository.CartItemRepository
type CartItemRepository struct {
	db *sqlx.DB
}

// NewCartItemRepository creates a new instance of CartItemRepository
func NewCartItemRepository(db *sqlx.DB) repository.CartItemRepository {
	return &CartItemRepository{db: db}
}

// Create inserts a new cart item into the database
func (r *CartItemRepository) Create(ctx context.Context, item *models.CartItem) error {
	query := `
		INSERT INTO cart_items (
			cart_id, item_type, reference_id, reference_type, quantity,
			unit_price, total_price, item_data, options, notes, created_at, updated_at
		) VALUES (
			:cart_id, :item_type, :reference_id, :reference_type, :quantity,
			:unit_price, :total_price, :item_data, :options, :notes, :created_at, :updated_at
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, map[string]interface{}{
		"cart_id":        item.CartID,
		"item_type":      item.ItemType,
		"reference_id":   item.ReferenceID,
		"reference_type": item.ReferenceType,
		"quantity":       item.Quantity,
		"unit_price":     item.UnitPrice,
		"total_price":    item.TotalPrice,
		"item_data":      item.ItemData,
		"options":        item.Options,
		"notes":          item.Notes,
		"created_at":     item.CreatedAt,
		"updated_at":     item.UpdatedAt,
	})
	if err != nil {
		return fmt.Errorf("failed to create cart item: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}

	item.ID = uint64(id)
	return nil
}

// GetByID fetches a cart item by ID
func (r *CartItemRepository) GetByID(ctx context.Context, itemID uint64) (*models.CartItem, error) {
	query := `
		SELECT 
			id, cart_id, item_type, reference_id, reference_type, quantity,
			unit_price, total_price, item_data, options, notes, created_at, updated_at
		FROM cart_items
		WHERE id = ?
	`

	var item models.CartItem
	err := r.db.GetContext(ctx, &item, query, itemID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("cart item not found with ID %d", itemID)
		}
		return nil, fmt.Errorf("failed to get cart item: %w", err)
	}

	return &item, nil
}

// Update updates an existing cart item in the database
func (r *CartItemRepository) Update(ctx context.Context, item *models.CartItem) error {
	query := `
		UPDATE cart_items
		SET cart_id = :cart_id,
			item_type = :item_type,
			reference_id = :reference_id,
			reference_type = :reference_type,
			quantity = :quantity,
			unit_price = :unit_price,
			total_price = :total_price,
			item_data = :item_data,
			options = :options,
			notes = :notes,
			updated_at = :updated_at
		WHERE id = :id
	`

	_, err := r.db.NamedExecContext(ctx, query, map[string]interface{}{
		"id":             item.ID,
		"cart_id":        item.CartID,
		"item_type":      item.ItemType,
		"reference_id":   item.ReferenceID,
		"reference_type": item.ReferenceType,
		"quantity":       item.Quantity,
		"unit_price":     item.UnitPrice,
		"total_price":    item.TotalPrice,
		"item_data":      item.ItemData,
		"options":        item.Options,
		"notes":          item.Notes,
		"updated_at":     item.UpdatedAt,
	})
	if err != nil {
		return fmt.Errorf("failed to update cart item: %w", err)
	}

	return nil
}

// UpdateQuantity updates the quantity of a cart item
func (r *CartItemRepository) UpdateQuantity(ctx context.Context, itemID uint64, quantity uint) error {
	query := `
		UPDATE cart_items
		SET quantity = ?,
			total_price = unit_price * ?,
			updated_at = NOW()
		WHERE id = ?
	`

	_, err := r.db.ExecContext(ctx, query, quantity, quantity, itemID)
	if err != nil {
		return fmt.Errorf("failed to update cart item quantity: %w", err)
	}

	return nil
}

// Delete deletes a cart item from the database
func (r *CartItemRepository) Delete(ctx context.Context, itemID uint64) error {
	query := `DELETE FROM cart_items WHERE id = ?`

	_, err := r.db.ExecContext(ctx, query, itemID)
	if err != nil {
		return fmt.Errorf("failed to delete cart item: %w", err)
	}

	return nil
}

// GetByCartID fetches all items for a cart
func (r *CartItemRepository) GetByCartID(ctx context.Context, cartID uint64) ([]*models.CartItem, error) {
	query := `
		SELECT 
			id, cart_id, item_type, reference_id, reference_type, quantity,
			unit_price, total_price, item_data, options, notes, created_at, updated_at
		FROM cart_items
		WHERE cart_id = ?
		ORDER BY created_at ASC
	`

	var items []*models.CartItem
	err := r.db.SelectContext(ctx, &items, query, cartID)
	if err != nil {
		return nil, fmt.Errorf("failed to get cart items: %w", err)
	}

	return items, nil
}

// DeleteByCartID deletes all items for a cart
func (r *CartItemRepository) DeleteByCartID(ctx context.Context, cartID uint64) error {
	query := `DELETE FROM cart_items WHERE cart_id = ?`

	_, err := r.db.ExecContext(ctx, query, cartID)
	if err != nil {
		return fmt.Errorf("failed to delete cart items: %w", err)
	}

	return nil
}
