package internal

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// CartConfig chứa cấu hình cho cart module
type CartConfig struct {
	// Thông tin cơ bản
	Message string `env:"CART_MESSAGE" envDefault:"Xin chào từ module Cart!"`
	
	// Cấu hình liên quan đến cart
	CartExpiryTime time.Duration `env:"CART_EXPIRY_TIME" envDefault:"168h"` // 7 ngày
	MaxCartItems   int           `env:"MAX_CART_ITEMS" envDefault:"50"`
	
	// Cấu hình liên quan đến coupon
	EnableCoupons  bool   `env:"ENABLE_COUPONS" envDefault:"true"`
	CouponAPIURL   string `env:"COUPON_API_URL" envDefault:"http://localhost:8080/api/coupons"`
	
	// Cấu hình liên quan đến pricing
	TaxRate        float64 `env:"TAX_RATE" envDefault:"0.1"` // 10%
	ShippingMethod string  `env:"SHIPPING_METHOD" envDefault:"standard"`
}

// LoadCartConfig đọc cấu hình cart từ biến môi trường
func LoadCartConfig() (*CartConfig, error) {
	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	// Đọc cấu hình từ biến môi trường
	config := &CartConfig{}
	if err := env.Parse(config); err != nil {
		return nil, fmt.Errorf("không thể đọc cấu hình Cart từ biến môi trường: %w", err)
	}

	return config, nil
}
