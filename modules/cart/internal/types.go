package internal

import "net/http"

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrCartNotFound là lỗi khi không tìm thấy cart
	ErrCartNotFound ServiceError = "cart_not_found"
	// ErrCartItemNotFound là lỗi khi không tìm thấy cart item
	ErrCartItemNotFound ServiceError = "cart_item_not_found"
	// ErrCartItemAddonNotFound là lỗi khi không tìm thấy cart item addon
	ErrCartItemAddonNotFound ServiceError = "cart_item_addon_not_found"
	// ErrMaxCartItemsExceeded là lỗi khi vượt quá số lượng item tối đa trong cart
	ErrMaxCartItemsExceeded ServiceError = "max_cart_items_exceeded"
	// ErrInvalidCartID là lỗi khi ID cart không hợp lệ
	ErrInvalidCartID ServiceError = "invalid_cart_id"
	// ErrInvalidCartItemID là lỗi khi ID cart item không hợp lệ
	ErrInvalidCartItemID ServiceError = "invalid_cart_item_id"
	// ErrInvalidCartItemAddonID là lỗi khi ID cart item addon không hợp lệ
	ErrInvalidCartItemAddonID ServiceError = "invalid_cart_item_addon_id"
	// ErrInvalidQuantity là lỗi khi số lượng không hợp lệ
	ErrInvalidQuantity ServiceError = "invalid_quantity"
	// ErrInvalidPrice là lỗi khi giá không hợp lệ
	ErrInvalidPrice ServiceError = "invalid_price"
	// ErrCouponNotFound là lỗi khi không tìm thấy coupon
	ErrCouponNotFound ServiceError = "coupon_not_found"
	// ErrCouponExpired là lỗi khi coupon đã hết hạn
	ErrCouponExpired ServiceError = "coupon_expired"
	// ErrCouponNotApplicable là lỗi khi coupon không áp dụng được
	ErrCouponNotApplicable ServiceError = "coupon_not_applicable"
	// ErrDatabaseError là lỗi khi có vấn đề với database
	ErrDatabaseError ServiceError = "database_error"
	// ErrInternalServerError là lỗi server nội bộ
	ErrInternalServerError ServiceError = "internal_server_error"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrCartNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy giỏ hàng",
		ErrorCode:  "CART_NOT_FOUND",
	},
	ErrCartItemNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy sản phẩm trong giỏ hàng",
		ErrorCode:  "CART_ITEM_NOT_FOUND",
	},
	ErrCartItemAddonNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy phụ kiện của sản phẩm trong giỏ hàng",
		ErrorCode:  "CART_ITEM_ADDON_NOT_FOUND",
	},
	ErrMaxCartItemsExceeded: {
		StatusCode: http.StatusBadRequest,
		Message:    "Vượt quá số lượng sản phẩm tối đa trong giỏ hàng",
		ErrorCode:  "MAX_CART_ITEMS_EXCEEDED",
	},
	ErrInvalidCartID: {
		StatusCode: http.StatusBadRequest,
		Message:    "ID giỏ hàng không hợp lệ",
		ErrorCode:  "INVALID_CART_ID",
	},
	ErrInvalidCartItemID: {
		StatusCode: http.StatusBadRequest,
		Message:    "ID sản phẩm trong giỏ hàng không hợp lệ",
		ErrorCode:  "INVALID_CART_ITEM_ID",
	},
	ErrInvalidCartItemAddonID: {
		StatusCode: http.StatusBadRequest,
		Message:    "ID phụ kiện của sản phẩm trong giỏ hàng không hợp lệ",
		ErrorCode:  "INVALID_CART_ITEM_ADDON_ID",
	},
	ErrInvalidQuantity: {
		StatusCode: http.StatusBadRequest,
		Message:    "Số lượng không hợp lệ",
		ErrorCode:  "INVALID_QUANTITY",
	},
	ErrInvalidPrice: {
		StatusCode: http.StatusBadRequest,
		Message:    "Giá không hợp lệ",
		ErrorCode:  "INVALID_PRICE",
	},
	ErrCouponNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy mã giảm giá",
		ErrorCode:  "COUPON_NOT_FOUND",
	},
	ErrCouponExpired: {
		StatusCode: http.StatusBadRequest,
		Message:    "Mã giảm giá đã hết hạn",
		ErrorCode:  "COUPON_EXPIRED",
	},
	ErrCouponNotApplicable: {
		StatusCode: http.StatusBadRequest,
		Message:    "Mã giảm giá không áp dụng được cho giỏ hàng này",
		ErrorCode:  "COUPON_NOT_APPLICABLE",
	},
	ErrDatabaseError: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi cơ sở dữ liệu",
		ErrorCode:  "DATABASE_ERROR",
	},
	ErrInternalServerError: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống nội bộ",
		ErrorCode:  "INTERNAL_SERVER_ERROR",
	},
}
