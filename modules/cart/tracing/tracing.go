package tracing

import (
	"context"
	"log"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"github.com/webnew/wn-backend-v2/modules/cart/configs"
)

var tracer trace.Tracer

// InitTracing initializes OpenTelemetry tracing
func InitTracing(cfg *configs.Config) error {
	if !cfg.Tracing.Enabled {
		log.Println("Tracing is disabled")
		return nil
	}

	// In a real implementation, you would set up an exporter and provider here
	// For now, we'll just create a no-op tracer
	tracer = otel.Tracer(cfg.Tracing.ServiceName)

	log.Printf("Tracing initialized for service: %s", cfg.Tracing.ServiceName)
	return nil
}

// CloseTracer closes the tracer provider
func CloseTracer() {
	// In a real implementation, you would close the tracer provider here
	log.Println("Tracer closed")
}

// StartSpan starts a new span with the given name
func StartSpan(ctx context.Context, name string) (context.Context, trace.Span) {
	if tracer == nil {
		// Return a no-op span if tracing is not initialized
		return ctx, trace.SpanFromContext(ctx)
	}
	return tracer.Start(ctx, name)
}

// GetTracer returns the tracer
func GetTracer() trace.Tracer {
	if tracer == nil {
		// Return a no-op tracer if tracing is not initialized
		return otel.Tracer("cart-service-noop")
	}
	return tracer
}

// AddAttribute adds an attribute to the current span
func AddAttribute(ctx context.Context, key string, value interface{}) {
	// In a real implementation, you would add attributes to the span here
	// For now, we'll just log the attribute
	log.Printf("Span attribute: %s=%v", key, value)
}

// RecordError records an error in the current span
func RecordError(ctx context.Context, err error) {
	if err == nil {
		return
	}
	// In a real implementation, you would record the error in the span here
	// For now, we'll just log the error
	log.Printf("Span error: %v", err)
}

// EndSpan ends the current span
func EndSpan(span trace.Span) {
	if span != nil {
		span.End()
	}
}
