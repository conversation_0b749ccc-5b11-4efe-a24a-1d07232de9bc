package cart

import (
	"context"
	"path/filepath"
	"time"

	"wnapi/internal/core"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/cart/api"
	"wnapi/modules/cart/internal"
	"wnapi/modules/cart/repository/mysql"
	"wnapi/modules/cart/service"
)

func init() {
	core.RegisterModuleFactory("cart", NewModule)
}

// Module triển khai cart module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Khởi tạo repository
	cartRepo, err := mysql.NewCartRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	cartItemRepo, err := mysql.NewCartItemRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	cartItemAddonRepo, err := mysql.NewCartItemAddonRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	cartSummaryRepo, err := mysql.NewCartSummaryRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	// Đọc cấu hình từ biến môi trường
	cartConfig, err := internal.LoadCartConfig()
	if err != nil {
		logger.Warn("Không thể đọc cấu hình từ biến môi trường, sử dụng giá trị mặc định: %v", err)
		// Khởi tạo cấu hình mặc định nếu cần
	}

	// Khởi tạo service và handler
	cartService := service.NewCartService(cartRepo, cartItemRepo, cartItemAddonRepo, cartSummaryRepo, logger)
	handler := api.NewHandler(cartService)

	return &Module{
		name:    "cart",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
	}, nil
}

// Name trả về tên của module
func (m *Module) Name() string {
	return m.name
}

// Init khởi tạo module
func (m *Module) Init(ctx context.Context) error {
	m.logger.Info("Initializing cart module")
	return nil
}

// RegisterRoutes đăng ký các route của module
func (m *Module) RegisterRoutes(server *core.Server) error {
	if m.handler == nil {
		m.logger.Warn("Cart handler is not initialized, skipping route registration")
		return nil
	}

	err := registerRoutes(server, m.handler)
	if err != nil {
		return err
	}

	return nil
}

// Cleanup dọn dẹp tài nguyên của module
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Cleaning up cart module")
	return nil
}

// GetMigrationPath trả về đường dẫn chứa migrations
func (m *Module) GetMigrationPath() string {
	return filepath.Join("modules", "cart", "migrations", "mysql")
}

// GetMigrationOrder trả về thứ tự ưu tiên khi chạy migration của module
func (m *Module) GetMigrationOrder() int {
	return 2 // Cart module chạy sau Auth module
}
