package api

import (
	"fmt"
	"wnapi/internal/core"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/middleware"
	"wnapi/modules/cart/api/handlers"
	"wnapi/modules/cart/service"
)

// Handler là đối tượng chính xử lý API cho module Cart
type Handler struct {
	cartService service.CartService
	jwtService  *auth.JWTService
	routes      []string
}

// NewHandler tạo một handler mới
func NewHandler(cartService service.CartService) *Handler {
	return &Handler{
		cartService: cartService,
		routes:      []string{},
	}
}

// RegisterRoutes đăng ký các route của module Cart
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// Lấy router từ server
	router := server.GetRouter()
	
	// Khởi tạo JWT service
	jwtService := auth.NewJWTService(auth.JWTConfig{
		AccessSigningKey:       "your-access-signing-key",  // <PERSON><PERSON><PERSON> hình từ env
		RefreshSigningKey:      "your-refresh-signing-key", // <PERSON><PERSON><PERSON> hình từ env
		AccessTokenExpiration:  24 * 60 * 60,               // 24 giờ
		RefreshTokenExpiration: 7 * 24 * 60 * 60,           // 7 ngày
		Issuer:                 "wnapi-cart-module",
	})
	
	// Khởi tạo cart handler
	cartHandler := handlers.NewCartHandler(h.cartService, jwtService)
	
	// Định nghĩa base path
	basePath := "/api/v1/carts"
	
	// Định nghĩa API group
	apiGroup := router.Group(basePath)
	
	// Thêm route health check
	apiGroup.GET("/health", h.healthCheck)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/health", basePath))
	
	// Public routes (không yêu cầu xác thực)
	apiGroup.POST("", cartHandler.GetOrCreateCart)
	h.routes = append(h.routes, fmt.Sprintf("POST %s", basePath))
	
	// Routes yêu cầu xác thực
	authedGroup := apiGroup.Group("", middleware.JWTAuth(jwtService))
	
	// Danh sách giỏ hàng (yêu cầu quyền)
	authedGroup.GET("", middleware.RequirePermission("cart.carts.list"), cartHandler.ListCarts)
	h.routes = append(h.routes, fmt.Sprintf("GET %s (auth)", basePath))
	
	// Get, update, delete giỏ hàng
	authedGroup.GET("/:id", cartHandler.GetCartByID)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/:id (auth)", basePath))
	
	authedGroup.PUT("/:id", cartHandler.UpdateCart)
	h.routes = append(h.routes, fmt.Sprintf("PUT %s/:id (auth)", basePath))
	
	authedGroup.DELETE("/:id", middleware.RequirePermission("cart.carts.delete"), cartHandler.DeleteCart)
	h.routes = append(h.routes, fmt.Sprintf("DELETE %s/:id (auth)", basePath))
	
	// Cart item operations
	authedGroup.POST("/:id/items", cartHandler.AddItemToCart)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/:id/items (auth)", basePath))
	
	authedGroup.PUT("/items/:itemId", cartHandler.UpdateCartItem)
	h.routes = append(h.routes, fmt.Sprintf("PUT %s/items/:itemId (auth)", basePath))
	
	authedGroup.DELETE("/items/:itemId", cartHandler.RemoveCartItem)
	h.routes = append(h.routes, fmt.Sprintf("DELETE %s/items/:itemId (auth)", basePath))
	
	// Cart item addon operations
	authedGroup.POST("/items/:itemId/addons", cartHandler.AddAddonToCartItem)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/items/:itemId/addons (auth)", basePath))
	
	authedGroup.DELETE("/addons/:addonId", cartHandler.RemoveAddonFromCartItem)
	h.routes = append(h.routes, fmt.Sprintf("DELETE %s/addons/:addonId (auth)", basePath))
	
	// Cart operations
	authedGroup.POST("/:id/recalculate", cartHandler.RecalculateCart)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/:id/recalculate (auth)", basePath))
	
	authedGroup.POST("/:id/apply-coupon", cartHandler.ApplyCoupon)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/:id/apply-coupon (auth)", basePath))
	
	authedGroup.POST("/:id/convert-to-order", middleware.RequirePermission("cart.carts.convert"), cartHandler.ConvertCartToOrder)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/:id/convert-to-order (auth)", basePath))
	
	authedGroup.POST("/merge", middleware.RequirePermission("cart.carts.merge"), cartHandler.MergeAnonymousCart)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/merge (auth)", basePath))
	
	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *core.Context) {
	c.JSON(200, map[string]interface{}{
		"status":  "ok",
		"module":  "cart",
		"message": "Cart module is running",
	})
}
