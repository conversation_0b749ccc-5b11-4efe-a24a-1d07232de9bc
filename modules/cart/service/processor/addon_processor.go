package processor

import (
	"context"
	"fmt"
)

// AddonProcessor defines the interface for processing different types of cart item addons
type AddonProcessor interface {
	ValidateAddon(ctx context.Context, tenantID int64, referenceID string, itemReferenceID string, quantity int) error
	CalculateAddonPrice(ctx context.Context, referenceID string, itemReferenceID string, quantity int) (float64, error)
	FetchAddonData(ctx context.Context, referenceID string) (map[string]interface{}, error)
}

// GetAddonProcessor returns the appropriate addon processor for the given addon type
func GetAddonProcessor(addonType string) (AddonProcessor, error) {
	switch addonType {
	case "topping":
		return &ToppingProcessor{}, nil
	case "option":
		return &OptionProcessor{}, nil
	case "customization":
		return &CustomizationProcessor{}, nil
	default:
		return nil, fmt.Errorf("unknown addon type: %s", addonType)
	}
}
