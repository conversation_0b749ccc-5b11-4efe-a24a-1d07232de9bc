package processor

import (
	"context"
	"fmt"
)

// ItemProcessor defines the interface for processing different types of cart items
type ItemProcessor interface {
	ValidateItem(ctx context.Context, tenantID int64, referenceID string, quantity int) error
	CalculateItemPrice(ctx context.Context, referenceID string, quantity int, options map[string]interface{}) (float64, error)
	FetchItemData(ctx context.Context, referenceID string) (map[string]interface{}, error)
}

// GetItemProcessor returns the appropriate item processor for the given item type
func GetItemProcessor(itemType string) (ItemProcessor, error) {
	switch itemType {
	case "ecom_product":
		return &EcomProductProcessor{}, nil
	case "dinein_product":
		return &DineInProductProcessor{}, nil
	case "service":
		return &ServiceProcessor{}, nil
	default:
		return nil, fmt.Errorf("unknown item type: %s", itemType)
	}
}
