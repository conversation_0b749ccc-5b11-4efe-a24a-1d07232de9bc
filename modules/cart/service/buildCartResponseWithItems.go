package service

import (
	"context"
	"encoding/json"

	"github.com/webnew/wn-backend-v2/modules/cart/dto/response"
	"github.com/webnew/wn-backend-v2/modules/cart/models"
)

// buildCartResponseWithItems xây dựng response với items
func (s *CartServiceImpl) buildCartResponseWithItems(ctx context.Context, cart *models.Cart, summary *models.CartSummary, items []*models.CartItem) (*response.CartResponse, error) {
	// Create response items
	itemResponses := make([]*response.CartItemResponse, 0, len(items))
	for _, item := range items {
		// Get addons for this item
		addons, err := s.cartItemAddonRepo.GetByCartItemID(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		// Create addon responses
		addonResponses := make([]*response.CartItemAddonResponse, 0, len(addons))
		for _, addon := range addons {
			// Convert GroupID to string (handling nil)
			groupID := ""
			if addon.GroupID != nil {
				groupID = *addon.GroupID
			}

			addonResponses = append(addonResponses, &response.CartItemAddonResponse{
				ID:            int64(addon.ID),
				CartItemID:    int64(addon.CartItemID),
				AddonType:     addon.AddonType,
				ReferenceID:   addon.ReferenceID,
				ReferenceType: addon.ReferenceType,
				Quantity:      int(addon.Quantity),
				UnitPrice:     addon.UnitPrice,
				TotalPrice:    addon.TotalPrice,
				GroupID:       groupID,
				AddonData:     convertJSONToRawMessage(addon.AddonData),
				CreatedAt:     addon.CreatedAt,
			})
		}

		itemResponses = append(itemResponses, &response.CartItemResponse{
			ID:            int64(item.ID),
			CartID:        int64(item.CartID),
			ItemType:      item.ItemType,
			ReferenceID:   item.ReferenceID,
			ReferenceType: item.ReferenceType,
			Quantity:      int(item.Quantity),
			UnitPrice:     item.UnitPrice,
			TotalPrice:    item.TotalPrice,
			ItemData:      convertJSONToRawMessage(item.ItemData),
			Options:       convertJSONToRawMessage(item.Options),
			Notes:         item.Notes,
			CreatedAt:     item.CreatedAt,
			UpdatedAt:     item.UpdatedAt,
			Addons:        addonResponses,
		})
	}

	// Convert JSON types for cart
	var metadataJSON json.RawMessage
	if cart.Metadata != nil {
		metadataJSON = convertJSONToRawMessage(cart.Metadata)
	}

	// Convert JSON types for summary
	var otherFeesJSON json.RawMessage
	if summary.OtherFees != nil {
		otherFeesJSON = convertJSONToRawMessage(summary.OtherFees)
	}

	var couponCodesJSON json.RawMessage
	if summary.CouponCodes != nil {
		couponCodesJSON = convertJSONToRawMessage(summary.CouponCodes)
	}

	// Create the complete cart response
	return &response.CartResponse{
		ID:        int64(cart.ID),
		TenantID:  int64(cart.TenantID),
		UserID:    convertUint64PtrToInt64Ptr(cart.UserID),
		SessionID: cart.SessionID,
		CartType:  cart.CartType,
		Status:    string(cart.Status),
		Currency:  cart.Currency,
		Notes:     cart.Notes,
		Metadata:  metadataJSON,
		ExpiresAt: cart.ExpiresAt,
		CreatedAt: cart.CreatedAt,
		UpdatedAt: cart.UpdatedAt,
		Items:     itemResponses,
		Summary: &response.CartSummaryResponse{
			ID:             int64(summary.ID),
			CartID:         int64(summary.CartID),
			Subtotal:       summary.Subtotal,
			DiscountAmount: summary.DiscountAmount,
			TaxAmount:      summary.TaxAmount,
			ShippingAmount: summary.ShippingAmount,
			ServiceFee:     summary.ServiceFee,
			OtherFees:      otherFeesJSON,
			TotalAmount:    summary.TotalAmount,
			CouponCodes:    couponCodesJSON,
			CreatedAt:      summary.CreatedAt,
			UpdatedAt:      summary.UpdatedAt,
		},
	}, nil
}
