package service

import (
	"context"

	"github.com/webnew/wn-backend-v2/modules/cart/dto/request"
	"github.com/webnew/wn-backend-v2/modules/cart/dto/response"
)

// CartService defines the interface for cart business logic
type CartService interface {
	// Cart management
	GetOrCreateCart(ctx context.Context, req request.GetOrCreateCartRequest) (*response.CartResponse, error)
	GetCartByID(ctx context.Context, cartID int64) (*response.CartResponse, error)
	GetCartWithItems(ctx context.Context, cartID int64) (*response.CartResponse, error)
	UpdateCart(ctx context.Context, cartID int64, req request.UpdateCartRequest) (*response.CartResponse, error)
	DeleteCart(ctx context.Context, cartID int64) error
	ListCarts(ctx context.Context, tenantID int64, req request.ListCartRequest) (*response.CartListResponse, error)

	// Cart item management
	AddItemToCart(ctx context.Context, cartID int64, req request.AddCartItemRequest) (*response.CartItemResponse, error)
	UpdateCartItem(ctx context.Context, itemID int64, req request.UpdateCartItemRequest) (*response.CartItemResponse, error)
	RemoveCartItem(ctx context.Context, itemID int64) error

	// Cart item addon management
	AddAddonToCartItem(ctx context.Context, itemID int64, req request.AddCartItemAddonRequest) (*response.CartItemAddonResponse, error)
	RemoveAddonFromCartItem(ctx context.Context, addonID int64) error

	// Cart calculation
	RecalculateCart(ctx context.Context, cartID int64) (*response.CartSummaryResponse, error)
	ApplyCoupon(ctx context.Context, cartID int64, couponCode string) (*response.CartSummaryResponse, error)

	// Cart lifecycle management
	ExpireCart(ctx context.Context, cartID int64) error
	ConvertCartToOrder(ctx context.Context, cartID int64, orderType string) (string, error) // Returns order reference ID
	MergeAnonymousCart(ctx context.Context, sessionID string, userID int64) (*response.CartResponse, error)
}
