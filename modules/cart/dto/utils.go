package dto

import (
	"encoding/json"

	"github.com/webnew/wn-backend-v2/modules/cart/models"
)

// RawMessageToJSON chuyển đổi json.RawMessage thành models.JSON
func RawMessageToJSON(raw json.RawMessage) (models.JSON, error) {
	if raw == nil {
		return nil, nil
	}

	var data models.JSON
	if err := json.Unmarshal(raw, &data); err != nil {
		return nil, err
	}

	return data, nil
}

// JSONToRawMessage chuyển đổi models.JSON thành json.RawMessage
func JSONToRawMessage(data models.JSON) (json.RawMessage, error) {
	if data == nil {
		return nil, nil
	}

	raw, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	return raw, nil
}

// MapToJSON chuyển đổi map[string]interface{} thành models.JSON
func MapToJSON(data map[string]interface{}) models.JSON {
	if data == nil {
		return nil
	}

	result := models.JSON{}
	for k, v := range data {
		result[k] = v
	}

	return result
}

// InterfaceToMap chuyển đổi map[string]interface{} từ interface{}
func InterfaceToMap(data interface{}) (map[string]interface{}, error) {
	if data == nil {
		return nil, nil
	}

	resultMap := make(map[string]interface{})

	dataBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal(dataBytes, &resultMap); err != nil {
		return nil, err
	}

	return resultMap, nil
}
