package response

import (
	"encoding/json"
	"time"
)

// CartItemResponse defines the response structure for cart items
type CartItemResponse struct {
	ID            int64                    `json:"id"`
	CartID        int64                    `json:"cart_id"`
	ItemType      string                   `json:"item_type"`
	ReferenceID   string                   `json:"reference_id"`
	ReferenceType string                   `json:"reference_type"`
	Quantity      int                      `json:"quantity"`
	UnitPrice     float64                  `json:"unit_price"`
	TotalPrice    float64                  `json:"total_price"`
	ItemData      json.RawMessage          `json:"item_data"`
	Options       json.RawMessage          `json:"options,omitempty"`
	Notes         string                   `json:"notes,omitempty"`
	CreatedAt     time.Time                `json:"created_at"`
	UpdatedAt     time.Time                `json:"updated_at"`
	Addons        []*CartItemAddonResponse `json:"addons,omitempty"`
}
