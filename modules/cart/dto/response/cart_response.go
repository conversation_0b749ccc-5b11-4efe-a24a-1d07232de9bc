package response

import (
	"encoding/json"
	"time"
)

// CartResponse defines the response structure for a cart
type CartResponse struct {
	ID        int64                `json:"id"`
	TenantID  int64                `json:"tenant_id"`
	UserID    *int64               `json:"user_id,omitempty"`
	SessionID *string              `json:"session_id,omitempty"`
	CartType  string               `json:"cart_type"`
	Status    string               `json:"status"`
	Currency  string               `json:"currency"`
	Notes     string               `json:"notes,omitempty"`
	Metadata  json.RawMessage      `json:"metadata,omitempty"`
	ExpiresAt *time.Time           `json:"expires_at,omitempty"`
	CreatedAt time.Time            `json:"created_at"`
	UpdatedAt time.Time            `json:"updated_at"`
	Items     []*CartItemResponse  `json:"items,omitempty"`
	Summary   *CartSummaryResponse `json:"summary,omitempty"`
}

// CartListResponse defines the response structure for a list of carts
type CartListResponse struct {
	Items      []*CartResponse `json:"items"`
	NextCursor string          `json:"next_cursor,omitempty"`
	<PERSON><PERSON><PERSON>    bool            `json:"has_more"`
}
