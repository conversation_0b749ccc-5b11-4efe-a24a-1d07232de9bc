package response

import (
	"encoding/json"
	"time"
)

// CartSummaryResponse defines the response structure for cart summary
type CartSummaryResponse struct {
	ID             int64           `json:"id"`
	CartID         int64           `json:"cart_id"`
	Subtotal       float64         `json:"subtotal"`
	DiscountAmount float64         `json:"discount_amount"`
	TaxAmount      float64         `json:"tax_amount"`
	ShippingAmount float64         `json:"shipping_amount"`
	ServiceFee     float64         `json:"service_fee"`
	OtherFees      json.RawMessage `json:"other_fees,omitempty"`
	TotalAmount    float64         `json:"total_amount"`
	CouponCodes    json.RawMessage `json:"coupon_codes,omitempty"`
	CreatedAt      time.Time       `json:"created_at"`
	UpdatedAt      time.Time       `json:"updated_at"`
}
