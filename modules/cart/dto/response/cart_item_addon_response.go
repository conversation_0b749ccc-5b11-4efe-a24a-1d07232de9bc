package response

import (
	"encoding/json"
	"time"
)

// CartItemAddonResponse defines the response structure for cart item addons
type CartItemAddonResponse struct {
	ID            int64           `json:"id"`
	CartItemID    int64           `json:"cart_item_id"`
	AddonType     string          `json:"addon_type"`
	ReferenceID   string          `json:"reference_id"`
	ReferenceType string          `json:"reference_type"`
	Quantity      int             `json:"quantity"`
	UnitPrice     float64         `json:"unit_price"`
	TotalPrice    float64         `json:"total_price"`
	GroupID       string          `json:"group_id,omitempty"`
	AddonData     json.RawMessage `json:"addon_data"`
	CreatedAt     time.Time       `json:"created_at"`
}
