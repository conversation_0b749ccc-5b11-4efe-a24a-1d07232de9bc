package request

import "encoding/json"

// AddCartItemRequest defines the request to add an item to a cart
type AddCartItemRequest struct {
	ItemType      string          `json:"item_type" binding:"required"`
	ReferenceID   string          `json:"reference_id" binding:"required"`
	ReferenceType string          `json:"reference_type" binding:"required"`
	Quantity      int             `json:"quantity" binding:"required,min=1"`
	Options       json.RawMessage `json:"options"`
	Notes         string          `json:"notes"`
}

// UpdateCartItemRequest defines the request to update a cart item
type UpdateCartItemRequest struct {
	Quantity int             `json:"quantity" binding:"min=1"`
	Options  json.RawMessage `json:"options"`
	Notes    string          `json:"notes"`
}
