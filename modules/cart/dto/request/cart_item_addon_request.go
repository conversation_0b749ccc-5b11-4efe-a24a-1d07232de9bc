package request

import "encoding/json"

// AddCartItemAddonRequest defines the request to add an addon to a cart item
type AddCartItemAddonRequest struct {
	AddonType     string          `json:"addon_type" binding:"required"`
	ReferenceID   string          `json:"reference_id" binding:"required"`
	ReferenceType string          `json:"reference_type" binding:"required"`
	Quantity      int             `json:"quantity" binding:"required,min=1"`
	GroupID       string          `json:"group_id"`
	AddonData     json.RawMessage `json:"addon_data"`
}
