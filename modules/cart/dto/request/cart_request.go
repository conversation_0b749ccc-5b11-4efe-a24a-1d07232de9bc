package request

import "encoding/json"

// GetOrCreateCartRequest defines the request to get or create a cart
type GetOrCreateCartRequest struct {
	TenantID         int64           `json:"tenant_id" binding:"required"`
	UserID           int64           `json:"user_id"`
	SessionID        string          `json:"session_id"`
	CartType         string          `json:"cart_type" binding:"required"`
	Currency         string          `json:"currency" default:"VND"`
	ExpiresInMinutes int             `json:"expires_in_minutes"`
	Metadata         json.RawMessage `json:"metadata"`
}

// UpdateCartRequest defines the request to update a cart
type UpdateCartRequest struct {
	Currency string          `json:"currency"`
	Notes    *string         `json:"notes"`
	Metadata json.RawMessage `json:"metadata"`
}

// ListCartRequest defines the request to list carts
type ListCartRequest struct {
	Cursor    string `json:"cursor" form:"cursor"`
	Limit     int    `json:"limit" form:"limit" binding:"required,min=1,max=100" default:"20"`
	Status    string `json:"status" form:"status"`
	CartType  string `json:"cart_type" form:"cart_type"`
	UserID    int64  `json:"user_id" form:"user_id"`
	SessionID string `json:"session_id" form:"session_id"`
	SortBy    string `json:"sort_by" form:"sort_by"`
	SortDir   string `json:"sort_dir" form:"sort_dir" default:"DESC"`
}

// ApplyCouponRequest defines the request to apply a coupon to a cart
type ApplyCouponRequest struct {
	CouponCode string `json:"coupon_code" binding:"required"`
}

// ConvertCartToOrderRequest defines the request to convert a cart to an order
type ConvertCartToOrderRequest struct {
	OrderType string `json:"order_type" binding:"required"`
}

// MergeAnonymousCartRequest defines the request to merge an anonymous cart into a user's cart
type MergeAnonymousCartRequest struct {
	SessionID string `json:"session_id" binding:"required"`
	UserID    int64  `json:"user_id" binding:"required"`
}
