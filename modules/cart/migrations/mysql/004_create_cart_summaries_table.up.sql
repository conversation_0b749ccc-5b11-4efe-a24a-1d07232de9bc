-- Create cart_summaries table
CREATE TABLE IF NOT EXISTS cart_summaries (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    cart_id BIGINT UNSIGNED NOT NULL,
    subtotal DECIMAL(20, 2) NOT NULL DEFAULT 0.00,
    discount_amount DECIMAL(20, 2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(20, 2) NOT NULL DEFAULT 0.00,
    shipping_amount DECIMAL(20, 2) NOT NULL DEFAULT 0.00,
    service_fee DECIMAL(20, 2) NOT NULL DEFAULT 0.00,
    other_fees JSON NULL,
    total_amount DECIMAL(20, 2) NOT NULL DEFAULT 0.00,
    coupon_codes JSON NULL,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    UNIQUE INDEX idx_cart_summaries_cart_id (cart_id),
    CONSTRAINT fk_cart_summaries_cart FOREIGN KEY (cart_id) REFERENCES carts(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;