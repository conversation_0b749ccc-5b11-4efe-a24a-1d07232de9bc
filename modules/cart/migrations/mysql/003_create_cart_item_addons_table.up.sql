-- Create cart_item_addons table
CREATE TABLE IF NOT EXISTS cart_item_addons (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    cart_item_id BIGINT UNSIGNED NOT NULL,
    addon_type VARCHAR(50) NOT NULL,
    reference_id VARCHAR(255) NOT NULL,
    reference_type VARCHAR(50) NOT NULL,
    quantity INT UNSIGNED NOT NULL,
    unit_price DECIMAL(20, 2) NOT NULL,
    total_price DECIMAL(20, 2) NOT NULL,
    group_id VARCHAR(255) NULL,
    addon_data JSON NULL,
    created_at TIMESTAMP NOT NULL,
    INDEX idx_cart_item_addons_cart_item_id (cart_item_id),
    CONSTRAINT fk_cart_item_addons_item FOREIGN KEY (cart_item_id) REFERENCES cart_items(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;