package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/tenant/api/handlers/user"
	"github.com/webnew/wn-backend-v2/pkg/middleware"
)

// RegisterUserRoutes đăng ký routes cho user API
func RegisterUserRoutes(router *gin.RouterGroup, handler *user.TenantHandler, permService middleware.PermissionService) {
	// Tenant routes
	tenants := router.Group("/tenants")
	{
		tenants.GET("", middleware.RequirePermission(permService, "tenants.read"), handler.List)
		tenants.GET("/:tenant_code", middleware.RequirePermission(permService, "tenants.read"), handler.GetByCode)
		tenants.POST("", middleware.RequirePermission(permService, "tenants.create"), handler.Create)
	}
}
