package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/tenant/api/handlers/admin"
	"github.com/webnew/wn-backend-v2/pkg/middleware"
)

// RegisterAdminRoutes đăng ký routes cho admin API
func RegisterAdminRoutes(router *gin.RouterGroup, handler *admin.TenantHandler, permService middleware.PermissionService) {
	// Tenant routes
	tenants := router.Group("/admin/tenants")
	{
		tenants.GET("", middleware.RequirePermission(permService, "tenants.read"), handler.List)
		tenants.POST("", middleware.RequirePermission(permService, "tenants.create"), handler.Create)
		tenants.GET("/:tenant_id", middleware.RequirePermission(permService, "tenants.read"), handler.GetByID)
		tenants.PUT("/:tenant_id", middleware.RequirePermission(permService, "tenants.update"), handler.Update)
		tenants.DELETE("/:tenant_id", middleware.RequirePermission(permService, "tenants.delete"), handler.Delete)
		tenants.PATCH("/:tenant_id/status", middleware.RequirePermission(permService, "tenants.manage_status"), handler.UpdateStatus)
		tenants.PATCH("/:tenant_id/plan", middleware.RequirePermission(permService, "tenants.manage_plan"), handler.UpdatePlan)
	}
}
