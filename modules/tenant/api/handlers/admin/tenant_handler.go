package admin

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/webnew/wn-backend-v2/modules/tenant/api/handlers"
	"github.com/webnew/wn-backend-v2/modules/tenant/dto/admin/tenant"
	"github.com/webnew/wn-backend-v2/modules/tenant/service/admin"
)

// TenantHandler handles HTTP requests for tenant administration
type TenantHandler struct {
	service *admin.TenantService
}

// NewTenantHandler creates a new admin tenant handler
func NewTenantHandler(service *admin.TenantService) *TenantHandler {
	return &TenantHandler{
		service: service,
	}
}

// Create handles POST /api/v1/admin/tenants
func (h *TenantHandler) Create(c *gin.Context) {
	var req tenant.CreateTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handlers.HandleInvalidRequestDataError(c, err)
		return
	}

	result, err := h.service.Create(c, req)
	if err != nil {
		handlers.HandleServerError(c, "Failed to create tenant", err)
		return
	}

	handlers.ApiSuccess(c, http.StatusCreated, "Tenant created successfully", result)
}

// GetByID handles GET /api/v1/admin/tenants/:tenant_id
func (h *TenantHandler) GetByID(c *gin.Context) {
	var req tenant.GetTenantRequest
	if err := c.ShouldBindUri(&req); err != nil {
		handlers.HandleInvalidTenantIDError(c, err)
		return
	}

	result, err := h.service.GetByID(c.Request.Context(), uint(req.TenantID))
	if err != nil {
		handlers.HandleTenantNotFoundError(c, err)
		return
	}

	handlers.ApiSuccess(c, http.StatusOK, "Tenant retrieved successfully", result)
}

// Update handles PUT /api/v1/admin/tenants/:tenant_id
func (h *TenantHandler) Update(c *gin.Context) {
	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 64)
	if err != nil {
		handlers.HandleInvalidTenantIDError(c, err)
		return
	}

	var req tenant.UpdateTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handlers.HandleInvalidRequestDataError(c, err)
		return
	}

	result, err := h.service.Update(c, uint(tenantID), req)
	if err != nil {
		handlers.HandleServerError(c, "Failed to update tenant", err)
		return
	}

	handlers.ApiSuccess(c, http.StatusOK, "Tenant updated successfully", result)
}

// Delete handles DELETE /api/v1/admin/tenants/:tenant_id
func (h *TenantHandler) Delete(c *gin.Context) {
	var req tenant.DeleteTenantRequest
	if err := c.ShouldBindUri(&req); err != nil {
		handlers.HandleInvalidTenantIDError(c, err)
		return
	}

	err := h.service.Delete(c.Request.Context(), uint(req.TenantID))
	if err != nil {
		handlers.HandleServerError(c, "Failed to delete tenant", err)
		return
	}

	handlers.ApiSuccess(c, http.StatusOK, "Tenant deleted successfully", nil)
}

// List handles GET /api/v1/admin/tenants
func (h *TenantHandler) List(c *gin.Context) {
	var req tenant.ListTenantsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		handlers.HandleInvalidRequestDataError(c, err)
		return
	}

	result, err := h.service.List(c, req)
	if err != nil {
		handlers.HandleServerError(c, "Failed to list tenants", err)
		return
	}

	meta := map[string]interface{}{
		"next_cursor": result.Meta.NextCursor,
		"has_more":    result.Meta.HasMore,
	}

	handlers.ApiSuccessWithMeta(c, http.StatusOK, "Tenants retrieved successfully", result.Data, meta)
}

// UpdateStatus handles PATCH /api/v1/admin/tenants/:tenant_id/status
func (h *TenantHandler) UpdateStatus(c *gin.Context) {
	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 64)
	if err != nil {
		handlers.HandleInvalidTenantIDError(c, err)
		return
	}

	var req tenant.UpdateTenantStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handlers.HandleInvalidRequestDataError(c, err)
		return
	}

	result, err := h.service.UpdateStatus(c, uint(tenantID), req.Status)
	if err != nil {
		handlers.HandleServerError(c, "Failed to update tenant status", err)
		return
	}

	handlers.ApiSuccess(c, http.StatusOK, "Tenant status updated successfully", result)
}

// UpdatePlan handles PATCH /api/v1/admin/tenants/:tenant_id/plan
func (h *TenantHandler) UpdatePlan(c *gin.Context) {
	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 64)
	if err != nil {
		handlers.HandleInvalidTenantIDError(c, err)
		return
	}

	var req tenant.UpdateTenantPlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		handlers.HandleInvalidRequestDataError(c, err)
		return
	}

	var expiryDate *time.Time
	if req.SubscriptionExpiresAt != nil {
		expiryDate = req.SubscriptionExpiresAt
	}

	result, err := h.service.UpdatePlan(c, uint(tenantID), req.PlanType, expiryDate)
	if err != nil {
		handlers.HandleServerError(c, "Failed to update tenant plan", err)
		return
	}

	handlers.ApiSuccess(c, http.StatusOK, "Tenant plan updated successfully", result)
}
