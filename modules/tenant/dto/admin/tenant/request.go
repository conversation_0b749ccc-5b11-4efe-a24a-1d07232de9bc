package tenant

import (
	"time"
)

// CreateTenantRequest represents the request to create a new tenant
type CreateTenantRequest struct {
	TenantName            string     `json:"tenant_name" binding:"required"`
	TenantCode            string     `json:"tenant_code" binding:"required,alphanum"`
	Status                string     `json:"status" binding:"required,oneof=active inactive suspended trial"`
	PlanType              string     `json:"plan_type" binding:"required"`
	SubscriptionExpiresAt *time.Time `json:"subscription_expires_at"`
}

// UpdateTenantRequest represents the request to update an existing tenant
type UpdateTenantRequest struct {
	TenantName            string     `json:"tenant_name" binding:"required"`
	Status                string     `json:"status" binding:"required,oneof=active inactive suspended trial"`
	PlanType              string     `json:"plan_type" binding:"required"`
	SubscriptionExpiresAt *time.Time `json:"subscription_expires_at"`
}

// GetTenantRequest represents the request to get tenant details
type GetTenantRequest struct {
	TenantID uint `uri:"tenant_id" binding:"required,min=1"`
}

// ListTenantsRequest represents the request to list tenants with pagination
type ListTenantsRequest struct {
	Cursor string `form:"cursor"`
	Limit  int    `form:"limit,default=10" binding:"max=100"`
	Status string `form:"status" binding:"omitempty,oneof=active inactive suspended trial"`
	Search string `form:"search"`
}

// DeleteTenantRequest represents the request to delete a tenant
type DeleteTenantRequest struct {
	TenantID uint `uri:"tenant_id" binding:"required,min=1"`
}

// UpdateTenantStatusRequest represents the request to update tenant status
type UpdateTenantStatusRequest struct {
	Status string `json:"status" binding:"required,oneof=active inactive suspended trial"`
}

// UpdateTenantPlanRequest represents the request to update tenant subscription plan
type UpdateTenantPlanRequest struct {
	PlanType              string     `json:"plan_type" binding:"required"`
	SubscriptionExpiresAt *time.Time `json:"subscription_expires_at"`
}
