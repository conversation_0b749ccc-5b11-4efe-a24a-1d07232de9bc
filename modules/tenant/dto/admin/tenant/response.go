package tenant

import (
	"time"

	"github.com/webnew/wn-backend-v2/modules/tenant/models"
	"github.com/webnew/wn-backend-v2/modules/tenant/repository"
)

// TenantResponse represents a tenant response
type TenantResponse struct {
	ID                    uint       `json:"id"`
	TenantName            string     `json:"tenant_name"`
	TenantCode            string     `json:"tenant_code"`
	CreatedAt             time.Time  `json:"created_at"`
	UpdatedAt             time.Time  `json:"updated_at"`
	Status                string     `json:"status"`
	PlanType              string     `json:"plan_type"`
	SubscriptionExpiresAt *time.Time `json:"subscription_expires_at,omitempty"`
}

// ListTenantsResponse represents a paginated list of tenants
type ListTenantsResponse struct {
	Data []TenantResponse `json:"data"`
	Meta struct {
		NextCursor string `json:"next_cursor"`
		HasMore    bool   `json:"has_more"`
	} `json:"meta"`
}

// FromModel converts a tenant model to a tenant response
func FromModel(tenant *models.Tenant) TenantResponse {
	return TenantResponse{
		ID:                    tenant.TenantID,
		TenantName:            tenant.TenantName,
		TenantCode:            tenant.TenantCode,
		CreatedAt:             tenant.CreatedAt,
		UpdatedAt:             tenant.UpdatedAt,
		Status:                tenant.Status,
		PlanType:              tenant.PlanType,
		SubscriptionExpiresAt: tenant.SubscriptionExpiresAt,
	}
}

// FromRepository converts a repository tenant to a tenant response
func FromRepository(tenant repository.Tenant) TenantResponse {
	// Handle SubscriptionExpiresAt nếu cần
	var expiresAt *time.Time
	return TenantResponse{
		ID:                    tenant.ID,
		TenantName:            tenant.Name,
		TenantCode:            tenant.Code,
		CreatedAt:             tenant.CreatedAt,
		UpdatedAt:             tenant.UpdatedAt,
		Status:                tenant.Status,
		PlanType:              tenant.PlanType,
		SubscriptionExpiresAt: expiresAt,
	}
}

// FromModelList converts a list of tenant models to a list of tenant responses
func FromModelList(tenants []models.Tenant) []TenantResponse {
	result := make([]TenantResponse, len(tenants))
	for i, tenant := range tenants {
		result[i] = FromModel(&tenant)
	}
	return result
}

// FromRepositoryList converts a list of repository tenants to a list of tenant responses
func FromRepositoryList(tenants []repository.Tenant) []TenantResponse {
	result := make([]TenantResponse, len(tenants))
	for i, tenant := range tenants {
		result[i] = FromRepository(tenant)
	}
	return result
}
