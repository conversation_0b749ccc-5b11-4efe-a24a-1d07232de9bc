package tenant

import (
	"time"
)

// CustomTime là kiểu tùy chỉnh để xử lý nhiều định dạng thời gian khác nhau
type CustomTime time.Time

// UnmarshalJSON tùy chỉnh hỗ trợ nhiều định dạng thời gian
func (ct *CustomTime) UnmarshalJSON(b []byte) error {
	s := string(b)
	s = s[1 : len(s)-1] // Bỏ dấu ngoặc kép

	// Thử các định dạng khác nhau
	formats := []string{
		time.RFC3339,          // Định dạng chuẩn ISO: 2006-01-02T15:04:05Z07:00
		"2006-01-02 15:04:05", // Định dạng MySQL: 2025-05-31 00:00:00
		"2006-01-02",          // Chỉ ngày: 2025-05-31
	}

	var t time.Time
	var err error

	for _, format := range formats {
		t, err = time.Parse(format, s)
		if err == nil {
			*ct = CustomTime(t)
			return nil
		}
	}

	return err // Tr<PERSON> về lỗi của lần thử cuối cùng
}

// Time chuyển đổi CustomTime thành time.Time
func (ct *CustomTime) Time() time.Time {
	return time.Time(*ct)
}

// CreateTenantRequest represents the request to create a new tenant
type CreateTenantRequest struct {
	TenantName            string      `json:"tenant_name" binding:"required"`
	TenantCode            string      `json:"tenant_code" binding:"required,alphanum"`
	Status                string      `json:"status" binding:"required,oneof=active inactive suspended trial"`
	PlanType              string      `json:"plan_type" binding:"required"`
	SubscriptionExpiresAt *CustomTime `json:"subscription_expires_at"`
}

// GetTenantRequest represents the request to get tenant details
type GetTenantRequest struct {
	TenantCode string `uri:"tenant_code" binding:"required"`
}

// ListTenantsRequest represents the request to list tenants with pagination
type ListTenantsRequest struct {
	Cursor string `form:"cursor"`
	Limit  int    `form:"limit,default=10" binding:"max=100"`
	Status string `form:"status" binding:"omitempty,oneof=active inactive suspended trial"`
}
