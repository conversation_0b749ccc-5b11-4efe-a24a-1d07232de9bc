package common

// PaginationRequest là struct dùng để nhận các tham số phân trang
type PaginationRequest struct {
	Cursor string `form:"cursor" json:"cursor"`
	Limit  int    `form:"limit" json:"limit" binding:"omitempty,min=1,max=100"`
}

// PaginationOptions là struct chứa các tùy chọn phân trang
type PaginationOptions struct {
	Cursor string
	Limit  int
}

// ToOptions chuyển đổi PaginationRequest thành PaginationOptions
func (p *PaginationRequest) ToOptions() PaginationOptions {
	limit := p.Limit
	if limit <= 0 {
		limit = 10 // Giá trị mặc định
	}
	if limit > 100 {
		limit = 100 // Giới hạn tối đa
	}

	return PaginationOptions{
		Cursor: p.Cursor,
		Limit:  limit,
	}
}

// PaginationMeta represents metadata for paginated responses
type PaginationMeta struct {
	NextCursor string `json:"next_cursor"`
	Has<PERSON><PERSON>    bool   `json:"has_more"`
}

// PaginationMetadata chứa thông tin phân trang
type PaginationMetadata struct {
	NextCursor string
	HasMore    bool
}

// NewPaginationMeta tạo mới PaginationMeta từ PaginationMetadata
func NewPaginationMeta(meta PaginationMetadata) PaginationMeta {
	return PaginationMeta{
		NextCursor: meta.NextCursor,
		HasMore:    meta.HasMore,
	}
}

// PaginatedResponse represents a generic paginated response
type PaginatedResponse struct {
	Meta PaginationMeta `json:"meta"`
}
