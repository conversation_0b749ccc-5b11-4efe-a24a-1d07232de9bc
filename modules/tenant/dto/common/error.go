package common

// ErrorCodes chứa các mã lỗi được định nghĩa cho module tenant
const (
	// Tenant errors
	ErrorCodeTenantNotFound     = "TENANT_NOT_FOUND"
	ErrorCodeTenantAlreadyExist = "TENANT_ALREADY_EXISTS"
	ErrorCodeInvalidTenantData  = "INVALID_TENANT_DATA"

	// Setting errors
	ErrorCodeSettingNotFound    = "SETTING_NOT_FOUND"
	ErrorCodeInvalidSettingData = "INVALID_SETTING_DATA"

	// Permission errors
	ErrorCodePermissionDenied = "PERMISSION_DENIED"

	// Server errors
	ErrorCodeInternalServer = "INTERNAL_SERVER_ERROR"
	ErrorCodeBadRequest     = "BAD_REQUEST"
)

// ErrorResponse represents an error response
type ErrorResponse struct {
	Status struct {
		Code      int      `json:"code"`
		Message   string   `json:"message"`
		Success   bool     `json:"success"`
		ErrorCode string   `json:"error_code"`
		Path      string   `json:"path"`
		Timestamp string   `json:"timestamp"`
		Details   []Detail `json:"details,omitempty"`
	} `json:"status"`
}

// Detail represents an error detail
type Detail struct {
	Field   string `json:"field,omitempty"`
	Message string `json:"message"`
}

// NewErrorResponse tạo một cấu trúc lỗi mới
func NewErrorResponse(code int, message, errCode, path, timestamp string, details []Detail) ErrorResponse {
	resp := ErrorResponse{}
	resp.Status.Code = code
	resp.Status.Message = message
	resp.Status.Success = false
	resp.Status.ErrorCode = errCode
	resp.Status.Path = path
	resp.Status.Timestamp = timestamp
	resp.Status.Details = details
	return resp
}
