package dto

import "time"

// Pagination chứa thông tin phân trang
type Pagination struct {
	CurrentPage int   `json:"current_page"`
	PageSize    int   `json:"page_size"`
	TotalItems  int64 `json:"total_items"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrev     bool  `json:"has_prev"`
}

// Tenant DTOs
// ------------------------------

// CreateTenantRequest chứa thông tin để tạo tenant mới
type CreateTenantRequest struct {
	Name         string `json:"name" binding:"required,max=100"`
	Subdomain    string `json:"subdomain" binding:"required,min=3,max=63,alphanum"`
	PlanID       int    `json:"plan_id" binding:"required"`
	CustomDomain string `json:"custom_domain" binding:"omitempty"`
}

// UpdateTenantRequest chứa thông tin để cập nhật tenant
type UpdateTenantRequest struct {
	Name         *string `json:"name" binding:"omitempty,max=100"`
	PlanID       *int    `json:"plan_id" binding:"omitempty"`
	Status       *string `json:"status" binding:"omitempty,oneof=active inactive suspended"`
	CustomDomain *string `json:"custom_domain" binding:"omitempty"`
}

// TenantResponse chứa thông tin trả về về tenant
type TenantResponse struct {
	ID             int64      `json:"id"`
	Name           string     `json:"name"`
	Subdomain      string     `json:"subdomain"`
	PlanID         int        `json:"plan_id"`
	PlanName       string     `json:"plan_name,omitempty"`
	OwnerID        int64      `json:"owner_id"`
	Status         string     `json:"status"`
	StorageLimit   int64      `json:"storage_limit"`
	StorageUsed    int64      `json:"storage_used"`
	UserLimit      int        `json:"user_limit"`
	BandwidthLimit int64      `json:"bandwidth_limit"`
	CustomDomain   string     `json:"custom_domain,omitempty"`
	ExpiresAt      *time.Time `json:"expires_at,omitempty"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

// ListTenantsParams chứa các tham số để lọc và phân trang danh sách tenant
type ListTenantsParams struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy     string `form:"sort_by" binding:"omitempty"`
	SortOrder  string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	OwnerID    *int64 `form:"owner_id" binding:"omitempty"`
	Status     string `form:"status" binding:"omitempty,oneof=active inactive suspended"`
	Search     string `form:"search" binding:"omitempty"`
	PlanID     *int   `form:"plan_id" binding:"omitempty"`
}

// ListTenantsResponse chứa kết quả danh sách tenant có phân trang
type ListTenantsResponse struct {
	Tenants    []TenantResponse `json:"tenants"`
	Pagination Pagination       `json:"pagination"`
}

// UserTenantsResponse chứa danh sách tenant của một user
type UserTenantsResponse struct {
	Tenants []TenantResponse `json:"tenants"`
}

// Plan DTOs
// ------------------------------

// PlanResponse chứa thông tin trả về về plan
type PlanResponse struct {
	ID             int       `json:"id"`
	Name           string    `json:"name"`
	Description    string    `json:"description"`
	Price          float64   `json:"price"`
	BillingCycle   string    `json:"billing_cycle"`
	StorageLimit   int64     `json:"storage_limit"`
	BandwidthLimit int64     `json:"bandwidth_limit"`
	UserLimit      int       `json:"user_limit"`
	IsActive       bool      `json:"is_active"`
	Features       []string  `json:"features"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// ListPlansParams chứa các tham số để lọc và phân trang danh sách plan
type ListPlansParams struct {
	Page      int    `form:"page" binding:"omitempty,min=1"`
	PageSize  int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy    string `form:"sort_by" binding:"omitempty"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	ActiveOnly bool  `form:"active_only" binding:"omitempty"`
	Search    string `form:"search" binding:"omitempty"`
}

// ListPlansResponse chứa kết quả danh sách plan có phân trang
type ListPlansResponse struct {
	Plans      []PlanResponse `json:"plans"`
	Pagination Pagination     `json:"pagination"`
}

// TenantUser DTOs
// ------------------------------

// AddUserRequest chứa thông tin để thêm user vào tenant
type AddUserRequest struct {
	UserID int64  `json:"user_id" binding:"required"`
	Role   string `json:"role" binding:"required,oneof=admin member viewer"`
}

// UpdateTenantUserRequest chứa thông tin để cập nhật quyền của user trong tenant
type UpdateTenantUserRequest struct {
	Role   string `json:"role" binding:"required,oneof=admin member viewer"`
	Status string `json:"status" binding:"omitempty,oneof=active inactive"`
}

// TenantUserResponse chứa thông tin trả về về mối quan hệ giữa tenant và user
type TenantUserResponse struct {
	TenantID  int64     `json:"tenant_id"`
	UserID    int64     `json:"user_id"`
	Role      string    `json:"role"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ListTenantUsersParams chứa các tham số để lọc và phân trang danh sách user trong tenant
type ListTenantUsersParams struct {
	Page      int    `form:"page" binding:"omitempty,min=1"`
	PageSize  int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy    string `form:"sort_by" binding:"omitempty"`
	SortOrder string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	Role      string `form:"role" binding:"omitempty,oneof=admin member viewer"`
	Status    string `form:"status" binding:"omitempty,oneof=active inactive"`
	Search    string `form:"search" binding:"omitempty"`
}

// ListTenantUsersResponse chứa kết quả danh sách user trong tenant có phân trang
type ListTenantUsersResponse struct {
	Users      []TenantUserResponse `json:"users"`
	Pagination Pagination           `json:"pagination"`
}
