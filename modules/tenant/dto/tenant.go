package dto

import "time"

// Pagination chứa thông tin phân trang
type Pagination struct {
	CurrentPage int   `json:"current_page"`
	PageSize    int   `json:"page_size"`
	TotalItems  int64 `json:"total_items"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrev     bool  `json:"has_prev"`
}

// Tenant DTOs
// ------------------------------

// CreateTenantRequest chứa thông tin để tạo tenant mới
type CreateTenantRequest struct {
	Name         string `json:"name" binding:"required,min=3,max=50"`
	Slug         string `json:"slug" binding:"omitempty,min=3,max=30,alphanum"`
	Description  string `json:"description" binding:"max=500"`
	OwnerUserID  int64  `json:"owner_user_id" binding:"required"`
	PlanID       string `json:"plan_id" binding:"omitempty,max=50"`
	Currency     string `json:"currency" binding:"omitempty,len=3"`
	TimeZone     string `json:"time_zone" binding:"omitempty,max=50"`
	Logo         string `json:"logo" binding:"omitempty,max=255"`
	ContactEmail string `json:"contact_email" binding:"omitempty,email,max=255"`
	ContactPhone string `json:"contact_phone" binding:"omitempty,max=50"`
}

// UpdateTenantRequest chứa thông tin để cập nhật tenant
type UpdateTenantRequest struct {
	Name         *string `json:"name" binding:"omitempty,min=3,max=50"`
	Description  *string `json:"description" binding:"omitempty,max=500"`
	PlanID       *string `json:"plan_id" binding:"omitempty,max=50"`
	Currency     *string `json:"currency" binding:"omitempty,len=3"`
	IsActive     *bool   `json:"is_active" binding:"omitempty"`
	TimeZone     *string `json:"time_zone" binding:"omitempty,max=50"`
	Logo         *string `json:"logo" binding:"omitempty,max=255"`
	ContactEmail *string `json:"contact_email" binding:"omitempty,email,max=255"`
	ContactPhone *string `json:"contact_phone" binding:"omitempty,max=50"`
}

// TenantResponse chứa thông tin trả về về tenant
type TenantResponse struct {
	ID           int64      `json:"id"`
	Name         string     `json:"name"`
	Slug         string     `json:"slug"`
	Description  string     `json:"description"`
	OwnerUserID  int64      `json:"owner_user_id"`
	PlanID       string     `json:"plan_id"`
	Currency     string     `json:"currency"`
	IsActive     bool       `json:"is_active"`
	TimeZone     string     `json:"time_zone"`
	TrialEndsAt  *time.Time `json:"trial_ends_at,omitempty"`
	Logo         string     `json:"logo"`
	ContactEmail string     `json:"contact_email"`
	ContactPhone string     `json:"contact_phone"`
	CreatedAt    time.Time  `json:"created_at"`
	UpdatedAt    time.Time  `json:"updated_at"`
}

// ListTenantsParams chứa các tham số để lọc và phân trang danh sách tenant
type ListTenantsParams struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy     string `form:"sort_by" binding:"omitempty"`
	SortOrder  string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	OwnerID    *int64 `form:"owner_id" binding:"omitempty"`
	ActiveOnly bool   `form:"active_only" binding:"omitempty"`
	Search     string `form:"search" binding:"omitempty"`
	PlanID     string `form:"plan_id" binding:"omitempty"`
}

// ListTenantsResponse chứa kết quả danh sách tenant có phân trang
type ListTenantsResponse struct {
	Tenants    []TenantResponse `json:"tenants"`
	Pagination Pagination       `json:"pagination"`
}

// TenantUser DTOs
// ------------------------------

// AddUserRequest chứa thông tin để thêm user vào tenant
type AddUserRequest struct {
	TenantID int64  `json:"tenant_id" binding:"required"`
	UserID   int64  `json:"user_id" binding:"required"`
	Role     string `json:"role" binding:"required,max=50"`
}

// UpdateTenantUserRequest chứa thông tin để cập nhật liên kết user và tenant
type UpdateTenantUserRequest struct {
	Role     *string `json:"role" binding:"omitempty,max=50"`
	IsActive *bool   `json:"is_active" binding:"omitempty"`
}

// TenantUserResponse chứa thông tin trả về về liên kết user và tenant
type TenantUserResponse struct {
	ID        int64     `json:"id"`
	TenantID  int64     `json:"tenant_id"`
	UserID    int64     `json:"user_id"`
	Role      string    `json:"role"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	// Thông tin bổ sung
	TenantName  string `json:"tenant_name,omitempty"`
	TenantSlug  string `json:"tenant_slug,omitempty"`
	UserEmail   string `json:"user_email,omitempty"`
	UserName    string `json:"user_name,omitempty"`
}

// ListTenantUsersParams chứa các tham số để lọc và phân trang danh sách user trong tenant
type ListTenantUsersParams struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	SortBy     string `form:"sort_by" binding:"omitempty"`
	SortOrder  string `form:"sort_order" binding:"omitempty,oneof=asc desc"`
	Role       string `form:"role" binding:"omitempty"`
	ActiveOnly bool   `form:"active_only" binding:"omitempty"`
	Search     string `form:"search" binding:"omitempty"`
}

// ListTenantUsersResponse chứa kết quả danh sách user trong tenant có phân trang
type ListTenantUsersResponse struct {
	Users      []TenantUserResponse `json:"users"`
	Pagination Pagination           `json:"pagination"`
}
