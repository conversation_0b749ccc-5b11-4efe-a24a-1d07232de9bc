package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/webnew/wn-backend-v2/modules/tenant/models"
	"github.com/webnew/wn-backend-v2/modules/tenant/repository"
)

// TenantRepository implements the repository.TenantRepository interface
type TenantRepository struct {
	db *sqlx.DB
}

// NewTenantRepository creates a new tenant repository
func NewTenantRepository(db *sqlx.DB) *TenantRepository {
	return &TenantRepository{
		db: db,
	}
}

// ExistsByCode checks if a tenant with the given code already exists
func (r *TenantRepository) ExistsByCode(ctx context.Context, code string) (bool, error) {
	query := "SELECT COUNT(*) FROM tenants WHERE tenant_code = ?"

	var count int
	err := r.db.GetContext(ctx, &count, query, code)
	if err != nil {
		return false, fmt.Errorf("failed to check tenant code existence: %w", err)
	}

	return count > 0, nil
}

// Create adds a new tenant to the database
func (r *TenantRepository) Create(ctx context.Context, tenant repository.Tenant) (repository.Tenant, error) {
	// Chuyển đổi sang model
	tenantModel := repository.ConvertTenantToModel(tenant)

	query := `
		INSERT INTO tenants (
			tenant_name, tenant_code, status, plan_type, created_at, updated_at
		) VALUES (
			:tenant_name, :tenant_code, :status, :plan_type, NOW(), NOW()
		)
	`

	result, err := r.db.NamedExecContext(ctx, query, tenantModel)
	if err != nil {
		return repository.Tenant{}, fmt.Errorf("failed to create tenant: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return repository.Tenant{}, fmt.Errorf("failed to get tenant ID: %w", err)
	}

	// Lấy tenant vừa tạo
	createdTenant, err := r.GetByID(ctx, uint(id))
	if err != nil {
		return repository.Tenant{}, fmt.Errorf("failed to retrieve created tenant: %w", err)
	}

	return createdTenant, nil
}

// GetByID retrieves a tenant by ID
func (r *TenantRepository) GetByID(ctx context.Context, tenantID uint) (repository.Tenant, error) {
	query := `
		SELECT tenant_id, tenant_name, tenant_code, created_at, updated_at, 
		       status, plan_type, subscription_expires_at
		FROM tenants
		WHERE tenant_id = ?
	`

	var tenantModel models.Tenant
	err := r.db.GetContext(ctx, &tenantModel, query, tenantID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return repository.Tenant{}, fmt.Errorf("tenant not found with ID %d", tenantID)
		}
		return repository.Tenant{}, fmt.Errorf("failed to get tenant: %w", err)
	}

	// Chuyển đổi sang repository.Tenant
	tenant := repository.ConvertModelToTenant(tenantModel)

	return tenant, nil
}

// GetByCode retrieves a tenant by code
func (r *TenantRepository) GetByCode(ctx context.Context, code string) (repository.Tenant, error) {
	query := `
		SELECT tenant_id, tenant_name, tenant_code, created_at, updated_at, 
		       status, plan_type, subscription_expires_at
		FROM tenants
		WHERE tenant_code = ?
	`

	var tenantModel models.Tenant
	err := r.db.GetContext(ctx, &tenantModel, query, code)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return repository.Tenant{}, fmt.Errorf("tenant not found with code %s", code)
		}
		return repository.Tenant{}, fmt.Errorf("failed to get tenant: %w", err)
	}

	// Chuyển đổi sang repository.Tenant
	tenant := repository.ConvertModelToTenant(tenantModel)

	return tenant, nil
}

// Update updates a tenant in the database
func (r *TenantRepository) Update(ctx context.Context, tenant repository.Tenant) (repository.Tenant, error) {
	// Chuyển đổi sang model
	tenantModel := repository.ConvertTenantToModel(tenant)

	query := `
		UPDATE tenants
		SET tenant_name = :tenant_name,
		    status = :status,
		    plan_type = :plan_type,
		    updated_at = NOW()
		WHERE tenant_id = :tenant_id
	`

	result, err := r.db.NamedExecContext(ctx, query, tenantModel)
	if err != nil {
		return repository.Tenant{}, fmt.Errorf("failed to update tenant: %w", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return repository.Tenant{}, fmt.Errorf("failed to get affected rows: %w", err)
	}

	if affected == 0 {
		return repository.Tenant{}, fmt.Errorf("tenant not found with ID %d", tenant.ID)
	}

	// Lấy tenant sau khi cập nhật
	updatedTenant, err := r.GetByID(ctx, tenant.ID)
	if err != nil {
		return repository.Tenant{}, fmt.Errorf("failed to retrieve updated tenant: %w", err)
	}

	return updatedTenant, nil
}

// Delete removes a tenant from the database
func (r *TenantRepository) Delete(ctx context.Context, tenantID uint) error {
	query := `DELETE FROM tenants WHERE tenant_id = ?`

	result, err := r.db.ExecContext(ctx, query, tenantID)
	if err != nil {
		return fmt.Errorf("failed to delete tenant: %w", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get affected rows: %w", err)
	}

	if affected == 0 {
		return fmt.Errorf("tenant not found with ID %d", tenantID)
	}

	return nil
}

// List retrieves a list of tenants with cursor pagination
func (r *TenantRepository) List(ctx context.Context, params repository.ListParams) ([]repository.Tenant, string, bool, error) {
	query := `
		SELECT tenant_id, tenant_name, tenant_code, created_at, updated_at, 
		       status, plan_type, subscription_expires_at
		FROM tenants
		WHERE 1=1
	`

	args := []interface{}{}

	// Apply search if provided
	if params.Search != "" {
		query += ` AND (tenant_name LIKE ? OR tenant_code LIKE ?)`
		searchPattern := "%" + params.Search + "%"
		args = append(args, searchPattern, searchPattern)
	}

	// Apply status filter if needed
	if params.Status != "" {
		query += ` AND status = ?`
		args = append(args, params.Status)
	}

	// Add cursor pagination logic
	if params.Cursor != "" {
		cursorID, err := strconv.ParseUint(params.Cursor, 10, 64)
		if err != nil {
			return nil, "", false, fmt.Errorf("invalid cursor: %w", err)
		}

		query += ` AND tenant_id > ?`
		args = append(args, cursorID)
	}

	// Add ordering and limit
	query += ` ORDER BY tenant_id ASC LIMIT ?`
	args = append(args, params.Limit+1) // Request one more to check if there are more results

	// Execute query
	rows, err := r.db.QueryxContext(ctx, query, args...)
	if err != nil {
		return nil, "", false, fmt.Errorf("failed to list tenants: %w", err)
	}
	defer rows.Close()

	// Parse results
	tenants := []repository.Tenant{}
	var lastID uint
	hasMore := false

	for rows.Next() {
		var model models.Tenant
		if err := rows.StructScan(&model); err != nil {
			return nil, "", false, fmt.Errorf("failed to scan tenant: %w", err)
		}

		// Keep track of the last ID for cursor
		lastID = model.TenantID

		// Chuyển đổi sang repository.Tenant
		tenant := repository.ConvertModelToTenant(model)

		// Only add up to the limit
		if len(tenants) < params.Limit {
			tenants = append(tenants, tenant)
		} else {
			hasMore = true
			break
		}
	}

	if err = rows.Err(); err != nil {
		return nil, "", false, fmt.Errorf("error iterating rows: %w", err)
	}

	// Set the next cursor
	nextCursor := ""
	if hasMore {
		nextCursor = strconv.FormatUint(uint64(lastID), 10)
	}

	return tenants, nextCursor, hasMore, nil
}

// UpdateStatus updates a tenant's status
func (r *TenantRepository) UpdateStatus(ctx context.Context, tenantID uint, status string) (repository.Tenant, error) {
	query := `
		UPDATE tenants
		SET status = ?,
		    updated_at = NOW()
		WHERE tenant_id = ?
	`

	result, err := r.db.ExecContext(ctx, query, status, tenantID)
	if err != nil {
		return repository.Tenant{}, fmt.Errorf("failed to update tenant status: %w", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return repository.Tenant{}, fmt.Errorf("failed to get affected rows: %w", err)
	}

	if affected == 0 {
		return repository.Tenant{}, fmt.Errorf("tenant not found with ID %d", tenantID)
	}

	// Lấy tenant sau khi cập nhật
	updatedTenant, err := r.GetByID(ctx, tenantID)
	if err != nil {
		return repository.Tenant{}, fmt.Errorf("failed to retrieve updated tenant: %w", err)
	}

	return updatedTenant, nil
}

// UpdatePlan updates the plan type and expiration date of a tenant
func (r *TenantRepository) UpdatePlan(ctx context.Context, tenantID uint, planType string, expiresAt *string) error {
	var query string
	var args []interface{}

	if expiresAt != nil {
		// Parse the expiration date
		expiryDate, err := time.Parse(time.RFC3339, *expiresAt)
		if err != nil {
			return fmt.Errorf("invalid expiration date format: %w", err)
		}

		query = `UPDATE tenants SET plan_type = ?, subscription_expires_at = ? WHERE tenant_id = ?`
		args = []interface{}{planType, expiryDate, tenantID}
	} else {
		query = `UPDATE tenants SET plan_type = ?, subscription_expires_at = NULL WHERE tenant_id = ?`
		args = []interface{}{planType, tenantID}
	}

	result, err := r.db.ExecContext(ctx, query, args...)
	if err != nil {
		return fmt.Errorf("failed to update tenant plan: %w", err)
	}

	affected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get affected rows: %w", err)
	}

	if affected == 0 {
		return fmt.Errorf("tenant not found with ID %d", tenantID)
	}

	return nil
}

// CountByStatus counts tenants by status
func (r *TenantRepository) CountByStatus(ctx context.Context, status string) (int64, error) {
	query := `SELECT COUNT(*) FROM tenants WHERE status = ?`

	var count int64
	err := r.db.GetContext(ctx, &count, query, status)
	if err != nil {
		return 0, fmt.Errorf("failed to count tenants by status: %w", err)
	}

	return count, nil
}

// CountAll counts all tenants
func (r *TenantRepository) CountAll(ctx context.Context) (int64, error) {
	query := `SELECT COUNT(*) FROM tenants`

	var count int64
	err := r.db.GetContext(ctx, &count, query)
	if err != nil {
		return 0, fmt.Errorf("failed to count all tenants: %w", err)
	}

	return count, nil
}
