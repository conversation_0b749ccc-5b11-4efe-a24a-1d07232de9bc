package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/tenant/dto"
	"wnapi/modules/tenant/internal"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// mysqlRepository triển khai Repository interface sử dụng GORM
type mysqlRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	sqlxDB := dbManager.GetDB()
	if sqlxDB == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	return &mysqlRepository{
		db:     gormDB,
		logger: logger,
	}, nil
}

// Tenant methods
// ---------------------------------

// CreateTenant tạo tenant mới
func (r *mysqlRepository) CreateTenant(ctx context.Context, tenant *internal.Tenant) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_tenants")
	defer span.End()

	result := r.db.WithContext(ctx).Create(tenant)
	if result.Error != nil {
		r.logger.Error("Không thể tạo tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetTenantByID lấy tenant theo ID
func (r *mysqlRepository) GetTenantByID(ctx context.Context, id int64) (*internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenant internal.Tenant
	result := r.db.WithContext(ctx).First(&tenant, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenant, nil
}

// GetTenantBySubdomain lấy tenant theo subdomain
func (r *mysqlRepository) GetTenantBySubdomain(ctx context.Context, subdomain string) (*internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenant internal.Tenant
	result := r.db.WithContext(ctx).Where("subdomain = ?", subdomain).First(&tenant)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy tenant theo subdomain", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenant, nil
}

// UpdateTenant cập nhật tenant
func (r *mysqlRepository) UpdateTenant(ctx context.Context, tenant *internal.Tenant) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_tenants")
	defer span.End()

	result := r.db.WithContext(ctx).Save(tenant)
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrTenantNotFound
	}

	return nil
}

// DeleteTenant xóa tenant
func (r *mysqlRepository) DeleteTenant(ctx context.Context, id int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_tenants")
	defer span.End()

	// Sử dụng transaction để đảm bảo xóa cả tenant và tenant_users
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		r.logger.Error("Không thể bắt đầu transaction", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return internal.ErrDatabaseError
	}

	// Xóa tenant_users trước
	if err := tx.Where("tenant_id = ?", id).Delete(&internal.TenantUser{}).Error; err != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa tenant_users", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	// Xóa tenant
	result := tx.Delete(&internal.Tenant{}, id)
	if result.Error != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		tx.Rollback()
		return internal.ErrTenantNotFound
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		r.logger.Error("Không thể commit transaction", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	return nil
}

// ListTenants lấy danh sách tenant với phân trang và lọc
func (r *mysqlRepository) ListTenants(ctx context.Context, params dto.ListTenantsParams) ([]internal.Tenant, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Tenant{})

	// Áp dụng các điều kiện lọc
	if params.OwnerID != nil {
		query = query.Where("owner_id = ?", *params.OwnerID)
	}

	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}

	if params.PlanID != nil {
		query = query.Where("plan_id = ?", *params.PlanID)
	}

	if params.Search != "" {
		searchTerm := "%" + params.Search + "%"
		query = query.Where("name LIKE ? OR subdomain LIKE ?", searchTerm, searchTerm)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var tenants []internal.Tenant
	if err := query.Find(&tenants).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return tenants, total, nil
}

// GetTenantsByOwnerID lấy danh sách tenant theo owner ID
func (r *mysqlRepository) GetTenantsByOwnerID(ctx context.Context, ownerID int64) ([]internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenants []internal.Tenant
	result := r.db.WithContext(ctx).Where("owner_id = ?", ownerID).Find(&tenants)
	if result.Error != nil {
		r.logger.Error("Không thể lấy danh sách tenant theo owner", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return tenants, nil
}

// CountTenantsByOwnerID đếm số lượng tenant theo owner ID
func (r *mysqlRepository) CountTenantsByOwnerID(ctx context.Context, ownerID int64) (int, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var count int64
	result := r.db.WithContext(ctx).Model(&internal.Tenant{}).Where("owner_id = ?", ownerID).Count(&count)
	if result.Error != nil {
		r.logger.Error("Không thể đếm tenant theo owner", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return 0, internal.ErrDatabaseError
	}

	return int(count), nil
}

// Plan methods
// ---------------------------------

// GetPlanByID lấy plan theo ID
func (r *mysqlRepository) GetPlanByID(ctx context.Context, id int) (*internal.Plan, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_plans")
	defer span.End()

	var plan internal.Plan
	result := r.db.WithContext(ctx).First(&plan, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrPlanNotFound
		}
		r.logger.Error("Không thể lấy plan", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &plan, nil
}

// ListPlans lấy danh sách plan với phân trang và lọc
func (r *mysqlRepository) ListPlans(ctx context.Context, params dto.ListPlansParams) ([]internal.Plan, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_plans")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Plan{})

	// Áp dụng các điều kiện lọc
	if params.ActiveOnly {
		query = query.Where("is_active = ?", true)
	}

	if params.Search != "" {
		searchTerm := "%" + params.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchTerm, searchTerm)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số plan", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "price"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var plans []internal.Plan
	if err := query.Find(&plans).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách plan", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return plans, total, nil
}

// TenantUser methods
// ---------------------------------

// AddUserToTenant thêm user vào tenant
func (r *mysqlRepository) AddUserToTenant(ctx context.Context, tenantUser *internal.TenantUser) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).Create(tenantUser)
	if result.Error != nil {
		r.logger.Error("Không thể thêm user vào tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// RemoveUserFromTenant xóa user khỏi tenant
func (r *mysqlRepository) RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Delete(&internal.TenantUser{})
	
	if result.Error != nil {
		r.logger.Error("Không thể xóa user khỏi tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrTenantNotFound
	}

	return nil
}

// UpdateTenantUserRole cập nhật quyền của user trong tenant
func (r *mysqlRepository) UpdateTenantUserRole(ctx context.Context, tenantID, userID int64, role string) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).
		Model(&internal.TenantUser{}).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		Update("role", role)
	
	if result.Error != nil {
		r.logger.Error("Không thể cập nhật quyền user trong tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrTenantNotFound
	}

	return nil
}

// GetTenantUsers lấy danh sách user trong tenant
func (r *mysqlRepository) GetTenantUsers(ctx context.Context, tenantID int64, params dto.ListTenantUsersParams) ([]internal.TenantUser, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.TenantUser{}).Where("tenant_id = ?", tenantID)

	// Áp dụng các điều kiện lọc
	if params.Role != "" {
		query = query.Where("role = ?", params.Role)
	}

	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số user trong tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var tenantUsers []internal.TenantUser
	if err := query.Find(&tenantUsers).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách user trong tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return tenantUsers, total, nil
}

// GetUserTenants lấy danh sách tenant của user
func (r *mysqlRepository) GetUserTenants(ctx context.Context, userID int64) ([]internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	var tenants []internal.Tenant
	
	// Lấy danh sách tenant thông qua bảng tenant_users
	err := r.db.WithContext(ctx).
		Table("tenant_tenants").
		Joins("JOIN tenant_users ON tenant_users.tenant_id = tenant_tenants.id").
		Where("tenant_users.user_id = ? AND tenant_users.status = ?", userID, "active").
		Find(&tenants).Error
	
	if err != nil {
		r.logger.Error("Không thể lấy danh sách tenant của user", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, internal.ErrDatabaseError
	}

	return tenants, nil
}

// GetTenantUser lấy thông tin mối quan hệ giữa tenant và user
func (r *mysqlRepository) GetTenantUser(ctx context.Context, tenantID, userID int64) (*internal.TenantUser, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	var tenantUser internal.TenantUser
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND user_id = ?", tenantID, userID).
		First(&tenantUser)
	
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy thông tin user trong tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenantUser, nil
}
