package repository

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/tenant/dto"
	"wnapi/modules/tenant/internal"

	"github.com/gosimple/slug"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// mysqlRepository triển khai Repository interface sử dụng GORM
type mysqlRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	sqlxDB := dbManager.GetDB()
	if sqlxDB == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	// Create GORM DB from sqlx DB
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlxDB.DB,
	}), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GORM: %w", err)
	}

	return &mysqlRepository{
		db:     gormDB,
		logger: logger,
	}, nil
}

// Tenant methods
// ---------------------------------

// CreateTenant tạo tenant mới
func (r *mysqlRepository) CreateTenant(ctx context.Context, tenant *internal.Tenant) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_tenants")
	defer span.End()

	// Tạo slug từ tên nếu không có
	if tenant.Slug == "" {
		tenant.Slug = slug.Make(tenant.Name)
	}

	result := r.db.WithContext(ctx).Create(tenant)
	if result.Error != nil {
		if strings.Contains(result.Error.Error(), "Duplicate entry") && strings.Contains(result.Error.Error(), "slug") {
			r.logger.Error("Không thể tạo tenant do slug đã tồn tại", logger.String("error", result.Error.Error()))
			tracing.RecordError(ctx, result.Error)
			return internal.ErrTenantSlugExists
		}
		r.logger.Error("Không thể tạo tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetTenantByID lấy tenant theo ID
func (r *mysqlRepository) GetTenantByID(ctx context.Context, id int64) (*internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenant internal.Tenant
	result := r.db.WithContext(ctx).First(&tenant, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenant, nil
}

// GetTenantBySlug lấy tenant theo slug
func (r *mysqlRepository) GetTenantBySlug(ctx context.Context, slug string) (*internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenant internal.Tenant
	result := r.db.WithContext(ctx).Where("slug = ?", slug).First(&tenant)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrTenantNotFound
		}
		r.logger.Error("Không thể lấy tenant theo slug", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenant, nil
}

// UpdateTenant cập nhật tenant
func (r *mysqlRepository) UpdateTenant(ctx context.Context, tenant *internal.Tenant) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_tenants")
	defer span.End()

	result := r.db.WithContext(ctx).Save(tenant)
	if result.Error != nil {
		if strings.Contains(result.Error.Error(), "Duplicate entry") && strings.Contains(result.Error.Error(), "slug") {
			r.logger.Error("Không thể cập nhật tenant do slug đã tồn tại", logger.String("error", result.Error.Error()))
			tracing.RecordError(ctx, result.Error)
			return internal.ErrTenantSlugExists
		}
		r.logger.Error("Không thể cập nhật tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrTenantNotFound
	}

	return nil
}

// DeleteTenant xóa tenant
func (r *mysqlRepository) DeleteTenant(ctx context.Context, id int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_tenants")
	defer span.End()

	// Sử dụng transaction để đảm bảo xóa cả tenant users
	tx := r.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		r.logger.Error("Không thể bắt đầu transaction", logger.String("error", tx.Error.Error()))
		tracing.RecordError(ctx, tx.Error)
		return internal.ErrDatabaseError
	}

	// Xóa tenant users trước
	if err := tx.Where("tenant_id = ?", id).Delete(&internal.TenantUser{}).Error; err != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa tenant users", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	// Xóa tenant
	result := tx.Delete(&internal.Tenant{}, id)
	if result.Error != nil {
		tx.Rollback()
		r.logger.Error("Không thể xóa tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		tx.Rollback()
		return internal.ErrTenantNotFound
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		r.logger.Error("Không thể commit transaction", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return internal.ErrDatabaseError
	}

	return nil
}

// ListTenants lấy danh sách tenant với phân trang và lọc
func (r *mysqlRepository) ListTenants(ctx context.Context, params dto.ListTenantsParams) ([]internal.Tenant, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.Tenant{})

	// Áp dụng các điều kiện lọc
	if params.OwnerID != nil {
		query = query.Where("owner_user_id = ?", *params.OwnerID)
	}

	if params.ActiveOnly {
		query = query.Where("is_active = ?", true)
	}

	if params.PlanID != "" {
		query = query.Where("plan_id = ?", params.PlanID)
	}

	if params.Search != "" {
		searchTerm := "%" + params.Search + "%"
		query = query.Where("name LIKE ? OR slug LIKE ? OR description LIKE ?", searchTerm, searchTerm, searchTerm)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var tenants []internal.Tenant
	if err := query.Find(&tenants).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return tenants, total, nil
}

// GetTenantsByOwnerID lấy danh sách tenant theo owner ID
func (r *mysqlRepository) GetTenantsByOwnerID(ctx context.Context, ownerID int64) ([]internal.Tenant, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_tenants")
	defer span.End()

	var tenants []internal.Tenant
	result := r.db.WithContext(ctx).Where("owner_user_id = ?", ownerID).Find(&tenants)
	if result.Error != nil {
		r.logger.Error("Không thể lấy danh sách tenant theo owner", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return tenants, nil
}

// TenantUser methods
// ---------------------------------

// AddUserToTenant thêm user vào tenant
func (r *mysqlRepository) AddUserToTenant(ctx context.Context, tenantUser *internal.TenantUser) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "INSERT", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).Create(tenantUser)
	if result.Error != nil {
		if strings.Contains(result.Error.Error(), "Duplicate entry") {
			r.logger.Error("Không thể thêm user vào tenant do đã tồn tại", logger.String("error", result.Error.Error()))
			tracing.RecordError(ctx, result.Error)
			
			// Nếu đã tồn tại, có thể cập nhật
			tenantUser.UpdatedAt = time.Now()
			result = r.db.WithContext(ctx).Model(&internal.TenantUser{}).
				Where("tenant_id = ? AND user_id = ?", tenantUser.TenantID, tenantUser.UserID).
				Updates(map[string]interface{}{
					"role":       tenantUser.Role,
					"is_active":  tenantUser.IsActive,
					"updated_at": tenantUser.UpdatedAt,
				})
			
			if result.Error != nil {
				r.logger.Error("Không thể cập nhật user trong tenant", logger.String("error", result.Error.Error()))
				tracing.RecordError(ctx, result.Error)
				return internal.ErrDatabaseError
			}
			
			return nil
		}
		
		r.logger.Error("Không thể thêm user vào tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	return nil
}

// GetTenantUser lấy thông tin liên kết giữa tenant và user
func (r *mysqlRepository) GetTenantUser(ctx context.Context, tenantID, userID int64) (*internal.TenantUser, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	var tenantUser internal.TenantUser
	result := r.db.WithContext(ctx).Where("tenant_id = ? AND user_id = ?", tenantID, userID).First(&tenantUser)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin user trong tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return &tenantUser, nil
}

// UpdateTenantUser cập nhật liên kết giữa tenant và user
func (r *mysqlRepository) UpdateTenantUser(ctx context.Context, tenantUser *internal.TenantUser) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "UPDATE", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).Model(&internal.TenantUser{}).
		Where("tenant_id = ? AND user_id = ?", tenantUser.TenantID, tenantUser.UserID).
		Updates(map[string]interface{}{
			"role":       tenantUser.Role,
			"is_active":  tenantUser.IsActive,
			"updated_at": time.Now(),
		})

	if result.Error != nil {
		r.logger.Error("Không thể cập nhật user trong tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrUserNotFound
	}

	return nil
}

// RemoveUserFromTenant xóa user khỏi tenant
func (r *mysqlRepository) RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error {
	ctx, span := tracing.DatabaseMiddleware(ctx, "DELETE", "tenant_users")
	defer span.End()

	result := r.db.WithContext(ctx).Where("tenant_id = ? AND user_id = ?", tenantID, userID).Delete(&internal.TenantUser{})
	if result.Error != nil {
		r.logger.Error("Không thể xóa user khỏi tenant", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return internal.ErrDatabaseError
	}

	if result.RowsAffected == 0 {
		return internal.ErrUserNotFound
	}

	return nil
}

// ListTenantUsers lấy danh sách user trong tenant với phân trang và lọc
func (r *mysqlRepository) ListTenantUsers(ctx context.Context, tenantID int64, params dto.ListTenantUsersParams) ([]internal.TenantUser, int64, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	query := r.db.WithContext(ctx).Model(&internal.TenantUser{}).Where("tenant_id = ?", tenantID)

	// Áp dụng các điều kiện lọc
	if params.Role != "" {
		query = query.Where("role = ?", params.Role)
	}

	if params.ActiveOnly {
		query = query.Where("is_active = ?", true)
	}

	// Đếm tổng số bản ghi
	var total int64
	if err := query.Count(&total).Error; err != nil {
		r.logger.Error("Không thể đếm tổng số user trong tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	// Xác định thứ tự sắp xếp
	sortBy := params.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}

	sortOrder := params.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	// Áp dụng phân trang và sắp xếp
	page := params.Page
	if page <= 0 {
		page = 1
	}

	pageSize := params.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize
	query = query.Order(clause.OrderByColumn{Column: clause.Column{Name: sortBy}, Desc: strings.ToLower(sortOrder) == "desc"})
	query = query.Offset(offset).Limit(pageSize)

	// Lấy dữ liệu
	var tenantUsers []internal.TenantUser
	if err := query.Find(&tenantUsers).Error; err != nil {
		r.logger.Error("Không thể lấy danh sách user trong tenant", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, 0, internal.ErrDatabaseError
	}

	return tenantUsers, total, nil
}

// GetUserTenants lấy danh sách tenant mà user tham gia
func (r *mysqlRepository) GetUserTenants(ctx context.Context, userID int64) ([]internal.TenantUser, error) {
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "tenant_users")
	defer span.End()

	var tenantUsers []internal.TenantUser
	result := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&tenantUsers)
	if result.Error != nil {
		r.logger.Error("Không thể lấy danh sách tenant của user", logger.String("error", result.Error.Error()))
		tracing.RecordError(ctx, result.Error)
		return nil, internal.ErrDatabaseError
	}

	return tenantUsers, nil
}
