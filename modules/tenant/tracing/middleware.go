package tracing

import (
	"github.com/gin-gonic/gin"
)

// TraceRequest là middleware Gin để theo dõi tất cả HTTP requests
func TraceRequest() gin.HandlerFunc {
	return func(c *gin.Context) {
		operationName := "HTTP " + c.Request.Method + " " + c.FullPath()
		if operationName == "" {
			operationName = "HTTP " + c.Request.Method + " " + c.Request.URL.Path
		}

		// Bắt đầu span mới
		_, otSpan, jaegerSpan := StartHTTPSpan(c, operationName)
		defer FinishTenantSpan(otSpan, jaegerSpan)

		// Thêm các thuộc tính tenant nếu có
		tenantID, exists := c.Get("tenant_id")
		if exists {
			tenantAttrs := map[string]interface{}{
				"tenant.id": tenantID,
			}
			AddTenantAttributes(otSpan, jaegerSpan, tenantAttrs)
		}

		// Tiế<PERSON> tụ<PERSON> x<PERSON> lý request
		c.Next()

		// <PERSON>hi lại thông tin phản hồi
		responseAttrs := map[string]interface{}{
			"http.status_code": c.Writer.Status(),
			"http.completed":   true,
		}
		AddTenantAttributes(otSpan, jaegerSpan, responseAttrs)

		// Ghi lại lỗi nếu có
		if len(c.Errors) > 0 {
			errAttrs := map[string]interface{}{
				"error":         true,
				"error.message": c.Errors.String(),
			}
			AddTenantAttributes(otSpan, jaegerSpan, errAttrs)
		}
	}
}

// TraceTenantContext là middleware để thêm thông tin chi tiết về tenant vào spans
func TraceTenantContext() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Trích xuất thông tin tenant từ context hoặc JWT
		tenantID, _ := c.Get("tenant_id")
		tenantName, _ := c.Get("tenant_name")

		// Lưu trữ thông tin vào context cho các spans con
		c.Set("trace.tenant_id", tenantID)
		c.Set("trace.tenant_name", tenantName)

		c.Next()
	}
}
