package tracing

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/opentracing/opentracing-go"
	"github.com/webnew/wn-backend-v2/pkg/tracing"
	"go.opentelemetry.io/otel/trace"
)

// StartTenantSpan bắt đầu một span mới cho các hoạt động tenant
func StartTenantSpan(ctx context.Context, operationName string) (context.Context, trace.Span, opentracing.Span) {
	var otSpan trace.Span
	var jaegerSpan opentracing.Span
	var newCtx context.Context

	// Nếu GlobalTracer (Jaeger) được khởi tạo
	if GlobalTracer != nil {
		jaegerSpan = GlobalTracer.StartSpan(operationName)
		return ctx, nil, jaegerSpan
	} else {
		// Sử dụng OpenTelemetry (SignOz)
		newCtx, otSpan = tracing.StartSpan(ctx, operationName)
		return newCtx, otSpan, nil
	}
}

// AddTenantAttributes thêm các thuộc t<PERSON>h tenant vào span
func AddTenantAttributes(otSpan trace.Span, jaegerSpan opentracing.Span, attrs map[string]interface{}) {
	// Nếu sử dụng OpenTelemetry (SignOz)
	if otSpan != nil {
		for key, value := range attrs {
			tracing.AddAttribute(otSpan, key, value)
		}
	}

	// Nếu sử dụng Jaeger
	if jaegerSpan != nil {
		for key, value := range attrs {
			tracing.SetTag(jaegerSpan, key, value)
		}
	}
}

// RecordTenantError ghi lại lỗi cho các hoạt động tenant
func RecordTenantError(otSpan trace.Span, jaegerSpan opentracing.Span, err error) {
	if err == nil {
		return
	}

	// Nếu sử dụng OpenTelemetry (SignOz)
	if otSpan != nil {
		tracing.RecordError(otSpan, err)
	}

	// Nếu sử dụng Jaeger
	if jaegerSpan != nil {
		tracing.LogError(jaegerSpan, err)
	}
}

// FinishTenantSpan kết thúc span
func FinishTenantSpan(otSpan trace.Span, jaegerSpan opentracing.Span) {
	if jaegerSpan != nil {
		tracing.FinishSpan(jaegerSpan)
	}
	// Không cần làm gì với otSpan vì nó sẽ tự động kết thúc khi context kết thúc
}

// GetTenantAttributes tạo bản đồ thuộc tính cho tenant
func GetTenantAttributes(tenantID int, tenantName string, operation string, resource string) map[string]interface{} {
	return map[string]interface{}{
		"tenant.id":        tenantID,
		"tenant.name":      tenantName,
		"tenant.operation": operation,
		"tenant.resource":  resource,
	}
}

// StartHTTPSpan bắt đầu một span mới cho HTTP request trong Gin
func StartHTTPSpan(c *gin.Context, operationName string) (context.Context, trace.Span, opentracing.Span) {
	// Lấy context từ gin
	ctx := c.Request.Context()

	// Bắt đầu span mới
	newCtx, otSpan, jaegerSpan := StartTenantSpan(ctx, operationName)

	// Thêm các thuộc tính HTTP
	httpAttrs := map[string]interface{}{
		"http.method":     c.Request.Method,
		"http.url":        c.Request.URL.String(),
		"http.user_agent": c.Request.UserAgent(),
		"http.path":       c.Request.URL.Path,
	}
	AddTenantAttributes(otSpan, jaegerSpan, httpAttrs)

	// Cập nhật context trong gin
	c.Request = c.Request.WithContext(newCtx)

	return newCtx, otSpan, jaegerSpan
}

// TraceTenantOperation bọc một hàm với tracing
func TraceTenantOperation(ctx context.Context, operationName string, f func(context.Context) error) error {
	newCtx, otSpan, jaegerSpan := StartTenantSpan(ctx, operationName)
	defer FinishTenantSpan(otSpan, jaegerSpan)

	err := f(newCtx)
	if err != nil {
		RecordTenantError(otSpan, jaegerSpan, err)
	}

	return err
}
