package tracing

import (
	"log"

	"github.com/webnew/wn-backend-v2/modules/tenant/configs"
)

// InitTracer khởi tạo tracing từ cấu hình module và trả về hàm cleanup
// Hàm này được gọi từ main.go
func InitTracer(cfg *configs.Config) (func(), error) {
	err := InitFromConfig(cfg)
	if err != nil {
		return nil, err
	}

	// Trả về hàm cleanup
	return ShutdownTracing, nil
}

// InitFromConfig khởi tạo tracing từ cấu hình module
func InitFromConfig(cfg *configs.Config) error {
	if cfg == nil || cfg.Tracing == nil {
		log.Println("[WARNING] Cấu hình tracing không được cung cấp hoặc bị vô hiệu hóa")
		return nil
	}

	// Chuyển đổi từ configs.TracingConfig sang tracing.TracingConfig
	tracingConfig := &TracingConfig{
		Enabled:      cfg.Tracing.Enabled,
		ServiceName:  cfg.Tracing.ServiceName,
		ExporterType: cfg.Tracing.ExporterType,
		SampleRatio:  cfg.Tracing.SampleRatio,
	}

	// Thiết lập Signoz
	tracingConfig.Signoz.Endpoint = cfg.Tracing.Signoz.Endpoint

	// Thiết lập Jaeger
	tracingConfig.Jaeger.Host = cfg.Tracing.Jaeger.Host
	tracingConfig.Jaeger.Port = cfg.Tracing.Jaeger.Port

	// Khởi tạo tracing
	err := InitTracing(tracingConfig)
	if err != nil {
		return err
	}

	log.Println("[INFO] Tracing được khởi tạo thành công từ cấu hình module")
	return nil
}

// ShutdownTracing đóng tracer khi module dừng hoạt động
func ShutdownTracing() {
	Shutdown()
	log.Println("[INFO] Tracers đã được đóng thành công")
}
