package tracing

import (
	"fmt"
	"io"
	"log"

	"github.com/opentracing/opentracing-go"
	"github.com/webnew/wn-backend-v2/pkg/tracing"
)

// TracingConfig chứa cấu hình cho tracing
type TracingConfig struct {
	Enabled      bool    `mapstructure:"enabled"`
	ServiceName  string  `mapstructure:"service_name"`
	ExporterType string  `mapstructure:"exporter_type"`
	SampleRatio  float64 `mapstructure:"sample_ratio"`
	Signoz       struct {
		Endpoint string `mapstructure:"endpoint"`
	} `mapstructure:"signoz"`
	Jaeger struct {
		Host string `mapstructure:"host"`
		Port string `mapstructure:"port"`
	} `mapstructure:"jaeger"`
}

// GlobalTracer lưu trữ tracer toàn cục
var GlobalTracer opentracing.Tracer

// GlobalCloser lưu trữ closer để đóng tracer
var GlobalCloser io.Closer

// ShutdownFunc là hàm đóng tracer cho SignOz
var ShutdownFunc func()

// InitTracing khởi tạo hệ thống tracing dựa vào cấu hình
func InitTracing(config *TracingConfig) error {
	if config == nil {
		return fmt.Errorf("cấu hình tracing không được cung cấp")
	}

	if !config.Enabled {
		log.Println("[INFO] Tracing không được bật, bỏ qua khởi tạo")
		return nil
	}

	serviceName := config.ServiceName
	if serviceName == "" {
		serviceName = "tenant-service"
	}

	var err error

	// Dựa vào loại exporter để khởi tạo tracer tương ứng
	switch config.ExporterType {
	case "signoz":
		log.Println("[INFO] Khởi tạo SignOz tracer")
		ShutdownFunc, err = tracing.InitSignozTracer(serviceName, config.Signoz.Endpoint)
		if err != nil {
			return fmt.Errorf("không thể khởi tạo SignOz tracer: %w", err)
		}
	case "jaeger", "":
		// Mặc định sử dụng Jaeger nếu không chỉ định hoặc chỉ định rõ
		log.Println("[INFO] Khởi tạo Jaeger tracer")
		GlobalTracer, GlobalCloser, err = tracing.InitJaeger(serviceName, config.Jaeger.Host, config.Jaeger.Port)
		if err != nil {
			return fmt.Errorf("không thể khởi tạo Jaeger tracer: %w", err)
		}
	default:
		return fmt.Errorf("loại exporter không hỗ trợ: %s", config.ExporterType)
	}

	log.Printf("[INFO] Tracing đã được khởi tạo thành công cho service %s với exporter %s",
		serviceName, config.ExporterType)

	return nil
}

// Shutdown đóng tracer
func Shutdown() {
	if GlobalCloser != nil {
		log.Println("[INFO] Đóng Jaeger tracer")
		if err := GlobalCloser.Close(); err != nil {
			log.Printf("[ERROR] Lỗi khi đóng Jaeger tracer: %v", err)
		}
	}

	if ShutdownFunc != nil {
		log.Println("[INFO] Đóng SignOz tracer")
		ShutdownFunc()
	}
}
