package internal

import (
	"context"
	"net/http"
	"time"
	"wnapi/modules/tenant/dto"
)

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrTenantNotFound là lỗi khi không tìm thấy tenant
	ErrTenantNotFound ServiceError = "tenant_not_found"
	// ErrInvalidTenantData là lỗi khi dữ liệu tenant không hợp lệ
	ErrInvalidTenantData ServiceError = "invalid_tenant_data"
	// ErrTenantNameTooShort là lỗi khi tên tenant quá ngắn
	ErrTenantNameTooShort ServiceError = "tenant_name_too_short"
	// ErrTenantNameTooLong là lỗi khi tên tenant quá dài
	ErrTenantNameTooLong ServiceError = "tenant_name_too_long"
	// ErrTenantSlugTooShort là lỗi khi slug tenant quá ngắn
	ErrTenantSlugTooShort ServiceError = "tenant_slug_too_short"
	// ErrTenantSlugTooLong là lỗi khi slug tenant quá dài
	ErrTenantSlugTooLong ServiceError = "tenant_slug_too_long"
	// ErrTenantSlugExists là lỗi khi slug tenant đã tồn tại
	ErrTenantSlugExists ServiceError = "tenant_slug_exists"
	// ErrMaxTenantsPerUserExceeded là lỗi khi vượt quá số lượng tenant tối đa cho mỗi user
	ErrMaxTenantsPerUserExceeded ServiceError = "max_tenants_per_user_exceeded"
	// ErrUserNotFound là lỗi khi không tìm thấy user
	ErrUserNotFound ServiceError = "user_not_found"
	// ErrPlanNotFound là lỗi khi không tìm thấy plan
	ErrPlanNotFound ServiceError = "plan_not_found"
	// ErrDatabaseError là lỗi khi tương tác với database
	ErrDatabaseError ServiceError = "database_error"
	// ErrInvalidSortField là lỗi khi trường sắp xếp không hợp lệ
	ErrInvalidSortField ServiceError = "invalid_sort_field"
	// ErrUnauthorizedAccess là lỗi khi không có quyền truy cập
	ErrUnauthorizedAccess ServiceError = "unauthorized_access"
	// ErrTenantDisabled là lỗi khi tenant đã bị vô hiệu hóa
	ErrTenantDisabled ServiceError = "tenant_disabled"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrTenantNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy tenant",
		ErrorCode:  "TENANT_NOT_FOUND",
	},
	ErrInvalidTenantData: {
		StatusCode: http.StatusBadRequest,
		Message:    "Dữ liệu tenant không hợp lệ",
		ErrorCode:  "INVALID_TENANT_DATA",
	},
	ErrTenantNameTooShort: {
		StatusCode: http.StatusBadRequest,
		Message:    "Tên tenant quá ngắn",
		ErrorCode:  "TENANT_NAME_TOO_SHORT",
	},
	ErrTenantNameTooLong: {
		StatusCode: http.StatusBadRequest,
		Message:    "Tên tenant quá dài",
		ErrorCode:  "TENANT_NAME_TOO_LONG",
	},
	ErrTenantSlugTooShort: {
		StatusCode: http.StatusBadRequest,
		Message:    "Slug tenant quá ngắn",
		ErrorCode:  "TENANT_SLUG_TOO_SHORT",
	},
	ErrTenantSlugTooLong: {
		StatusCode: http.StatusBadRequest,
		Message:    "Slug tenant quá dài",
		ErrorCode:  "TENANT_SLUG_TOO_LONG",
	},
	ErrTenantSlugExists: {
		StatusCode: http.StatusConflict,
		Message:    "Slug tenant đã tồn tại",
		ErrorCode:  "TENANT_SLUG_EXISTS",
	},
	ErrMaxTenantsPerUserExceeded: {
		StatusCode: http.StatusBadRequest,
		Message:    "Đã vượt quá số lượng tenant tối đa cho mỗi user",
		ErrorCode:  "MAX_TENANTS_PER_USER_EXCEEDED",
	},
	ErrUserNotFound: {
		StatusCode: http.StatusBadRequest,
		Message:    "Không tìm thấy user",
		ErrorCode:  "USER_NOT_FOUND",
	},
	ErrPlanNotFound: {
		StatusCode: http.StatusBadRequest,
		Message:    "Không tìm thấy gói dịch vụ",
		ErrorCode:  "PLAN_NOT_FOUND",
	},
	ErrDatabaseError: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi cơ sở dữ liệu",
		ErrorCode:  "DATABASE_ERROR",
	},
	ErrInvalidSortField: {
		StatusCode: http.StatusBadRequest,
		Message:    "Trường sắp xếp không hợp lệ",
		ErrorCode:  "INVALID_SORT_FIELD",
	},
	ErrUnauthorizedAccess: {
		StatusCode: http.StatusForbidden,
		Message:    "Không có quyền truy cập",
		ErrorCode:  "UNAUTHORIZED_ACCESS",
	},
	ErrTenantDisabled: {
		StatusCode: http.StatusForbidden,
		Message:    "Tenant đã bị vô hiệu hóa",
		ErrorCode:  "TENANT_DISABLED",
	},
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}
	
	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// Tenant định nghĩa cấu trúc dữ liệu cho tenant
type Tenant struct {
	ID           int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name         string    `gorm:"column:name;size:50;not null" json:"name"`
	Slug         string    `gorm:"column:slug;size:30;uniqueIndex" json:"slug"`
	Description  string    `gorm:"column:description;size:500" json:"description"`
	OwnerUserID  int64     `gorm:"column:owner_user_id;index" json:"owner_user_id"`
	PlanID       string    `gorm:"column:plan_id;size:50" json:"plan_id"`
	Currency     string    `gorm:"column:currency;size:3" json:"currency"`
	IsActive     bool      `gorm:"column:is_active;default:true" json:"is_active"`
	TimeZone     string    `gorm:"column:time_zone;size:50" json:"time_zone"`
	TrialEndsAt  *time.Time `gorm:"column:trial_ends_at" json:"trial_ends_at"`
	Logo         string    `gorm:"column:logo;size:255" json:"logo"`
	ContactEmail string    `gorm:"column:contact_email;size:255" json:"contact_email"`
	ContactPhone string    `gorm:"column:contact_phone;size:50" json:"contact_phone"`
	CreatedAt    time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (Tenant) TableName() string {
	return "tenant_tenants"
}

// TenantUser định nghĩa cấu trúc dữ liệu cho liên kết giữa tenant và user
type TenantUser struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	TenantID  int64     `gorm:"column:tenant_id;index:idx_tenant_user,unique:true" json:"tenant_id"`
	UserID    int64     `gorm:"column:user_id;index:idx_tenant_user,unique:true" json:"user_id"`
	Role      string    `gorm:"column:role;size:50;not null" json:"role"`
	IsActive  bool      `gorm:"column:is_active;default:true" json:"is_active"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (TenantUser) TableName() string {
	return "tenant_users"
}

// Repository định nghĩa interface cho tenant repository
type Repository interface {
	// Tenant methods
	CreateTenant(ctx context.Context, tenant *Tenant) error
	GetTenantByID(ctx context.Context, id int64) (*Tenant, error)
	GetTenantBySlug(ctx context.Context, slug string) (*Tenant, error)
	UpdateTenant(ctx context.Context, tenant *Tenant) error
	DeleteTenant(ctx context.Context, id int64) error
	ListTenants(ctx context.Context, params dto.ListTenantsParams) ([]Tenant, int64, error)
	GetTenantsByOwnerID(ctx context.Context, ownerID int64) ([]Tenant, error)
	
	// TenantUser methods
	AddUserToTenant(ctx context.Context, tenantUser *TenantUser) error
	GetTenantUser(ctx context.Context, tenantID, userID int64) (*TenantUser, error)
	UpdateTenantUser(ctx context.Context, tenantUser *TenantUser) error
	RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error
	ListTenantUsers(ctx context.Context, tenantID int64, params dto.ListTenantUsersParams) ([]TenantUser, int64, error)
	GetUserTenants(ctx context.Context, userID int64) ([]TenantUser, error)
}

// TenantService định nghĩa interface cho tenant service
type TenantService interface {
	// Tenant methods
	CreateTenant(ctx context.Context, req dto.CreateTenantRequest) (*dto.TenantResponse, error)
	GetTenant(ctx context.Context, id int64) (*dto.TenantResponse, error)
	GetTenantBySlug(ctx context.Context, slug string) (*dto.TenantResponse, error)
	UpdateTenant(ctx context.Context, id int64, req dto.UpdateTenantRequest) (*dto.TenantResponse, error)
	DeleteTenant(ctx context.Context, id int64) error
	ListTenants(ctx context.Context, params dto.ListTenantsParams) (*dto.ListTenantsResponse, error)
	GetMyTenants(ctx context.Context, userID int64) (*dto.ListTenantsResponse, error)
	
	// TenantUser methods
	AddUserToTenant(ctx context.Context, req dto.AddUserRequest) (*dto.TenantUserResponse, error)
	UpdateTenantUser(ctx context.Context, tenantID, userID int64, req dto.UpdateTenantUserRequest) (*dto.TenantUserResponse, error)
	RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error
	ListTenantUsers(ctx context.Context, tenantID int64, params dto.ListTenantUsersParams) (*dto.ListTenantUsersResponse, error)
	GetUserTenants(ctx context.Context, userID int64) (*dto.ListTenantUsersResponse, error)
}
