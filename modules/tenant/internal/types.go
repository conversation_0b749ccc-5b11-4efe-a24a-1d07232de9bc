package internal

import (
	"context"
	"net/http"
	"time"
	"wnapi/modules/tenant/dto"
)

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrTenantNotFound là lỗi khi không tìm thấy tenant
	ErrTenantNotFound ServiceError = "tenant_not_found"
	// ErrPlanNotFound là lỗi khi không tìm thấy plan
	ErrPlanNotFound ServiceError = "plan_not_found"
	// ErrInvalidTenantData là lỗi khi dữ liệu tenant không hợp lệ
	ErrInvalidTenantData ServiceError = "invalid_tenant_data"
	// ErrTenantNameTooLong là lỗi khi tên tenant quá dài
	ErrTenantNameTooLong ServiceError = "tenant_name_too_long"
	// ErrSubdomainTooLong là lỗi khi subdomain quá dài
	ErrSubdomainTooLong ServiceError = "subdomain_too_long"
	// ErrSubdomainAlreadyExists là lỗi khi subdomain đã tồn tại
	ErrSubdomainAlreadyExists ServiceError = "subdomain_already_exists"
	// ErrSubdomainReserved là lỗi khi subdomain nằm trong danh sách đã được đặt trước
	ErrSubdomainReserved ServiceError = "subdomain_reserved"
	// ErrMaxTenantsReached là lỗi khi đã đạt đến số lượng tenant tối đa cho user
	ErrMaxTenantsReached ServiceError = "max_tenants_reached"
	// ErrDatabaseError là lỗi khi tương tác với database
	ErrDatabaseError ServiceError = "database_error"
	// ErrInvalidSortField là lỗi khi trường sắp xếp không hợp lệ
	ErrInvalidSortField ServiceError = "invalid_sort_field"
	// ErrInvalidPlan là lỗi khi plan không hợp lệ
	ErrInvalidPlan ServiceError = "invalid_plan"
	// ErrForbidden là lỗi khi không có quyền truy cập
	ErrForbidden ServiceError = "forbidden"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrTenantNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy tenant",
		ErrorCode:  "TENANT_NOT_FOUND",
	},
	ErrPlanNotFound: {
		StatusCode: http.StatusNotFound,
		Message:    "Không tìm thấy plan",
		ErrorCode:  "PLAN_NOT_FOUND",
	},
	ErrInvalidTenantData: {
		StatusCode: http.StatusBadRequest,
		Message:    "Dữ liệu tenant không hợp lệ",
		ErrorCode:  "INVALID_TENANT_DATA",
	},
	ErrTenantNameTooLong: {
		StatusCode: http.StatusBadRequest,
		Message:    "Tên tenant quá dài",
		ErrorCode:  "TENANT_NAME_TOO_LONG",
	},
	ErrSubdomainTooLong: {
		StatusCode: http.StatusBadRequest,
		Message:    "Subdomain quá dài",
		ErrorCode:  "SUBDOMAIN_TOO_LONG",
	},
	ErrSubdomainAlreadyExists: {
		StatusCode: http.StatusConflict,
		Message:    "Subdomain đã tồn tại",
		ErrorCode:  "SUBDOMAIN_ALREADY_EXISTS",
	},
	ErrSubdomainReserved: {
		StatusCode: http.StatusBadRequest,
		Message:    "Subdomain đã được đặt trước",
		ErrorCode:  "SUBDOMAIN_RESERVED",
	},
	ErrMaxTenantsReached: {
		StatusCode: http.StatusForbidden,
		Message:    "Đã đạt đến số lượng tenant tối đa",
		ErrorCode:  "MAX_TENANTS_REACHED",
	},
	ErrDatabaseError: {
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi cơ sở dữ liệu",
		ErrorCode:  "DATABASE_ERROR",
	},
	ErrInvalidSortField: {
		StatusCode: http.StatusBadRequest,
		Message:    "Trường sắp xếp không hợp lệ",
		ErrorCode:  "INVALID_SORT_FIELD",
	},
	ErrInvalidPlan: {
		StatusCode: http.StatusBadRequest,
		Message:    "Plan không hợp lệ",
		ErrorCode:  "INVALID_PLAN",
	},
	ErrForbidden: {
		StatusCode: http.StatusForbidden,
		Message:    "Không có quyền thực hiện hành động này",
		ErrorCode:  "FORBIDDEN",
	},
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}
	
	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// Tenant định nghĩa cấu trúc dữ liệu cho tenant
type Tenant struct {
	ID            int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name          string    `gorm:"column:name;size:100;not null" json:"name"`
	Subdomain     string    `gorm:"column:subdomain;size:63;uniqueIndex" json:"subdomain"`
	PlanID        int       `gorm:"column:plan_id;index" json:"plan_id"`
	OwnerID       int64     `gorm:"column:owner_id;index" json:"owner_id"`
	Status        string    `gorm:"column:status;size:20;default:active" json:"status"`
	StorageLimit  int64     `gorm:"column:storage_limit" json:"storage_limit"`
	StorageUsed   int64     `gorm:"column:storage_used" json:"storage_used"`
	UserLimit     int       `gorm:"column:user_limit" json:"user_limit"`
	BandwidthLimit int64    `gorm:"column:bandwidth_limit" json:"bandwidth_limit"`
	CustomDomain  string    `gorm:"column:custom_domain;size:255" json:"custom_domain"`
	ExpiresAt     *time.Time `gorm:"column:expires_at" json:"expires_at"`
	CreatedAt     time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (Tenant) TableName() string {
	return "tenant_tenants"
}

// Plan định nghĩa cấu trúc dữ liệu cho plan
type Plan struct {
	ID             int       `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Name           string    `gorm:"column:name;size:100;not null" json:"name"`
	Description    string    `gorm:"column:description;size:500" json:"description"`
	Price          float64   `gorm:"column:price" json:"price"`
	BillingCycle   string    `gorm:"column:billing_cycle;size:20;default:monthly" json:"billing_cycle"`
	StorageLimit   int64     `gorm:"column:storage_limit" json:"storage_limit"`
	BandwidthLimit int64     `gorm:"column:bandwidth_limit" json:"bandwidth_limit"`
	UserLimit      int       `gorm:"column:user_limit" json:"user_limit"`
	IsActive       bool      `gorm:"column:is_active;default:true" json:"is_active"`
	Features       string    `gorm:"column:features;type:text" json:"features"`
	CreatedAt      time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (Plan) TableName() string {
	return "tenant_plans"
}

// TenantUser định nghĩa cấu trúc dữ liệu cho mối quan hệ giữa tenant và user
type TenantUser struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	TenantID  int64     `gorm:"column:tenant_id;index" json:"tenant_id"`
	UserID    int64     `gorm:"column:user_id;index" json:"user_id"`
	Role      string    `gorm:"column:role;size:20;default:member" json:"role"`
	Status    string    `gorm:"column:status;size:20;default:active" json:"status"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime" json:"updated_at"`
}

// TableName sets the insert table name for this struct type
func (TenantUser) TableName() string {
	return "tenant_users"
}

// Repository định nghĩa interface cho tenant repository
type Repository interface {
	// Tenant methods
	CreateTenant(ctx context.Context, tenant *Tenant) error
	GetTenantByID(ctx context.Context, id int64) (*Tenant, error)
	GetTenantBySubdomain(ctx context.Context, subdomain string) (*Tenant, error)
	UpdateTenant(ctx context.Context, tenant *Tenant) error
	DeleteTenant(ctx context.Context, id int64) error
	ListTenants(ctx context.Context, params dto.ListTenantsParams) ([]Tenant, int64, error)
	GetTenantsByOwnerID(ctx context.Context, ownerID int64) ([]Tenant, error)
	CountTenantsByOwnerID(ctx context.Context, ownerID int64) (int, error)
	
	// Plan methods
	GetPlanByID(ctx context.Context, id int) (*Plan, error)
	ListPlans(ctx context.Context, params dto.ListPlansParams) ([]Plan, int64, error)
	
	// TenantUser methods
	AddUserToTenant(ctx context.Context, tenantUser *TenantUser) error
	RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error
	UpdateTenantUserRole(ctx context.Context, tenantID, userID int64, role string) error
	GetTenantUsers(ctx context.Context, tenantID int64, params dto.ListTenantUsersParams) ([]TenantUser, int64, error)
	GetUserTenants(ctx context.Context, userID int64) ([]Tenant, error)
	GetTenantUser(ctx context.Context, tenantID, userID int64) (*TenantUser, error)
}

// TenantService định nghĩa interface cho tenant service
type TenantService interface {
	// Tenant methods
	CreateTenant(ctx context.Context, req dto.CreateTenantRequest, ownerID int64) (*dto.TenantResponse, error)
	GetTenant(ctx context.Context, id int64) (*dto.TenantResponse, error)
	GetTenantBySubdomain(ctx context.Context, subdomain string) (*dto.TenantResponse, error)
	UpdateTenant(ctx context.Context, id int64, req dto.UpdateTenantRequest) (*dto.TenantResponse, error)
	DeleteTenant(ctx context.Context, id int64) error
	ListTenants(ctx context.Context, params dto.ListTenantsParams) (*dto.ListTenantsResponse, error)
	GetUserTenants(ctx context.Context, userID int64) (*dto.UserTenantsResponse, error)
	
	// Plan methods
	GetPlan(ctx context.Context, id int) (*dto.PlanResponse, error)
	ListPlans(ctx context.Context, params dto.ListPlansParams) (*dto.ListPlansResponse, error)
	
	// TenantUser methods
	AddUserToTenant(ctx context.Context, req dto.AddUserRequest, tenantID int64) (*dto.TenantUserResponse, error)
	RemoveUserFromTenant(ctx context.Context, tenantID, userID int64) error
	UpdateTenantUserRole(ctx context.Context, tenantID, userID int64, req dto.UpdateTenantUserRequest) (*dto.TenantUserResponse, error)
	GetTenantUsers(ctx context.Context, tenantID int64, params dto.ListTenantUsersParams) (*dto.ListTenantUsersResponse, error)
	GetCurrentUserTenants(ctx context.Context, userID int64) (*dto.UserTenantsResponse, error)
	
	// Validation methods
	IsValidSubdomain(subdomain string) (bool, error)
}
