package internal

import (
	"fmt"
	"log"
	"os"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// TenantConfig chứa cấu hình tenant service
type TenantConfig struct {
	DefaultPlanID          string `env:"DEFAULT_PLAN_ID" envDefault:"free"`
	MaxTenantsPerUser      int    `env:"MAX_TENANTS_PER_USER" envDefault:"5"`
	DefaultCurrency        string `env:"DEFAULT_CURRENCY" envDefault:"VND"`
	EnableTrialPeriod      bool   `env:"ENABLE_TRIAL_PERIOD" envDefault:"true"`
	TrialPeriodDays        int    `env:"TRIAL_PERIOD_DAYS" envDefault:"14"`
	DefaultTenantTimeZone  string `env:"DEFAULT_TENANT_TIMEZONE" envDefault:"Asia/Ho_Chi_Minh"`
	TenantNameMinLength    int    `env:"TENANT_NAME_MIN_LENGTH" envDefault:"3"`
	TenantNameMaxLength    int    `env:"TENANT_NAME_MAX_LENGTH" envDefault:"50"`
	TenantSlugMinLength    int    `env:"TENANT_SLUG_MIN_LENGTH" envDefault:"3"`
	TenantSlugMaxLength    int    `env:"TENANT_SLUG_MAX_LENGTH" envDefault:"30"`
	MaxPageSize            int    `env:"MAX_PAGE_SIZE" envDefault:"100"`
	DefaultPageSize        int    `env:"DEFAULT_PAGE_SIZE" envDefault:"10"`
	CacheTimeoutMinutes    int    `env:"CACHE_TIMEOUT_MINUTES" envDefault:"10"`
	Message                string `env:"MESSAGE" envDefault:"Xin chào từ module Tenant!"`
}

// LoadTenantConfig đọc cấu hình tenant từ biến môi trường
func LoadTenantConfig() (*TenantConfig, error) {
	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	// Khởi tạo config mặc định
	cfg := GetDefaultConfig()

	// Đọc cấu hình từ biến môi trường với prefix TENANT_
	opts := env.Options{
		Prefix: "TENANT_",
	}
	if err := env.ParseWithOptions(cfg, opts); err != nil {
		return nil, fmt.Errorf("lỗi đọc cấu hình tenant từ biến môi trường: %w", err)
	}

	return cfg, nil
}

// GetDefaultConfig trả về cấu hình mặc định
func GetDefaultConfig() *TenantConfig {
	return &TenantConfig{
		DefaultPlanID:          "free",
		MaxTenantsPerUser:      5,
		DefaultCurrency:        "VND",
		EnableTrialPeriod:      true,
		TrialPeriodDays:        14,
		DefaultTenantTimeZone:  "Asia/Ho_Chi_Minh",
		TenantNameMinLength:    3,
		TenantNameMaxLength:    50,
		TenantSlugMinLength:    3,
		TenantSlugMaxLength:    30,
		MaxPageSize:            100,
		DefaultPageSize:        10,
		CacheTimeoutMinutes:    10,
		Message:                "Xin chào từ module Tenant!",
	}
}
