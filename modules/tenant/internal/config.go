package internal

import (
	"fmt"
	"log"
	"os"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// TenantConfig chứa cấu hình tenant service
type TenantConfig struct {
	MaxTenantsPerUser      int    `env:"MAX_TENANTS_PER_USER" envDefault:"10"`
	DefaultPlanID          int    `env:"DEFAULT_PLAN_ID" envDefault:"1"`
	MaxTenantNameLength    int    `env:"MAX_TENANT_NAME_LENGTH" envDefault:"100"`
	MaxSubdomainLength     int    `env:"MAX_SUBDOMAIN_LENGTH" envDefault:"63"`
	DefaultStorageLimit    int64  `env:"DEFAULT_STORAGE_LIMIT" envDefault:"1073741824"` // 1GB in bytes
	DefaultBandwidthLimit  int64  `env:"DEFAULT_BANDWIDTH_LIMIT" envDefault:"5368709120"` // 5GB in bytes
	DefaultUserLimit       int    `env:"DEFAULT_USER_LIMIT" envDefault:"5"`
	EnableMultiTenancy     bool   `env:"ENABLE_MULTI_TENANCY" envDefault:"true"`
	DefaultPageSize        int    `env:"DEFAULT_PAGE_SIZE" envDefault:"10"`
	MaxPageSize            int    `env:"MAX_PAGE_SIZE" envDefault:"100"`
	DefaultSortField       string `env:"DEFAULT_SORT_FIELD" envDefault:"created_at"`
	DefaultSortOrder       string `env:"DEFAULT_SORT_ORDER" envDefault:"desc"`
	SubdomainReservedNames string `env:"SUBDOMAIN_RESERVED_NAMES" envDefault:"www,api,admin,app,mail,blog,docs,support,help"`
	Message                string `env:"MESSAGE" envDefault:"Xin chào từ module Tenant!"`
}

// LoadTenantConfig đọc cấu hình tenant từ biến môi trường
func LoadTenantConfig() (*TenantConfig, error) {
	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	// Khởi tạo config mặc định
	cfg := GetDefaultConfig()

	// Đọc cấu hình từ biến môi trường với prefix TENANT_
	opts := env.Options{
		Prefix: "TENANT_",
	}
	if err := env.ParseWithOptions(cfg, opts); err != nil {
		return nil, fmt.Errorf("lỗi đọc cấu hình tenant từ biến môi trường: %w", err)
	}

	return cfg, nil
}

// GetDefaultConfig trả về cấu hình mặc định
func GetDefaultConfig() *TenantConfig {
	return &TenantConfig{
		MaxTenantsPerUser:      10,
		DefaultPlanID:          1,
		MaxTenantNameLength:    100,
		MaxSubdomainLength:     63,
		DefaultStorageLimit:    1073741824,  // 1GB in bytes
		DefaultBandwidthLimit:  5368709120,  // 5GB in bytes
		DefaultUserLimit:       5,
		EnableMultiTenancy:     true,
		DefaultPageSize:        10,
		MaxPageSize:            100,
		DefaultSortField:       "created_at",
		DefaultSortOrder:       "desc",
		SubdomainReservedNames: "www,api,admin,app,mail,blog,docs,support,help",
		Message:                "Xin chào từ module Tenant!",
	}
}
