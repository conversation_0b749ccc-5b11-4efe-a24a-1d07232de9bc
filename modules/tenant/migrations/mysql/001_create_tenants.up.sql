CREATE TABLE IF NOT EXISTS tenants (
  tenant_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_name VARCHAR(255) NOT NULL,
  tenant_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'Unique code for the tenant, useful for API and URL paths',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  status ENUM('active', 'inactive', 'suspended', 'trial') DEFAULT 'active',
  plan_type VARCHAR(50) DEFAULT 'standard' COMMENT 'Subscription plan type',
  subscription_expires_at TIMESTAMP NULL COMMENT 'When the current subscription expires',
  INDEX idx_tenants_status (status),
  INDEX idx_tenants_code (tenant_code)
) ENGINE=InnoDB CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; 