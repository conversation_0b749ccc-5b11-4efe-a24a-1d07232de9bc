package models

import (
	"time"
)

// Tenant represents a tenant in the multi-tenant system
type Tenant struct {
	TenantID              uint      `db:"tenant_id" json:"tenant_id" gorm:"primaryKey"`
	TenantName            string     `db:"tenant_name" json:"tenant_name"`
	TenantCode            string     `db:"tenant_code" json:"tenant_code"`
	CreatedAt             time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt             time.Time  `db:"updated_at" json:"updated_at"`
	Status                string     `db:"status" json:"status"`
	PlanType              string     `db:"plan_type" json:"plan_type"`
	SubscriptionExpiresAt *time.Time `db:"subscription_expires_at" json:"subscription_expires_at,omitempty"`
}
