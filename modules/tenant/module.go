package tenant

import (
	"context"
	"path/filepath"
	"runtime"

	"wnapi/internal/core"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/tenant/api"
	"wnapi/modules/tenant/internal"
	"wnapi/modules/tenant/repository"
	"wnapi/modules/tenant/service"
)

func init() {
	core.RegisterModuleFactory("tenant", NewModule)
}

// Module triển khai tenant module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Khởi tạo repository
	repo, err := repository.NewMySQLRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	// Đọc cấu hình từ biến môi trường
	tenantConfig, err := internal.LoadTenantConfig()
	if err != nil {
		logger.Warn("<PERSON>h<PERSON>ng thể đọc cấu hình từ biến môi trường, sử dụng giá trị mặc định: %v", err)
		// Sử dụng cấu hình mặc định
		tenantConfig = internal.GetDefaultConfig()
	}

	// Khởi tạo service và handler
	tenantService := service.NewService(repo, *tenantConfig, logger)
	handler := api.NewHandler(tenantService, logger)

	return &Module{
		name:    "tenant",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
	}, nil
}

// Name trả về tên của module
func (m *Module) Name() string {
	return m.name
}

// Init khởi tạo module
func (m *Module) Init() error {
	m.logger.Info("Khởi tạo module tenant")
	return nil
}

// RegisterRoutes đăng ký routes của module
func (m *Module) RegisterRoutes(server *core.Server) error {
	m.logger.Info("Đăng ký routes cho module tenant")
	return registerRoutes(server, m.handler)
}

// Cleanup giải phóng tài nguyên khi shutdown
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Giải phóng tài nguyên module tenant")
	return nil
}

// GetMigrationPath trả về đường dẫn đến thư mục migrations
func (m *Module) GetMigrationPath() string {
	_, filename, _, ok := runtime.Caller(0)
	if !ok {
		return ""
	}
	return filepath.Join(filepath.Dir(filename), "migrations")
}

// GetMigrationOrder trả về thứ tự ưu tiên cho migration
func (m *Module) GetMigrationOrder() int {
	return 5 // Ưu tiên cao, vì tenant là nền tảng cho các module khác
}
