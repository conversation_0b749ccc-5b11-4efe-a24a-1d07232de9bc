-- Test tenants
INSERT INTO tenants (tenant_name, tenant_code, status, plan_type, subscription_expires_at) VALUES
('Công ty ABC', 'abc', 'active', 'standard', DATE_ADD(NOW(), INTERVAL 1 YEAR)),
('Blog C<PERSON> nhân XYZ', 'xyz-blog', 'active', 'basic', DATE_ADD(NOW(), INTERVAL 6 MONTH)),
('Tổ chức Phi lợi nhuận DEF', 'def-org', 'trial', 'premium', DATE_ADD(NOW(), INTERVAL 30 DAY));

-- -- T<PERSON><PERSON><PERSON> lậ<PERSON> giá trị setting cho tenants test
-- INSERT INTO tenant_values (tenant_id, setting_id, value, created_at, updated_at) VALUES
-- -- Tenant ABC (ID=1)
-- (1, (SELECT id FROM tenant_settings WHERE name = 'site_name'), 'Công ty ABC', NOW(), NOW()),
-- (1, (SELECT id FROM tenant_settings WHERE name = 'site_description'), 'Blog chính thức của Công ty ABC', NOW(), NOW()),
-- (1, (SELECT id FROM tenant_settings WHERE name = 'primary_color'), '#e74c3c', NOW(), NOW()),

-- -- Tenant XYZ (ID=2)
-- (2, (SELECT id FROM tenant_settings WHERE name = 'site_name'), 'Blog Cá nhân XYZ', NOW(), NOW()),
-- (2, (SELECT id FROM tenant_settings WHERE name = 'site_description'), 'Chia sẻ kinh nghiệm cá nhân', NOW(), NOW()),
-- (2, (SELECT id FROM tenant_settings WHERE name = 'primary_color'), '#9b59b6', NOW(), NOW()),

-- -- Tenant DEF (ID=3)
-- (3, (SELECT id FROM tenant_settings WHERE name = 'site_name'), 'Tổ chức Phi lợi nhuận DEF', NOW(), NOW()),
-- (3, (SELECT id FROM tenant_settings WHERE name = 'site_description'), 'Blog của tổ chức phi lợi nhuận', NOW(), NOW()),
-- (3, (SELECT id FROM tenant_settings WHERE name = 'primary_color'), '#27ae60', NOW(), NOW()); 