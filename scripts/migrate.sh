#!/bin/bash
# scripts/migrate.sh

PROJECT_NAME=$1
ACTION=${2:-"up"}

if [ -z "$PROJECT_NAME" ]; then
    echo "Usage: $0 <project-name> [action]"
    echo "Actions: up, down, version"
    exit 1
fi

echo "Running migration for project: $PROJECT_NAME"

case $ACTION in
    "up")
        go run cmd/migrate/main.go -project="$PROJECT_NAME" -action=up
        ;;
    "down")
        echo "Warning: This will rollback ALL migrations. Are you sure? (y/N)"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            go run cmd/migrate/main.go -project="$PROJECT_NAME" -action=down
        else
            echo "Migration rollback cancelled"
        fi
        ;;
    "version")
        go run cmd/migrate/main.go -project="$PROJECT_NAME" -action=version
        ;;
    *)
        echo "Unknown action: $ACTION"
        echo "Available actions: up, down, version"
        exit 1
        ;;
esac 