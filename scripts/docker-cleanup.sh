#!/bin/bash

# Script dọn dẹp tài nguyên Docker không sử dụng

echo "==== Docker Cleanup Tool ===="

# Kiểm tra Docker
if ! command -v docker &> /dev/null; then
    echo "Error: Docker không được cài đặt."
    exit 1
fi

# Dừng tất cả container đang chạy
echo "Kiểm tra container đang chạy..."
RUNNING_CONTAINERS=$(docker ps -q)
if [ -n "$RUNNING_CONTAINERS" ]; then
    echo "Cảnh báo: Có container đang chạy. Nếu muốn dừng tất cả, hãy chạy: docker stop \$(docker ps -q)"
    read -p "Bạn có muốn dừng tất cả container không? (y/n): " STOP_ALL
    if [ "$STOP_ALL" = "y" ]; then
        echo "Dừng tất cả container..."
        docker stop $(docker ps -q)
    fi
else
    echo "Không có container nào đang chạy."
fi

# Xóa container đã dừng
echo "Xóa container đã dừng..."
STOPPED_CONTAINERS=$(docker ps -a -q -f status=exited)
if [ -n "$STOPPED_CONTAINERS" ]; then
    docker rm $STOPPED_CONTAINERS
    echo "Đã xóa container đã dừng."
else
    echo "Không có container đã dừng nào."
fi

# Xóa image không sử dụng
echo "Xóa image không sử dụng..."
DANGLING_IMAGES=$(docker images -q -f dangling=true)
if [ -n "$DANGLING_IMAGES" ]; then
    docker rmi $DANGLING_IMAGES
    echo "Đã xóa image không sử dụng."
else
    echo "Không có image không sử dụng nào."
fi

# Xóa volume không sử dụng
echo "Xóa volume không sử dụng..."
DANGLING_VOLUMES=$(docker volume ls -q -f dangling=true)
if [ -n "$DANGLING_VOLUMES" ]; then
    docker volume rm $DANGLING_VOLUMES
    echo "Đã xóa volume không sử dụng."
else
    echo "Không có volume không sử dụng nào."
fi

# Xóa network không sử dụng
echo "Xóa network không sử dụng..."
docker network prune -f

echo "==== Cleanup hoàn tất ===="
echo "Thống kê hiện tại:"
echo "Container: $(docker ps -q | wc -l) đang chạy, $(docker ps -a -q | wc -l) tổng cộng"
echo "Images: $(docker images -q | wc -l)"
echo "Volumes: $(docker volume ls -q | wc -l)"
echo "Networks: $(docker network ls -q | wc -l)" 