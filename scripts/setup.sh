#!/bin/bash

# Script cài đặt môi trường phát triển WNAPI

echo "==== Cài đặt môi trường phát triển WNAPI ===="

# Kiểm tra Go
if ! command -v go &> /dev/null; then
    echo "Error: Go không được cài đặt. Vui lòng cài đặt Go trước."
    exit 1
fi

GO_VERSION=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | cut -c 3-)
echo "Go version: $GO_VERSION"

# Kiểm tra version Go
go_major=$(echo $GO_VERSION | cut -d. -f1)
go_minor=$(echo $GO_VERSION | cut -d. -f2)

if [ "$go_major" -lt 1 ] || [ "$go_major" -eq 1 -a "$go_minor" -lt 19 ]; then
    echo "Warning: Phiên bản Go khuyến nghị là 1.19 hoặc cao hơn"
fi

# Tải dependencies
echo "Tải dependencies..."
go mod download
if [ $? -ne 0 ]; then
    echo "Error: <PERSON>hông thể tải dependencies"
    exit 1
fi

# Tạ<PERSON> thư mục cần thiết
echo "Tạo thư mục cần thiết..."
mkdir -p build logs projects

# Kiểm tra Docker
if command -v docker &> /dev/null; then
    echo "Docker đã được cài đặt"
    
    if command -v docker-compose &> /dev/null; then
        echo "Docker Compose đã được cài đặt"
    else
        echo "Warning: Docker Compose chưa được cài đặt. Cài đặt để sử dụng đầy đủ tính năng."
    fi
else
    echo "Warning: Docker chưa được cài đặt. Cài đặt để sử dụng đầy đủ tính năng."
fi

# Kiểm tra database MySQL
echo "Kiểm tra kết nối MySQL..."
if command -v mysql &> /dev/null; then
    echo "MySQL client đã được cài đặt"
else
    echo "Warning: MySQL client chưa được cài đặt. Cài đặt để thuận tiện cho việc phát triển."
fi

echo "==== Cài đặt hoàn tất ===="
echo "Bạn có thể bắt đầu phát triển WNAPI!"
echo "Chạy 'make run' để khởi động ứng dụng" 