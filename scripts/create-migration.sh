#!/bin/bash
# scripts/create-migration.sh

MODULE_NAME=$1
MIGRATION_NAME=$2

if [ -z "$MODULE_NAME" ] || [ -z "$MIGRATION_NAME" ]; then
    echo "Usage: $0 <module-name> <migration-name>"
    echo "Example: $0 auth add_user_roles"
    exit 1
fi

echo "Creating migration for module: $MODULE_NAME"
echo "Migration name: $MIGRATION_NAME"

go run cmd/migrate/main.go -action=create -module="$MODULE_NAME" -name="$MIGRATION_NAME"

echo "Migration files created successfully!"
echo "Remember to:"
echo "1. Edit the .up.sql file with your schema changes"
echo "2. Edit the .down.sql file with rollback statements"
echo "3. Test the migration before deploying" 