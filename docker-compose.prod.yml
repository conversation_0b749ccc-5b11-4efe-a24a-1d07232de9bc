version: '3.8'

services:
  # API service
  api:
    image: wnapi:latest
    container_name: wnapi
    restart: always
    ports:
      - "8080:8080"
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./projects:/app/projects
    depends_on:
      - mysql
    networks:
      - wnapi-network
    command: ["--project=sample"]
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "20m"
        max-file: "5"

  # MySQL database
  mysql:
    image: mysql:8.0
    container_name: wnapi-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-wnapi}
      MYSQL_USER: ${MYSQL_USER:-wnapi}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-password}
    ports:
      - "3307:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - wnapi-network
    command: --default-authentication-plugin=mysql_native_password
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "20m"
        max-file: "5"

volumes:
  mysql-data:
    driver: local

networks:
  wnapi-network:
    driver: bridge 