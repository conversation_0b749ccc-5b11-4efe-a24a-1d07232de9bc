package core

import (
	"context"
	"fmt"
	"net/http"
	"time"
	"wnapi/internal/middleware"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"

	"github.com/gin-gonic/gin"
)

// Server là wrapper cho HTTP server
type Server struct {
	engine     *gin.Engine
	httpServer *http.Server
	logger     logger.Logger
}

// NewServer tạo một server mới
func NewServer(host string, port int, readTimeout, writeTimeout time.Duration, log logger.Logger) *Server {
	// Tạo router với Gin
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()

	// Đăng ký middleware cơ bản
	router.Use(middleware.Recovery(log))
	router.Use(middleware.Logger(log))

	// Đăng ký tracing middleware nếu tracing được kích hoạt
	if manager := tracing.GetGlobalManager(); manager != nil && manager.IsEnabled() {
		router.Use(tracing.GinMiddleware("wnapi"))
	}

	// Tạo HTTP server
	address := fmt.Sprintf("%s:%d", host, port)
	httpServer := &http.Server{
		Addr:         address,
		Handler:      router,
		ReadTimeout:  readTimeout,
		WriteTimeout: writeTimeout,
	}

	return &Server{
		engine:     router,
		httpServer: httpServer,
		logger:     log,
	}
}

// Start khởi động HTTP server
func (s *Server) Start() error {
	return s.httpServer.ListenAndServe()
}

// Stop dừng HTTP server
func (s *Server) Stop(ctx context.Context) error {
	return s.httpServer.Shutdown(ctx)
}

// RegisterMiddleware đăng ký middleware toàn cục
func (s *Server) RegisterMiddleware(middleware gin.HandlerFunc) {
	s.engine.Use(middleware)
}

// Group tạo group route mới
func (s *Server) Group(relativePath string, handlers ...gin.HandlerFunc) *gin.RouterGroup {
	return s.engine.Group(relativePath, handlers...)
}

// GET đăng ký route GET
func (s *Server) GET(relativePath string, handlers ...gin.HandlerFunc) {
	s.engine.GET(relativePath, handlers...)
}

// POST đăng ký route POST
func (s *Server) POST(relativePath string, handlers ...gin.HandlerFunc) {
	s.engine.POST(relativePath, handlers...)
}

// PUT đăng ký route PUT
func (s *Server) PUT(relativePath string, handlers ...gin.HandlerFunc) {
	s.engine.PUT(relativePath, handlers...)
}

// DELETE đăng ký route DELETE
func (s *Server) DELETE(relativePath string, handlers ...gin.HandlerFunc) {
	s.engine.DELETE(relativePath, handlers...)
}
