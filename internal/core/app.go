package core

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/plugins"

	"github.com/gin-gonic/gin"
)

// AppOptions chứa các tùy chọn khởi tạo ứng dụng
type AppOptions struct {
	ConfigPath  string
	ProjectName string
}

// App đại diện cho ứng dụng chính
type App struct {
	config           *Config
	logger           logger.Logger
	modules          []Module
	plugins          []plugins.Plugin
	dbManager        *database.Manager
	migrationManager *database.MigrationManager
	projectName      string
	wg               sync.WaitGroup
	ctx              context.Context
	cancel           context.CancelFunc
	server           *http.Server
	router           *http.ServeMux
	modulesMu        sync.RWMutex
	pluginsMu        sync.RWMutex
	shutdownCh       chan os.Signal
	httpServer       *Server
}

// NewApp tạo một ứng dụng mới
func NewApp(options AppOptions) (*App, error) {
	// Tạo context hủy
	ctx, cancel := context.WithCancel(context.Background())

	// Khởi tạo logger
	log := logger.NewConsoleLogger("app", logger.LevelInfo)

	// Nạp cấu hình
	configPath := options.ConfigPath
	if options.ProjectName != "" {
		projectConfigDir := filepath.Join("projects", options.ProjectName)
		configPath = filepath.Join(projectConfigDir, ".env")
		log.Info("Using project config", "project", options.ProjectName, "config", configPath)
	} else if configPath == "" {
		configPath = ".env"
	}

	cfg, err := NewConfig(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// Tạo kết nối database
	dbManager, err := database.NewManager(database.Config{
		Type:            cfg.GetString("DB_TYPE"),
		Host:            cfg.GetString("DB_HOST"),
		Port:            cfg.GetInt("DB_PORT"),
		Username:        cfg.GetString("DB_USERNAME"),
		Password:        cfg.GetString("DB_PASSWORD"),
		Database:        cfg.GetString("DB_DATABASE"),
		MaxOpenConns:    cfg.GetInt("DB_MAX_OPEN_CONNS"),
		MaxIdleConns:    cfg.GetInt("DB_MAX_IDLE_CONNS"),
		ConnMaxLifetime: cfg.GetDuration("DB_CONN_MAX_LIFETIME"),
		MigrationPath:   cfg.GetString("DB_MIGRATION_PATH"),
	}, log)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Tạo migration manager
	migrationManager := database.NewMigrationManager(dbManager, database.Config{
		MigrationPath: cfg.GetString("DB_MIGRATION_PATH"),
	}, log)

	// Tạo HTTP router
	router := http.NewServeMux()

	// Lấy cấu hình server
	serverHost := cfg.GetString("SERVER_HOST")
	serverPort := cfg.GetInt("SERVER_PORT")
	if serverPort == 0 {
		serverPort = 8080
	}

	// Lấy timeout từ cấu hình
	var readTimeout, writeTimeout time.Duration
	readTimeoutStr := cfg.GetString("SERVER_READ_TIMEOUT")
	if readTimeoutStr != "" {
		readTimeout, _ = time.ParseDuration(readTimeoutStr)
	} else {
		readTimeout = 15 * time.Second
	}

	writeTimeoutStr := cfg.GetString("SERVER_WRITE_TIMEOUT")
	if writeTimeoutStr != "" {
		writeTimeout, _ = time.ParseDuration(writeTimeoutStr)
	} else {
		writeTimeout = 15 * time.Second
	}

	// Tạo HTTP server (chuẩn net/http)
	server := &http.Server{
		Addr:         net.JoinHostPort(serverHost, strconv.Itoa(serverPort)),
		Handler:      router,
		ReadTimeout:  readTimeout,
		WriteTimeout: writeTimeout,
	}

	// Tạo server Gin
	httpServer := NewServer(serverHost, serverPort, readTimeout, writeTimeout, log)

	app := &App{
		config:           cfg,
		logger:           log,
		projectName:      options.ProjectName,
		dbManager:        dbManager,
		migrationManager: migrationManager,
		ctx:              ctx,
		cancel:           cancel,
		server:           server,
		router:           router,
		shutdownCh:       make(chan os.Signal, 1),
		httpServer:       httpServer,
	}

	return app, nil
}

// Initialize khởi tạo ứng dụng và các module
func (a *App) Initialize() error {
	a.logger.Info("Initializing application")

	// Khởi tạo plugins nếu được cấu hình
	if err := a.initializePlugins(); err != nil {
		return err
	}

	// Nạp danh sách module được kích hoạt
	enabledModules := a.config.GetStringSlice("MODULES_ENABLED")
	if len(enabledModules) == 0 {
		a.logger.Warn("No modules enabled")
		return nil
	}

	// Khởi tạo các module
	for _, moduleName := range enabledModules {
		// Lấy module factory
		factory, ok := GlobalModuleRegistry.Get(moduleName)
		if !ok {
			a.logger.Warn("Module factory not found", "module", moduleName)
			continue
		}

		// Lấy cấu hình module từ biến môi trường
		moduleConfig := a.config.GetModuleSettings(moduleName)

		// Tạo module
		module, err := factory(a, moduleConfig)
		if err != nil {
			return fmt.Errorf("failed to create module %s: %w", moduleName, err)
		}

		// Khởi tạo module
		if err := module.Init(a.ctx); err != nil {
			return fmt.Errorf("failed to initialize module %s: %w", moduleName, err)
		}

		// Đăng ký routes
		if err := module.RegisterRoutes(a.httpServer); err != nil {
			return fmt.Errorf("failed to register routes for module %s: %w", moduleName, err)
		}

		// Đăng ký module vào registry
		GlobalModuleRegistry.RegisterModule(module)

		// Lưu module vào danh sách
		a.modules = append(a.modules, module)

		a.logger.Info("Module initialized", "module", moduleName)
	}

	return nil
}

// initializePlugins khởi tạo các plugin
func (a *App) initializePlugins() error {
	// Lấy cấu hình plugins từ biến môi trường
	enabledPlugins := a.config.GetStringSlice("PLUGINS_ENABLED")
	if len(enabledPlugins) == 0 {
		a.logger.Info("No plugins configured")
		return nil
	}

	a.logger.Info("Plugins được cấu hình:", "count", len(enabledPlugins))

	// Khởi tạo từng plugin được cấu hình
	for _, pluginName := range enabledPlugins {
		a.logger.Info("Đang xử lý plugin", "plugin", pluginName)

		// Lấy plugin config từ biến môi trường
		pluginConfig := a.config.GetStringMap("PLUGIN_" + strings.ToUpper(pluginName))

		// Tạo và khởi tạo plugin
		plugin, err := plugins.GlobalRegistry.CreateAndInitialize(a.ctx, pluginName, pluginConfig)
		if err != nil {
			a.logger.Error("Failed to initialize plugin", "plugin", pluginName, "error", err.Error())
			continue
		}

		// Thêm vào danh sách plugins đã khởi tạo
		a.pluginsMu.Lock()
		a.plugins = append(a.plugins, plugin)
		a.pluginsMu.Unlock()

		a.logger.Info("Plugin initialized", "plugin", pluginName)
	}

	return nil
}

// Start khởi động ứng dụng
func (a *App) Start() error {
	a.logger.Info("Starting application")

	// Thêm route health check cho server Gin
	a.httpServer.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// Đăng ký signal handler
	signal.Notify(a.shutdownCh, os.Interrupt, syscall.SIGTERM)
	go func() {
		sig := <-a.shutdownCh
		a.logger.Info("Received shutdown signal", "signal", sig.String())
		a.Shutdown()
	}()

	// Khởi động HTTP server với Gin engine
	serverAddr := a.httpServer.httpServer.Addr
	a.logger.Info("Starting HTTP server", "address", serverAddr)
	go func() {
		if err := a.httpServer.Start(); err != nil && err != http.ErrServerClosed {
			a.logger.Error("HTTP server error", "error", err.Error())
		}
	}()

	return nil
}

// Shutdown dừng ứng dụng
func (a *App) Shutdown() error {
	a.logger.Info("Shutting down application")

	// Hủy context
	a.cancel()

	// Dừng HTTP server
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := a.httpServer.Stop(ctx); err != nil {
		a.logger.Error("Error shutting down HTTP server", "error", err.Error())
	}

	// Dừng các module
	for _, module := range a.modules {
		if err := module.Cleanup(a.ctx); err != nil {
			a.logger.Error("Error cleaning up module", "module", module.Name(), "error", err.Error())
		}
	}

	// Dừng các plugin
	for _, plugin := range a.plugins {
		if err := plugin.Shutdown(a.ctx); err != nil {
			a.logger.Error("Error shutting down plugin", "plugin", plugin.Name(), "error", err.Error())
		}
	}

	// Đóng kết nối database
	if a.dbManager != nil {
		if err := a.dbManager.Close(); err != nil {
			a.logger.Error("Error closing database connection", "error", err.Error())
		}
	}

	// Đợi tất cả goroutine hoàn thành
	a.wg.Wait()

	return nil
}

// GetLogger trả về logger
func (a *App) GetLogger() logger.Logger {
	return a.logger
}

// GetDBManager trả về database manager
func (a *App) GetDBManager() *database.Manager {
	return a.dbManager
}

// GetMigrationManager trả về migration manager
func (a *App) GetMigrationManager() *database.MigrationManager {
	return a.migrationManager
}

// GetConfig trả về cấu hình
func (a *App) GetConfig() *Config {
	return a.config
}

// GetProjectName trả về tên project
func (a *App) GetProjectName() string {
	return a.projectName
}

// GetModules trả về danh sách các module đã tải
func (a *App) GetModules() []Module {
	return a.modules
}

// GetModule trả về module theo tên
func (a *App) GetModule(name string) (Module, bool) {
	for _, module := range a.modules {
		if module.Name() == name {
			return module, true
		}
	}
	return nil, false
}

// GetServerAddress trả về địa chỉ server
func (a *App) GetServerAddress() string {
	return a.httpServer.httpServer.Addr
}

// RouteInfo chứa thông tin về một route
type RouteInfo struct {
	Method string
	Path   string
}

// GetRoutes trả về danh sách các routes đã đăng ký
func (a *App) GetRoutes() []RouteInfo {
	var routes []RouteInfo

	// Thêm route health check mặc định
	routes = append(routes, RouteInfo{
		Method: "GET",
		Path:   "/health",
	})

	// Lấy danh sách routes từ các module
	for _, module := range a.modules {
		// Thêm các route dựa trên tên module
		switch module.Name() {
		case "hello":
			routes = append(routes, RouteInfo{
				Method: "GET",
				Path:   "/hello",
			})
			routes = append(routes, RouteInfo{
				Method: "GET",
				Path:   "/hello/json",
			})
			routes = append(routes, RouteInfo{
				Method: "GET",
				Path:   "/hello/ping",
			})
		case "auth":
			routes = append(routes, RouteInfo{
				Method: "POST",
				Path:   "/auth/register",
			})
			routes = append(routes, RouteInfo{
				Method: "POST",
				Path:   "/auth/login",
			})
			routes = append(routes, RouteInfo{
				Method: "POST",
				Path:   "/auth/refresh",
			})
		}
	}

	return routes
}

// GetPlugins trả về danh sách các plugin đã tải
func (a *App) GetPlugins() []plugins.Plugin {
	a.pluginsMu.RLock()
	defer a.pluginsMu.RUnlock()
	return a.plugins
}

// GetPlugin trả về plugin theo tên
func (a *App) GetPlugin(name string) (plugins.Plugin, bool) {
	a.pluginsMu.RLock()
	defer a.pluginsMu.RUnlock()
	for _, plugin := range a.plugins {
		if plugin.Name() == name {
			return plugin, true
		}
	}
	return nil, false
}
