package core

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Config quản lý cấu hình ứng dụng
type Config struct {
	loaded bool
}

// NewConfig tạo đối tượng Config từ biến môi trường
func NewConfig(envFile string) (*Config, error) {
	cfg := &Config{}

	// Nếu không có đường dẫn cụ thể, sử dụng .env trong thư mục hiện tại
	if envFile == "" {
		envFile = ".env"
	}

	// Kiểm tra file có tồn tại không
	if _, err := os.Stat(envFile); err == nil {
		// Đọc file .env
		if err := godotenv.Load(envFile); err != nil {
			return nil, fmt.Errorf("error loading .env file: %w", err)
		}
		cfg.loaded = true
	} else {
		// Nếu không tìm thấy file, vẫn tiếp tục nhưng ghi log
		fmt.Printf("Warning: .env file %s not found, using environment variables only\n", envFile)
		cfg.loaded = false
	}

	return cfg, nil
}

// Get lấy giá trị theo key từ biến môi trường
func (c *Config) Get(key string) interface{} {
	// Chuyển đổi key thành định dạng biến môi trường
	// Ví dụ: app.name -> APP_NAME
	envKey := strings.ToUpper(strings.ReplaceAll(key, ".", "_"))

	// Lấy giá trị từ biến môi trường
	return os.Getenv(envKey)
}

// GetString lấy giá trị string theo key
func (c *Config) GetString(key string) string {
	val := c.Get(key)
	if val == nil {
		return ""
	}
	return fmt.Sprintf("%v", val)
}

// GetInt lấy giá trị int theo key
func (c *Config) GetInt(key string) int {
	strVal := c.GetString(key)
	if strVal == "" {
		return 0
	}

	val, err := strconv.Atoi(strVal)
	if err != nil {
		return 0
	}

	return val
}

// GetBool lấy giá trị bool theo key
func (c *Config) GetBool(key string) bool {
	strVal := c.GetString(key)
	if strVal == "" {
		return false
	}

	// Các giá trị true
	strVal = strings.ToLower(strVal)
	return strVal == "true" || strVal == "1" || strVal == "yes" || strVal == "y"
}

// GetDuration lấy giá trị duration theo key
func (c *Config) GetDuration(key string) time.Duration {
	strVal := c.GetString(key)
	if strVal == "" {
		return 0
	}

	duration, err := time.ParseDuration(strVal)
	if err != nil {
		return 0
	}

	return duration
}

// GetStringSlice lấy []string theo key
func (c *Config) GetStringSlice(key string) []string {
	strVal := c.GetString(key)
	if strVal == "" {
		return nil
	}

	// Phân tách theo dấu phẩy
	parts := strings.Split(strVal, ",")

	// Xóa khoảng trắng
	for i, part := range parts {
		parts[i] = strings.TrimSpace(part)
	}

	return parts
}

// GetStringMap lấy map[string]interface{} theo key
func (c *Config) GetStringMap(key string) map[string]interface{} {
	// Với biến môi trường, chúng ta cần tìm tất cả các biến có tiền tố là key
	// Ví dụ: MODULE_SETTINGS_AUTH_JWT_SECRET
	envKey := strings.ToUpper(strings.ReplaceAll(key, ".", "_")) + "_"

	result := make(map[string]interface{})

	// Duyệt qua tất cả biến môi trường
	for _, envString := range os.Environ() {
		parts := strings.SplitN(envString, "=", 2)
		if len(parts) != 2 {
			continue
		}

		envName := parts[0]
		envValue := parts[1]

		// Nếu biến bắt đầu bằng tiền tố cần tìm
		if strings.HasPrefix(envName, envKey) {
			// Xóa tiền tố để lấy key thực
			subKey := strings.ToLower(strings.Replace(envName, envKey, "", 1))
			result[subKey] = envValue
		}
	}

	return result
}

// GetModuleSettings lấy cấu hình cho một module cụ thể
func (c *Config) GetModuleSettings(moduleName string) map[string]interface{} {
	prefix := strings.ToUpper(moduleName) + "_"

	result := make(map[string]interface{})

	// Duyệt qua tất cả biến môi trường
	for _, envString := range os.Environ() {
		parts := strings.SplitN(envString, "=", 2)
		if len(parts) != 2 {
			continue
		}

		envName := parts[0]
		envValue := parts[1]

		// Nếu biến bắt đầu bằng tiền tố module
		if strings.HasPrefix(envName, prefix) {
			// Xóa tiền tố để lấy key thực
			subKey := strings.ToLower(strings.Replace(envName, prefix, "", 1))
			result[subKey] = envValue
		}
	}

	return result
}
