// internal/bootstrap/rbac_bootstrap.go
package bootstrap

import (
	"time"

	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/rbac/repository/mysql"
	rbacSvc "wnapi/modules/rbac/service"

	"gorm.io/gorm"
)

// BootstrapRBAC khởi tạo và kết nối toàn bộ thành phần của hệ thống phân quyền RBAC
func BootstrapRBAC(
	db *gorm.DB,
	appCache cache.Cache,
	appLogger logger.Logger,
	cfg config.Config,
) (*permission.MiddlewareFactory, error) {
	// 1. Khởi tạo các repositories
	userRoleRepo := mysql.NewUserRoleRepository(db, appLogger)
	rolePermRepo := mysql.NewRolePermissionRepository(db, appLogger)

	// 2. Khởi tạo RBAC Permission Service (internal)
	rbacPermissionInternal := rbacSvc.NewRBACPermissionService(
		userRoleRepo,
		rolePermRepo,
		appLogger,
	)

	// 3. Tạo PermissionService public API (đây là PermissionChecker thực tế)
	rbacPermissionService := rbacSvc.NewPermissionService(rbacPermissionInternal)

	// 4. Đọc config TTL cho cache
	permissionCacheTTL := cfg.GetDurationWithDefault("cache.permission.ttl", 5*time.Minute)

	// 5. Khởi tạo CachedPermissionChecker
	cachedPermChecker := permission.NewCachedPermissionChecker(
		rbacPermissionService, // delegate
		appCache,
		permissionCacheTTL,
		appLogger,
	)

	// 6. Khởi tạo MiddlewareFactory
	middlewareFactory := permission.NewMiddlewareFactory(
		cachedPermChecker, // Sử dụng cached checker
		appLogger,
	)

	// 7. Trả về MiddlewareFactory để sử dụng ở main.go hoặc core.App
	return middlewareFactory, nil
}
