package database

import (
	"errors"
	"fmt"
	"path/filepath"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database"
	"github.com/golang-migrate/migrate/v4/database/mysql"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
)

// MigrationManager quản lý migration database
type MigrationManager struct {
	dbManager *Manager
	config    Config
	log       Logger
}

// NewMigrationManager tạo một MigrationManager mới
func NewMigrationManager(dbManager *Manager, config Config, log Logger) *MigrationManager {
	return &MigrationManager{
		dbManager: dbManager,
		config:    config,
		log:       log,
	}
}

// RunMigrations chạy migrations
func (m *MigrationManager) RunMigrations(path string) error {
	var driver database.Driver
	var err error

	switch m.config.Type {
	case "mysql":
		driver, err = mysql.WithInstance(m.dbManager.DB.DB, &mysql.Config{})
	case "postgres":
		driver, err = postgres.WithInstance(m.dbManager.DB.DB, &postgres.Config{})
	default:
		return fmt.Errorf("unsupported database type for migration: %s", m.config.Type)
	}

	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Chuẩn hóa đường dẫn
	sourceURL := fmt.Sprintf("file://%s", filepath.Clean(path))

	// Khởi tạo migrate
	migrator, err := migrate.NewWithDatabaseInstance(sourceURL, m.config.Type, driver)
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}

	// Thực hiện migration
	if err := migrator.Up(); err != nil {
		if errors.Is(err, migrate.ErrNoChange) {
			m.log.Info("No migrations to run", "path", path)
			return nil
		}
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	m.log.Info("Migrations completed successfully", "path", path)
	return nil
}

// RunModuleMigrations chạy migrations cho một module cụ thể
func (m *MigrationManager) RunModuleMigrations(moduleName, path string) error {
	fullPath := filepath.Join(path, "migrations")
	m.log.Info("Running migrations for module", "module", moduleName, "path", fullPath)
	return m.RunMigrations(fullPath)
}

// RunGlobalMigrations chạy migrations chung cho toàn bộ ứng dụng
func (m *MigrationManager) RunGlobalMigrations() error {
	return m.RunMigrations(m.config.MigrationPath)
}

// GetMigrationStatus lấy thông tin về migration hiện tại
func (m *MigrationManager) GetMigrationStatus(path string) (int, error) {
	var driver database.Driver
	var err error

	switch m.config.Type {
	case "mysql":
		driver, err = mysql.WithInstance(m.dbManager.DB.DB, &mysql.Config{})
	case "postgres":
		driver, err = postgres.WithInstance(m.dbManager.DB.DB, &postgres.Config{})
	default:
		return 0, fmt.Errorf("unsupported database type for migration: %s", m.config.Type)
	}

	if err != nil {
		return 0, fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Chuẩn hóa đường dẫn
	sourceURL := fmt.Sprintf("file://%s", filepath.Clean(path))

	// Khởi tạo migrate
	migrator, err := migrate.NewWithDatabaseInstance(sourceURL, m.config.Type, driver)
	if err != nil {
		return 0, fmt.Errorf("failed to create migrator: %w", err)
	}

	version, dirty, err := migrator.Version()
	if err != nil {
		if errors.Is(err, migrate.ErrNilVersion) {
			return 0, nil
		}
		return 0, fmt.Errorf("failed to get migration version: %w", err)
	}

	if dirty {
		return int(version), fmt.Errorf("migration version %d is dirty", version)
	}

	return int(version), nil
}

// ForceVersion đặt version của migration mà không cần chạy migration
func (m *MigrationManager) ForceVersion(path string, version int) error {
	var driver database.Driver
	var err error

	switch m.config.Type {
	case "mysql":
		driver, err = mysql.WithInstance(m.dbManager.DB.DB, &mysql.Config{})
	case "postgres":
		driver, err = postgres.WithInstance(m.dbManager.DB.DB, &postgres.Config{})
	default:
		return fmt.Errorf("unsupported database type for migration: %s", m.config.Type)
	}

	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Chuẩn hóa đường dẫn
	sourceURL := fmt.Sprintf("file://%s", filepath.Clean(path))

	// Khởi tạo migrate
	migrator, err := migrate.NewWithDatabaseInstance(sourceURL, m.config.Type, driver)
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}

	// Force version
	if err := migrator.Force(version); err != nil {
		return fmt.Errorf("failed to force migration version: %w", err)
	}

	m.log.Info("Migration version forced", "path", path, "version", version)
	return nil
}

// DropAll xóa tất cả migrations
func (m *MigrationManager) DropAll(path string) error {
	var driver database.Driver
	var err error

	switch m.config.Type {
	case "mysql":
		driver, err = mysql.WithInstance(m.dbManager.DB.DB, &mysql.Config{})
	case "postgres":
		driver, err = postgres.WithInstance(m.dbManager.DB.DB, &postgres.Config{})
	default:
		return fmt.Errorf("unsupported database type for migration: %s", m.config.Type)
	}

	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Chuẩn hóa đường dẫn
	sourceURL := fmt.Sprintf("file://%s", filepath.Clean(path))

	// Khởi tạo migrate
	migrator, err := migrate.NewWithDatabaseInstance(sourceURL, m.config.Type, driver)
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}

	// Drop all migrations
	if err := migrator.Drop(); err != nil {
		return fmt.Errorf("failed to drop migrations: %w", err)
	}

	m.log.Info("All migrations dropped", "path", path)
	return nil
}
