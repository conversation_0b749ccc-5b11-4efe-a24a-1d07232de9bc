package database

import (
	"fmt"
	"os"
	"path/filepath"
	"time"
)

type MigrationGenerator struct {
	ModuleName    string
	MigrationName string
	BasePath      string
}

func (g *MigrationGenerator) Generate() error {
	timestamp := time.Now().Unix()

	var migrationDir string
	if g.ModuleName == "system" {
		migrationDir = g.BasePath // Thư mục migrations/ cho system
	} else {
		// <PERSON> cấu trúc mới: migrations trong module
		migrationDir = filepath.Join("modules", g.ModuleName, "migrations")
	}

	// T<PERSON><PERSON> thư mục nếu chưa tồn tại
	if err := os.MkdirAll(migrationDir, 0755); err != nil {
		return fmt.Errorf("failed to create migration directory: %w", err)
	}

	// Tạo files up và down
	baseName := fmt.Sprintf("%d_%s", timestamp, g.MigrationName)
	upFile := filepath.Join(migrationDir, baseName+".up.sql")
	downFile := filepath.Join(migrationDir, baseName+".down.sql")

	// Tạo file up
	upContent := fmt.Sprintf(`-- Migration: %s
-- Module: %s
-- Created: %s

-- Add your SQL statements here
`, g.MigrationName, g.ModuleName, time.Now().Format("2006-01-02 15:04:05"))

	if err := os.WriteFile(upFile, []byte(upContent), 0644); err != nil {
		return fmt.Errorf("failed to create up file: %w", err)
	}

	// Tạo file down
	downContent := fmt.Sprintf(`-- Rollback: %s
-- Module: %s
-- Created: %s

-- Add your rollback SQL statements here
`, g.MigrationName, g.ModuleName, time.Now().Format("2006-01-02 15:04:05"))

	if err := os.WriteFile(downFile, []byte(downContent), 0644); err != nil {
		return fmt.Errorf("failed to create down file: %w", err)
	}

	return nil
}
