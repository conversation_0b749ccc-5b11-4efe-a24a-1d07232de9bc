package database

import (
	"context"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"
)

// Config ch<PERSON>a c<PERSON><PERSON>nh database
type Config struct {
	Type            string        `yaml:"type"` // mysql, postgres
	Host            string        `yaml:"host"`
	Port            int           `yaml:"port"`
	Username        string        `yaml:"username"`
	Password        string        `yaml:"password"`
	Database        string        `yaml:"database"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
	MigrationPath   string        `yaml:"migration_path"`
}

// Manager quản lý kết nối database
type Manager struct {
	config Config
	DB     *sqlx.DB
	log    Logger
}

// Logger interface để tr<PERSON>h phụ thuộc trực tiếp vào package logger
type Logger interface {
	Info(msg string, keysAndValues ...interface{})
	Error(msg string, keysAndValues ...interface{})
	Debug(msg string, keysAndValues ...interface{})
}

// NewManager tạo một Manager mới
func NewManager(cfg Config, log Logger) (*Manager, error) {
	m := &Manager{
		config: cfg,
		log:    log,
	}

	if err := m.Connect(); err != nil {
		return nil, err
	}

	return m, nil
}

// Connect thiết lập kết nối database
func (m *Manager) Connect() error {
	var dsn string
	var dbType string

	switch m.config.Type {
	case "mysql":
		dsn = fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true",
			m.config.Username, m.config.Password, m.config.Host, m.config.Port, m.config.Database)
		dbType = "mysql"
	case "postgres":
		dsn = fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable",
			m.config.Host, m.config.Port, m.config.Username, m.config.Password, m.config.Database)
		dbType = "postgres"
	default:
		return fmt.Errorf("unsupported database type: %s", m.config.Type)
	}

	db, err := sqlx.Connect(dbType, dsn)
	if err != nil {
		return fmt.Errorf("error connecting to database: %w", err)
	}

	// Thiết lập pool connection
	db.SetMaxOpenConns(m.config.MaxOpenConns)
	db.SetMaxIdleConns(m.config.MaxIdleConns)
	db.SetConnMaxLifetime(m.config.ConnMaxLifetime)

	m.DB = db
	m.log.Info("Database connected successfully", "type", m.config.Type, "host", m.config.Host)

	return nil
}

// Ping kiểm tra kết nối database
func (m *Manager) Ping() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := m.DB.PingContext(ctx); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}

// Close đóng kết nối database
func (m *Manager) Close() error {
	if m.DB != nil {
		return m.DB.Close()
	}
	return nil
}

// GetDB trả về đối tượng database để sử dụng
func (m *Manager) GetDB() *sqlx.DB {
	return m.DB
}
