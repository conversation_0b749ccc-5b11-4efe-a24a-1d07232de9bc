package database

import (
	"database/sql"
	"fmt"
	"sort"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/mysql"
	_ "github.com/golang-migrate/migrate/v4/source/file"
)

// ModuleInfo định nghĩa interface cho module info để tránh import cycle
type ModuleInfo interface {
	Name() string
	GetMigrationPath() string
	GetMigrationOrder() int
}

// ModuleRegistry định nghĩa interface cho module registry
type ModuleRegistry interface {
	GetModule(name string) (ModuleInfo, bool)
}

type MigrationConfig struct {
	DatabaseURL    string
	MigrationsPath string
	TablePrefix    string
	Registry       ModuleRegistry // Registry để truy cập module
}

type Migrator struct {
	db         *sql.DB
	migrate    *migrate.Migrate
	config     *MigrationConfig
	moduleName string
}

type ModuleMigrator struct {
	systemMigrator  *Migrator
	moduleMigrators map[string]*Migrator
	config          *MigrationConfig
}

func NewModuleMigrator(db *sql.DB, config *MigrationConfig) (*ModuleMigrator, error) {
	// Khởi tạo system migrator
	systemMigrator, err := NewMigrator(db, &MigrationConfig{
		DatabaseURL:    config.DatabaseURL,
		MigrationsPath: config.MigrationsPath, // Thư mục migrations/ cho hệ thống
		TablePrefix:    "system",
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create system migrator: %w", err)
	}

	return &ModuleMigrator{
		systemMigrator:  systemMigrator,
		moduleMigrators: make(map[string]*Migrator),
		config:          config,
	}, nil
}

func NewMigrator(db *sql.DB, config *MigrationConfig) (*Migrator, error) {
	driver, err := mysql.WithInstance(db, &mysql.Config{
		MigrationsTable: fmt.Sprintf("%s_schema_migrations", config.TablePrefix),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create database driver: %w", err)
	}

	sourceURL := fmt.Sprintf("file://%s", config.MigrationsPath)
	m, err := migrate.NewWithDatabaseInstance(sourceURL, "mysql", driver)
	if err != nil {
		return nil, fmt.Errorf("failed to create migrate instance: %w", err)
	}

	return &Migrator{
		db:      db,
		migrate: m,
		config:  config,
	}, nil
}

// Thêm module migrator
func (mm *ModuleMigrator) AddModule(moduleName string) error {
	// Lấy đường dẫn migration từ module
	if mm.config.Registry == nil {
		return fmt.Errorf("module registry is not provided")
	}

	module, ok := mm.config.Registry.GetModule(moduleName)
	if !ok {
		return fmt.Errorf("module %s not found in registry", moduleName)
	}

	migrationPath := module.GetMigrationPath()
	if migrationPath == "" {
		// Module không có migrations
		return nil
	}

	migrator, err := NewMigrator(mm.systemMigrator.db, &MigrationConfig{
		DatabaseURL:    mm.config.DatabaseURL,
		MigrationsPath: migrationPath, // Đường dẫn trực tiếp từ module
		TablePrefix:    fmt.Sprintf("module_%s", moduleName),
	})
	if err != nil {
		return fmt.Errorf("failed to create migrator for module %s: %w", moduleName, err)
	}

	mm.moduleMigrators[moduleName] = migrator
	return nil
}

// Migration theo thứ tự: system -> modules
func (mm *ModuleMigrator) MigrateUp() error {
	// 1. Migrate system tables trước
	if err := mm.systemMigrator.migrate.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("system migration failed: %w", err)
	}

	// 2. Migrate từng module theo thứ tự migration
	// Tạo map module và migration order
	moduleOrders := make(map[string]int)
	moduleList := make([]string, 0, len(mm.moduleMigrators))

	for name := range mm.moduleMigrators {
		if module, ok := mm.config.Registry.GetModule(name); ok {
			moduleOrders[name] = module.GetMigrationOrder()
			moduleList = append(moduleList, name)
		}
	}

	// Sắp xếp modules theo thứ tự ưu tiên (số nhỏ hơn chạy trước)
	sort.SliceStable(moduleList, func(i, j int) bool {
		return moduleOrders[moduleList[i]] < moduleOrders[moduleList[j]]
	})

	for _, moduleName := range moduleList {
		migrator := mm.moduleMigrators[moduleName]
		if err := migrator.migrate.Up(); err != nil && err != migrate.ErrNoChange {
			return fmt.Errorf("module %s migration failed: %w", moduleName, err)
		}
	}

	return nil
}

// Rollback ngược lại: modules -> system
func (mm *ModuleMigrator) MigrateDown() error {
	// Sắp xếp modules theo thứ tự ưu tiên ngược lại (số lớn hơn rollback trước)
	moduleOrders := make(map[string]int)
	moduleList := make([]string, 0, len(mm.moduleMigrators))

	for name := range mm.moduleMigrators {
		if module, ok := mm.config.Registry.GetModule(name); ok {
			moduleOrders[name] = module.GetMigrationOrder()
			moduleList = append(moduleList, name)
		}
	}

	// Sắp xếp modules theo thứ tự ngược lại cho rollback
	sort.SliceStable(moduleList, func(i, j int) bool {
		return moduleOrders[moduleList[i]] > moduleOrders[moduleList[j]]
	})

	// 1. Rollback từng module theo thứ tự
	for _, moduleName := range moduleList {
		migrator := mm.moduleMigrators[moduleName]
		if err := migrator.migrate.Down(); err != nil && err != migrate.ErrNoChange {
			return fmt.Errorf("module %s rollback failed: %w", moduleName, err)
		}
	}

	// 2. Rollback system tables
	if err := mm.systemMigrator.migrate.Down(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("system rollback failed: %w", err)
	}

	return nil
}

func (mm *ModuleMigrator) Close() error {
	if err, _ := mm.systemMigrator.migrate.Close(); err != nil {
		return err
	}

	for _, migrator := range mm.moduleMigrators {
		if err, _ := migrator.migrate.Close(); err != nil {
			return err
		}
	}

	return nil
}
