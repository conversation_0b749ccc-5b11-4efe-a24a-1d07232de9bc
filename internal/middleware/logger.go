package middleware

import (
	"fmt"
	"time"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Logger returns a middleware that logs HTTP requests
func Logger(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Thời gian bắt đầu
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Xử lý request
		c.Next()

		// Thời gian kết thúc
		end := time.Now()
		latency := end.Sub(start)

		// Lấy trạng thái
		status := c.Writer.Status()

		// Log thông tin request
		if raw != "" {
			path = path + "?" + raw
		}

		// Tạo log message
		msg := fmt.Sprintf("[GIN] %d | %s | %s | %s",
			status,
			latency.String(),
			c.Request.Method,
			path,
		)

		// Log theo status code
		if status >= 500 {
			log.Error(msg, logger.Int("status", status), logger.String("method", c.Request.Method), logger.String("path", path))
		} else if status >= 400 {
			log.Warn(msg, logger.Int("status", status), logger.String("method", c.Request.Method), logger.String("path", path))
		} else {
			log.Info(msg, logger.Int("status", status), logger.String("method", c.Request.Method), logger.String("path", path))
		}
	}
}
