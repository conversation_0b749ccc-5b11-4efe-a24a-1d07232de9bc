package middleware

import (
	"fmt"
	"net/http"
	"runtime/debug"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Recovery returns a middleware that recovers from panics
func Recovery(log logger.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				// Log stack trace
				stack := string(debug.Stack())
				log.Error("Panic recovered",
					logger.String("error", fmt.Sprintf("%v", err)),
					logger.String("stack", stack),
					logger.String("path", c.Request.URL.Path),
					logger.String("method", c.Request.Method),
				)

				// Trả về lỗi server
				c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
					"code":    http.StatusInternalServerError,
					"message": "Internal Server Error",
				})
			}
		}()

		c.Next()
	}
}
