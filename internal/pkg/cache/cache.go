// internal/pkg/cache/cache.go
package cache

import (
	"context"
	"errors"
	"time"
)

// <PERSON><PERSON><PERSON> lỗi chuẩn cho cache
var (
	ErrCacheMiss = errors.New("cache: key not found")
)

// Cache định nghĩa interface cho các hoạt động cache
type Cache interface {
	// Get lấy giá trị từ cache dựa vào key
	Get(ctx context.Context, key string) (interface{}, error)

	// Set lưu giá trị vào cache với TTL
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error

	// Delete xóa một key khỏi cache
	Delete(ctx context.Context, key string) error

	// DeletePattern xóa các key theo pattern
	DeletePattern(ctx context.Context, pattern string) error

	// IsErrCacheMiss kiểm tra xem lỗi có phải là cache miss không
	IsErrCacheMiss(err error) bool
}
