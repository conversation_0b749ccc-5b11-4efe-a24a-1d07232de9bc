package pagination

import (
	"encoding/base64"
	"strconv"
	"wnapi/internal/pkg/errors"

	"github.com/gin-gonic/gin"
)

const (
	DefaultLimit = 10
	MaxLimit     = 100
)

// Params holds the pagination parameters
type Params struct {
	Cursor string
	Limit  int
}

// CursorInfo holds cursor-based pagination information
type CursorInfo struct {
	NextCursor string `json:"next_cursor"`
	HasMore    bool   `json:"has_more"`
}

// Cursor represents a pagination cursor
type Cursor struct {
	ID int `json:"id"`
}

// ParseFromRequest extracts pagination parameters from the request
func ParseFromRequest(c *gin.Context) Params {
	cursor := c.Query("cursor")
	limitStr := c.Query("limit")

	limit := DefaultLimit
	if limitStr != "" {
		parsedLimit, err := strconv.Atoi(limitStr)
		if err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	if limit > MaxLimit {
		limit = MaxLimit
	}

	return Params{
		Cursor: cursor,
		Limit:  limit,
	}
}

// EncodeCursor encodes a cursor value (typically an ID or timestamp)
func EncodeCursor(value string) string {
	return base64.StdEncoding.EncodeToString([]byte(value))
}

// DecodeCursor decodes a cursor back to its original value
func DecodeCursor(cursor string) (string, error) {
	decoded, err := base64.StdEncoding.DecodeString(cursor)
	if err != nil {
		return "", errors.New(errors.ErrCodeInvalidInput, "Invalid cursor format", err)
	}
	return string(decoded), nil
}

// CreateNextCursor creates a next cursor based on the last item in the result set
// The idExtractor function should extract the ID or value to be used as cursor from the last item
func CreateNextCursor(results []interface{}, idExtractor func(interface{}) string) string {
	if len(results) == 0 {
		return ""
	}

	lastItem := results[len(results)-1]
	id := idExtractor(lastItem)
	return EncodeCursor(id)
}
