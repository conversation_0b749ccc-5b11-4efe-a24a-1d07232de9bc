package auth

import (
	"fmt"

	"github.com/gin-gonic/gin"
)

// ContextKey type for context keys
type Context<PERSON>ey string

// DefaultAuthMiddlewareKey is the key used to store auth data in context
const DefaultAuthMiddlewareKey = "auth"

// Các key dùng trong context - thống nhất toàn bộ hệ thống
const (
	UserIDKey   = "userID"
	TenantIDKey = "tenantID"
	ClaimsKey   = "claims"
)

// Claims represents JWT claims with user information
type Claims struct {
	UserID   int    `json:"user_id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	TenantID int    `json:"tenant_id,omitempty"`
	// Add other fields as needed
}

// GetUserID lấy user ID từ context
func GetUserID(c *gin.Context) *uint {
	userID, exists := c.Get(UserIDKey)
	if !exists {
		return nil
	}

	// Tạo biến tạm để lưu kết quả của type assertion
	value := userID.(uint)
	return &value
}

// GetTenantID lấy tenant ID từ context
func GetTenantID(c *gin.Context) uint {
	tenantID, exists := c.Get(TenantIDKey)
	if !exists {
		return 0
	}

	return tenantID.(uint)
}

// GetTenantIDFromContext lấy tenant ID từ context request
func GetTenantIDFromContext(c *gin.Context) (uint, error) {
	// Lấy claims từ context
	claims, exists := GetClaimsFromContext(c)
	if !exists {
		return 0, fmt.Errorf("không tìm thấy thông tin xác thực")
	}

	return claims.TenantID, nil
}
