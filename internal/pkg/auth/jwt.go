package auth

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"
	"wnapi/internal/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
)

// TokenType định nghĩa các loại token
type TokenType string

const (
	AccessToken  TokenType = "access"
	RefreshToken TokenType = "refresh"
)

// Các lỗi liên quan đến JWT - sử dụng standard errors
var (
	ErrInvalidToken          = errors.New("token không hợp lệ")
	ErrExpiredToken          = errors.New("token đã hết hạn")
	ErrTokenValidationFailed = errors.New("xác thực token thất bại")
	ErrMissingToken          = errors.New("không tìm thấy token")
	ErrInvalidTokenType      = errors.New("loại token không hợp lệ")
)

// JWTConfig lưu trữ cấu hình cho JWT
type JWTConfig struct {
	AccessSigningKey       string        // Khóa ký JWT cho access token
	RefreshSigningKey      string        // Khóa ký JWT cho refresh token
	AccessTokenExpiration  time.Duration // Thời gian hết hạn access token
	RefreshTokenExpiration time.Duration // Thời gian hết hạn refresh token
	Issuer                 string        // Tổ chức phát hành
}

// CustomClaims là cấu trúc claims tùy chỉnh
type CustomClaims struct {
	UserID    uint      `json:"user_id"`
	TenantID  uint      `json:"tenant_id"`
	Email     string    `json:"email"`
	Role      string    `json:"role"`
	TokenType TokenType `json:"token_type"`
	jwt.StandardClaims
}

// JWTService cung cấp các phương thức để làm việc với JWT
type JWTService struct {
	config JWTConfig
}

// NewJWTService tạo một instance mới của JWTService
func NewJWTService(config JWTConfig) *JWTService {
	return &JWTService{
		config: config,
	}
}

// GenerateTokenPair tạo cả access token và refresh token
func (s *JWTService) GenerateTokenPair(userID uint, tenantID uint, email, role string) (accessToken string, refreshToken string, err error) {
	// Tạo access token
	accessToken, err = s.generateToken(userID, tenantID, email, role, AccessToken, s.config.AccessTokenExpiration, s.config.AccessSigningKey)
	if err != nil {
		return "", "", fmt.Errorf("không thể tạo access token: %w", err)
	}

	// Tạo refresh token
	refreshToken, err = s.generateToken(userID, tenantID, email, role, RefreshToken, s.config.RefreshTokenExpiration, s.config.RefreshSigningKey)
	if err != nil {
		return "", "", fmt.Errorf("không thể tạo refresh token: %w", err)
	}

	return accessToken, refreshToken, nil
}

// generateToken tạo một JWT token mới với claims tùy chỉnh
func (s *JWTService) generateToken(userID uint, tenantID uint, email, role string, tokenType TokenType, expiration time.Duration, signingKey string) (string, error) {
	now := time.Now()
	expirationTime := now.Add(expiration)

	claims := CustomClaims{
		UserID:    userID,
		TenantID:  tenantID,
		Email:     email,
		Role:      role,
		TokenType: tokenType,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expirationTime.Unix(),
			IssuedAt:  now.Unix(),
			NotBefore: now.Unix(),
			Issuer:    s.config.Issuer,
			Subject:   fmt.Sprintf("%d", userID),
		},
	}
	log.Printf("JWTService: generateToken: {UserID:%d TenantID:%d Email:%s Role:%s TokenType:%s ExpireAt:%d Issuer:%s JTI:%s}",
		claims.UserID,
		claims.TenantID,
		claims.Email,
		claims.Role,
		claims.TokenType,
		claims.ExpiresAt,
		claims.Issuer,
		claims.Id)
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString([]byte(signingKey))
	if err != nil {
		return "", fmt.Errorf("không thể ký token: %w", err)
	}

	return signedToken, nil
}

// ValidateAccessToken xác thực access token và trả về claims
func (s *JWTService) ValidateAccessToken(tokenString string) (*CustomClaims, error) {
	return s.validateToken(tokenString, s.config.AccessSigningKey, AccessToken)
}

// ValidateRefreshToken xác thực refresh token và trả về claims
func (s *JWTService) ValidateRefreshToken(tokenString string) (*CustomClaims, error) {
	return s.validateToken(tokenString, s.config.RefreshSigningKey, RefreshToken)
}

// validateToken là hàm chung để xác thực token và kiểm tra loại token
func (s *JWTService) validateToken(tokenString, signingKey string, expectedTokenType TokenType) (*CustomClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Kiểm tra phương thức ký
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("phương thức ký không hợp lệ: %v", token.Header["alg"])
		}
		return []byte(signingKey), nil
	})

	if err != nil {
		ve, ok := err.(*jwt.ValidationError)
		if ok && ve.Errors&jwt.ValidationErrorExpired != 0 {
			return nil, ErrExpiredToken
		}
		return nil, fmt.Errorf("%w: %v", ErrTokenValidationFailed, err)
	}

	if !token.Valid {
		return nil, ErrInvalidToken
	}

	claims, ok := token.Claims.(*CustomClaims)
	if !ok {
		return nil, ErrInvalidToken
	}

	// Kiểm tra loại token (fix: luôn so sánh string, tránh lỗi type)
	if string(claims.TokenType) != string(expectedTokenType) {
		return nil, ErrInvalidTokenType
	}

	return claims, nil
}

// RefreshTokens làm mới cả access token và refresh token
func (s *JWTService) RefreshTokens(refreshToken string) (newAccessToken string, newRefreshToken string, err error) {
	// Xác thực refresh token
	claims, err := s.ValidateRefreshToken(refreshToken)
	if err != nil {
		return "", "", err
	}

	// Tạo cặp token mới
	return s.GenerateTokenPair(claims.UserID, claims.TenantID, claims.Email, claims.Role)
}

// GetConfig trả về cấu hình JWT hiện tại
func (s *JWTService) GetConfig() JWTConfig {
	return s.config
}

// JWTAuthMiddleware tạo middleware xác thực JWT cho Gin
func (s *JWTService) JWTAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Printf("JWTAuthMiddleware: Bắt đầu xác thực token cho path: %s", c.Request.URL.Path)

		tokenString, err := ExtractTokenFromRequest(c.Request)
		if err != nil {
			log.Printf("JWTAuthMiddleware: Lỗi trích xuất token: %v", err)
			response.Unauthorized(c, "Không tìm thấy token xác thực")
			return
		}

		log.Printf("JWTAuthMiddleware: Token string: %s", tokenString)
		log.Printf("JWTAuthMiddleware: Config: %+v", s.config)

		claims, err := s.ValidateAccessToken(tokenString)
		if err != nil {
			log.Printf("JWTAuthMiddleware: Lỗi xác thực token: %v", err)

			switch {
			case errors.Is(err, ErrExpiredToken):
				response.Error(c, http.StatusUnauthorized, "Token đã hết hạn", "TOKEN_EXPIRED")
			case errors.Is(err, ErrInvalidToken):
				response.Error(c, http.StatusUnauthorized, "Token không hợp lệ", "INVALID_TOKEN")
			case errors.Is(err, ErrMissingToken):
				response.Error(c, http.StatusUnauthorized, "Không tìm thấy token xác thực", "TOKEN_NOT_FOUND")
			default:
				response.Error(c, http.StatusUnauthorized, "Xác thực token thất bại", "INVALID_TOKEN")
			}
			return
		}

		log.Printf("JWTAuthMiddleware: Claims hợp lệ: %+v", claims)

		// Lưu claims vào context với key thống nhất từ auth.go
		c.Set(ClaimsKey, claims)
		c.Set(UserIDKey, claims.UserID)
		c.Set(TenantIDKey, claims.TenantID)

		log.Printf("JWTAuthMiddleware: Đã lưu claims vào context với keys: %s, %s, %s",
			ClaimsKey, UserIDKey, TenantIDKey)

		c.Next()
	}
}

// RoleAuthMiddleware tạo middleware kiểm tra vai trò người dùng
func RoleAuthMiddleware(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		log.Printf("RoleAuthMiddleware: Bắt đầu kiểm tra role cho path: %s", c.Request.URL.Path)
		log.Printf("RoleAuthMiddleware: Các role yêu cầu: %v", requiredRoles)

		// Lấy claims từ context sử dụng key đã thống nhất
		claimsValue, exists := c.Get(ClaimsKey)
		if !exists {
			log.Printf("RoleAuthMiddleware: Không tìm thấy claims trong context")
			response.Unauthorized(c, "Yêu cầu xác thực")
			return
		}

		claims, ok := claimsValue.(*CustomClaims)
		if !ok {
			log.Printf("RoleAuthMiddleware: Claims không đúng định dạng")
			response.InternalServerError(c, "Lỗi hệ thống")
			return
		}

		log.Printf("RoleAuthMiddleware: Claims của user: UserID=%d, Role=%s", claims.UserID, claims.Role)

		// Kiểm tra xem người dùng có vai trò cần thiết không
		hasRole := false
		for _, role := range requiredRoles {
			if claims.Role == role {
				hasRole = true
				log.Printf("RoleAuthMiddleware: User có role %s phù hợp", role)
				break
			}
		}

		if !hasRole {
			log.Printf("RoleAuthMiddleware: User không có quyền truy cập. Role hiện tại: %s", claims.Role)
			response.Forbidden(c, "Không có quyền truy cập")
			return
		}

		log.Printf("RoleAuthMiddleware: Xác thực role thành công")
		c.Next()
	}
}

// ExtractTokenFromRequest trích xuất JWT token từ HTTP request
func ExtractTokenFromRequest(r *http.Request) (string, error) {
	// Kiểm tra header Authorization
	bearerToken := r.Header.Get("Authorization")
	if bearerToken != "" {
		return ExtractTokenFromBearerString(bearerToken)
	}

	// Kiểm tra token trong query string
	token := r.URL.Query().Get("token")
	if token != "" {
		return token, nil
	}

	// Kiểm tra token trong cookie
	cookie, err := r.Cookie("token")
	if err == nil {
		return cookie.Value, nil
	}

	return "", ErrMissingToken
}

// ExtractTokenFromGinContext trích xuất token từ Gin context
func ExtractTokenFromGinContext(c *gin.Context) (string, error) {
	return ExtractTokenFromRequest(c.Request)
}

// GetClaimsFromContext lấy claims từ Gin context
func GetClaimsFromContext(c *gin.Context) (*CustomClaims, bool) {
	claims, exists := c.Get(ClaimsKey)
	if !exists {
		return nil, false
	}
	return claims.(*CustomClaims), true
}

// GetUserIDFromContext lấy user ID từ Gin context
func GetUserIDFromContext(c *gin.Context) (uint, bool) {
	userID, exists := c.Get(UserIDKey)
	if !exists {
		return 0, false
	}
	return userID.(uint), true
}

// ExtractTokenFromBearerString trích xuất token từ chuỗi Bearer
// Format: "Bearer <token>"
func ExtractTokenFromBearerString(bearerString string) (string, error) {
	parts := strings.Split(bearerString, " ")
	if len(parts) == 2 && strings.ToLower(parts[0]) == "bearer" {
		return parts[1], nil
	}
	return "", errors.New("chuỗi bearer không hợp lệ")
}
