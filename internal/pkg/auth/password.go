package auth

import (
	"golang.org/x/crypto/bcrypt"
)

// HashPassword mã hóa mật khẩu sử dụng bcrypt
func HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// ComparePassword so sánh mật khẩu đã hash với mật khẩu gốc
func ComparePassword(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}
