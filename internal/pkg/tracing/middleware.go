package tracing

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
)

// HTTPMiddleware creates an HTTP middleware for tracing
func HTTPMiddleware(serviceName string) func(http.Handler) http.Handler {
	tracer := otel.Tracer(serviceName)

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Extract trace context from incoming request
			ctx := otel.GetTextMapPropagator().Extract(r.Context(), propagation.HeaderCarrier(r.Header))

			// Start a new span
			spanName := r.Method + " " + r.URL.Path
			ctx, span := tracer.Start(ctx, spanName, trace.WithSpanKind(trace.SpanKindServer))
			defer span.End()

			// Set span attributes
			span.SetAttributes(
				HTTPMethod(r.Method),
				HTTPURL(r.URL.String()),
				attribute.String("http.scheme", r.URL.Scheme),
				attribute.String("http.host", r.Host),
				attribute.String("http.target", r.URL.Path),
				attribute.String("http.route", r.URL.Path),
				HTTPUserAgent(r.UserAgent()),
				attribute.String("http.remote_addr", r.RemoteAddr),
			)

			// Add request headers as attributes (selective)
			if contentType := r.Header.Get("Content-Type"); contentType != "" {
				span.SetAttributes(attribute.String("http.request.content_type", contentType))
			}
			if contentLength := r.Header.Get("Content-Length"); contentLength != "" {
				span.SetAttributes(attribute.String("http.request.content_length", contentLength))
			}

			// Wrap response writer to capture status code
			wrappedWriter := &responseWriter{
				ResponseWriter: w,
				statusCode:     http.StatusOK,
			}

			// Record start time
			startTime := time.Now()

			// Call the next handler with the traced context
			next.ServeHTTP(wrappedWriter, r.WithContext(ctx))

			// Record request duration
			duration := time.Since(startTime)
			span.SetAttributes(
				attribute.Float64("http.request.duration_ms", float64(duration.Nanoseconds())/1000000),
			)

			// Set response attributes
			span.SetAttributes(
				HTTPStatusCode(wrappedWriter.statusCode),
				attribute.Int("http.response.size", wrappedWriter.bytesWritten),
			)

			// Set span status based on HTTP status code
			if wrappedWriter.statusCode >= 400 {
				span.SetStatus(codes.Error, http.StatusText(wrappedWriter.statusCode))
			} else {
				span.SetStatus(codes.Ok, "")
			}

			// Add response headers as attributes (selective)
			if contentType := w.Header().Get("Content-Type"); contentType != "" {
				span.SetAttributes(attribute.String("http.response.content_type", contentType))
			}
		})
	}
}

// responseWriter wraps http.ResponseWriter to capture response details
type responseWriter struct {
	http.ResponseWriter
	statusCode   int
	bytesWritten int
}

func (rw *responseWriter) WriteHeader(statusCode int) {
	rw.statusCode = statusCode
	rw.ResponseWriter.WriteHeader(statusCode)
}

func (rw *responseWriter) Write(data []byte) (int, error) {
	n, err := rw.ResponseWriter.Write(data)
	rw.bytesWritten += n
	return n, err
}

// AddRequestIDToSpan extracts request ID from context or headers and adds it to span
func AddRequestIDToSpan(ctx context.Context, r *http.Request) {
	var requestID string

	// Try to get request ID from context first
	if id := ctx.Value("request_id"); id != nil {
		if idStr, ok := id.(string); ok {
			requestID = idStr
		}
	}

	// Fallback to headers
	if requestID == "" {
		requestID = r.Header.Get("X-Request-ID")
	}

	// Fallback to trace ID
	if requestID == "" {
		requestID = TraceID(ctx)
	}

	if requestID != "" {
		AddSpanAttributes(ctx, RequestID(requestID))
	}
}

// DatabaseMiddleware creates a span for database operations
func DatabaseMiddleware(ctx context.Context, operation, query string) (context.Context, trace.Span) {
	tracer := otel.Tracer("database")
	spanName := "db." + operation

	ctx, span := tracer.Start(ctx, spanName, trace.WithSpanKind(trace.SpanKindClient))

	// Set database attributes
	span.SetAttributes(
		DBSystem("mysql"),
		DBOperation(operation),
		DBStatement(query),
	)

	return ctx, span
}

// ModuleMiddleware creates a span for module operations
func ModuleMiddleware(ctx context.Context, moduleName, operation string) (context.Context, trace.Span) {
	tracer := otel.Tracer(moduleName)
	spanName := moduleName + "." + operation

	ctx, span := tracer.Start(ctx, spanName, trace.WithSpanKind(trace.SpanKindInternal))

	// Set module attributes
	span.SetAttributes(
		Module(moduleName),
		Function(operation),
	)

	return ctx, span
}

// InjectHTTPHeaders injects trace context into HTTP headers for outgoing requests
func InjectHTTPHeaders(ctx context.Context, req *http.Request) {
	otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(req.Header))
}

// HTTPClientMiddleware creates tracing for outgoing HTTP requests
func HTTPClientMiddleware(client *http.Client) *http.Client {
	// Wrap the transport
	if client.Transport == nil {
		client.Transport = http.DefaultTransport
	}

	client.Transport = &tracingTransport{
		base: client.Transport,
	}

	return client
}

// tracingTransport wraps http.RoundTripper to add tracing
type tracingTransport struct {
	base http.RoundTripper
}

func (t *tracingTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	ctx := req.Context()
	tracer := otel.Tracer("http.client")

	spanName := req.Method + " " + req.URL.Host
	ctx, span := tracer.Start(ctx, spanName, trace.WithSpanKind(trace.SpanKindClient))
	defer span.End()

	// Set request attributes
	span.SetAttributes(
		HTTPMethod(req.Method),
		HTTPURL(req.URL.String()),
		attribute.String("http.scheme", req.URL.Scheme),
		attribute.String("http.host", req.URL.Host),
	)

	// Inject trace context into request headers
	InjectHTTPHeaders(ctx, req)

	// Update request context
	req = req.WithContext(ctx)

	// Make the request
	startTime := time.Now()
	resp, err := t.base.RoundTrip(req)
	duration := time.Since(startTime)

	// Record duration
	span.SetAttributes(
		attribute.Float64("http.request.duration_ms", float64(duration.Nanoseconds())/1000000),
	)

	if err != nil {
		span.RecordError(err)
		span.SetStatus(codes.Error, err.Error())
		return resp, err
	}

	// Set response attributes
	span.SetAttributes(
		HTTPStatusCode(resp.StatusCode),
		attribute.String("http.response.content_type", resp.Header.Get("Content-Type")),
	)

	// Set span status based on response
	if resp.StatusCode >= 400 {
		span.SetStatus(codes.Error, "HTTP "+strconv.Itoa(resp.StatusCode))
	} else {
		span.SetStatus(codes.Ok, "")
	}

	return resp, nil
}
