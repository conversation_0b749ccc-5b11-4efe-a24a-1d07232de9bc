package tracing

import (
	"context"
	"fmt"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/exporters/stdout/stdouttrace"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.26.0"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// Provider manages OpenTelemetry tracing setup
type Provider struct {
	config         *Config
	tracerProvider *sdktrace.TracerProvider
	exporter       sdktrace.SpanExporter
}

// NewProvider creates a new tracing provider
func NewProvider(config *Config) (*Provider, error) {
	if config == nil {
		config = DefaultConfig()
	}

	provider := &Provider{
		config: config,
	}

	if !config.Enabled {
		// Return provider with no-op tracer
		return provider, nil
	}

	if err := provider.setupTracing(); err != nil {
		return nil, fmt.Errorf("failed to setup tracing: %w", err)
	}

	return provider, nil
}

// setupTracing initializes OpenTelemetry tracing
func (p *Provider) setupTracing() error {
	ctx := context.Background()

	// Create resource
	res, err := p.createResource()
	if err != nil {
		return fmt.Errorf("failed to create resource: %w", err)
	}

	// Create exporter based on configuration
	exporter, err := p.createExporter(ctx)
	if err != nil {
		return fmt.Errorf("failed to create exporter: %w", err)
	}
	p.exporter = exporter

	// Create sampler
	sampler := p.createSampler()

	// Create tracer provider
	p.tracerProvider = sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
		sdktrace.WithSampler(sampler),
	)

	// Set global tracer provider
	otel.SetTracerProvider(p.tracerProvider)

	// Set global propagator
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	return nil
}

// createResource creates an OpenTelemetry resource
func (p *Provider) createResource() (*resource.Resource, error) {
	return resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(p.config.ServiceName),
			semconv.ServiceVersion(p.config.ServiceVersion),
			semconv.DeploymentEnvironment(p.config.Environment),
		),
	)
}

// createOTLPExporter creates an OTLP exporter
func (p *Provider) createOTLPExporter(ctx context.Context) (sdktrace.SpanExporter, error) {
	// Create gRPC connection options
	var opts []otlptracegrpc.Option

	opts = append(opts, otlptracegrpc.WithEndpoint(p.config.OTLP.Endpoint))
	opts = append(opts, otlptracegrpc.WithTimeout(p.config.OTLP.Timeout))

	if p.config.OTLP.Insecure {
		opts = append(opts, otlptracegrpc.WithTLSCredentials(insecure.NewCredentials()))
	}

	if len(p.config.OTLP.Headers) > 0 {
		opts = append(opts, otlptracegrpc.WithHeaders(p.config.OTLP.Headers))
	}

	// Add dial options for better connection handling
	dialOpts := []grpc.DialOption{
		grpc.WithBlock(),
		grpc.WithTimeout(p.config.OTLP.Timeout),
	}
	opts = append(opts, otlptracegrpc.WithDialOption(dialOpts...))

	return otlptracegrpc.New(ctx, opts...)
}

// createExporter creates the appropriate exporter based on configuration
func (p *Provider) createExporter(ctx context.Context) (sdktrace.SpanExporter, error) {
	switch p.config.ExporterType {
	case "console":
		return p.createConsoleExporter()
	case "jaeger":
		return p.createJaegerExporter()
	case "otlp":
		return p.createOTLPExporter(ctx)
	default:
		return p.createConsoleExporter() // Default to console
	}
}

// createConsoleExporter creates a console/stdout exporter
func (p *Provider) createConsoleExporter() (sdktrace.SpanExporter, error) {
	return stdouttrace.New(
		stdouttrace.WithPrettyPrint(),
		stdouttrace.WithoutTimestamps(), // For cleaner output in development
	)
}

// createJaegerExporter creates a Jaeger exporter
func (p *Provider) createJaegerExporter() (sdktrace.SpanExporter, error) {
	return jaeger.New(jaeger.WithCollectorEndpoint(jaeger.WithEndpoint(p.config.Jaeger.Endpoint)))
}

// createSampler creates a trace sampler based on configuration
func (p *Provider) createSampler() sdktrace.Sampler {
	switch p.config.Sampling.Type {
	case "always":
		return sdktrace.AlwaysSample()
	case "never":
		return sdktrace.NeverSample()
	case "ratio":
		return sdktrace.TraceIDRatioBased(p.config.Sampling.Ratio)
	default:
		return sdktrace.TraceIDRatioBased(0.1) // Default to 10%
	}
}

// Tracer returns a named tracer
func (p *Provider) Tracer(name string, opts ...trace.TracerOption) trace.Tracer {
	if p.tracerProvider == nil {
		return otel.Tracer(name, opts...)
	}
	return p.tracerProvider.Tracer(name, opts...)
}

// TracerProvider returns the tracer provider
func (p *Provider) TracerProvider() *sdktrace.TracerProvider {
	return p.tracerProvider
}

// Shutdown gracefully shuts down the tracing provider
func (p *Provider) Shutdown(ctx context.Context) error {
	if p.tracerProvider == nil {
		return nil
	}

	// Create a context with timeout for shutdown
	shutdownCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	return p.tracerProvider.Shutdown(shutdownCtx)
}

// ForceFlush forces all pending spans to be exported
func (p *Provider) ForceFlush(ctx context.Context) error {
	if p.tracerProvider == nil {
		return nil
	}

	flushCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	return p.tracerProvider.ForceFlush(flushCtx)
}
