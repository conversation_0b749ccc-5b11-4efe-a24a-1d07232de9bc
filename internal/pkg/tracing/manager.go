package tracing

import (
	"context"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Manager manages the lifecycle of tracing
type Manager struct {
	provider *Provider
	config   *Config
}

// NewManager creates a new tracing manager
func NewManager(config *Config) (*Manager, error) {
	var err error
	if config == nil {
		config, err = LoadConfigFromEnv()
		if err != nil {
			return nil, err
		}
	}

	provider, err := NewProvider(config)
	if err != nil {
		return nil, err
	}

	return &Manager{
		provider: provider,
		config:   config,
	}, nil
}

// LoadConfigFromEnv loads tracing configuration from environment variables
func LoadConfigFromEnv() (*Config, error) {
	// Try to load .env file (ignore error if not found)
	_ = godotenv.Load()

	config := &Config{}

	// Parse environment variables with custom parsing for nested structs
	config.ServiceName = getEnvOrDefault("TRACING_SERVICE_NAME", "wnapi")
	config.ServiceVersion = getEnvOrDefault("TRACING_SERVICE_VERSION", "0.1.0")
	config.Environment = getEnvOrDefault("TRACING_ENVIRONMENT", "development")
	config.Enabled = getEnvBoolOrDefault("TRACING_ENABLED", true)
	config.ExporterType = getEnvOrDefault("TRACING_EXPORTER_TYPE", "console")

	// OTLP configuration
	config.OTLP.Endpoint = getEnvOrDefault("TRACING_OTLP_ENDPOINT", "http://localhost:4317")
	config.OTLP.Insecure = getEnvBoolOrDefault("TRACING_OTLP_INSECURE", true)
	config.OTLP.Timeout = getEnvDurationOrDefault("TRACING_OTLP_TIMEOUT", 10*time.Second)
	config.OTLP.Headers = make(map[string]string)

	// Jaeger configuration
	config.Jaeger.Endpoint = getEnvOrDefault("TRACING_JAEGER_ENDPOINT", "http://localhost:14268/api/traces")
	config.Jaeger.Username = getEnvOrDefault("TRACING_JAEGER_USERNAME", "")
	config.Jaeger.Password = getEnvOrDefault("TRACING_JAEGER_PASSWORD", "")

	// Sampling configuration
	config.Sampling.Type = getEnvOrDefault("TRACING_SAMPLING_TYPE", "ratio")
	config.Sampling.Ratio = getEnvFloatOrDefault("TRACING_SAMPLING_RATIO", 0.1)

	return config, nil
}

// Helper functions for environment variable parsing
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvBoolOrDefault(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getEnvFloatOrDefault(key string, defaultValue float64) float64 {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseFloat(value, 64); err == nil {
			return parsed
		}
	}
	return defaultValue
}

func getEnvDurationOrDefault(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if parsed, err := time.ParseDuration(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// Initialize initializes tracing for the application
func (m *Manager) Initialize() error {
	if !m.config.Enabled {
		log.Println("Tracing is disabled")
		return nil
	}

	log.Printf("Initializing tracing with service: %s, version: %s, environment: %s",
		m.config.ServiceName, m.config.ServiceVersion, m.config.Environment)

	return nil
}

// Shutdown gracefully shuts down tracing
func (m *Manager) Shutdown(ctx context.Context) error {
	if m.provider == nil {
		return nil
	}

	log.Println("Shutting down tracing...")
	return m.provider.Shutdown(ctx)
}

// Provider returns the tracing provider
func (m *Manager) Provider() *Provider {
	return m.provider
}

// Config returns the tracing configuration
func (m *Manager) Config() *Config {
	return m.config
}

// IsEnabled returns whether tracing is enabled
func (m *Manager) IsEnabled() bool {
	return m.config.Enabled
}

// Global tracing manager instance
var globalManager *Manager

// InitializeGlobal initializes global tracing
func InitializeGlobal() error {
	manager, err := NewManager(nil)
	if err != nil {
		return err
	}

	globalManager = manager
	return manager.Initialize()
}

// ShutdownGlobal shuts down global tracing
func ShutdownGlobal(ctx context.Context) error {
	if globalManager == nil {
		return nil
	}
	return globalManager.Shutdown(ctx)
}

// GetGlobalManager returns the global tracing manager
func GetGlobalManager() *Manager {
	return globalManager
}
