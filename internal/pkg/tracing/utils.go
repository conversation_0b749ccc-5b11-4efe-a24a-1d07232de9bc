package tracing

import (
	"context"
	"fmt"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// SpanFromContext returns the span from the context
func SpanFromContext(ctx context.Context) trace.Span {
	return trace.SpanFromContext(ctx)
}

// StartSpan starts a new span with the given name and options
func StartSpan(ctx context.Context, tracerName, spanName string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	tracer := otel.Tracer(tracerName)
	return tracer.Start(ctx, spanName, opts...)
}

// AddSpanAttributes adds attributes to the span in the context
func AddSpanAttributes(ctx context.Context, attrs ...attribute.KeyValue) {
	span := SpanFromContext(ctx)
	if span.IsRecording() {
		span.SetAttributes(attrs...)
	}
}

// AddSpanEvent adds an event to the span in the context
func AddSpanEvent(ctx context.Context, name string, attrs ...attribute.KeyValue) {
	span := SpanFromContext(ctx)
	if span.IsRecording() {
		span.AddEvent(name, trace.WithAttributes(attrs...))
	}
}

// SetSpanStatus sets the status of the span in the context
func SetSpanStatus(ctx context.Context, code codes.Code, description string) {
	span := SpanFromContext(ctx)
	if span.IsRecording() {
		span.SetStatus(code, description)
	}
}

// RecordError records an error on the span in the context
func RecordError(ctx context.Context, err error, attrs ...attribute.KeyValue) {
	span := SpanFromContext(ctx)
	if span.IsRecording() && err != nil {
		span.RecordError(err, trace.WithAttributes(attrs...))
		span.SetStatus(codes.Error, err.Error())
	}
}

// WithSpan executes a function within a new span
func WithSpan(ctx context.Context, tracerName, spanName string, fn func(context.Context) error, opts ...trace.SpanStartOption) error {
	ctx, span := StartSpan(ctx, tracerName, spanName, opts...)
	defer span.End()

	err := fn(ctx)
	if err != nil {
		RecordError(ctx, err)
	}

	return err
}

// WithSpanResult executes a function within a new span and returns its result
func WithSpanResult[T any](ctx context.Context, tracerName, spanName string, fn func(context.Context) (T, error), opts ...trace.SpanStartOption) (T, error) {
	ctx, span := StartSpan(ctx, tracerName, spanName, opts...)
	defer span.End()

	result, err := fn(ctx)
	if err != nil {
		RecordError(ctx, err)
	}

	return result, err
}

// Common attribute keys
const (
	// HTTP attributes
	HTTPMethodKey     = attribute.Key("http.method")
	HTTPURLKey        = attribute.Key("http.url")
	HTTPStatusCodeKey = attribute.Key("http.status_code")
	HTTPUserAgentKey  = attribute.Key("http.user_agent")

	// Database attributes
	DBSystemKey    = attribute.Key("db.system")
	DBNameKey      = attribute.Key("db.name")
	DBStatementKey = attribute.Key("db.statement")
	DBOperationKey = attribute.Key("db.operation")

	// Service attributes
	ServiceNameKey    = attribute.Key("service.name")
	ServiceVersionKey = attribute.Key("service.version")

	// Custom attributes
	UserIDKey    = attribute.Key("user.id")
	RequestIDKey = attribute.Key("request.id")
	ModuleKey    = attribute.Key("module.name")
	FunctionKey  = attribute.Key("function.name")

	// Media attributes
	FileIDKey      = attribute.Key("media.file_id")
	TenantIDKey    = attribute.Key("tenant_id")
	ContentTypeKey = attribute.Key("content_type")
	FileSizeKey    = attribute.Key("file_size")
	FilenameKey    = attribute.Key("filename")
	MediaTypeKey   = attribute.Key("media.type")
	SearchQueryKey = attribute.Key("search.query")
	ResultCountKey = attribute.Key("result.count")
	HasMoreKey     = attribute.Key("has_more")
	PermanentKey   = attribute.Key("permanent")
	DataSizeKey    = attribute.Key("data.size")
)

// Common attribute constructors
func HTTPMethod(method string) attribute.KeyValue {
	return HTTPMethodKey.String(method)
}

func HTTPURL(url string) attribute.KeyValue {
	return HTTPURLKey.String(url)
}

func HTTPStatusCode(code int) attribute.KeyValue {
	return HTTPStatusCodeKey.Int(code)
}

func HTTPUserAgent(ua string) attribute.KeyValue {
	return HTTPUserAgentKey.String(ua)
}

func DBSystem(system string) attribute.KeyValue {
	return DBSystemKey.String(system)
}

func DBName(name string) attribute.KeyValue {
	return DBNameKey.String(name)
}

func DBStatement(statement string) attribute.KeyValue {
	return DBStatementKey.String(statement)
}

func DBOperation(operation string) attribute.KeyValue {
	return DBOperationKey.String(operation)
}

func ServiceName(name string) attribute.KeyValue {
	return ServiceNameKey.String(name)
}

func ServiceVersion(version string) attribute.KeyValue {
	return ServiceVersionKey.String(version)
}

func UserID(id string) attribute.KeyValue {
	return UserIDKey.String(id)
}

func RequestID(id string) attribute.KeyValue {
	return RequestIDKey.String(id)
}

func Module(name string) attribute.KeyValue {
	return ModuleKey.String(name)
}

func Function(name string) attribute.KeyValue {
	return FunctionKey.String(name)
}

// Media attribute constructors
func FileID(id string) attribute.KeyValue {
	return FileIDKey.String(id)
}

func TenantID(id string) attribute.KeyValue {
	return TenantIDKey.String(id)
}

func ContentType(contentType string) attribute.KeyValue {
	return ContentTypeKey.String(contentType)
}

func FileSize(size int) attribute.KeyValue {
	return FileSizeKey.Int(size)
}

func Filename(name string) attribute.KeyValue {
	return FilenameKey.String(name)
}

func MediaType(mediaType string) attribute.KeyValue {
	return MediaTypeKey.String(mediaType)
}

func SearchQuery(query string) attribute.KeyValue {
	return SearchQueryKey.String(query)
}

func ResultCount(count int) attribute.KeyValue {
	return ResultCountKey.Int(count)
}

func HasMore(hasMore bool) attribute.KeyValue {
	return HasMoreKey.Bool(hasMore)
}

func Permanent(permanent bool) attribute.KeyValue {
	return PermanentKey.Bool(permanent)
}

func DataSize(size int) attribute.KeyValue {
	return DataSizeKey.Int(size)
}

// SpanKind constants for common span kinds
const (
	SpanKindServer   = trace.SpanKindServer
	SpanKindClient   = trace.SpanKindClient
	SpanKindProducer = trace.SpanKindProducer
	SpanKindConsumer = trace.SpanKindConsumer
	SpanKindInternal = trace.SpanKindInternal
)

// TraceID returns the trace ID from the context as a string
func TraceID(ctx context.Context) string {
	span := SpanFromContext(ctx)
	if span.SpanContext().IsValid() {
		return span.SpanContext().TraceID().String()
	}
	return ""
}

// SpanID returns the span ID from the context as a string
func SpanID(ctx context.Context) string {
	span := SpanFromContext(ctx)
	if span.SpanContext().IsValid() {
		return span.SpanContext().SpanID().String()
	}
	return ""
}

// TracingInfo returns both trace ID and span ID as a formatted string
func TracingInfo(ctx context.Context) string {
	traceID := TraceID(ctx)
	spanID := SpanID(ctx)
	if traceID != "" && spanID != "" {
		return fmt.Sprintf("trace_id=%s span_id=%s", traceID, spanID)
	}
	return ""
}
