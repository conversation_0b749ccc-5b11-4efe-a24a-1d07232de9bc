package tracing

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/trace"
)

// GinMiddleware creates a Gin middleware for tracing
func GinMiddleware(serviceName string) gin.HandlerFunc {
	tracer := otel.Tracer(serviceName)

	return func(c *gin.Context) {
		// Extract trace context from incoming request
		ctx := otel.GetTextMapPropagator().Extract(c.Request.Context(), propagation.HeaderCarrier(c.Request.Header))

		// Start a new span
		spanName := c.Request.Method + " " + c.FullPath()
		if spanName == " " { // Handle case where FullPath is empty
			spanName = c.Request.Method + " " + c.Request.URL.Path
		}

		ctx, span := tracer.Start(ctx, spanName, trace.WithSpanKind(trace.SpanKindServer))
		defer span.End()

		// Set span attributes
		span.SetAttributes(
			HTTPMethod(c.Request.Method),
			HTTPURL(c.Request.URL.String()),
			attribute.String("http.scheme", c.Request.URL.Scheme),
			attribute.String("http.host", c.Request.Host),
			attribute.String("http.target", c.Request.URL.Path),
			attribute.String("http.route", c.FullPath()),
			HTTPUserAgent(c.Request.UserAgent()),
			attribute.String("http.remote_addr", c.ClientIP()),
		)

		// Add request headers as attributes (selective)
		if contentType := c.GetHeader("Content-Type"); contentType != "" {
			span.SetAttributes(attribute.String("http.request.content_type", contentType))
		}
		if contentLength := c.GetHeader("Content-Length"); contentLength != "" {
			span.SetAttributes(attribute.String("http.request.content_length", contentLength))
		}
		if requestID := c.GetHeader("X-Request-ID"); requestID != "" {
			span.SetAttributes(RequestID(requestID))
		}

		// Record start time
		startTime := time.Now()

		// Update context in Gin
		c.Request = c.Request.WithContext(ctx)

		// Process request
		c.Next()

		// Record request duration
		duration := time.Since(startTime)
		span.SetAttributes(
			attribute.Float64("http.request.duration_ms", float64(duration.Nanoseconds())/1000000),
		)

		// Set response attributes
		statusCode := c.Writer.Status()
		span.SetAttributes(
			HTTPStatusCode(statusCode),
			attribute.Int("http.response.size", c.Writer.Size()),
		)

		// Set span status based on HTTP status code
		if statusCode >= 400 {
			span.SetStatus(codes.Error, http.StatusText(statusCode))
		} else {
			span.SetStatus(codes.Ok, "")
		}

		// Add response headers as attributes (selective)
		if contentType := c.Writer.Header().Get("Content-Type"); contentType != "" {
			span.SetAttributes(attribute.String("http.response.content_type", contentType))
		}

		// Record any errors that occurred during request processing
		if len(c.Errors) > 0 {
			errorMsg := c.Errors.String()
			span.RecordError(errors.New(errorMsg))
			span.SetStatus(codes.Error, errorMsg)
		}
	}
}

// GinTraceFromContext returns trace information for use in Gin handlers
func GinTraceFromContext(c *gin.Context) (traceID, spanID string) {
	ctx := c.Request.Context()
	return TraceID(ctx), SpanID(ctx)
}

// AddGinSpanAttributes adds attributes to the current span from Gin context
func AddGinSpanAttributes(c *gin.Context, attrs ...attribute.KeyValue) {
	AddSpanAttributes(c.Request.Context(), attrs...)
}

// AddGinSpanEvent adds an event to the current span from Gin context
func AddGinSpanEvent(c *gin.Context, name string, attrs ...attribute.KeyValue) {
	AddSpanEvent(c.Request.Context(), name, attrs...)
}

// RecordGinError records an error on the current span from Gin context
func RecordGinError(c *gin.Context, err error, attrs ...attribute.KeyValue) {
	RecordError(c.Request.Context(), err, attrs...)
}

// StartGinSpan starts a new span within a Gin handler
func StartGinSpan(c *gin.Context, tracerName, spanName string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	return StartSpan(c.Request.Context(), tracerName, spanName, opts...)
}

// WithGinSpan executes a function within a new span in Gin context
func WithGinSpan(c *gin.Context, tracerName, spanName string, fn func(context.Context) error, opts ...trace.SpanStartOption) error {
	return WithSpan(c.Request.Context(), tracerName, spanName, fn, opts...)
}
