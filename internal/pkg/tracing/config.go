package tracing

import (
	"time"
)

// Config holds the configuration for OpenTelemetry tracing
type Config struct {
	// ServiceName is the name of the service for tracing
	ServiceName string `env:"SERVICE_NAME" envDefault:"wnapi"`

	// ServiceVersion is the version of the service
	ServiceVersion string `env:"SERVICE_VERSION" envDefault:"0.1.0"`

	// Environment is the deployment environment (dev, staging, prod)
	Environment string `env:"ENVIRONMENT" envDefault:"development"`

	// Enabled controls whether tracing is enabled
	Enabled bool `env:"TRACING_ENABLED" envDefault:"true"`

	// ExporterType defines which exporter to use (otlp, jaeger, console)
	ExporterType string `env:"EXPORTER_TYPE" envDefault:"console"`

	// OTLP configuration
	OTLP OTLPConfig `env:"OTLP"`

	// Jaeger configuration
	Jaeger JaegerConfig `env:"JAEGER"`

	// Sampling configuration
	Sampling SamplingConfig `env:"SAMPLING"`
}

// OTLPConfig holds OTLP exporter configuration
type OTLPConfig struct {
	// Endpoint is the OTLP collector endpoint
	Endpoint string `env:"ENDPOINT" envDefault:"http://localhost:4317"`

	// Insecure controls whether to use insecure connection
	Insecure bool `env:"INSECURE" envDefault:"true"`

	// Headers are additional headers to send with OTLP requests
	Headers map[string]string `env:"HEADERS"`

	// Timeout for OTLP requests
	Timeout time.Duration `env:"TIMEOUT" envDefault:"10s"`
}

// JaegerConfig holds Jaeger exporter configuration
type JaegerConfig struct {
	// Endpoint is the Jaeger collector endpoint
	Endpoint string `env:"ENDPOINT" envDefault:"http://localhost:14268/api/traces"`

	// Username for Jaeger authentication
	Username string `env:"USERNAME"`

	// Password for Jaeger authentication
	Password string `env:"PASSWORD"`
}

// SamplingConfig holds sampling configuration
type SamplingConfig struct {
	// Type of sampling (always, never, ratio)
	Type string `env:"TYPE" envDefault:"ratio"`

	// Ratio for ratio-based sampling (0.0 to 1.0)
	Ratio float64 `env:"RATIO" envDefault:"0.1"`
}

// DefaultConfig returns a default tracing configuration
func DefaultConfig() *Config {
	return &Config{
		ServiceName:    "wnapi",
		ServiceVersion: "0.1.0",
		Environment:    "development",
		Enabled:        true,
		ExporterType:   "console",
		OTLP: OTLPConfig{
			Endpoint: "http://localhost:4317",
			Insecure: true,
			Headers:  make(map[string]string),
			Timeout:  10 * time.Second,
		},
		Jaeger: JaegerConfig{
			Endpoint: "http://localhost:14268/api/traces",
		},
		Sampling: SamplingConfig{
			Type:  "ratio",
			Ratio: 0.1,
		},
	}
}
