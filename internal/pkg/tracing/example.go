package tracing

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
)

// Example demonstrates how to use the tracing package
func Example() {
	// 1. Initialize tracing provider
	config := &Config{
		ServiceName:    "wnapi",
		ServiceVersion: "0.1.0",
		Environment:    "development",
		Enabled:        true,
		OTLP: OTLPConfig{
			Endpoint: "http://localhost:4317",
			Insecure: true,
			Timeout:  10 * time.Second,
		},
		Sampling: SamplingConfig{
			Type:  "ratio",
			Ratio: 1.0, // Sample 100% for demo
		},
	}

	provider, err := NewProvider(config)
	if err != nil {
		log.Fatalf("Failed to create tracing provider: %v", err)
	}

	// Ensure proper shutdown
	defer func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := provider.Shutdown(ctx); err != nil {
			log.Printf("Failed to shutdown tracing provider: %v", err)
		}
	}()

	// 2. Create HTTP server with tracing middleware
	mux := http.NewServeMux()

	// Add tracing middleware
	tracedMux := HTTPMiddleware("wnapi")(mux)

	// Add sample handlers
	mux.HandleFunc("/hello", exampleHandler)
	mux.HandleFunc("/user/{id}", userHandler)
	mux.HandleFunc("/database", databaseHandler)

	// Start server
	server := &http.Server{
		Addr:    ":8080",
		Handler: tracedMux,
	}

	log.Println("Starting server on :8080")
	log.Fatal(server.ListenAndServe())
}

// exampleHandler demonstrates basic span usage
func exampleHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Add custom attributes to the span
	AddSpanAttributes(ctx,
		attribute.String("handler.name", "hello"),
		attribute.String("handler.type", "example"),
	)

	// Add an event
	AddSpanEvent(ctx, "processing_request",
		attribute.String("step", "start"),
	)

	// Simulate some work
	time.Sleep(50 * time.Millisecond)

	// Add another event
	AddSpanEvent(ctx, "processing_complete",
		attribute.String("step", "end"),
		attribute.Int("items_processed", 42),
	)

	// Write response
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"message": "Hello, World!", "trace_id": "%s"}`, TraceID(ctx))
}

// userHandler demonstrates nested spans
func userHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Extract user ID from path
	userID := r.PathValue("id")

	// Add user context to span
	AddSpanAttributes(ctx, UserID(userID))

	// Create a nested span for user validation
	err := WithSpan(ctx, "user-service", "validate_user", func(ctx context.Context) error {
		AddSpanAttributes(ctx,
			attribute.String("validation.type", "user_id"),
			attribute.String("validation.value", userID),
		)

		// Simulate validation
		time.Sleep(20 * time.Millisecond)

		if userID == "invalid" {
			return fmt.Errorf("user not found: %s", userID)
		}

		return nil
	})

	if err != nil {
		SetSpanStatus(ctx, codes.Error, "User validation failed")
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}

	// Create another nested span for user data retrieval
	var userData map[string]interface{}
	err = WithSpan(ctx, "user-service", "get_user_data", func(ctx context.Context) error {
		AddSpanAttributes(ctx,
			attribute.String("operation.type", "database_query"),
			attribute.String("table", "users"),
		)

		// Simulate database query
		time.Sleep(30 * time.Millisecond)

		userData = map[string]interface{}{
			"id":    userID,
			"name":  "John Doe",
			"email": "<EMAIL>",
		}

		return nil
	})

	if err != nil {
		RecordError(ctx, err)
		http.Error(w, "Failed to retrieve user data", http.StatusInternalServerError)
		return
	}

	// Set success status
	SetSpanStatus(ctx, codes.Ok, "User retrieved successfully")

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"user": %v, "trace_id": "%s"}`, userData, TraceID(ctx))
}

// databaseHandler demonstrates database tracing
func databaseHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Start database span
	ctx, span := DatabaseMiddleware(ctx, "SELECT", "SELECT * FROM users WHERE active = 1")
	defer span.End()

	// Add database-specific attributes
	AddSpanAttributes(ctx,
		DBName("wnapi"),
		attribute.String("db.table", "users"),
		attribute.String("db.where", "active = 1"),
	)

	// Simulate database operation
	time.Sleep(100 * time.Millisecond)

	// Simulate result
	results := []map[string]interface{}{
		{"id": 1, "name": "Alice", "active": true},
		{"id": 2, "name": "Bob", "active": true},
	}

	// Add result metrics
	AddSpanAttributes(ctx,
		attribute.Int("db.rows_affected", len(results)),
		attribute.Float64("db.query_duration_ms", 100.0),
	)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"users": %v, "count": %d, "trace_id": "%s"}`,
		results, len(results), TraceID(ctx))
}

// ExampleModuleTracing demonstrates how to add tracing to a module
func ExampleModuleTracing(ctx context.Context) error {
	// Create module span
	ctx, span := ModuleMiddleware(ctx, "auth", "authenticate_user")
	defer span.End()

	// Add module-specific attributes
	AddSpanAttributes(ctx,
		attribute.String("auth.method", "jwt"),
		attribute.String("auth.provider", "local"),
	)

	// Simulate authentication steps
	err := WithSpan(ctx, "auth", "validate_token", func(ctx context.Context) error {
		AddSpanEvent(ctx, "token_validation_start")

		// Simulate token validation
		time.Sleep(10 * time.Millisecond)

		AddSpanEvent(ctx, "token_validation_complete",
			attribute.Bool("token.valid", true),
		)

		return nil
	})

	if err != nil {
		return err
	}

	// Simulate user lookup
	return WithSpan(ctx, "auth", "lookup_user", func(ctx context.Context) error {
		AddSpanAttributes(ctx,
			attribute.String("lookup.type", "database"),
			attribute.String("lookup.table", "users"),
		)

		// Simulate database lookup
		time.Sleep(15 * time.Millisecond)

		return nil
	})
}

// ExampleHTTPClient demonstrates tracing outgoing HTTP requests
func ExampleHTTPClient(ctx context.Context) error {
	// Create HTTP client with tracing
	client := HTTPClientMiddleware(&http.Client{
		Timeout: 30 * time.Second,
	})

	// Create request
	req, err := http.NewRequestWithContext(ctx, "GET", "https://api.example.com/users", nil)
	if err != nil {
		return err
	}

	// Make request (tracing will be automatic)
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// The tracing middleware will automatically record the request details
	return nil
}
