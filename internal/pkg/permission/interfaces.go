// internal/pkg/permission/interfaces.go
package permission

import (
	"context"

	"github.com/gin-gonic/gin"
)

// PermissionChecker là interface cho việc kiểm tra quyền của người dùng
type PermissionChecker interface {
	// UserHasPermission kiểm tra xem người dùng có một quyền cụ thể không
	UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error)

	// UserHasAnyPermission kiểm tra xem người dùng có ít nhất một trong các quyền không
	UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error)

	// UserHasAllPermissions kiểm tra xem người dùng có tất cả các quyền không
	UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error)
}

// PermissionMiddleware là interface cho việc tạo ra các middleware functions
type PermissionMiddleware interface {
	RequirePermission(permission string) gin.HandlerFunc
	RequireAnyPermission(permissions ...string) gin.HandlerFunc
	RequireAllPermissions(permissions ...string) gin.HandlerFunc
}
