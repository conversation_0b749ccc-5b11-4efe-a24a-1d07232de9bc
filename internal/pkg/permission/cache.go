// internal/pkg/permission/cache.go
package permission

import (
	"context"
)

// CachedPermissionChecker là decorator cho PermissionChecker thêm lớp caching.
// Đ<PERSON>y là phiên bản stub, sẽ được triển khai đầy đủ trong Task 04
type CachedPermissionChecker struct {
	delegate PermissionChecker
}

// NewCachedPermissionChecker là constructor cho CachedPermissionChecker.
// Stub - sẽ được triển khai đầy đủ trong Task 04
func NewCachedPermissionChecker(delegate PermissionChecker) *CachedPermissionChecker {
	return &CachedPermissionChecker{
		delegate: delegate,
	}
}

// UserHasPermission kiểm tra quyền, ưu tiên lấy từ cache.
// Stub - sẽ được triển khai đầy đủ trong Task 04
func (cpc *CachedPermissionChecker) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	// Stub - hiện tại chỉ chuyển tiếp cho delegate
	return cpc.delegate.UserHasPermission(ctx, tenantID, userID, permissionCode)
}

// UserHasAnyPermission sử dụng UserHasPermission (đã được cache).
// Stub - sẽ được triển khai đầy đủ trong Task 04
func (cpc *CachedPermissionChecker) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	// Stub - hiện tại chỉ chuyển tiếp cho delegate
	return cpc.delegate.UserHasAnyPermission(ctx, tenantID, userID, permissions)
}

// UserHasAllPermissions sử dụng UserHasPermission (đã được cache).
// Stub - sẽ được triển khai đầy đủ trong Task 04
func (cpc *CachedPermissionChecker) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	// Stub - hiện tại chỉ chuyển tiếp cho delegate
	return cpc.delegate.UserHasAllPermissions(ctx, tenantID, userID, permissions)
}

// InvalidateUserPermissions xóa cache permission của user.
// Stub - sẽ được triển khai đầy đủ trong Task 04
func (cpc *CachedPermissionChecker) InvalidateUserPermissions(ctx context.Context, tenantID uint, userID uint) error {
	// Stub - chưa triển khai
	return nil
}

// InvalidateAllPermissions xóa tất cả cache permission.
// Stub - sẽ được triển khai đầy đủ trong Task 04
func (cpc *CachedPermissionChecker) InvalidateAllPermissions(ctx context.Context) error {
	// Stub - chưa triển khai
	return nil
}
