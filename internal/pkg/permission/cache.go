// internal/pkg/permission/cache.go
package permission

import (
	"context"
	"fmt"
	"strings"
	"time"

	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/logger"
)

// CachedPermissionChecker implement PermissionChecker và thêm lớp caching.
type CachedPermissionChecker struct {
	delegate PermissionChecker // PermissionChecker thực tế (ví dụ: truy vấn DB)
	cache    cache.Cache
	ttl      time.Duration
	logger   logger.Logger
}

// NewCachedPermissionChecker là constructor cho CachedPermissionChecker.
// `checker` ở đây là un-cached version (ví dụ: RBACPermissionService).
func NewCachedPermissionChecker(
	checker PermissionChecker,
	c cache.Cache,
	ttl time.Duration,
	log logger.Logger,
) *CachedPermissionChecker {
	return &CachedPermissionChecker{
		delegate: checker,
		cache:    c,
		ttl:      ttl,
		logger:   log,
	}
}

func (cpc *CachedPermissionChecker) buildCache<PERSON>ey(tenantID uint, userID uint, permissionCode string) string {
	normalizedPermissionCode := strings.ReplaceAll(strings.ToLower(permissionCode), " ", "_")
	return fmt.Sprintf("perm_cache:%d:%d:%s", tenantID, userID, normalizedPermissionCode)
}

// UserHasPermission kiểm tra quyền, ưu tiên lấy từ cache.
func (cpc *CachedPermissionChecker) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	cacheKey := cpc.buildCacheKey(tenantID, userID, permissionCode)

	// Thử lấy kết quả từ cache
	if val, err := cpc.cache.Get(ctx, cacheKey); err == nil {
		if bVal, ok := val.(bool); ok {
			cpc.logger.Debug("Cache hit for permission", "key", cacheKey)
			return bVal, nil
		}
		cpc.logger.Warn("Cache data type mismatch", "key", cacheKey)
	} else if !cpc.cache.IsErrCacheMiss(err) {
		cpc.logger.Error("Lỗi khi lấy permission từ cache", "key", cacheKey, "error", err)
	} else {
		cpc.logger.Debug("Cache miss for permission", "key", cacheKey)
	}

	// Gọi delegate để lấy kết quả thực tế
	hasPermission, err := cpc.delegate.UserHasPermission(ctx, tenantID, userID, permissionCode)
	if err != nil {
		return false, err
	}

	// Lưu kết quả vào cache để lần sau sử dụng
	if errSet := cpc.cache.Set(ctx, cacheKey, hasPermission, cpc.ttl); errSet != nil {
		cpc.logger.Error("Lỗi khi lưu permission vào cache", "key", cacheKey, "error", errSet)
	}

	return hasPermission, nil
}

// UserHasAnyPermission sử dụng UserHasPermission (đã được cache).
func (cpc *CachedPermissionChecker) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 {
		return true, nil
	}

	for _, p := range permissions {
		has, err := cpc.UserHasPermission(ctx, tenantID, userID, p)
		if err != nil {
			return false, fmt.Errorf("lỗi kiểm tra quyền '%s': %w", p, err)
		}
		if has {
			return true, nil
		}
	}

	return false, nil
}

// UserHasAllPermissions sử dụng UserHasPermission (đã được cache).
func (cpc *CachedPermissionChecker) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 {
		return true, nil
	}

	for _, p := range permissions {
		has, err := cpc.UserHasPermission(ctx, tenantID, userID, p)
		if err != nil {
			return false, fmt.Errorf("lỗi kiểm tra quyền '%s': %w", p, err)
		}
		if !has {
			return false, nil
		}
	}

	return true, nil
}

// InvalidateUserPermissions xóa cache permission của user.
func (cpc *CachedPermissionChecker) InvalidateUserPermissions(ctx context.Context, tenantID uint, userID uint) error {
	pattern := fmt.Sprintf("perm_cache:%d:%d:*", tenantID, userID)
	cpc.logger.Info("Invalidating user permissions cache", "pattern", pattern)
	return cpc.cache.DeletePattern(ctx, pattern)
}

// InvalidateAllPermissions xóa tất cả cache permission.
func (cpc *CachedPermissionChecker) InvalidateAllPermissions(ctx context.Context) error {
	pattern := "perm_cache:*"
	cpc.logger.Info("Invalidating all permissions cache", "pattern", pattern)
	return cpc.cache.DeletePattern(ctx, pattern)
}
