// internal/pkg/permission/factory.go
package permission

import (
	"fmt"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

// MiddlewareFactory tạo ra các middleware functions để kiểm tra quyền.
// Nó implement PermissionMiddleware interface.
type MiddlewareFactory struct {
	checker PermissionChecker // Checker này có thể là CachedPermissionChecker hoặc implementation khác
	logger  logger.Logger
}

// NewMiddlewareFactory là constructor cho MiddlewareFactory.
func NewMiddlewareFactory(checker PermissionChecker, log logger.Logger) *MiddlewareFactory {
	return &MiddlewareFactory{
		checker: checker,
		logger:  log,
	}
}

// RequirePermission tạo middleware kiểm tra một quyền cụ thể.
func (mf *MiddlewareFactory) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok {
			return // Lỗi đã được xử lý trong extractAuthContext
		}

		hasPermission, err := mf.checker.UserHasPermission(c.Request.Context(), tenantID, userID, permission)
		if err != nil {
			mf.logger.Error("Lỗi kiểm tra quyền (UserHasPermission)",
				"error", err, "user_id", userID, "tenant_id", tenantID, "permission", permission)
			AbortWithPermissionCheckError(c, err)
			return
		}

		if !hasPermission {
			mf.logger.Warn("Từ chối quyền truy cập",
				"user_id", userID, "tenant_id", tenantID, "required_permission", permission, "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, permission)
			return
		}
		c.Next()
	}
}

// RequireAnyPermission tạo middleware kiểm tra người dùng có ít nhất một trong các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(permissions) == 0 {
			mf.logger.Info("Không có quyền nào được yêu cầu trong RequireAnyPermission, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}

		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok {
			return
		}

		hasAny, err := mf.checker.UserHasAnyPermission(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.Error("Lỗi kiểm tra quyền (UserHasAnyPermission)",
				"error", err, "user_id", userID, "tenant_id", tenantID)
			AbortWithPermissionCheckError(c, err)
			return
		}
		if !hasAny {
			mf.logger.Warn("Từ chối quyền truy cập (UserHasAnyPermission)",
				"user_id", userID, "tenant_id", tenantID, "required_permissions", fmt.Sprintf("%v", permissions), "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, fmt.Sprintf("any of: %v", permissions))
			return
		}
		c.Next()
	}
}

// RequireAllPermissions tạo middleware kiểm tra người dùng có tất cả các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAllPermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(permissions) == 0 {
			mf.logger.Info("Không có quyền nào được yêu cầu trong RequireAllPermissions, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}

		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok {
			return
		}

		hasAll, err := mf.checker.UserHasAllPermissions(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.Error("Lỗi kiểm tra quyền (UserHasAllPermissions)",
				"error", err, "user_id", userID, "tenant_id", tenantID)
			AbortWithPermissionCheckError(c, err)
			return
		}
		if !hasAll {
			mf.logger.Warn("Từ chối quyền truy cập (UserHasAllPermissions)",
				"user_id", userID, "tenant_id", tenantID, "required_permissions", fmt.Sprintf("%v", permissions), "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, fmt.Sprintf("all of: %v", permissions))
			return
		}
		c.Next()
	}
}

// extractAuthContext là helper method để lấy UserID và TenantID từ Gin context.
func (mf *MiddlewareFactory) extractAuthContext(c *gin.Context) (userID uint, tenantID uint, ok bool) {
	userIDPointer := auth.GetUserID(c)
	if userIDPointer == nil {
		mf.logger.Warn("Không tìm thấy UserID trong context",
			"path", c.Request.URL.Path)
		AbortWithAuthRequired(c)
		return 0, 0, false
	}

	tID := auth.GetTenantID(c)
	if tID == 0 {
		mf.logger.Warn("Không tìm thấy TenantID trong context",
			"path", c.Request.URL.Path)
		AbortWithTenantRequired(c)
		return 0, 0, false
	}

	return *userIDPointer, tID, true
}
