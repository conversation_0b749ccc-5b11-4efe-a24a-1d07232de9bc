// internal/pkg/permission/constants.go
package permission

import "fmt"

const (
	// Standard Actions
	ActionCreate = "create"
	ActionRead   = "read"
	ActionUpdate = "update"
	ActionDelete = "delete"
	ActionList   = "list"
	ActionManage = "manage"

	// PermissionSeparator là ký tự phân tách các phần trong chuỗi permission
	PermissionSeparator = "."

	// Error Codes
	ErrorCodeAuthRequired     = "AUTHENTICATION_REQUIRED"
	ErrorCodeTenantRequired   = "TENANT_REQUIRED"
	ErrorCodePermissionDenied = "PERMISSION_DENIED"
	ErrorCodePermissionCheck  = "PERMISSION_CHECK_ERROR"
)

// PermissionBuilder là một helper struct để xây dựng chuỗi permission một cách an toàn và nhất quán
type PermissionBuilder struct {
	module   string
	action   string
	resource string // Optional
}

// NewPermissionBuilder khởi tạo một PermissionBuilder với tên module
func NewPermissionBuilder(module string) *PermissionBuilder {
	return &PermissionBuilder{module: module}
}

// Action thiết lập action cho permission
func (pb *PermissionBuilder) Action(action string) *PermissionBuilder {
	pb.action = action
	return pb
}

// Resource thiết lập resource (tùy chọn) cho permission
func (pb *PermissionBuilder) Resource(resource string) *PermissionBuilder {
	pb.resource = resource
	return pb
}

// Build tạo ra chuỗi permission hoàn chỉnh
func (pb *PermissionBuilder) Build() string {
	if pb.module == "" || pb.action == "" {
		return ""
	}
	if pb.resource != "" {
		return fmt.Sprintf("%s%s%s%s%s", pb.module, PermissionSeparator, pb.action, PermissionSeparator, pb.resource)
	}
	return fmt.Sprintf("%s%s%s", pb.module, PermissionSeparator, pb.action)
}
