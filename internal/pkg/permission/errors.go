// internal/pkg/permission/errors.go
package permission

import (
	"wnapi/internal/pkg/errors"
	"wnapi/internal/pkg/response"

	"github.com/gin-gonic/gin"
)

// Domain error types định nghĩa các lỗi domain-specific
var (
	ErrAuthRequired     = errors.New(errors.ErrorCode(ErrorCodeAuthRequired), "Yêu cầu xác thực", nil)
	ErrTenantRequired   = errors.New(errors.ErrorCode(ErrorCodeTenantRequired), "Không tìm thấy thông tin tenant hoặc tenant không hợp lệ", nil)
	ErrPermissionDenied = errors.New(errors.ErrorCode(ErrorCodePermissionDenied), "Không có quyền truy cập", nil)
	ErrPermissionCheck  = errors.New(errors.ErrorCode(ErrorCodePermissionCheck), "Lỗi hệ thống khi kiểm tra quyền truy cập", nil)
)

// AbortWithAuthRequired dừng request và trả về lỗi 401 yêu cầu xác thực
func AbortWithAuthRequired(c *gin.Context) {
	response.Unauthorized(c, "Yêu cầu xác thực")
	c.Abort()
}

// AbortWithTenantRequired dừng request và trả về lỗi yêu cầu tenant
func AbortWithTenantRequired(c *gin.Context) {
	response.BadRequest(c, "Không tìm thấy thông tin tenant hoặc tenant không hợp lệ", ErrorCodeTenantRequired, nil)
	c.Abort()
}

// AbortWithPermissionDenied dừng request và trả về lỗi 403 từ chối quyền truy cập
func AbortWithPermissionDenied(c *gin.Context, permission string) {
	// Tạo thông tin chi tiết về permission bị từ chối
	details := []response.Detail{
		{
			Field:   "required_permission",
			Message: permission,
		},
	}

	response.ErrorWithDetails(c, 403, "Không có quyền truy cập", ErrorCodePermissionDenied, details)
	c.Abort()
}

// AbortWithPermissionCheckError dừng request và trả về lỗi 500 do lỗi hệ thống khi kiểm tra quyền
func AbortWithPermissionCheckError(c *gin.Context, err error) {
	// Tạo thông tin chi tiết về lỗi hệ thống
	details := []response.Detail{
		{
			Message: err.Error(),
		},
	}

	response.ErrorWithDetails(c, 500, "Lỗi hệ thống khi kiểm tra quyền truy cập", ErrorCodePermissionCheck, details)
	c.Abort()
}
