package errors

import (
	"context"
	"net/http"
)

type contextKey string

const (
	errorContextKey contextKey = "api_error"
)

// WithError attaches an API error to a context
func WithError(ctx context.Context, err *APIError) context.Context {
	return context.WithValue(ctx, errorContextKey, err)
}

// GetErrorFromContext retrieves an API error from a context
func GetErrorFromContext(ctx context.Context) (*APIError, bool) {
	err, ok := ctx.Value(errorContextKey).(*APIError)
	return err, ok
}

// NewResponseWriter creates a new response writer wrapper
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func newResponseWriter(w http.ResponseWriter) *responseWriter {
	return &responseWriter{w, http.StatusOK}
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}
