package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode represents a unique error code
type ErrorCode string

// HTTP error codes
const (
	// Authentication errors
	AuthenticationRequired ErrorCode = "AUTHENTICATION_REQUIRED"
	InvalidCredentials     ErrorCode = "INVALID_CREDENTIALS"

	// Authorization errors
	AccessDenied ErrorCode = "ACCESS_DENIED"

	// Resource errors
	ResourceNotFound ErrorCode = "RESOURCE_NOT_FOUND"

	// Validation errors
	ValidationError ErrorCode = "VALIDATION_ERROR"

	// Rate limiting
	RateLimitExceeded ErrorCode = "RATE_LIMIT_EXCEEDED"

	// Server errors
	InternalServerError ErrorCode = "INTERNAL_SERVER_ERROR"
	ServiceUnavailable  ErrorCode = "SERVICE_UNAVAILABLE"

	// Business logic errors
	DuplicateEntry    ErrorCode = "DUPLICATE_ENTRY"
	InsufficientFunds ErrorCode = "INSUFFICIENT_FUNDS"
	AccountLocked     ErrorCode = "ACCOUNT_LOCKED"
	ExpiredResource   ErrorCode = "EXPIRED_RESOURCE"
	DependencyFailed  ErrorCode = "DEPENDENCY_FAILED"
)

// HTTPStatusMap maps error codes to HTTP status codes
var HTTPStatusMap = map[ErrorCode]int{
	AuthenticationRequired: http.StatusUnauthorized,
	InvalidCredentials:     http.StatusUnauthorized,
	AccessDenied:           http.StatusForbidden,
	ResourceNotFound:       http.StatusNotFound,
	ValidationError:        http.StatusUnprocessableEntity,
	RateLimitExceeded:      http.StatusTooManyRequests,
	InternalServerError:    http.StatusInternalServerError,
	ServiceUnavailable:     http.StatusServiceUnavailable,
	DuplicateEntry:         http.StatusConflict,
	InsufficientFunds:      http.StatusUnprocessableEntity,
	AccountLocked:          http.StatusForbidden,
	ExpiredResource:        http.StatusGone,
	DependencyFailed:       http.StatusFailedDependency,
}

// APIError represents an API error response
type APIError struct {
	Code    ErrorCode   `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
	err     error       // Internal error (not exposed in JSON)
}

// Error returns the error message
func (e *APIError) Error() string {
	if e.err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.err)
	}
	return e.Message
}

// New creates a new API error
func New(code ErrorCode, message string, err error) *APIError {
	return &APIError{
		Code:    code,
		Message: message,
		err:     err,
	}
}

// NewWithDetails creates a new API error with details
func NewWithDetails(code ErrorCode, message string, details interface{}, err error) *APIError {
	return &APIError{
		Code:    code,
		Message: message,
		Details: details,
		err:     err,
	}
}

// GetHTTPStatus returns the corresponding HTTP status code for an error
func GetHTTPStatus(err *APIError) int {
	if status, ok := HTTPStatusMap[err.Code]; ok {
		return status
	}
	return http.StatusInternalServerError
}

// ValidationErrorDetails represents field validation errors
type ValidationErrorDetails struct {
	Fields map[string][]string `json:"fields"`
}

// NewValidationError creates a new validation error with field details
func NewValidationError(fieldErrors map[string][]string, err error) *APIError {
	details := ValidationErrorDetails{
		Fields: fieldErrors,
	}

	return &APIError{
		Code:    ValidationError,
		Message: "The request data failed validation",
		Details: details,
		err:     err,
	}
}
