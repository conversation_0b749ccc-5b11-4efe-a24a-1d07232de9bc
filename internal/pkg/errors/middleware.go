package errors

import (
	"encoding/json"
	"log"
	"net/http"
)

// ErrorHandlerMiddleware is middleware that handles API errors
func ErrorHandlerMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Create a response writer that can capture the response
		rw := newResponseWriter(w)

		// Call the next handler
		next.ServeHTTP(rw, r)

		// If there was an error stored in the context, handle it
		if err, ok := GetErrorFromContext(r.Context()); ok {
			handleAPIError(rw, err)
		}
	})
}

// handleAPIError writes an API error response
func handleAPIError(w http.ResponseWriter, err *APIError) {
	status := GetHTTPStatus(err)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	// Log the error
	log.Printf("API Error: %s (%d): %v", err.Code, status, err.Error())

	// Write the error response
	json.NewEncoder(w).Encode(err)
}

// WriteError writes an error response directly
func WriteError(w http.ResponseWriter, err *APIError) {
	status := GetHTTPStatus(err)

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	// Write the error response
	json.NewEncoder(w).Encode(err)
}
