package errors

import (
	"fmt"
	"net/http"
)

// AppError là cấu trúc lỗi chung cho toàn hệ thống
type AppError struct {
	Code       ErrorCode         `json:"code"`
	Message    string            `json:"message"`
	Details    string            `json:"details,omitempty"`
	Fields     map[string]string `json:"fields,omitempty"` // Cho lỗi validation
	HTTPStatus int               `json:"-"`
	Internal   error             `json:"-"`
	UserID     *int64            `json:"-"`
	TenantID   *int64            `json:"-"`
	RequestID  string            `json:"-"`
}

// Error trả về chuỗi mô tả lỗi
func (e *AppError) Error() string {
	return fmt.Sprintf("Mã lỗi: %s, Thông báo: %s", e.Code, e.Message)
}

// NewAppError tạo một lỗi mới với mã lỗi và thông báo
func NewAppError(code ErrorCode, message string, httpStatus int) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
	}
}

// NewFromMap tạo một lỗi mới từ bản đồ thông báo theo ngôn ngữ
func NewFromMap(code ErrorCode, messages map[string]string, lang string, httpStatus int) *AppError {
	message := string(code)
	if msg, ok := messages[lang]; ok {
		message = msg
	} else if msg, ok := messages["en"]; ok {
		message = msg
	}

	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
	}
}

// NewAppErrorWithDetails tạo một lỗi mới với mã lỗi và chi tiết
func NewAppErrorWithDetails(code ErrorCode, message string, details string, httpStatus int) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		Details:    details,
		HTTPStatus: httpStatus,
	}
}

// NewAppValidationError tạo một lỗi validation với các trường không hợp lệ
func NewAppValidationError(code ErrorCode, message string, fields map[string]string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		Fields:     fields,
		HTTPStatus: http.StatusBadRequest,
	}
}

// WrapAppError tạo một lỗi mới từ một lỗi có sẵn
func WrapAppError(code ErrorCode, message string, err error, httpStatus int) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
		Internal:   err,
	}
}
