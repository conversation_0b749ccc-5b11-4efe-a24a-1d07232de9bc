package errors

import (
	"net/http"
)

// GetDefaultHTTPStatus chuyển đổi mã lỗi thành mã HTTP tương ứng
func GetDefaultHTTPStatus(code ErrorCode) int {
	switch code {
	// Lỗi xác thực và phân quyền
	case ErrCodeUnauthorized:
		return http.StatusUnauthorized
	case ErrCodeForbidden:
		return http.StatusForbidden

	// Lỗi tài nguyên
	case ErrCodeNotFound:
		return http.StatusNotFound
	case ErrCodeConflict:
		return http.StatusConflict

	// Lỗi đầu vào
	case ErrCodeValidationFailed, ErrCodeBadRequest:
		return http.StatusBadRequest

	// Lỗi tốc độ
	case ErrCodeTooManyRequests:
		return http.StatusTooManyRequests

	// Lỗi thời gian
	case ErrCodeTimeout:
		return http.StatusRequestTimeout

	// Lỗi hệ thống
	case ErrCodeInternalServer:
		return http.StatusInternalServerError
	case ErrCodeExternalServiceUnavailable:
		return http.StatusServiceUnavailable

	// Mặc định
	default:
		return http.StatusInternalServerError
	}
}

// NewCommonError tạo một lỗi chung với mã lỗi và ngôn ngữ
func NewCommonError(code ErrorCode, lang string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetCommonMessage(code, lang),
		HTTPStatus: GetDefaultHTTPStatus(code),
	}
}

// NewCommonErrorWithDetails tạo một lỗi chung với mã lỗi, ngôn ngữ và chi tiết
func NewCommonErrorWithDetails(code ErrorCode, lang string, details string) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetCommonMessage(code, lang),
		Details:    details,
		HTTPStatus: GetDefaultHTTPStatus(code),
	}
}

// WrapCommonError tạo một lỗi chung từ một lỗi có sẵn
func WrapCommonError(code ErrorCode, lang string, err error) *AppError {
	return &AppError{
		Code:       code,
		Message:    GetCommonMessage(code, lang),
		HTTPStatus: GetDefaultHTTPStatus(code),
		Internal:   err,
	}
}

// NewCommonValidationError tạo một lỗi validation với các trường không hợp lệ
func NewCommonValidationError(lang string, fields map[string]string) *AppError {
	return &AppError{
		Code:       ErrCodeValidationFailed,
		Message:    GetCommonMessage(ErrCodeValidationFailed, lang),
		Fields:     fields,
		HTTPStatus: http.StatusBadRequest,
	}
}
