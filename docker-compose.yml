version: '3.8'

services:
  # API service
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: wnapi
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./projects:/app/projects
    depends_on:
      - mysql
    networks:
      - wnapi-network
    command: ["--project=sample"]

  # MySQL database (updated version)
  mysql:
    image: mysql:8.0
    container_name: wn-mysql-v2
    restart: always
    profiles: ["core"]
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: blog_v4
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./infrastructure/docker/mysql/mysql-custom.cnf:/etc/mysql/conf.d/custom.cnf
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - wnapi-network

  # Minio - S3 compatible storage
  minio:
    image: minio/minio:latest
    container_name: wn-minio
    restart: always
    profiles: ["core"]
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: minio123
    ports:
      - "9071:9000"
      - "9072:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - wnapi-network

  # Redis
  redis:
    image: redis:7.0-alpine
    container_name: wn-redis
    restart: always
    profiles: ["core"]
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 10
    networks:
      - wnapi-network

  # Traefik - API Gateway
  traefik:
    image: traefik:v2.10
    container_name: wn-traefik
    profiles: ["traefik", "core"]
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - ./infrastructure/traefik/traefik.yaml:/etc/traefik/traefik.yaml
      - ./infrastructure/traefik/providers:/etc/traefik/providers
      - ./infrastructure/traefik/logs:/var/log/traefik
    networks:
      - wnapi-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.wn-api.local`)"
      - "traefik.http.routers.dashboard.service=api@internal"
      - "traefik.http.routers.dashboard.entrypoints=web"

  # MailCatcher
  mailcatcher:
    image: schickling/mailcatcher
    container_name: wn-mailcatcher
    restart: always
    profiles: ["core"]
    ports:
      - "1025:1025"
      - "1080:1080"
    networks:
      - wnapi-network

  # Jaeger
  jaeger:
    image: jaegertracing/all-in-one:1.46
    container_name: wn-jaeger
    restart: always
    profiles: ["core"]
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
      - COLLECTOR_OTLP_ENABLED=true
    ports:
      - "5775:5775/udp"
      - "6831:6831/udp"
      - "6832:6832/udp"
      - "5778:5778"
      - "16686:16686"
      - "14250:14250"
      - "14268:14268"
      - "14269:14269"
      - "9411:9411"
    networks:
      - wnapi-network

volumes:
  mysql-data:
    driver: local
  minio_data:
    driver: local
  redis_data:
    driver: local

networks:
  wnapi-network:
    driver: bridge