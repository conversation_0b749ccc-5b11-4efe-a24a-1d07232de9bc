.PHONY: build run dev clean migrate migrate-create test test-coverage create-module create-plugin docker-build docker-up docker-down docker-logs plan docs

# Biến môi trường
APP_NAME=wnapi
BUILD_DIR=./build
MAIN_FILE=./cmd/server/main.go
PROJECT ?= default
DOCKER_IMAGE_NAME=wnapi
DOCKER_TAG=latest
COVER_PROFILE=coverage.out
COVER_HTML=coverage.html

# Build ứng dụng
build:
	@echo "Building application..."
	@mkdir -p $(BUILD_DIR)
	@go build -o $(BUILD_DIR)/$(APP_NAME) $(MAIN_FILE)

# Chạy ứng dụng
run:
	@echo "Running application..."
ifdef PROJECT
	@go run $(MAIN_FILE) --project=$(PROJECT)
else
	@go run $(MAIN_FILE)
endif

# Chạy ứng dụng với debugger (dev mode)
dev:
	@echo "Building application for debugging..."
	@mkdir -p ./tmp
	@go build -gcflags="all=-N -l" -o ./tmp/main $(MAIN_FILE)
	@echo "Starting application with debugger..."
	@dlv exec --accept-multiclient --log --headless --continue --listen :49202 --api-version 2 ./tmp/main

# Dọn dẹp build
clean:
	@echo "Cleaning build..."
	@rm -rf $(BUILD_DIR)
	@rm -f $(COVER_PROFILE) $(COVER_HTML)

# Tải dependencies
deps:
	@echo "Downloading dependencies..."
	@go mod download

# Tạo thư mục build nếu chưa tồn tại
init:
	@echo "Initializing project..."
	@mkdir -p $(BUILD_DIR)
	@mkdir -p logs
	@mkdir -p projects

# Format code
fmt:
	@echo "Formatting code..."
	@go fmt ./...

# Chạy tests
test:
	@echo "Running tests..."
	@go test -v ./...

# Chạy test với coverage
test-coverage:
	@echo "Running tests with coverage..."
	@go test -v -coverprofile=$(COVER_PROFILE) ./...
	@go tool cover -html=$(COVER_PROFILE) -o $(COVER_HTML)
	@echo "Coverage report generated at $(COVER_HTML)"
	@go tool cover -func=$(COVER_PROFILE)

# Chạy migrate database
migrate: migrate-up

migrate-up:
	@echo "Running database migrations for project: $(PROJECT)"
	@./scripts/migrate.sh $(PROJECT) up

migrate-down:
	@echo "Rolling back migrations for project: $(PROJECT)"
	@./scripts/migrate.sh $(PROJECT) down

migrate-version:
	@echo "Checking migration version for project: $(PROJECT)"
	@./scripts/migrate.sh $(PROJECT) version

create-migration:
	@if [ -z "$(MODULE)" ] || [ -z "$(NAME)" ]; then \
		echo "Usage: make create-migration MODULE=<module-name> NAME=<migration-name>"; \
		exit 1; \
	fi
	@echo "Creating migration for module: $(MODULE)"
	@./scripts/create-migration.sh $(MODULE) $(NAME)

# Tạo module mới
create-module:
	@if [ -z "$(name)" ]; then \
		echo "Usage: make create-module name=<module-name>"; \
		exit 1; \
	fi
	@echo "Creating new module: $(name)"
	@mkdir -p modules/$(name)
	@mkdir -p modules/$(name)/api
	@mkdir -p modules/$(name)/domain
	@mkdir -p modules/$(name)/dto
	@mkdir -p modules/$(name)/repository
	@mkdir -p modules/$(name)/migrations
	@mkdir -p modules/$(name)/internal
	@cat > modules/$(name)/module.go <<-'EOF'
	package $(name)

	import (
		"context"
		"wnapi/internal/core"
	)

	func init() {
		// Đăng ký module với global registry
		core.RegisterModuleFactory("$(name)", NewModule)
	}

	// Module triển khai $(name) module
	type Module struct {
		name   string
		app    *core.App
		config map[string]interface{}
	}

	// NewModule tạo module mới
	func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
		return &Module{
			name:   "$(name)",
			app:    app,
			config: config,
		}, nil
	}

	// Name trả về tên của module
	func (m *Module) Name() string {
		return m.name
	}

	// Init khởi tạo module
	func (m *Module) Init(ctx context.Context) error {
		log := m.app.GetLogger()
		log.Info("Initializing $(name) module")

		// TODO: Khởi tạo repository, service, etc.

		return nil
	}

	// RegisterRoutes đăng ký các routes của module
	func (m *Module) RegisterRoutes(router *core.Server) error {
		log := m.app.GetLogger()
		log.Info("Registering $(name) module routes")

		// TODO: Đăng ký routes

		return nil
	}

	// Cleanup dọn dẹp tài nguyên của module
	func (m *Module) Cleanup(ctx context.Context) error {
		log := m.app.GetLogger()
		log.Info("Cleaning up $(name) module")

		// TODO: Dọn dẹp tài nguyên

		return nil
	}

	// GetMigrationPath trả về đường dẫn chứa migrations của module
	func (m *Module) GetMigrationPath() string {
		return "modules/$(name)/migrations"
	}
	EOF
	@echo "Module $(name) created successfully!"

# Tạo plugin mới
create-plugin:
	@if [ -z "$(name)" ]; then \
		echo "Usage: make create-plugin name=<plugin-name>"; \
		exit 1; \
	fi
	@echo "Creating new plugin: $(name)"
	@mkdir -p plugins/$(name)
	@cat > plugins/$(name)/plugin.go <<-'EOF'
	package $(name)

	import (
		"context"
		"wnapi/plugins"
	)

	func init() {
		// Đăng ký plugin factory
		plugins.RegisterPluginFactory("$(name)", NewPlugin)
	}

	// Plugin triển khai plugin $(name)
	type Plugin struct {
		name   string
		config map[string]interface{}
	}

	// NewPlugin tạo một plugin mới
	func NewPlugin(config map[string]interface{}) (plugins.Plugin, error) {
		plugin := &Plugin{
			name:   "$(name)",
			config: config,
		}

		return plugin, nil
	}

	// Name trả về tên của plugin
	func (p *Plugin) Name() string {
		return p.name
	}

	// Init khởi tạo plugin
	func (p *Plugin) Init(ctx context.Context) error {
		// TODO: Khởi tạo plugin
		return nil
	}

	// Shutdown dọn dẹp tài nguyên của plugin
	func (p *Plugin) Shutdown(ctx context.Context) error {
		// TODO: Dọn dẹp tài nguyên
		return nil
	}
	EOF
	@echo "Plugin $(name) created successfully!"

# Docker commands
docker-build:
	@echo "Building Docker image..."
	@docker build -t $(DOCKER_IMAGE_NAME):$(DOCKER_TAG) .

docker-up:
	@echo "Starting Docker containers..."
	@docker-compose up -d

docker-down:
	@echo "Stopping Docker containers..."
	@docker-compose down

docker-logs:
	@echo "Showing Docker logs..."
	@docker-compose logs -f

# Tạo file kế hoạch cho module mới
plan:
	@if [ -z "$(name)" ]; then \
		echo "Usage: make plan name=<module-name>"; \
		exit 1; \
	fi
	@mkdir -p docs/plans
	@if [ ! -f "docs/plans/$(name).md" ]; then \
		cp docs/plan-sample-module.md docs/plans/$(name).md; \
		echo "Created plan file: docs/plans/$(name).md"; \
	else \
		echo "Plan file already exists: docs/plans/$(name).md"; \
	fi

# Tạo tài liệu mẫu
docs:
	@mkdir -p docs
	@if [ ! -f "docs/plan-sample-module.md" ]; then \
		echo "Creating sample documentation..."; \
		mkdir -p docs/plans; \
		cat > docs/plan-sample-module.md <<-'EOF'\
		# Module Plan: [Module Name]

		## Tổng quan
		Mô tả ngắn gọn về mục đích và chức năng chính của module.

		## Yêu cầu chức năng
		- [ ] Chức năng 1
		- [ ] Chức năng 2
		- [ ] Chức năng 3

		## Công nghệ sử dụng
		- Công nghệ 1
		- Công nghệ 2

		## Cấu trúc thư mục
		```
		modules/[module-name]/
		├── api/             # API handlers
		├── domain/          # Business logic
		├── dto/             # Data transfer objects
		├── repository/      # Database operations
		├── migrations/      # Database migrations
		└── internal/        # Internal package code
		```

		## API Endpoints
		| Method | Path | Description |
		|--------|------|-------------|
		| GET    | /api/... | Get resource |
		| POST   | /api/... | Create resource |

		## Cấu hình
		```yaml
		modules:
		  [module-name]:
		    enabled: true
		    # Các thông số cấu hình khác
		```

		## Phụ thuộc
		- Module/Service 1
		- Module/Service 2

		## Tài liệu tham khảo
		- [Tài liệu 1](link)
		- [Tài liệu 2](link)
		EOF
		echo "Sample documentation created at: docs/plan-sample-module.md"; \
	else \
		echo "Sample documentation already exists at: docs/plan-sample-module.md"; \
	fi