# Hướng Dẫn Đóng Góp

Cảm ơn bạn đã quan tâm đến việc đóng góp cho WNAPI! Dưới đây là một số hướng dẫn để giúp quá trình đóng góp diễn ra suôn sẻ.

## Quy Trình Đóng Góp

1. Fork dự án này
2. Tạo một nhánh tính năng (`git checkout -b feature/amazing-feature`)
3. Commit các thay đổi của bạn (`git commit -m 'Add some amazing feature'`)
4. Push nhánh lên repository của bạn (`git push origin feature/amazing-feature`)
5. Mở Pull Request

## Tiêu Chuẩn Coding

### Go Styleguide

- Sử dụng `gofmt` để định dạng code
- Tu<PERSON> thủ [Effective Go](https://golang.org/doc/effective_go.html)
- Viết tests cho code mới
- Đạt coverage tối thiểu 70%

### Commit Messages

- Sử dụng thì hiện tại: "Add feature" không phải "Added feature"
- Dòng đầu tiên là tóm tắt dưới 50 ký tự
- Mô tả chi tiết sau dòng trống nếu cần

Ví dụ:
```
Add user authentication module

- Implement JWT token generation
- Add password hashing with bcrypt
- Create middleware for route protection
```

## Báo Cáo Lỗi

Khi báo cáo lỗi, vui lòng bao gồm:

- Mô tả rõ ràng về lỗi
- Các bước để tái hiện lỗi
- Kết quả mong đợi và kết quả thực tế
- Môi trường: hệ điều hành, phiên bản Go, etc.

## Tính Năng Mới

Nếu bạn muốn đề xuất một tính năng mới:

1. Kiểm tra issues để xem đã có ai đề xuất tính năng tương tự chưa
2. Mở một issue mới mô tả tính năng, lý do cần tính năng này, và cách triển khai (nếu có)
3. Chờ phản hồi từ maintainers

## Cấu Trúc Dự Án

Khi thêm mới code, hãy đảm bảo tuân thủ cấu trúc dự án:

- Module mới nên được đặt trong thư mục `modules/`
- Plugin mới nên được đặt trong thư mục `plugins/`
- Code chung nên được đặt trong thư mục `internal/`
- Tài liệu nên được cập nhật trong thư mục `docs/`

## Quy Trình Review

- Mỗi Pull Request cần ít nhất 1 lượt review từ maintainer
- CI phải pass (tests, linting)
- Các issues liên quan cần được đề cập trong PR

## Giấy Phép

Bằng cách đóng góp, bạn đồng ý rằng đóng góp của bạn sẽ được cấp phép theo giấy phép MIT của dự án.

Cảm ơn sự đóng góp của bạn! 