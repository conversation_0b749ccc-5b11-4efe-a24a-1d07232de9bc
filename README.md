# WNAPI - Framework Golang Modular Microservices

Framework Golang mô-đun hóa với khả năng mở rộng qua plugin, dễ dàng tùy chỉnh theo từng dự án cụ thể.

![Version](https://img.shields.io/badge/version-1.0.0-blue)
![Go Version](https://img.shields.io/badge/go-1.21+-blue)
![License](https://img.shields.io/badge/license-MIT-green)

## Tính Năng Chính

- **Mô-đun hóa cao**: Mỗi chức năng được tách thành module độc lập
- **<PERSON>h hoạt theo dự án**: Dễ dàng tạo nhiều dự án khác nhau từ cùng một codebase
- **Khả năng mở rộng**: <PERSON><PERSON> thống plugin cho phép thêm chức năng mới mà không cần sửa đổi code core
- **<PERSON>ễ dàng phát triển**: <PERSON><PERSON><PERSON> tr<PERSON><PERSON>à<PERSON>, tách biệt các tầng (API, domain, repository)
- **Docker & DevOps**: Hỗ trợ đầy đủ Docker, CI/CD và các công cụ DevOps

## Yêu Cầu Hệ Thống

- Go 1.19+
- MySQL 8.0+ (hoặc MariaDB)
- Docker & Docker Compose (tùy chọn)

## Cài Đặt

### Sử Dụng Script Cài Đặt

Cách đơn giản nhất để bắt đầu:

```bash
# Clone repository
git clone https://github.com/yourusername/wnapi.git
cd wnapi

# Chạy script cài đặt
./scripts/setup.sh
```

### Cài Đặt Thủ Công

```bash
# Clone repository
git clone https://github.com/yourusername/wnapi.git
cd wnapi

# Tải dependencies
go mod download

# Tạo thư mục cần thiết
mkdir -p build logs projects
```

## Chạy Ứng Dụng

### Sử Dụng Makefile

```bash
# Chạy ứng dụng mặc định
make run

# Chạy với project cụ thể
make run PROJECT=sample

# Build ứng dụng
make build
```

### Database Migrations

Framework hỗ trợ migration database với cấu trúc module, phân tách các migration theo từng module.

```bash
# Chạy migration cho project mặc định
make migrate

# Chạy migration cho project cụ thể
make migrate PROJECT=sample

# Rollback migration
make migrate-down PROJECT=sample

# Xem version migration hiện tại
make migrate-version PROJECT=sample

# Tạo migration mới cho module auth
make create-migration MODULE=auth NAME=add_roles
```

Cấu trúc migrations:
```
migrations/                      # Chứa migrations hệ thống
    ├── 000001_init_schema.up.sql
    └── 000001_init_schema.down.sql

modules/                         # Mỗi module có migration riêng
    ├── auth/
    │   └── migrations/          # Migrations của module auth
    │       ├── 000001_create_users_table.up.sql
    │       └── 000001_create_users_table.down.sql
    └── hello/
        └── migrations/          # Migrations của module hello
```

Thứ tự migration:
1. Các migration trong `/migrations/` (system) sẽ được chạy trước tiên
2. Các migration trong mỗi module sẽ được chạy theo thứ tự ưu tiên của module

### Sử Dụng Docker

```bash
# Build image Docker
make docker-build

# Chạy ứng dụng với Docker Compose
make docker-up

# Xem logs
make docker-logs

# Dừng ứng dụng
make docker-down
```

## Cấu Trúc Dự Án

```
wnapi/
├── cmd/                   # Entry points
│   ├── migrate/           # Migration tool
│   └── server/            # API server
├── config/                # Cấu hình mặc định
├── docs/                  # Tài liệu
├── internal/              # Code nội bộ, không xuất ra ngoài
│   ├── core/              # Core framework
│   ├── database/          # Kết nối database
│   ├── middleware/        # Middlewares
│   └── pkg/               # Packages dùng nội bộ
├── logs/                  # Log files
├── migrations/            # Database migrations
├── modules/               # Các module chức năng
│   ├── auth/              # Auth module
│   │   ├── api/           # API layer
│   │   ├── domain/        # Business logic
│   │   ├── dto/           # Data Transfer Objects
│   │   ├── migrations/    # Module-specific migrations
│   │   └── repository/    # Data access
│   └── hello/             # Hello World module
├── plugins/               # Plugins mở rộng
│   └── logger/            # Logger plugin
├── projects/              # Cấu hình theo dự án
│   ├── sample/            # Sample project
│   └── sample2/           # Another sample project
├── scripts/               # Scripts hỗ trợ
├── tests/                 # Integration tests
├── .github/               # GitHub workflows (CI/CD)
├── docker-compose.yml     # Docker Compose config
├── docker-compose.prod.yml # Production Docker config
├── Dockerfile             # Docker build file
├── go.mod                 # Go modules
├── go.sum                 # Go modules checksums
└── Makefile               # Build/deployment commands
```

## Phát Triển

### Tạo Module Mới

```bash
# Sử dụng lệnh Makefile
make create-module name=my_module

# Cấu trúc module mới
modules/my_module/
├── api/
├── domain/
├── dto/
├── migrations/
├── repository/
└── module.go
```

### Tạo và Quản lý Migrations

Mỗi module có thể có migrations riêng. Để tạo và quản lý migrations:

```bash
# Tạo migration mới cho module
make create-migration MODULE=my_module NAME=create_table

# Migrations sẽ được tạo tại
migrations/modules/my_module/TIMESTAMP_create_table.up.sql
migrations/modules/my_module/TIMESTAMP_create_table.down.sql
```

Trong file module.go, cần triển khai các phương thức migration:

```go
// GetMigrationPath trả về đường dẫn chứa migrations
func (m *Module) GetMigrationPath() string {
    return filepath.Join("modules", "my_module", "migrations")
}

// GetMigrationOrder trả về thứ tự ưu tiên khi chạy migration
func (m *Module) GetMigrationOrder() int {
    return 5 // Số thấp hơn sẽ chạy trước
}
```

### Tạo Plugin Mới

```bash
# Sử dụng lệnh Makefile
make create-plugin name=my_plugin

# Cấu trúc plugin mới
plugins/my_plugin/
└── plugin.go
```

### Tạo Project Mới

Tạo cấu hình dự án mới trong thư mục `projects/`:

```bash
mkdir -p projects/my_project
cp projects/sample/config.yaml projects/my_project/
cp projects/sample/modules.yaml projects/my_project/
```

Sau đó chỉnh sửa cấu hình trong `config.yaml` để định nghĩa các module và plugin sẽ sử dụng.

## DevOps

### Continuous Integration

Dự án có sẵn cấu hình GitHub Actions trong `.github/workflows/ci.yml` để:
- Chạy unit tests
- Build ứng dụng
- Tạo Docker image

### Docker

Sử dụng các lệnh Docker:

```bash
# Dọn dẹp Docker
./scripts/docker-cleanup.sh

# Build Docker image
docker build -t wnapi:latest .

# Chạy với Docker Compose
docker-compose up -d
```

## Tài Liệu

Xem các tài liệu chi tiết tại:

- [Tổng quan kiến trúc](docs/architecture.md)
- [Kế hoạch phát triển](docs/plan.md)
- [Hướng dẫn phát triển](docs/development.md)
- [Hướng dẫn sử dụng Migration](docs/migration.md)

## Đóng Góp

Vui lòng xem [CONTRIBUTING.md](CONTRIBUTING.md) để biết thêm thông tin về cách đóng góp cho dự án.

## Giấy Phép

Mã nguồn của dự án được phân phối theo giấy phép MIT. Xem [LICENSE](LICENSE) để biết thêm chi tiết.

## Liên Hệ

Nếu có bất kỳ câu hỏi hoặc đóng góp nào, vui lòng liên hệ với chúng tôi qua [GitHub Issues](https://github.com/yourusername/wnapi/issues). 