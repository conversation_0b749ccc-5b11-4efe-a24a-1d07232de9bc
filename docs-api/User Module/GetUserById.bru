meta {
  name: GetUserById
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/v1/users/{{user_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Get User By ID"
  desc: "Retrieve a specific user by ID (admin or owner)"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Thành công",
      "success": true,
      "path": "/api/v1/users/1",
      "timestamp": "2025-06-01T10:35:00+07:00"
    },
    "data": {
      "id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "full_name": "<PERSON>",
      "status": "active",
      "role": "admin",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-05-20T14:30:00Z",
      "last_login": "2025-05-30T15:45:00Z",
      "is_email_verified": true,
      "profile": {
        "avatar_url": "https://example.com/avatars/john.jpg",
        "phone_number": "+84123456789",
        "address": "123 Example Street",
        "city": "Ho Chi Minh",
        "country": "Vietnam",
        "bio": "Software developer with 10+ years experience"
      }
    }
  }
}
