meta {
  name: GetAllUsers
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/v1/users
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

query {
  page: 1
  limit: 20
  status: active
  role: 
  search: 
  sort: created_at_desc
}

docs {
  title: "Get All Users"
  desc: "Retrieve a list of users with pagination and filtering (admin only)"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Thành công",
      "success": true,
      "path": "/api/v1/users",
      "timestamp": "2025-06-01T10:30:00+07:00"
    },
    "data": {
      "users": [
        {
          "id": 1,
          "username": "john_doe",
          "email": "<EMAIL>",
          "full_name": "<PERSON>",
          "status": "active",
          "role": "admin",
          "created_at": "2025-01-01T00:00:00Z",
          "last_login": "2025-05-30T15:45:00Z"
        }
      ],
      "pagination": {
        "total": 50,
        "page": 1,
        "limit": 20,
        "pages": 3
      }
    }
  }
}
