meta {
  name: UpdateUser
  type: http
  seq: 3
}

put {
  url: {{api_url}}/api/v1/users/{{user_id}}
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "full_name": "John Updated Doe",
    "status": "active",
    "role": "admin",
    "profile": {
      "avatar_url": "https://example.com/avatars/john_updated.jpg",
      "phone_number": "+84987654321",
      "address": "456 New Street",
      "city": "Hanoi",
      "country": "Vietnam",
      "bio": "Updated biography information"
    }
  }
}

docs {
  title: "Update User"
  desc: "Update a user's information (admin or owner)"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Cập nhật thành công",
      "success": true,
      "path": "/api/v1/users/1",
      "timestamp": "2025-06-01T10:40:00+07:00"
    },
    "data": {
      "id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "full_name": "John Updated Doe",
      "status": "active",
      "role": "admin",
      "updated_at": "2025-06-01T10:40:00Z"
    }
  }
}
