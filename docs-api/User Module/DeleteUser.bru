meta {
  name: DeleteUser
  type: http
  seq: 4
}

delete {
  url: {{api_url}}/api/v1/users/{{user_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Delete User"
  desc: "Delete a user (admin only)"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Xóa người dùng thành công",
      "success": true,
      "path": "/api/v1/users/1",
      "timestamp": "2025-06-01T10:45:00+07:00"
    },
    "data": null
  }
}
