meta {
  name: ProcessPayment
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/v1/payments/process
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "order_id": "ORD-12345",
    "payment_method": "credit_card",
    "payment_details": {
      "card_number": "****************",
      "expiry_month": 12,
      "expiry_year": 2025,
      "cvv": "123",
      "card_holder_name": "John Doe"
    },
    "billing_address": {
      "full_name": "<PERSON>",
      "phone": "+84123456789",
      "address_line1": "123 Example Street",
      "address_line2": "Apt 4B",
      "city": "Ho Chi Minh",
      "state": "",
      "postal_code": "70000",
      "country": "Vietnam"
    }
  }
}

docs {
  title: "Process Payment"
  desc: "Process a payment for an order"
}

response {
  {
    "status": {
      "code": 200,
      "message": "<PERSON>h toán thành công",
      "success": true,
      "path": "/api/v1/payments/process",
      "timestamp": "2025-06-01T11:30:00+07:00"
    },
    "data": {
      "payment_id": "PAY-98765",
      "order_id": "ORD-12345",
      "amount": 299.97,
      "status": "completed",
      "transaction_id": "TXN-54321",
      "payment_method": "credit_card",
      "card_last4": "1111",
      "created_at": "2025-06-01T11:30:00Z"
    }
  }
}
