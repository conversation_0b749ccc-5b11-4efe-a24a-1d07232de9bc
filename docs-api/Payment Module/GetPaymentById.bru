meta {
  name: GetPaymentById
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/v1/payments/{{payment_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Get Payment By ID"
  desc: "Retrieve details of a specific payment"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Thành công",
      "success": true,
      "path": "/api/v1/payments/PAY-98765",
      "timestamp": "2025-06-01T11:35:00+07:00"
    },
    "data": {
      "payment_id": "PAY-98765",
      "order_id": "ORD-12345",
      "user_id": 1,
      "amount": 299.97,
      "status": "completed",
      "transaction_id": "TXN-54321",
      "payment_method": "credit_card",
      "card_last4": "1111",
      "payment_provider": "stripe",
      "created_at": "2025-06-01T11:30:00Z",
      "updated_at": "2025-06-01T11:30:00Z"
    }
  }
}
