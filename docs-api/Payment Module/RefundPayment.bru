meta {
  name: RefundPayment
  type: http
  seq: 3
}

post {
  url: {{api_url}}/api/v1/payments/{{payment_id}}/refund
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "amount": 299.97,
    "reason": "Customer request",
    "full_refund": true
  }
}

docs {
  title: "Refund Payment"
  desc: "Process a refund for a completed payment (admin only)"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Hoàn tiền thành công",
      "success": true,
      "path": "/api/v1/payments/PAY-98765/refund",
      "timestamp": "2025-06-01T11:40:00+07:00"
    },
    "data": {
      "refund_id": "REF-24680",
      "payment_id": "PAY-98765",
      "order_id": "ORD-12345",
      "amount": 299.97,
      "status": "completed",
      "reason": "Customer request",
      "created_at": "2025-06-01T11:40:00Z"
    }
  }
}
