meta {
  name: UpdateCartItem
  type: http
  seq: 3
}

put {
  url: {{api_url}}/api/v1/cart/items/{{item_id}}
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "quantity": 3,
    "attributes": {
      "color": "Black",
      "size": "Large"
    }
  }
}

docs {
  title: "Update Cart Item"
  desc: "Update the quantity or attributes of an item in the cart"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Giỏ hàng đã được cập nhật",
      "success": true,
      "path": "/api/v1/cart/items/CI-003",
      "timestamp": "2025-06-01T12:10:00+07:00"
    },
    "data": {
      "item": {
        "id": "CI-003",
        "product_id": "PROD-003",
        "product_name": "Wireless Earbuds",
        "quantity": 3,
        "unit_price": 89.99,
        "total_price": 269.97,
        "image_url": "https://example.com/images/wireless-earbuds.jpg",
        "attributes": {
          "color": "Black",
          "size": "Large"
        }
      },
      "summary": {
        "subtotal": 569.94,
        "estimated_tax": 57.00,
        "shipping": 0,
        "total": 626.94,
        "items_count": 6
      }
    }
  }
}
