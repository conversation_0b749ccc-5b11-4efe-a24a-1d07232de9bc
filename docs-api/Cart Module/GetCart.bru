meta {
  name: GetCart
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/v1/cart
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Get Cart"
  desc: "Retrieve the current user's shopping cart"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Thành công",
      "success": true,
      "path": "/api/v1/cart",
      "timestamp": "2025-06-01T12:00:00+07:00"
    },
    "data": {
      "cart_id": "CART-54321",
      "user_id": 1,
      "items": [
        {
          "id": "CI-001",
          "product_id": "PROD-001",
          "product_name": "Smartphone X",
          "quantity": 1,
          "unit_price": 199.99,
          "total_price": 199.99,
          "image_url": "https://example.com/images/smartphone-x.jpg",
          "attributes": {
            "color": "Black",
            "storage": "128GB"
          }
        },
        {
          "id": "CI-002",
          "product_id": "PROD-002",
          "product_name": "Phone Case",
          "quantity": 2,
          "unit_price": 49.99,
          "total_price": 99.98,
          "image_url": "https://example.com/images/phone-case.jpg",
          "attributes": {
            "color": "Clear",
            "material": "Silicone"
          }
        }
      ],
      "summary": {
        "subtotal": 299.97,
        "estimated_tax": 30.00,
        "shipping": 0,
        "total": 329.97,
        "items_count": 3
      },
      "updated_at": "2025-06-01T11:55:00Z"
    }
  }
}
