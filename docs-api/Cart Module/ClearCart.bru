meta {
  name: ClearCart
  type: http
  seq: 5
}

delete {
  url: {{api_url}}/api/v1/cart
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Clear Cart"
  desc: "Remove all items from the shopping cart"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Giỏ hàng đã được xóa",
      "success": true,
      "path": "/api/v1/cart",
      "timestamp": "2025-06-01T12:20:00+07:00"
    },
    "data": {
      "cart_id": "CART-54321",
      "items": [],
      "summary": {
        "subtotal": 0,
        "estimated_tax": 0,
        "shipping": 0,
        "total": 0,
        "items_count": 0
      }
    }
  }
}
