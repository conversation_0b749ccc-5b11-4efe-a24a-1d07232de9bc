meta {
  name: AddToCart
  type: http
  seq: 2
}

post {
  url: {{api_url}}/api/v1/cart/items
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "product_id": "PROD-003",
    "quantity": 1,
    "attributes": {
      "color": "White",
      "size": "Medium"
    }
  }
}

docs {
  title: "Add To Cart"
  desc: "Add a product to the user's shopping cart"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Sản phẩm đã được thêm vào giỏ hàng",
      "success": true,
      "path": "/api/v1/cart/items",
      "timestamp": "2025-06-01T12:05:00+07:00"
    },
    "data": {
      "cart_id": "CART-54321",
      "item": {
        "id": "CI-003",
        "product_id": "PROD-003",
        "product_name": "Wireless Earbuds",
        "quantity": 1,
        "unit_price": 89.99,
        "total_price": 89.99,
        "image_url": "https://example.com/images/wireless-earbuds.jpg",
        "attributes": {
          "color": "White",
          "size": "Medium"
        }
      },
      "summary": {
        "subtotal": 389.96,
        "estimated_tax": 39.00,
        "shipping": 0,
        "total": 428.96,
        "items_count": 4
      }
    }
  }
}
