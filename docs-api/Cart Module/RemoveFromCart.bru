meta {
  name: RemoveFromCart
  type: http
  seq: 4
}

delete {
  url: {{api_url}}/api/v1/cart/items/{{item_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Remove From Cart"
  desc: "Remove an item from the shopping cart"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Sản phẩm đã được xóa khỏi giỏ hàng",
      "success": true,
      "path": "/api/v1/cart/items/CI-003",
      "timestamp": "2025-06-01T12:15:00+07:00"
    },
    "data": {
      "summary": {
        "subtotal": 299.97,
        "estimated_tax": 30.00,
        "shipping": 0,
        "total": 329.97,
        "items_count": 3
      }
    }
  }
}
