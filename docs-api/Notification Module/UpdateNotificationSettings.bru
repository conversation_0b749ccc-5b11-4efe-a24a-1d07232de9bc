meta {
  name: UpdateNotificationSettings
  type: http
  seq: 7
}

put {
  url: {{api_url}}/api/v1/notifications/settings
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "emailNotifications": true,
    "pushNotifications": true,
    "inAppNotifications": true,
    "notificationTypes": {
      "system": true,
      "message": true,
      "activity": true,
      "marketing": false
    }
  }
}

docs {
  title: "Update Notification Settings"
  desc: "Update notification preferences for the authenticated user"
}
