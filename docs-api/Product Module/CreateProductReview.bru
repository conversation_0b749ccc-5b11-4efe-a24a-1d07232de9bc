meta {
  name: CreateProductReview
  type: http
  seq: 10
}

post {
  url: {{api_url}}/api/v1/products/{{product_id}}/reviews
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "rating": 4.5,
    "title": "Great product!",
    "content": "This product exceeded my expectations. It's well built and works perfectly.",
    "images": [
      "https://example.com/review-image1.jpg",
      "https://example.com/review-image2.jpg"
    ]
  }
}

docs {
  title: "Create Product Review"
  desc: "Add a review for a product (requires authentication and purchase verification)"
}
