meta {
  name: BulkUpdateProducts
  type: http
  seq: 13
}

patch {
  url: {{api_url}}/api/v1/products/bulk
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "productIds": ["product_id_1", "product_id_2", "product_id_3"],
    "updates": {
      "isActive": true,
      "categories": ["special-offer"],
      "salePrice": {
        "operation": "discount",
        "value": 10,
        "type": "percent"
      }
    }
  }
}

docs {
  title: "Bulk Update Products"
  desc: "Update multiple products at once (requires admin authentication)"
}
