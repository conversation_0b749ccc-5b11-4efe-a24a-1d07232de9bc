meta {
  name: CreateProduct
  type: http
  seq: 3
}

post {
  url: {{api_url}}/api/v1/products
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "name": "Sample Product",
    "description": "This is a detailed description of the product",
    "price": 99.99,
    "salePrice": 79.99,
    "categories": ["electronics", "gadgets"],
    "images": [
      {
        "url": "https://example.com/image1.jpg",
        "isMain": true
      },
      {
        "url": "https://example.com/image2.jpg",
        "isMain": false
      }
    ],
    "attributes": [
      {
        "name": "Color",
        "value": "Black"
      },
      {
        "name": "Size",
        "value": "Medium"
      }
    ],
    "sku": "PROD-12345",
    "stock": 100,
    "isActive": true,
    "weight": 1.5,
    "dimensions": {
      "length": 10,
      "width": 5,
      "height": 2
    }
  }
}

docs {
  title: "Create Product"
  desc: "Create a new product (requires admin/seller authentication)"
}
