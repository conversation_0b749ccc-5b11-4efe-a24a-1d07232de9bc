meta {
  name: CreateProductCategory
  type: http
  seq: 8
}

post {
  url: {{api_url}}/api/v1/products/categories
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "name": "Smart Home",
    "description": "Smart home devices and accessories",
    "slug": "smart-home",
    "parentId": null,
    "image": "https://example.com/categories/smart-home.jpg",
    "isActive": true
  }
}

docs {
  title: "Create Product Category"
  desc: "Create a new product category (requires admin authentication)"
}
