meta {
  name: UpdateOrderStatus
  type: http
  seq: 4
}

patch {
  url: {{api_url}}/api/v1/orders/{{order_id}}/status
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "status": "shipped",
    "tracking_number": "TRK123456789",
    "carrier": "DHL",
    "notes": "Package was shipped today"
  }
}

docs {
  title: "Update Order Status"
  desc: "Update the status of an existing order (admin only)"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Cập nhật trạng thái đơn hàng thành công",
      "success": true,
      "path": "/api/v1/orders/ORD-12345/status",
      "timestamp": "2025-06-01T11:15:00+07:00"
    },
    "data": {
      "order_id": "ORD-12345",
      "status": "shipped",
      "tracking_number": "TRK123456789",
      "carrier": "DHL",
      "updated_at": "2025-06-01T11:15:00Z"
    }
  }
}
