meta {
  name: CreateOrder
  type: http
  seq: 3
}

post {
  url: {{api_url}}/api/v1/orders
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "items": [
      {
        "product_id": "PROD-001",
        "quantity": 1,
        "unit_price": 199.99
      },
      {
        "product_id": "PROD-002",
        "quantity": 2,
        "unit_price": 49.99
      }
    ],
    "shipping_address": {
      "full_name": "John Doe",
      "phone": "+84123456789",
      "address_line1": "123 Example Street",
      "address_line2": "Apt 4B",
      "city": "Ho Chi Minh",
      "state": "",
      "postal_code": "70000",
      "country": "Vietnam"
    },
    "payment_method": "credit_card",
    "coupon_code": "SUMMER10"
  }
}

docs {
  title: "Create Order"
  desc: "Create a new order from cart items or specific products"
}

response {
  {
    "status": {
      "code": 201,
      "message": "<PERSON><PERSON><PERSON> hàng đã được tạo thành công",
      "success": true,
      "path": "/api/v1/orders",
      "timestamp": "2025-06-01T11:10:00+07:00"
    },
    "data": {
      "order_id": "ORD-12345",
      "total_amount": 299.97,
      "status": "pending",
      "payment_url": "https://example.com/payment/checkout/12345"
    }
  }
}
