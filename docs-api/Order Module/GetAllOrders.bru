meta {
  name: GetAllOrders
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/v1/orders
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

query {
  page: 1
  limit: 20
  status: all
  start_date: 2025-01-01
  end_date: 2025-06-01
  sort: created_at_desc
}

docs {
  title: "Get All Orders"
  desc: "Retrieve a list of orders with pagination and filtering (admin sees all, users see only their own)"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Thành công",
      "success": true,
      "path": "/api/v1/orders",
      "timestamp": "2025-06-01T11:00:00+07:00"
    },
    "data": {
      "orders": [
        {
          "id": "ORD-12345",
          "user_id": 1,
          "status": "completed",
          "total_amount": 299.97,
          "items_count": 3,
          "payment_status": "paid",
          "created_at": "2025-05-30T14:30:00Z",
          "updated_at": "2025-05-30T16:45:00Z"
        }
      ],
      "pagination": {
        "total": 45,
        "page": 1,
        "limit": 20,
        "pages": 3
      }
    }
  }
}
