meta {
  name: GetOrderById
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/v1/orders/{{order_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Get Order By ID"
  desc: "Retrieve a specific order by ID with all details (admin or order owner)"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Thành công",
      "success": true,
      "path": "/api/v1/orders/ORD-12345",
      "timestamp": "2025-06-01T11:05:00+07:00"
    },
    "data": {
      "id": "ORD-12345",
      "user_id": 1,
      "user_email": "<EMAIL>",
      "status": "completed",
      "total_amount": 299.97,
      "subtotal": 269.97,
      "tax": 30.00,
      "discount": 0,
      "shipping_fee": 0,
      "payment_method": "credit_card",
      "payment_status": "paid",
      "shipping_address": {
        "full_name": "<PERSON>",
        "phone": "+84123456789",
        "address_line1": "123 Example Street",
        "address_line2": "Apt 4B",
        "city": "Ho Chi Minh",
        "state": "",
        "postal_code": "70000",
        "country": "Vietnam"
      },
      "items": [
        {
          "product_id": "PROD-001",
          "product_name": "Smartphone X",
          "quantity": 1,
          "unit_price": 199.99,
          "total_price": 199.99,
          "sku": "SMX-12345",
          "image_url": "https://example.com/images/smartphone-x.jpg"
        },
        {
          "product_id": "PROD-002",
          "product_name": "Phone Case",
          "quantity": 2,
          "unit_price": 49.99,
          "total_price": 99.98,
          "sku": "PC-67890",
          "image_url": "https://example.com/images/phone-case.jpg"
        }
      ],
      "created_at": "2025-05-30T14:30:00Z",
      "updated_at": "2025-05-30T16:45:00Z",
      "completed_at": "2025-05-30T16:45:00Z"
    }
  }
}
