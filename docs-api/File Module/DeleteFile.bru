meta {
  name: DeleteFile
  type: http
  seq: 3
}

delete {
  url: {{api_url}}/api/v1/files/{{file_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Delete File"
  desc: "Delete a file from the server"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Tệp đã được xóa thành công",
      "success": true,
      "path": "/api/v1/files/FILE-12345",
      "timestamp": "2025-06-01T12:40:00+07:00"
    },
    "data": null
  }
}
