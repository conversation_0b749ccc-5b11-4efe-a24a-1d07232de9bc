meta {
  name: UploadFile
  type: http
  seq: 1
}

post {
  url: {{api_url}}/api/v1/files/upload
  body: multipartForm
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

form:multipart {
  file: @file(sample.jpg)
  type: image
  folder: products
}

docs {
  title: "Upload File"
  desc: "Upload a file to the server"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Tệp đã được tải lên thành công",
      "success": true,
      "path": "/api/v1/files/upload",
      "timestamp": "2025-06-01T12:30:00+07:00"
    },
    "data": {
      "file_id": "FILE-12345",
      "filename": "sample.jpg",
      "original_name": "sample.jpg",
      "size": 1024567,
      "mime_type": "image/jpeg",
      "url": "https://cdn.example.com/files/products/sample-12345.jpg",
      "folder": "products",
      "created_at": "2025-06-01T12:30:00Z"
    }
  }
}
