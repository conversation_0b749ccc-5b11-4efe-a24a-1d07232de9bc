meta {
  name: GetFileById
  type: http
  seq: 2
}

get {
  url: {{api_url}}/api/v1/files/{{file_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Get File By ID"
  desc: "Retrieve file metadata by ID"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Thành công",
      "success": true,
      "path": "/api/v1/files/FILE-12345",
      "timestamp": "2025-06-01T12:35:00+07:00"
    },
    "data": {
      "file_id": "FILE-12345",
      "filename": "sample.jpg",
      "original_name": "sample.jpg",
      "size": 1024567,
      "mime_type": "image/jpeg",
      "url": "https://cdn.example.com/files/products/sample-12345.jpg",
      "folder": "products",
      "uploaded_by": 1,
      "created_at": "2025-06-01T12:30:00Z"
    }
  }
}
