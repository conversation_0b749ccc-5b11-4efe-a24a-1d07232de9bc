meta {
  name: CreatePermissionGroup
  type: http
  seq: 18
}

post {
  url: {{api_url}}/api/v1/rbac/permission-groups
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "tenant_id": 1,
    "permission_group_name": "Content Management",
    "permission_group_description": "Quản lý nội dung và bài viết"
  }
}

docs {
  title: "Tạo nhóm quyền mới"
  desc: "Tạo một nhóm quyền mới trong hệ thống"
}

response {
  {
    "status": {
      "code": 201,
      "message": "Tạo nhóm quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permission-groups",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "group_id": 3,
      "tenant_id": 1,
      "permission_group_name": "Content Management",
      "permission_group_description": "Quản lý nội dung và bài viết",
      "created_by": 1,
      "created_at": "2025-01-15T14:35:22Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T14:35:22Z"
    },
    "meta": null
  }
}
