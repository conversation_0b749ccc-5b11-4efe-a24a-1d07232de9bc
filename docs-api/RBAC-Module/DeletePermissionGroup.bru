meta {
  name: DeletePermissionGroup
  type: http
  seq: 21
}

delete {
  url: {{api_url}}/api/v1/rbac/permission-groups/{{group_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Xóa nhóm quyền"
  desc: "<PERSON><PERSON>a một nhóm quyền khỏi hệ thống"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Xóa nhóm quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permission-groups/1",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": null,
    "meta": null
  }
}
