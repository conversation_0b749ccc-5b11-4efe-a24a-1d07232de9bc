meta {
  name: UpdatePermission
  type: http
  seq: 4
}

put {
  url: {{api_url}}/api/v1/rbac/permissions/{{permission_id}}
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "permission_code": "users.create",
    "group_id": 1,
    "permission_name": "Tạo và quản lý người dùng",
    "permission_description": "Quyền tạo và quản lý người dùng trong hệ thống"
  }
}

docs {
  title: "Cập nhật quyền"
  desc: "Cậ<PERSON> nhật thông tin của một quyền"
}

response {
  {
    "status": {
      "code": 200,
      "message": "<PERSON>ậ<PERSON> nhật quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permissions/1",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "permission_id": 1,
      "permission_code": "users.create",
      "tenant_id": 1,
      "group_id": 1,
      "group": {
        "group_id": 1,
        "permission_group_name": "User Management",
        "permission_group_description": "Quản lý người dùng"
      },
      "permission_name": "Tạo và quản lý người dùng",
      "permission_description": "Quyền tạo và quản lý người dùng trong hệ thống",
      "created_by": 1,
      "created_at": "2025-01-15T10:00:00Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T14:35:22Z"
    },
    "meta": null
  }
}
