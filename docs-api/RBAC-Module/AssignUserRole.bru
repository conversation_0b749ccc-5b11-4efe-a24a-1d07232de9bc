meta {
  name: AssignUserRole
  type: http
  seq: 12
}

post {
  url: {{api_url}}/api/v1/rbac/user-roles/assign
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "user_id": 5,
    "role_id": 2
  }
}

docs {
  title: "Gán vai trò cho người dùng"
  desc: "<PERSON>án một vai trò cho người dùng"
}

response {
  {
    "status": {
      "code": 201,
      "message": "Gán vai trò cho người dùng thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/user-roles/assign",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "tenant_id": 1,
      "user_id": 5,
      "role_id": 2,
      "created_by": 1,
      "created_at": "2025-01-15T14:35:22Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T14:35:22Z",
      "role": {
        "role_id": 2,
        "role_code": "editor",
        "tenant_id": 1,
        "role_name": "Bi<PERSON>n tập viên",
        "role_description": "Vai trò biên tập viên nội dung",
        "created_by": 1,
        "created_at": "2025-01-15T10:00:00Z",
        "updated_by": 1,
        "updated_at": "2025-01-15T10:00:00Z",
        "permissions": [1, 2, 3]
      }
    },
    "meta": null
  }
}
