meta {
  name: ListPermissionGroups
  type: http
  seq: 17
}

get {
  url: {{api_url}}/api/v1/rbac/permission-groups?limit=20&cursor=1&tenant_id=1
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON>y danh sách nhóm quyền"
  desc: "L<PERSON>y danh sách nhóm quyền với phân trang"
}

response {
  {
    "status": {
      "code": 200,
      "message": "<PERSON><PERSON>y danh sách nhóm quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permission-groups",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": [
      {
        "group_id": 1,
        "tenant_id": 1,
        "permission_group_name": "User Management",
        "permission_group_description": "Quản lý người dùng và tà<PERSON>",
        "created_by": 1,
        "created_at": "2025-01-15T10:00:00Z",
        "updated_by": 1,
        "updated_at": "2025-01-15T10:00:00Z"
      },
      {
        "group_id": 2,
        "tenant_id": 1,
        "permission_group_name": "Product Management",
        "permission_group_description": "Quản lý sản phẩm và danh mục",
        "created_by": 1,
        "created_at": "2025-01-15T10:00:00Z",
        "updated_by": 1,
        "updated_at": "2025-01-15T10:00:00Z"
      }
    ],
    "meta": {
      "next_cursor": 3,
      "has_more": true
    }
  }
}
