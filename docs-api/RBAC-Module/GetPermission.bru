meta {
  name: GetPermission
  type: http
  seq: 3
}

get {
  url: {{api_url}}/api/v1/rbac/permissions/{{permission_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON><PERSON> chi tiết quyền"
  desc: "<PERSON><PERSON>y thông tin chi tiết của một quyền theo ID"
}

response {
  {
    "status": {
      "code": 200,
      "message": "L<PERSON>y thông tin quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permissions/1",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "permission_id": 1,
      "permission_code": "users.create",
      "tenant_id": 1,
      "group_id": 1,
      "group": {
        "group_id": 1,
        "permission_group_name": "User Management",
        "permission_group_description": "<PERSON><PERSON><PERSON>n lý người dùng"
      },
      "permission_name": "<PERSON><PERSON>o người dùng",
      "permission_description": "<PERSON>uyền tạo người dùng mới",
      "created_by": 1,
      "created_at": "2025-01-15T10:00:00Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T10:00:00Z"
    },
    "meta": null
  }
}
