meta {
  name: UpdateRole
  type: http
  seq: 10
}

put {
  url: {{api_url}}/api/v1/rbac/roles/{{role_id}}
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "role_code": "super_admin",
    "role_name": "Quản trị viên cấp cao",
    "role_description": "Vai trò quản trị viên cấp cao với toàn quyền",
    "permission_ids": [1, 2, 3, 4, 5, 6, 7]
  }
}

docs {
  title: "Cập nhật vai trò"
  desc: "<PERSON><PERSON><PERSON> nhật thông tin và quyền của một vai trò"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Cập nhật vai trò thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/roles/1",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "role_id": 1,
      "role_code": "super_admin",
      "tenant_id": 1,
      "role_name": "<PERSON><PERSON><PERSON>n trị viên cấp cao",
      "role_description": "Vai trò quản trị viên cấp cao với toàn quyền",
      "created_by": 1,
      "created_at": "2025-01-15T10:00:00Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T14:35:22Z",
      "permissions": [1, 2, 3, 4, 5, 6, 7]
    },
    "meta": null
  }
}
