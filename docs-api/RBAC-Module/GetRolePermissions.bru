meta {
  name: GetRolePermissions
  type: http
  seq: 9
}

get {
  url: {{api_url}}/api/v1/rbac/roles/{{role_id}}/permissions
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON><PERSON> quyền của vai trò"
  desc: "<PERSON><PERSON><PERSON> danh sách quyền của một vai trò"
}

response {
  {
    "status": {
      "code": 200,
      "message": "<PERSON><PERSON>y danh sách quyền của vai trò thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/roles/1/permissions",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": [1, 2, 3, 4, 5],
    "meta": null
  }
}
