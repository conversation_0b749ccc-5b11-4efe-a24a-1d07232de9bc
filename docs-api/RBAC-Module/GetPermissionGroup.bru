meta {
  name: GetPermissionGroup
  type: http
  seq: 19
}

get {
  url: {{api_url}}/api/v1/rbac/permission-groups/{{group_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON>y chi tiết nhóm quyền"
  desc: "L<PERSON>y thông tin chi tiết của một nhóm quyền theo ID"
}

response {
  {
    "status": {
      "code": 200,
      "message": "L<PERSON>y thông tin nhóm quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permission-groups/1",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "group_id": 1,
      "tenant_id": 1,
      "permission_group_name": "User Management",
      "permission_group_description": "Quản lý người dùng và tài kho<PERSON>n",
      "created_by": 1,
      "created_at": "2025-01-15T10:00:00Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T10:00:00Z"
    },
    "meta": null
  }
}
