meta {
  name: GetUserRoles
  type: http
  seq: 14
}

get {
  url: {{api_url}}/api/v1/rbac/user-roles/users/{{user_id}}/roles
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON><PERSON> danh sách vai trò của người dùng"
  desc: "Lấy tất cả vai trò được gán cho một người dùng"
}

response {
  {
    "status": {
      "code": 200,
      "message": "<PERSON><PERSON>y danh sách vai trò của người dùng thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/user-roles/users/5/roles",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": [
      {
        "tenant_id": 1,
        "user_id": 5,
        "role_id": 2,
        "created_by": 1,
        "created_at": "2025-01-15T14:35:22Z",
        "updated_by": 1,
        "updated_at": "2025-01-15T14:35:22Z",
        "role": {
          "role_id": 2,
          "role_code": "editor",
          "tenant_id": 1,
          "role_name": "Biên tập viên",
          "role_description": "Vai trò biên tập viên nội dung",
          "created_by": 1,
          "created_at": "2025-01-15T10:00:00Z",
          "updated_by": 1,
          "updated_at": "2025-01-15T10:00:00Z",
          "permissions": [1, 2, 3]
        }
      }
    ],
    "meta": null
  }
}
