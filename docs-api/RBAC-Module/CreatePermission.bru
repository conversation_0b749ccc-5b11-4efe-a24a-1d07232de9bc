meta {
  name: CreatePermission
  type: http
  seq: 2
}

post {
  url: {{api_url}}/api/v1/rbac/permissions
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "permission_code": "products.create",
    "tenant_id": 1,
    "group_id": 2,
    "permission_name": "Tạo sản phẩm",
    "permission_description": "Quyền tạo sản phẩm mới"
  }
}

docs {
  title: "Tạo quyền mới"
  desc: "Tạo một quyền mới trong hệ thống"
}

response {
  {
    "status": {
      "code": 201,
      "message": "Tạo quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permissions",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "permission_id": 15,
      "permission_code": "products.create",
      "tenant_id": 1,
      "group_id": 2,
      "group": {
        "group_id": 2,
        "permission_group_name": "Product Management",
        "permission_group_description": "Quản lý sản phẩm"
      },
      "permission_name": "Tạo sản phẩm",
      "permission_description": "Quyền tạo sản phẩm mới",
      "created_by": 1,
      "created_at": "2025-01-15T14:35:22Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T14:35:22Z"
    },
    "meta": null
  }
}
