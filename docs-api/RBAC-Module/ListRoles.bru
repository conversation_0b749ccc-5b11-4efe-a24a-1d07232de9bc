meta {
  name: ListRoles
  type: http
  seq: 6
}

get {
  url: {{api_url}}/api/v1/rbac/roles?limit=20&cursor=&with_permissions=true
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON>y danh sách vai trò"
  desc: "<PERSON><PERSON>y danh sách vai trò với phân trang và tùy chọn bao gồm quyền"
}

response {
  {
    "status": {
      "code": 200,
      "message": "L<PERSON>y danh sách vai trò thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/roles",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": [
      {
        "role_id": 1,
        "role_code": "admin",
        "tenant_id": 1,
        "role_name": "Quản trị viên",
        "role_description": "<PERSON>ai trò quản trị viê<PERSON> hệ thống",
        "created_by": 1,
        "created_at": "2025-01-15T10:00:00Z",
        "updated_by": 1,
        "updated_at": "2025-01-15T10:00:00Z",
        "permissions": [1, 2, 3, 4, 5]
      }
    ],
    "meta": {
      "next_cursor": "eyJpZCI6MX0=",
      "has_more": true
    }
  }
}
