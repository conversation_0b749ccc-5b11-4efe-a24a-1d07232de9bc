meta {
  name: UpdatePermissionGroup
  type: http
  seq: 20
}

put {
  url: {{api_url}}/api/v1/rbac/permission-groups/{{group_id}}
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "permission_group_name": "Advanced User Management",
    "permission_group_description": "Quản lý người dùng và tài khoản nâng cao"
  }
}

docs {
  title: "Cập nhật nhóm quyền"
  desc: "Cập nhật thông tin của một nhóm quyền"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Cập nhật nhóm quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permission-groups/1",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "group_id": 1,
      "tenant_id": 1,
      "permission_group_name": "Advanced User Management",
      "permission_group_description": "<PERSON>uản lý người dùng và tài khoản nâng cao",
      "created_by": 1,
      "created_at": "2025-01-15T10:00:00Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T14:35:22Z"
    },
    "meta": null
  }
}
