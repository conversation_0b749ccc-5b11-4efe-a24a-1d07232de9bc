meta {
  name: CreateRole
  type: http
  seq: 7
}

post {
  url: {{api_url}}/api/v1/rbac/roles
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

body:json {
  {
    "role_code": "editor",
    "tenant_id": 1,
    "role_name": "<PERSON><PERSON>ên tập viên",
    "role_description": "Vai trò biên tập viên nội dung",
    "permissions": [1, 2, 3]
  }
}

docs {
  title: "Tạo vai trò mới"
  desc: "Tạo một vai trò mới với danh sách quyền"
}

response {
  {
    "status": {
      "code": 201,
      "message": "Tạo vai trò thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/roles",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "role_id": 5,
      "role_code": "editor",
      "tenant_id": 1,
      "role_name": "<PERSON><PERSON><PERSON><PERSON> tập viên",
      "role_description": "<PERSON>ai trò biên tập viên nội dung",
      "created_by": 1,
      "created_at": "2025-01-15T14:35:22Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T14:35:22Z",
      "permissions": [1, 2, 3]
    },
    "meta": null
  }
}
