meta {
  name: CheckUserRole
  type: http
  seq: 15
}

get {
  url: {{api_url}}/api/v1/rbac/user-roles/users/{{user_id}}/roles/{{role_id}}/check
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Kiểm tra vai trò của người dùng"
  desc: "Kiểm tra xem người dùng có vai trò cụ thể hay không"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Kiểm tra vai trò của người dùng thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/user-roles/users/5/roles/2/check",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "has_role": true,
      "user_id": 5,
      "role_id": 2,
      "role_code": "editor",
      "role_name": "<PERSON><PERSON><PERSON><PERSON> tập viên"
    },
    "meta": null
  }
}
