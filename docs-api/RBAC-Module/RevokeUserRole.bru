meta {
  name: RevokeUserRole
  type: http
  seq: 13
}

delete {
  url: {{api_url}}/api/v1/rbac/user-roles/users/{{user_id}}/roles/{{role_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Thu hồi vai trò của người dùng"
  desc: "Thu hồi một vai trò từ người dùng"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Thu hồi vai trò của người dùng thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/user-roles/users/5/roles/2",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": null,
    "meta": null
  }
}
