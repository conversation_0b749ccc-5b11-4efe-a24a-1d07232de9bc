meta {
  name: ListPermissions
  type: http
  seq: 1
}

get {
  url: {{api_url}}/api/v1/rbac/permissions?limit=20&cursor=&group_id=1&search=user
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON>y danh sách quyền"
  desc: "<PERSON><PERSON>y danh sách quyền với phân trang và bộ lọc"
}

response {
  {
    "status": {
      "code": 200,
      "message": "<PERSON><PERSON>y danh sách quyền thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/permissions",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": [
      {
        "permission_id": 1,
        "permission_code": "users.create",
        "tenant_id": 1,
        "group_id": 1,
        "group": {
          "group_id": 1,
          "permission_group_name": "User Management",
          "permission_group_description": "<PERSON><PERSON><PERSON><PERSON> lý người dùng"
        },
        "permission_name": "<PERSON><PERSON>o người dùng",
        "permission_description": "<PERSON>uyền tạo người dùng mới",
        "created_by": 1,
        "created_at": "2025-01-15T10:00:00Z",
        "updated_by": 1,
        "updated_at": "2025-01-15T10:00:00Z"
      }
    ],
    "meta": {
      "next_cursor": "eyJpZCI6MX0=",
      "has_more": true
    }
  }
}
