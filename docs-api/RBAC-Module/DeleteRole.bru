meta {
  name: DeleteRole
  type: http
  seq: 11
}

delete {
  url: {{api_url}}/api/v1/rbac/roles/{{role_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "Xóa vai trò"
  desc: "<PERSON>óa một vai trò khỏi hệ thống"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Xóa vai trò thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/roles/1",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": null,
    "meta": null
  }
}
