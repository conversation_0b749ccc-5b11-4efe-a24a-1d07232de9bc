meta {
  name: GetRole
  type: http
  seq: 8
}

get {
  url: {{api_url}}/api/v1/rbac/roles/{{role_id}}
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON>y chi tiết vai trò"
  desc: "L<PERSON>y thông tin chi tiết của một vai trò theo ID"
}

response {
  {
    "status": {
      "code": 200,
      "message": "Lấy thông tin vai trò thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/roles/1",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": {
      "role_id": 1,
      "role_code": "admin",
      "tenant_id": 1,
      "role_name": "Quản trị viên",
      "role_description": "Vai trò quản trị viên hệ thống",
      "created_by": 1,
      "created_at": "2025-01-15T10:00:00Z",
      "updated_by": 1,
      "updated_at": "2025-01-15T10:00:00Z",
      "permissions": [1, 2, 3, 4, 5]
    },
    "meta": null
  }
}
