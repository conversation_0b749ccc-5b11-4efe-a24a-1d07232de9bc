meta {
  name: GetUsersWithRole
  type: http
  seq: 16
}

get {
  url: {{api_url}}/api/v1/rbac/user-roles/roles/{{role_id}}/users
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer {{access_token}}
}

docs {
  title: "<PERSON><PERSON><PERSON> danh sách người dùng có vai trò"
  desc: "L<PERSON>y tất cả người dùng được gán một vai trò cụ thể"
}

response {
  {
    "status": {
      "code": 200,
      "message": "<PERSON><PERSON><PERSON> danh sách người dùng có vai trò thành công",
      "success": true,
      "error_code": null,
      "path": "/api/v1/rbac/user-roles/roles/2/users",
      "timestamp": "2025-01-15T14:35:22Z",
      "details": null
    },
    "data": [
      {
        "tenant_id": 1,
        "user_id": 5,
        "role_id": 2,
        "created_by": 1,
        "created_at": "2025-01-15T14:35:22Z",
        "updated_by": 1,
        "updated_at": "2025-01-15T14:35:22Z"
      },
      {
        "tenant_id": 1,
        "user_id": 8,
        "role_id": 2,
        "created_by": 1,
        "created_at": "2025-01-15T12:00:00Z",
        "updated_by": 1,
        "updated_at": "2025-01-15T12:00:00Z"
      }
    ],
    "meta": null
  }
}
