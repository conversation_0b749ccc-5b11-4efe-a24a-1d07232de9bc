Bạn nói đúng! Sử dụng mã lỗi mô tả bằng chữ cụ thể sẽ dễ hiểu và maintain hơn nhiều. Đ<PERSON><PERSON> là cách tổ chức lại:

## 1. **Mã lỗi chung (Global Error Codes)**

```go
// internal/pkg/errors/codes.go
package errors

type ErrorCode string

// Common error codes
const (
    ErrCodeUnknown           ErrorCode = "UNKNOWN_ERROR"
    ErrCodeValidationFailed  ErrorCode = "VALIDATION_FAILED"
    ErrCodeUnauthorized      ErrorCode = "UNAUTHORIZED"
    ErrCodeForbidden         ErrorCode = "FORBIDDEN"
    ErrCodeNotFound          ErrorCode = "NOT_FOUND"
    ErrCodeConflict          ErrorCode = "CONFLICT"
    ErrCodeInternalServer    ErrorCode = "INTERNAL_SERVER_ERROR"
    ErrCodeRateLimit         ErrorCode = "RATE_LIMIT_EXCEEDED"
    ErrCodeMaintenance       ErrorCode = "MAINTENANCE_MODE"
    ErrCodeBadRequest        ErrorCode = "BAD_REQUEST"
    ErrCodeTimeout           ErrorCode = "REQUEST_TIMEOUT"
)

// Database error codes
const (
    ErrCodeDBConnectionFailed ErrorCode = "DB_CONNECTION_FAILED"
    ErrCodeDBQueryFailed      ErrorCode = "DB_QUERY_FAILED"
    ErrCodeDBTransactionFailed ErrorCode = "DB_TRANSACTION_FAILED"
    ErrCodeDBConstraintViolation ErrorCode = "DB_CONSTRAINT_VIOLATION"
    ErrCodeDBDeadlock         ErrorCode = "DB_DEADLOCK"
    ErrCodeDBTimeout          ErrorCode = "DB_TIMEOUT"
)

// Business logic error codes
const (
    ErrCodeBusinessRuleViolation ErrorCode = "BUSINESS_RULE_VIOLATION"
    ErrCodeInvalidState          ErrorCode = "INVALID_STATE"
    ErrCodeResourceLimitExceeded ErrorCode = "RESOURCE_LIMIT_EXCEEDED"
    ErrCodeOperationNotAllowed   ErrorCode = "OPERATION_NOT_ALLOWED"
)

// File/Upload error codes
const (
    ErrCodeFileTooLarge      ErrorCode = "FILE_TOO_LARGE"
    ErrCodeInvalidFileType   ErrorCode = "INVALID_FILE_TYPE"
    ErrCodeFileUploadFailed  ErrorCode = "FILE_UPLOAD_FAILED"
    ErrCodeFileNotFound      ErrorCode = "FILE_NOT_FOUND"
)

// Network/External service error codes
const (
    ErrCodeExternalServiceUnavailable ErrorCode = "EXTERNAL_SERVICE_UNAVAILABLE"
    ErrCodeNetworkTimeout            ErrorCode = "NETWORK_TIMEOUT"
    ErrCodeInvalidResponse           ErrorCode = "INVALID_RESPONSE"
)
```

## 2. **Mã lỗi User Module**

```go
// modules/user/errors/codes.go
package errors

import "myapp/internal/pkg/errors"

// Authentication error codes
const (
    ErrCodeUserNotFound          errors.ErrorCode = "USER_NOT_FOUND"
    ErrCodeUserAlreadyExists     errors.ErrorCode = "USER_ALREADY_EXISTS"
    ErrCodeEmailAlreadyExists    errors.ErrorCode = "EMAIL_ALREADY_EXISTS"
    ErrCodeUsernameAlreadyExists errors.ErrorCode = "USERNAME_ALREADY_EXISTS"
    ErrCodeInvalidCredentials    errors.ErrorCode = "INVALID_CREDENTIALS"
    ErrCodeInvalidPassword       errors.ErrorCode = "INVALID_PASSWORD"
    ErrCodeWeakPassword          errors.ErrorCode = "WEAK_PASSWORD"
    ErrCodePasswordExpired       errors.ErrorCode = "PASSWORD_EXPIRED"
    ErrCodePasswordRecentlyUsed  errors.ErrorCode = "PASSWORD_RECENTLY_USED"
)

// Account status error codes
const (
    ErrCodeAccountLocked        errors.ErrorCode = "ACCOUNT_LOCKED"
    ErrCodeAccountSuspended     errors.ErrorCode = "ACCOUNT_SUSPENDED"
    ErrCodeAccountInactive      errors.ErrorCode = "ACCOUNT_INACTIVE"
    ErrCodeAccountPending       errors.ErrorCode = "ACCOUNT_PENDING_VERIFICATION"
    ErrCodeAccountDeleted       errors.ErrorCode = "ACCOUNT_DELETED"
)

// Email verification error codes
const (
    ErrCodeEmailNotVerified     errors.ErrorCode = "EMAIL_NOT_VERIFIED"
    ErrCodeEmailVerificationExpired errors.ErrorCode = "EMAIL_VERIFICATION_EXPIRED"
    ErrCodeInvalidVerificationToken errors.ErrorCode = "INVALID_VERIFICATION_TOKEN"
    ErrCodeEmailVerificationFailed  errors.ErrorCode = "EMAIL_VERIFICATION_FAILED"
)

// Two-factor authentication error codes
const (
    ErrCodeTwoFactorRequired    errors.ErrorCode = "TWO_FACTOR_REQUIRED"
    ErrCodeInvalidTwoFactorCode errors.ErrorCode = "INVALID_TWO_FACTOR_CODE"
    ErrCodeTwoFactorNotEnabled  errors.ErrorCode = "TWO_FACTOR_NOT_ENABLED"
    ErrCodeTwoFactorSetupFailed errors.ErrorCode = "TWO_FACTOR_SETUP_FAILED"
)

// Session error codes
const (
    ErrCodeSessionExpired       errors.ErrorCode = "SESSION_EXPIRED"
    ErrCodeSessionNotFound      errors.ErrorCode = "SESSION_NOT_FOUND"
    ErrCodeInvalidToken         errors.ErrorCode = "INVALID_TOKEN"
    ErrCodeTokenExpired         errors.ErrorCode = "TOKEN_EXPIRED"
    ErrCodeInvalidRefreshToken  errors.ErrorCode = "INVALID_REFRESH_TOKEN"
    ErrCodeMaxSessionsExceeded  errors.ErrorCode = "MAX_SESSIONS_EXCEEDED"
)

// Profile error codes
const (
    ErrCodeProfileNotFound      errors.ErrorCode = "PROFILE_NOT_FOUND"
    ErrCodeInvalidProfileData   errors.ErrorCode = "INVALID_PROFILE_DATA"
    ErrCodeAvatarUploadFailed   errors.ErrorCode = "AVATAR_UPLOAD_FAILED"
    ErrCodeInvalidAvatar        errors.ErrorCode = "INVALID_AVATAR"
)

// Security error codes
const (
    ErrCodeTooManyLoginAttempts errors.ErrorCode = "TOO_MANY_LOGIN_ATTEMPTS"
    ErrCodeSuspiciousActivity   errors.ErrorCode = "SUSPICIOUS_ACTIVITY_DETECTED"
    ErrCodeIPBlocked            errors.ErrorCode = "IP_ADDRESS_BLOCKED"
    ErrCodeDeviceNotTrusted     errors.ErrorCode = "DEVICE_NOT_TRUSTED"
)
```

## 3. **Mã lỗi RBAC Module**

```go
// modules/rbac/errors/codes.go
package errors

import "myapp/internal/pkg/errors"

// Permission error codes
const (
    ErrCodeInsufficientPermission errors.ErrorCode = "INSUFFICIENT_PERMISSION"
    ErrCodePermissionNotFound     errors.ErrorCode = "PERMISSION_NOT_FOUND"
    ErrCodePermissionAlreadyExists errors.ErrorCode = "PERMISSION_ALREADY_EXISTS"
    ErrCodePermissionInUse        errors.ErrorCode = "PERMISSION_IN_USE"
    ErrCodeInvalidPermissionCode  errors.ErrorCode = "INVALID_PERMISSION_CODE"
)

// Role error codes
const (
    ErrCodeRoleNotFound           errors.ErrorCode = "ROLE_NOT_FOUND"
    ErrCodeRoleAlreadyExists      errors.ErrorCode = "ROLE_ALREADY_EXISTS"
    ErrCodeRoleInUse              errors.ErrorCode = "ROLE_IN_USE"
    ErrCodeCannotDeleteSystemRole errors.ErrorCode = "CANNOT_DELETE_SYSTEM_ROLE"
    ErrCodeCannotModifySystemRole errors.ErrorCode = "CANNOT_MODIFY_SYSTEM_ROLE"
    ErrCodeInvalidRoleHierarchy   errors.ErrorCode = "INVALID_ROLE_HIERARCHY"
    ErrCodeCircularRoleHierarchy  errors.ErrorCode = "CIRCULAR_ROLE_HIERARCHY"
    ErrCodeRoleLevelTooHigh       errors.ErrorCode = "ROLE_LEVEL_TOO_HIGH"
)

// Policy error codes
const (
    ErrCodePolicyNotFound         errors.ErrorCode = "POLICY_NOT_FOUND"
    ErrCodePolicyAlreadyExists    errors.ErrorCode = "POLICY_ALREADY_EXISTS"
    ErrCodeInvalidPolicyRule      errors.ErrorCode = "INVALID_POLICY_RULE"
    ErrCodePolicyRuleConflict     errors.ErrorCode = "POLICY_RULE_CONFLICT"
    ErrCodeCannotDeleteSystemPolicy errors.ErrorCode = "CANNOT_DELETE_SYSTEM_POLICY"
)

// Assignment error codes
const (
    ErrCodeUserRoleNotFound       errors.ErrorCode = "USER_ROLE_NOT_FOUND"
    ErrCodeUserRoleAlreadyExists  errors.ErrorCode = "USER_ROLE_ALREADY_EXISTS"
    ErrCodeCannotAssignHigherRole errors.ErrorCode = "CANNOT_ASSIGN_HIGHER_ROLE"
    ErrCodeCannotRemoveLastRole   errors.ErrorCode = "CANNOT_REMOVE_LAST_ROLE"
    ErrCodeMaxRolesExceeded       errors.ErrorCode = "MAX_ROLES_EXCEEDED"
)
```

## 4. **Mã lỗi Blog Module**

```go
// modules/blog/errors/codes.go
package errors

import "myapp/internal/pkg/errors"

// Post error codes
const (
    ErrCodePostNotFound          errors.ErrorCode = "POST_NOT_FOUND"
    ErrCodePostAlreadyExists     errors.ErrorCode = "POST_ALREADY_EXISTS"
    ErrCodePostSlugAlreadyExists errors.ErrorCode = "POST_SLUG_ALREADY_EXISTS"
    ErrCodePostNotPublished      errors.ErrorCode = "POST_NOT_PUBLISHED"
    ErrCodePostAlreadyPublished  errors.ErrorCode = "POST_ALREADY_PUBLISHED"
    ErrCodeCannotDeletePublishedPost errors.ErrorCode = "CANNOT_DELETE_PUBLISHED_POST"
    ErrCodeInvalidPostStatus     errors.ErrorCode = "INVALID_POST_STATUS"
)

// Category error codes
const (
    ErrCodeCategoryNotFound      errors.ErrorCode = "CATEGORY_NOT_FOUND"
    ErrCodeCategoryAlreadyExists errors.ErrorCode = "CATEGORY_ALREADY_EXISTS"
    ErrCodeCategoryInUse         errors.ErrorCode = "CATEGORY_IN_USE"
    ErrCodeInvalidCategoryHierarchy errors.ErrorCode = "INVALID_CATEGORY_HIERARCHY"
)

// Tag error codes
const (
    ErrCodeTagNotFound           errors.ErrorCode = "TAG_NOT_FOUND"
    ErrCodeTagAlreadyExists      errors.ErrorCode = "TAG_ALREADY_EXISTS"
    ErrCodeTooManyTags           errors.ErrorCode = "TOO_MANY_TAGS"
)

// Comment error codes
const (
    ErrCodeCommentNotFound       errors.ErrorCode = "COMMENT_NOT_FOUND"
    ErrCodeCommentNotAllowed     errors.ErrorCode = "COMMENT_NOT_ALLOWED"
    ErrCodeCommentSpamDetected   errors.ErrorCode = "COMMENT_SPAM_DETECTED"
    ErrCodeCommentTooLong        errors.ErrorCode = "COMMENT_TOO_LONG"
)
```

## 5. **Error Messages Mapping**

```go
// internal/pkg/errors/messages.go
package errors

import "fmt"

var ErrorMessages = map[ErrorCode]map[string]string{
    // Common errors
    ErrCodeUnknown: {
        "en": "An unknown error occurred",
        "vi": "Đã xảy ra lỗi không xác định",
    },
    ErrCodeValidationFailed: {
        "en": "Validation failed",
        "vi": "Dữ liệu không hợp lệ",
    },
    ErrCodeUnauthorized: {
        "en": "Unauthorized access",
        "vi": "Truy cập không được phép",
    },
    
    // User errors
    "USER_NOT_FOUND": {
        "en": "User not found",
        "vi": "Không tìm thấy người dùng",
    },
    "EMAIL_ALREADY_EXISTS": {
        "en": "Email address already exists",
        "vi": "Địa chỉ email đã tồn tại",
    },
    "INVALID_CREDENTIALS": {
        "en": "Invalid email or password",
        "vi": "Email hoặc mật khẩu không đúng",
    },
    "ACCOUNT_LOCKED": {
        "en": "Account is locked due to too many failed login attempts",
        "vi": "Tài khoản đã bị khóa do đăng nhập sai quá nhiều lần",
    },
    
    // RBAC errors
    "INSUFFICIENT_PERMISSION": {
        "en": "You don't have permission to perform this action",
        "vi": "Bạn không có quyền thực hiện hành động này",
    },
    "ROLE_NOT_FOUND": {
        "en": "Role not found",
        "vi": "Không tìm thấy vai trò",
    },
}

func GetMessage(code ErrorCode, lang string) string {
    if messages, exists := ErrorMessages[code]; exists {
        if message, exists := messages[lang]; exists {
            return message
        }
        // Fallback to English
        if message, exists := messages["en"]; exists {
            return message
        }
    }
    return fmt.Sprintf("Error: %s", string(code))
}
```

## 6. **Enhanced Error Handler**

```go
// internal/pkg/errors/errors.go
package errors

import (
    "fmt"
    "net/http"
)

type AppError struct {
    Code       ErrorCode              `json:"code"`
    Message    string                 `json:"message"`
    Details    string                 `json:"details,omitempty"`
    Fields     map[string]string      `json:"fields,omitempty"` // For validation errors
    HTTPStatus int                    `json:"-"`
    Internal   error                  `json:"-"`
    UserID     *int64                 `json:"-"`
    TenantID   *int64                 `json:"-"`
    RequestID  string                 `json:"-"`
}

func (e *AppError) Error() string {
    return fmt.Sprintf("Code: %s, Message: %s", e.Code, e.Message)
}

// Factory functions
func New(code ErrorCode, lang string) *AppError {
    return &AppError{
        Code:       code,
        Message:    GetMessage(code, lang),
        HTTPStatus: getHTTPStatus(code),
    }
}

func NewWithDetails(code ErrorCode, lang string, details string) *AppError {
    return &AppError{
        Code:       code,
        Message:    GetMessage(code, lang),
        Details:    details,
        HTTPStatus: getHTTPStatus(code),
    }
}

func NewValidation(code ErrorCode, lang string, fields map[string]string) *AppError {
    return &AppError{
        Code:       code,
        Message:    GetMessage(code, lang),
        Fields:     fields,
        HTTPStatus: http.StatusBadRequest,
    }
}

func getHTTPStatus(code ErrorCode) int {
    switch code {
    case ErrCodeUnauthorized, "INVALID_CREDENTIALS", "SESSION_EXPIRED", "INVALID_TOKEN":
        return http.StatusUnauthorized
    case ErrCodeForbidden, "INSUFFICIENT_PERMISSION", "ACCOUNT_LOCKED", "ACCOUNT_SUSPENDED":
        return http.StatusForbidden
    case ErrCodeNotFound, "USER_NOT_FOUND", "ROLE_NOT_FOUND", "POST_NOT_FOUND":
        return http.StatusNotFound
    case ErrCodeConflict, "EMAIL_ALREADY_EXISTS", "USERNAME_ALREADY_EXISTS", "ROLE_ALREADY_EXISTS":
        return http.StatusConflict
    case ErrCodeValidationFailed, "WEAK_PASSWORD", "INVALID_PROFILE_DATA":
        return http.StatusBadRequest
    case "TOO_MANY_LOGIN_ATTEMPTS", ErrCodeRateLimit:
        return http.StatusTooManyRequests
    default:
        return http.StatusInternalServerError
    }
}
```

## 7. **Sử dụng trong Service**

```go
// modules/user/service/user_service.go
package service

import (
    "context"
    "database/sql"
    
    commonErrors "myapp/internal/pkg/errors"
    userErrors "myapp/modules/user/errors"
)

func (s *UserService) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
    // Validate input
    if err := s.validator.Validate(req); err != nil {
        return nil, commonErrors.NewValidation(
            commonErrors.ErrCodeValidationFailed, 
            "en", 
            err.Fields,
        )
    }

    // Get user by email
    user, err := s.repo.GetByEmail(ctx, req.Email)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, commonErrors.New(userErrors.ErrCodeInvalidCredentials, "en")
        }
        return nil, commonErrors.Wrap(commonErrors.ErrCodeDBQueryFailed, "Failed to get user", err)
    }

    // Check account status
    switch user.Status {
    case UserStatusLocked:
        return nil, commonErrors.New(userErrors.ErrCodeAccountLocked, "en")
    case UserStatusSuspended:
        return nil, commonErrors.New(userErrors.ErrCodeAccountSuspended, "en")
    case UserStatusInactive:
        return nil, commonErrors.New(userErrors.ErrCodeAccountInactive, "en")
    }

    // Verify password
    if !s.passwordService.Verify(req.Password, user.PasswordHash) {
        // Increment failed attempts
        s.handleFailedLogin(ctx, user.ID)
        return nil, commonErrors.New(userErrors.ErrCodeInvalidCredentials, "en")
    }

    // Generate session
    session, err := s.sessionService.Create(ctx, user.ID, req.DeviceInfo)
    if err != nil {
        return nil, commonErrors.NewWithDetails(
            userErrors.ErrCodeSessionNotFound,
            "en", 
            "Failed to create session",
        )
    }

    return &LoginResponse{
        AccessToken:  session.AccessToken,
        RefreshToken: session.RefreshToken,
        User:         user,
    }, nil
}
```

**Ưu điểm của cách này:**

✅ **Dễ hiểu**: Mã lỗi mô tả rõ ràng vấn đề  
✅ **Dễ debug**: Developer biết ngay lỗi gì  
✅ **Dễ maintain**: Không cần nhớ số code  
✅ **Internationalization**: Hỗ trợ đa ngôn ngữ  
✅ **Consistent**: Chuẩn hóa trong toàn hệ thống  
✅ **Self-documenting**: Code tự document chính nó