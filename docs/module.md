Tôi sẽ viết lại tài liệu tập trung vào cấu trúc và quy tắc để AI dễ dàng tạo module mới:

# Hướng dẫn Tạo Module Mới

## Cấu trúc Module Chuẩn

```
modules/auth/
├── bootstrap.go                 # Khởi tạo dependencies và đăng ký routes
├── module.go                    # Định nghĩa và đăng ký module
├── internal/                    # Định nghĩa cấu hình và các kiểu dữ liệu nội bộ
│   ├── config.go                # Xử lý cấu hình từ biến môi trường
│   └── types.go                 # Định nghĩa types, interfaces và xử lý lỗi
├── dto/                         # Data Transfer Objects
│   ├── auth.go                  # DTO cơ bản
│   ├── login.go                 # DTO đăng nhập
│   └── register.go              # DTO đăng ký
├── repository/                  # Implement repository interfaces
│   ├── repository.go            # Triển khai repository với SQL thuần
│   └── mysql/                   # (<PERSON><PERSON><PERSON> chọ<PERSON>) Triển khai chi tiết cho MySQL
│       └── auth_repository.go   # Triển khai MySQL
├── service/                     # Implement service interfaces
│   └── auth_service.go          # Triển khai service
├── api/                         # API handlers
│   ├── routes.go                # Định nghĩa routes và Handler chính
│   └── handlers/                # Xử lý HTTP requests
│       └── admin_auth_handler.go # Handler xử lý auth
└── migrations/                  # SQL migrations
    ├── 001_create_users.up.sql           # Migration tạo bảng
    └── 001_create_users.down.sql         # Migration rollback
```

## Giải thích Chi Tiết Từng Thư Mục

### 1. File `bootstrap.go`
**Nhiệm vụ**: Đăng ký routes và khởi tạo dependencies.

**Ví dụ**:
```go
package auth

import (
	"wnapi/internal/core"
	"wnapi/modules/auth/api"
)

// registerRoutes đăng ký các route của module auth
func registerRoutes(server *core.Server, handler *api.Handler) error {
	return handler.RegisterRoutes(server)
}
```

### 2. File `module.go`
**Nhiệm vụ**: Định nghĩa và đăng ký module với hệ thống core.

**Ví dụ**:
```go
package auth

import (
	"context"
	"path/filepath"
	"time"

	"wnapi/internal/core"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/api"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/repository"
	"wnapi/modules/auth/service"
)

func init() {
	core.RegisterModuleFactory("auth", NewModule)
}

// Module triển khai auth module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Khởi tạo repository
	repo, err := repository.NewMySQLRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	// Đọc cấu hình từ biến môi trường
	authConfig, err := internal.LoadAuthConfig()
	if err != nil {
		logger.Warn("Không thể đọc cấu hình từ biến môi trường, sử dụng giá trị mặc định: %v", err)
		// Khởi tạo cấu hình mặc định
	}

	// Khởi tạo service và handler
	authService := service.NewService(repo, *authConfig, logger)
	handler := api.NewHandler(authService)

	return &Module{
		name:    "auth",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
	}, nil
}

// Implement các method bắt buộc: Name(), Init(), RegisterRoutes(), Cleanup(), GetMigrationPath(), GetMigrationOrder()
```

### 3. Thư mục `internal/`
**Nhiệm vụ**: Chứa cấu hình và các kiểu dữ liệu nội bộ của module.

#### File `config.go`
**Ví dụ**:
```go
package internal

import (
	"fmt"
	"log"
	"os"

	"github.com/caarlos0/env/v11"
	"github.com/joho/godotenv"
)

// LoadAuthConfig đọc cấu hình auth từ biến môi trường
func LoadAuthConfig() (*AuthConfig, error) {
	// Tải file .env nếu có
	if _, err := os.Stat(".env"); err == nil {
		if err := godotenv.Load(); err != nil {
			log.Printf("Cảnh báo: không thể tải file .env: %v", err)
		}
	}

	// Khởi tạo config mặc định
	cfg := &AuthConfig{
		JWTSecret:          "default_jwt_secret_change_me_in_production",
		AccessTokenExpiry:  15 * time.Minute,
		RefreshTokenExpiry: 168 * time.Hour,
		Message:            "Xin chào từ module Auth!",
	}

	// Đọc cấu hình từ biến môi trường với prefix AUTH_
	opts := env.Options{
		Prefix: "AUTH_",
	}
	if err := env.ParseWithOptions(cfg, opts); err != nil {
		return nil, fmt.Errorf("lỗi đọc cấu hình auth từ biến môi trường: %w", err)
	}

	return cfg, nil
}
```

#### File `types.go`
**Ví dụ**:
```go
package internal

import (
	"context"
	"net/http"
	"time"
	"wnapi/modules/auth/dto"
)

// AuthConfig chứa cấu hình auth service
type AuthConfig struct {
	JWTSecret          string        `yaml:"jwt_secret" env:"JWT_SECRET"`
	AccessTokenExpiry  time.Duration `yaml:"access_token_expiry" env:"ACCESS_TOKEN_EXPIRY" envDefault:"15m"`
	RefreshTokenExpiry time.Duration `yaml:"refresh_token_expiry" env:"REFRESH_TOKEN_EXPIRY" envDefault:"168h"`
	Message            string        `env:"MESSAGE" envDefault:"Xin chào từ module Auth!"`
}

// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	// ErrInvalidCredentials là lỗi khi thông tin đăng nhập không hợp lệ
	ErrInvalidCredentials ServiceError = "invalid_credentials"
	// ErrUserAlreadyExists là lỗi khi user đã tồn tại
	ErrUserAlreadyExists ServiceError = "user_already_exists"
	// ErrInvalidToken là lỗi khi token không hợp lệ
	ErrInvalidToken ServiceError = "invalid_token"
	// ErrExpiredToken là lỗi khi token đã hết hạn
	ErrExpiredToken ServiceError = "expired_token"
	// ErrUserNotFound là lỗi khi không tìm thấy user
	ErrUserNotFound ServiceError = "user_not_found"
	// ErrTokenNotFound là lỗi khi không tìm thấy token
	ErrTokenNotFound ServiceError = "token_not_found"
	// ErrInvalidPassword là lỗi khi mật khẩu không hợp lệ
	ErrInvalidPassword ServiceError = "invalid_password"
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrInvalidCredentials: {
		StatusCode: http.StatusUnauthorized,
		Message:    "Thông tin đăng nhập không hợp lệ",
		ErrorCode:  "INVALID_CREDENTIALS",
	},
	ErrUserAlreadyExists: {
		StatusCode: http.StatusConflict,
		Message:    "Người dùng đã tồn tại",
		ErrorCode:  "USER_ALREADY_EXISTS",
	},
	// Các lỗi khác...
}

func (e ServiceError) Error() string {
	return string(e)
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}
	
	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}

// AuthService định nghĩa interface cho auth service
type AuthService interface {
	Register(ctx context.Context, req dto.RegisterRequest) (*UserInfo, error)
	Login(ctx context.Context, req dto.LoginRequest) (*dto.LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*dto.LoginResponse, error)
	ValidateToken(tokenString string) (map[string]interface{}, error)
}

// Các định nghĩa khác...
```

### 4. Thư mục `dto/`
**Nhiệm vụ**: Định nghĩa các Data Transfer Objects (DTOs) để giao tiếp API.

**Ví dụ** (`login.go`):
```go
package dto

// LoginRequest chứa thông tin đăng nhập
type LoginRequest struct {
	Email      string `json:"email" binding:"required,email"`
	Password   string `json:"password" binding:"required"`
	AdminLogin bool   `json:"-"` // Field nội bộ, không xuất hiện trong JSON
}

// LoginResponse chứa thông tin trả về sau khi đăng nhập
type LoginResponse struct {
	AccessToken           string `json:"access_token"`
	AccessTokenExpiresIn  int    `json:"access_token_expires_in"`
	RefreshToken          string `json:"refresh_token"`
	RefreshTokenExpiresIn int    `json:"refresh_token_expires_in"`
	TokenType             string `json:"token_type"`
	UserID                int64  `json:"user_id"`
	Email                 string `json:"email"`
	TenantID              int    `json:"tenant_id"`
}
```

### 5. Thư mục `repository/`
**Nhiệm vụ**: Triển khai các repository interfaces để tương tác với cơ sở dữ liệu.

**Ví dụ** (`repository.go`):
```go
package repository

import (
	"context"
	"database/sql"
	"errors"
	"time"

	"wnapi/internal/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/internal"

	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	"go.opentelemetry.io/otel/attribute"
	"golang.org/x/crypto/bcrypt"
)

// mysqlRepository triển khai Repository interface sử dụng sqlx
type mysqlRepository struct {
	db     *sqlx.DB
	logger logger.Logger
}

// NewMySQLRepository tạo một repository mới
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
	if dbManager == nil {
		return nil, errors.New("database manager không được để trống")
	}

	db := dbManager.GetDB()
	if db == nil {
		return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
	}

	return &mysqlRepository{
		db:     db,
		logger: logger,
	}, nil
}

// GetUserByEmail lấy thông tin người dùng theo email
func (r *mysqlRepository) GetUserByEmail(ctx context.Context, email string) (*internal.User, error) {
	// Tạo span cho truy vấn database
	ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "auth_users")
	defer span.End()

	// Thêm thông tin về operation
	tracing.AddSpanAttributes(ctx,
		attribute.String("db.table", "auth_users"),
		attribute.String("db.operation", "get_user_by_email"),
		attribute.String("auth.email", email),
	)

	var user internal.User
	query := `SELECT id, username, email, password_hash, full_name, is_active, created_at, updated_at 
              FROM auth_users WHERE email = ?`

	tracing.AddSpanAttributes(ctx, tracing.DBStatement(query))

	err := r.db.GetContext(ctx, &user, query, email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			tracing.AddSpanAttributes(ctx, attribute.Bool("db.record_found", false))
			return nil, internal.ErrUserNotFound
		}
		r.logger.Error("Không thể lấy thông tin người dùng", logger.String("error", err.Error()))
		tracing.RecordError(ctx, err)
		return nil, err
	}

	tracing.AddSpanAttributes(ctx,
		attribute.Bool("db.record_found", true),
		attribute.Int("auth.user_id", user.ID),
		attribute.String("auth.username", user.Username),
	)

	return &user, nil
}

// Các phương thức khác...
```

### 6. Thư mục `service/`
**Nhiệm vụ**: Triển khai các service interfaces, chứa business logic.

**Ví dụ** (`auth_service.go`):
```go
package service

import (
	"context"
	"errors"
	"fmt"
	"time"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"

	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"golang.org/x/crypto/bcrypt"
)

// Service triển khai AuthService interface
type Service struct {
	repo   internal.Repository
	config internal.AuthConfig
	logger logger.Logger
}

// NewService tạo một auth service mới
func NewService(repo internal.Repository, config internal.AuthConfig, log logger.Logger) internal.AuthService {
	// Đặt giá trị mặc định
	if config.AccessTokenExpiry == 0 {
		config.AccessTokenExpiry = 15 * time.Minute
	}
	if config.RefreshTokenExpiry == 0 {
		config.RefreshTokenExpiry = 7 * 24 * time.Hour // 7 ngày
	}

	return &Service{
		repo:   repo,
		config: config,
		logger: log,
	}
}

// Login xác thực người dùng và trả về token
func (s *Service) Login(ctx context.Context, req dto.LoginRequest) (*dto.LoginResponse, error) {
	// Tạo span cho toàn bộ quá trình đăng nhập
	ctx, span := tracing.StartSpan(ctx, "auth-service", "login")
	defer span.End()

	// Thêm thuộc tính vào span (che dấu thông tin nhạy cảm)
	tracing.AddSpanAttributes(ctx,
		attribute.String("auth.email", req.Email),
		attribute.Bool("auth.admin_login", req.AdminLogin),
	)

	// Lấy user từ database bằng email
	var user *internal.User
	err := tracing.WithSpan(ctx, "auth-service", "get_user_by_email", func(ctx context.Context) error {
		var err error
		user, err = s.repo.GetUserByEmail(ctx, req.Email)
		if err != nil {
			if errors.Is(err, internal.ErrUserNotFound) {
				tracing.AddSpanAttributes(ctx, attribute.String("auth.error", "user_not_found"))
				return internal.ErrInvalidCredentials
			}
			s.logger.Error("Failed to get user", logger.String("error", err.Error()))
			return err
		}
		return nil
	})

	if err != nil {
		tracing.RecordError(ctx, err)
		return nil, err
	}

	// Logic xử lý khác...

	return &dto.LoginResponse{
		// Dữ liệu trả về...
	}, nil
}

// Các phương thức khác...
```

### 7. Thư mục `api/`
**Nhiệm vụ**: Xử lý HTTP requests, định nghĩa API endpoints.

#### File `routes.go`
**Ví dụ**:
```go
package api

import (
	"fmt"
	"wnapi/internal/core"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/api/handlers"
	"wnapi/modules/auth/internal"

	"github.com/gin-gonic/gin"
)

// Handler là đối tượng chính xử lý API cho module Auth
type Handler struct {
	adminAuthHandler *handlers.AdminAuthHandler
	routes           []string
}

// NewHandler tạo một handler mới
func NewHandler(authService internal.AuthService) *Handler {
	return &Handler{
		adminAuthHandler: handlers.NewAdminAuthHandler(authService),
		routes:           make([]string, 0),
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Auth
func (h *Handler) RegisterRoutes(server *core.Server) error {
	// API Group
	apiGroup := server.Group("/api/v1/auth")

	// Thêm middleware tracing cho tất cả các route auth
	apiGroup.Use(tracing.GinMiddleware("auth"))

	// Lưu lại danh sách các route để hiển thị
	basePath := "/api/v1/auth"

	// Health check endpoint
	apiGroup.GET("/healthy", h.healthCheck)
	h.routes = append(h.routes, fmt.Sprintf("GET %s/healthy", basePath))

	// Không yêu cầu xác thực
	apiGroup.POST("/login", h.adminAuthHandler.Login)
	h.routes = append(h.routes, fmt.Sprintf("POST %s/login", basePath))

	// Các routes khác...

	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "auth",
		"message": "Auth module is running",
	})
}
```

#### File `handlers/admin_auth_handler.go`
**Ví dụ**:
```go
package handlers

import (
	"net/http"

	"wnapi/internal/pkg/response"
	"wnapi/internal/pkg/tracing"
	"wnapi/modules/auth/dto"
	"wnapi/modules/auth/internal"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
)

// AdminAuthHandler xử lý HTTP requests cho admin auth
type AdminAuthHandler struct {
	authService internal.AuthService
}

// NewAdminAuthHandler tạo handler mới
func NewAdminAuthHandler(authService internal.AuthService) *AdminAuthHandler {
	return &AdminAuthHandler{authService: authService}
}

// Login xử lý request đăng nhập
func (h *AdminAuthHandler) Login(c *gin.Context) {
	// Bắt đầu span cho operation login
	ctx, span := tracing.StartGinSpan(c, "auth", "login")
	defer span.End()

	var req dto.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Ghi lại lỗi vào span
		tracing.RecordGinError(c, err)
		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", "invalid_request"))

		details := []interface{}{map[string]string{"message": err.Error()}}
		response.BadRequest(c, "Định dạng yêu cầu không hợp lệ", "INVALID_REQUEST", details)
		return
	}

	// Thêm tham số để chỉ định rằng đây là đăng nhập admin
	req.AdminLogin = true

	resp, err := h.authService.Login(ctx, req)
	if err != nil {
		// Ghi lại lỗi và phân loại lỗi trong span
		tracing.RecordGinError(c, err)
		
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		tracing.AddGinSpanAttributes(c, attribute.String("auth.error_type", errResp.ErrorCode))
		
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}

	// Đánh dấu span là thành công
	tracing.SetSpanStatus(ctx, codes.Ok, "Login successful")
	tracing.AddGinSpanAttributes(c,
		attribute.Int64("auth.user_id", resp.UserID),
		attribute.Bool("auth.success", true),
	)
	tracing.AddGinSpanEvent(c, "login_success")

	response.Success(c, resp, nil)
}

// Các phương thức khác...
```

### 8. Thư mục `migrations/`
**Nhiệm vụ**: Chứa các script SQL migration để quản lý schema cơ sở dữ liệu.

**Ví dụ** (`001_create_users.up.sql`):
```sql
CREATE TABLE auth_users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Ví dụ** (`001_create_users.down.sql`):
```sql
DROP TABLE IF EXISTS auth_users;
```

## Quy tắc quan trọng

### 1. Cấu hình từ biến môi trường
- Sử dụng thư viện `github.com/caarlos0/env/v11` để đọc cấu hình từ biến môi trường
- Định nghĩa cấu trúc config với tag `env` để map với biến môi trường
- Sử dụng prefix (như `AUTH_`) để tránh xung đột với các biến môi trường khác
- Đặt giá trị mặc định qua tag `envDefault`

### 2. Tên Module
- Sử dụng snake_case cho tên thư mục
- Sử dụng camelCase cho tên package Go

### 3. Database
- Luôn sử dụng `INT UNSIGNED` cho ID
- Luôn có `created_at` và `updated_at`
- Sử dụng `utf8mb4` và `utf8mb4_unicode_ci`
- Tên bảng nên có prefix là tên module (ví dụ: `auth_users`, `auth_sessions`)

### 4. Xử lý lỗi và Response Format
#### 4.1 Định nghĩa lỗi
- Định nghĩa lỗi trong package internal sử dụng `ServiceError`
- Định nghĩa bảng ánh xạ `ErrorMap` để kết nối `ServiceError` với HTTP status code và mã lỗi
- Sử dụng `GetErrorResponse()` để chuyển đổi lỗi thành cấu trúc phản hồi chuẩn

```go
// ServiceError định nghĩa các lỗi service
type ServiceError string

const (
	ErrInvalidCredentials ServiceError = "invalid_credentials"
	ErrUserAlreadyExists ServiceError = "user_already_exists"
	// Các lỗi khác...
)

// ErrorResponse định nghĩa cấu trúc phản hồi lỗi
type ErrorResponse struct {
	StatusCode int    // HTTP status code
	Message    string // Thông báo lỗi hiển thị cho người dùng
	ErrorCode  string // Mã lỗi nội bộ
}

// ErrorMap ánh xạ ServiceError với thông tin phản hồi lỗi tương ứng
var ErrorMap = map[ServiceError]ErrorResponse{
	ErrInvalidCredentials: {
		StatusCode: http.StatusUnauthorized,
		Message:    "Thông tin đăng nhập không hợp lệ",
		ErrorCode:  "INVALID_CREDENTIALS",
	},
	// Các lỗi khác...
}

// GetErrorResponse trả về thông tin phản hồi lỗi dựa trên ServiceError
func GetErrorResponse(err error) ErrorResponse {
	if serviceErr, ok := err.(ServiceError); ok {
		if resp, exists := ErrorMap[serviceErr]; exists {
			return resp
		}
	}
	
	// Mặc định trả về lỗi hệ thống nếu không tìm thấy lỗi trong map
	return ErrorResponse{
		StatusCode: http.StatusInternalServerError,
		Message:    "Lỗi hệ thống",
		ErrorCode:  "INTERNAL_ERROR",
	}
}
```

#### 4.2 Sử dụng trong Handler

```go
func (h *AdminAuthHandler) Login(c *gin.Context) {
	// Xử lý logic...
	
	resp, err := h.authService.Login(ctx, req)
	if err != nil {
		// Lấy thông tin phản hồi lỗi từ định nghĩa trong internal
		errResp := internal.GetErrorResponse(err)
		
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, errResp.StatusCode, errResp.Message, errResp.ErrorCode, details)
		return
	}
	
	// Trả về thành công
	response.Success(c, resp, nil)
}
```

#### 4.3 Cấu trúc phản hồi
```json
// Success
{
    "status": {
        "code": 200,
        "message": "Operation completed successfully",
        "success": true,
        "error_code": null,
        "path": "/api/auth/login",
        "timestamp": "2024-03-15T14:35:22Z"
    },
    "data": {} // hoặc []
}

// Error
{
    "status": {
        "code": 400,
        "message": "Error message",
        "success": false,
        "error_code": "ERROR_CODE",
        "path": "/api/auth/login",
        "timestamp": "2024-03-15T14:35:22Z",
        "details": [
            {
                "field": "email",
                "message": "Email không hợp lệ"
            }
        ]
    }
}
```

### 5. Logging và Tracing
- Sử dụng logger từ app
- Sử dụng OpenTelemetry để tracing
- Thêm middleware tracing cho các routes
- Thêm span cho các operations quan trọng

```go
// Tạo span cho truy vấn database
ctx, span := tracing.DatabaseMiddleware(ctx, "SELECT", "auth_users")
defer span.End()

// Thêm thông tin về operation
tracing.AddSpanAttributes(ctx,
	attribute.String("db.table", "auth_users"),
	attribute.String("db.operation", "get_user_by_email"),
	attribute.String("auth.email", email),
)

// Ghi lại lỗi trong span
tracing.RecordError(ctx, err)
```

### 6. Context
- Luôn truyền context trong các method
- Sử dụng context để cancel operations
- Truyền context giữa các layers (handler → service → repository)

### 7. Migration
- Đặt tên file theo định dạng: `[số thứ tự]_[mô tả].[up/down].sql`
- Số thứ tự bắt đầu từ 001 và tăng dần
- Luôn có cả file up và down
- Đảm bảo tên bảng có prefix là tên module

### 8. Bảo mật
- Không hard-code secret keys, sử dụng biến môi trường
- Sử dụng bcrypt để hash mật khẩu
- Validate input từ user
- Che giấu thông tin nhạy cảm trong logs và tracing (passwords, tokens)

### 9. Validation
- Sử dụng tags trong struct để validate dữ liệu đầu vào
- Xử lý lỗi validation và trả về thông báo phù hợp
- Sử dụng i18n cho thông báo lỗi

### 10. File .env và .env.example
- Tạo file `.env.example` trong thư mục dự án để làm mẫu
- Không commit file `.env` chứa thông tin nhạy cảm
- Sử dụng định dạng sau:

```
# Auth Module Configuration
AUTH_JWT_SECRET=your_very_secure_jwt_secret_key_here
AUTH_ACCESS_TOKEN_EXPIRY=15m
AUTH_REFRESH_TOKEN_EXPIRY=168h
AUTH_MESSAGE=Xin chào từ module Auth!
```

Với cấu trúc và quy tắc này, bạn có thể dễ dàng tạo một module mới tuân thủ các tiêu chuẩn của hệ thống.

## Chuyển đổi Repository từ sqlx sang GORM

### Tổng quan
GORM là một ORM (Object Relational Mapping) phổ biến cho Go, giúp đơn giản hóa các thao tác với cơ sở dữ liệu. Việc chuyển đổi từ sqlx sang GORM giúp code dễ đọc hơn, giảm thiểu lỗi và tận dụng các tính năng mạnh mẽ của GORM.

### Các bước chuyển đổi

#### 1. Cập nhật Model
- Thay đổi tag `db` thành tag `gorm`
- Thêm các tùy chọn như `column`, `primaryKey`, `autoIncrement`, `index`, `default`, v.v.

**Ví dụ**:
```go
// Trước
type User struct {
    UserID          uint       `db:"user_id" json:"user_id"`
    Email           string     `db:"email" json:"email"`
    PasswordHash    string     `db:"password_hash" json:"-"`
    CreatedAt       time.Time  `db:"created_at" json:"created_at"`
}

// Sau
type User struct {
    UserID          uint       `gorm:"column:user_id;primaryKey;autoIncrement" json:"user_id"`
    Email           string     `gorm:"column:email;uniqueIndex" json:"email"`
    PasswordHash    string     `gorm:"column:password_hash" json:"-"`
    CreatedAt       time.Time  `gorm:"column:created_at;autoCreateTime" json:"created_at"`
}
```

#### 2. Cập nhật Repository struct
- Đổi tên struct để phản ánh công nghệ mới (tùy chọn)
- Thay `*sqlx.DB` hoặc `*sql.DB` bằng `*gorm.DB`
- Thêm `logger` để ghi log lỗi

**Ví dụ**:
```go
// Trước
type mysqlRepository struct {
    db     *sqlx.DB
}

// Sau
type gormRepository struct {
    db     *gorm.DB
    logger logger.Logger
}
```

#### 3. Cập nhật constructor
- Chuyển đổi từ sqlx.DB sang gorm.DB
- Thêm tham số logger

**Ví dụ**:
```go
// Trước
func NewEmailVerificationRepository(db *sqlx.DB) repository.EmailVerificationRepository {
    return &SQLxEmailVerificationRepository{db: db}
}

// Sau
func NewEmailVerificationRepository(db *gorm.DB, logger logger.Logger) repository.EmailVerificationRepository {
    return &GormEmailVerificationRepository{
        db:     db,
        logger: logger,
    }
}
```

#### 4. Tạo kết nối GORM từ sqlx.DB
Nếu hệ thống vẫn đang sử dụng sqlx.DB ở cấp cao hơn, bạn cần tạo kết nối GORM từ sqlx:

```go
func NewMySQLRepository(dbManager *database.Manager, logger logger.Logger) (internal.Repository, error) {
    if dbManager == nil {
        return nil, errors.New("database manager không được để trống")
    }

    sqlxDB := dbManager.GetDB()
    if sqlxDB == nil {
        return nil, errors.New("không thể kết nối tới cơ sở dữ liệu")
    }

    // Create GORM DB from sqlx DB
    gormDB, err := gorm.Open(mysql.New(mysql.Config{
        Conn: sqlxDB.DB,
    }), &gorm.Config{})
    if err != nil {
        return nil, fmt.Errorf("failed to initialize GORM: %w", err)
    }

    return &mysqlRepository{
        db:     gormDB,
        logger: logger,
    }, nil
}
```

#### 5. Chuyển đổi các phương thức CRUD

**a. Create**

```go
// Trước (sqlx)
query := `INSERT INTO users (email, password_hash) VALUES (:email, :password_hash)`
_, err := r.db.NamedExecContext(ctx, query, user)

// Sau (GORM)
result := r.db.WithContext(ctx).Create(user)
if result.Error != nil {
    r.logger.Error("Không thể tạo user", logger.String("error", result.Error.Error()))
    return result.Error
}
```

**b. Read**

```go
// Trước (sqlx)
query := `SELECT * FROM users WHERE id = ?`
var user User
err := r.db.GetContext(ctx, &user, query, id)
if err == sql.ErrNoRows {
    return nil, ErrNotFound
}

// Sau (GORM)
var user User
result := r.db.WithContext(ctx).Where("id = ?", id).First(&user)
if result.Error != nil {
    if errors.Is(result.Error, gorm.ErrRecordNotFound) {
        return nil, ErrNotFound
    }
    r.logger.Error("Không thể lấy user", logger.String("error", result.Error.Error()))
    return nil, result.Error
}
```

**c. Update**

```go
// Trước (sqlx)
query := `UPDATE users SET email = :email, updated_at = :updated_at WHERE id = :id`
_, err := r.db.NamedExecContext(ctx, query, user)

// Sau (GORM)
// Phương pháp 1: Cập nhật toàn bộ bản ghi
result := r.db.WithContext(ctx).Save(user)

// Phương pháp 2: Cập nhật các trường cụ thể
result := r.db.WithContext(ctx).Model(&User{}).
    Where("id = ?", user.ID).
    Updates(map[string]interface{}{
        "email":      user.Email,
        "updated_at": time.Now(),
    })
```

**d. Delete**

```go
// Trước (sqlx)
query := `DELETE FROM users WHERE id = ?`
_, err := r.db.ExecContext(ctx, query, id)

// Sau (GORM)
result := r.db.WithContext(ctx).Delete(&User{}, id)
```

#### 6. Xử lý lỗi với GORM

```go
if result.Error != nil {
    if errors.Is(result.Error, gorm.ErrRecordNotFound) {
        // Xử lý khi không tìm thấy bản ghi
        return nil, ErrNotFound
    }
    // Log lỗi
    r.logger.Error("Lỗi truy vấn cơ sở dữ liệu", logger.String("error", result.Error.Error()))
    // Trả về lỗi có context
    return nil, fmt.Errorf("failed to get user: %w", result.Error)
}
```

### Ví dụ cụ thể: Chuyển đổi Email Verification Repository

#### Trước (SQLx)

```go
// SQLxEmailVerificationRepository là cài đặt MySQL cho EmailVerificationRepository
type SQLxEmailVerificationRepository struct {
    db *sqlx.DB
}

// Create lưu token xác thực email vào database
func (r *SQLxEmailVerificationRepository) Create(ctx context.Context, verification *models.EmailVerification) error {
    now := time.Now()
    verification.CreatedAt = now
    verification.UpdatedAt = now

    query := `
        INSERT INTO auth_email_verifications (
            user_id, email, token, verified, expires_at, created_at, updated_at
        ) VALUES (
            :user_id, :email, :token, :verified, :expires_at, :created_at, :updated_at
        )
    `

    _, err := r.db.NamedExecContext(ctx, query, verification)
    if err != nil {
        return fmt.Errorf("failed to create email verification: %w", err)
    }

    return nil
}
```

#### Sau (GORM)

```go
// GormEmailVerificationRepository là cài đặt MySQL cho EmailVerificationRepository sử dụng GORM
type GormEmailVerificationRepository struct {
    db     *gorm.DB
    logger logger.Logger
}

// Create lưu token xác thực email vào database
func (r *GormEmailVerificationRepository) Create(ctx context.Context, verification *models.EmailVerification) error {
    now := time.Now()
    verification.CreatedAt = now
    verification.UpdatedAt = now

    result := r.db.WithContext(ctx).Create(verification)
    if result.Error != nil {
        r.logger.Error("Không thể tạo xác thực email", logger.String("error", result.Error.Error()))
        return fmt.Errorf("failed to create email verification: %w", result.Error)
    }

    return nil
}
```

### Lưu ý quan trọng
1. Luôn sử dụng `WithContext(ctx)` để truyền context qua các lớp
2. Sử dụng `errors.Is` để so sánh lỗi
3. Ghi log lỗi chi tiết
4. Bổ sung các tag gorm phù hợp trong model
5. Khi cần truy vấn phức tạp, có thể vẫn sử dụng raw SQL với GORM:
   ```go
   r.db.WithContext(ctx).Raw("SELECT * FROM users WHERE age > ?", 18).Scan(&users)
   ```

### Ưu điểm khi chuyển sang GORM
1. Code ngắn gọn, dễ đọc hơn
2. Giảm thiểu lỗi liên quan đến SQL
3. Tự động xử lý map giữa struct và bảng dữ liệu
4. Các tính năng mạnh mẽ như hooks, preload, relationship
5. Dễ dàng mở rộng và bảo trì
