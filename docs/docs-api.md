# Product API (Bruno format)

## 1. <PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm (List)

**GET** `/api/v1/products`

### Request
- Method: GET
- URL: `/api/v1/products?limit=10&cursor=...`
- Headers:
  - `Authorization: Bearer <token>`

### Response (200)
```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/v1/products",
    "timestamp": "2025-03-15T14:35:22Z",
    "details": null
  },
  "data": [
    {
      "product_id": 1,
      "name": "Áo thun",
      "price": 199000
    }
  ],
  "meta": {
    "next_cursor": "abc123",
    "has_more": true
  }
}
```

---

## 2. <PERSON><PERSON><PERSON> chi tiết sản phẩm (Detail)

**GET** `/api/v1/products/{id}`

### Request
- Method: GET
- URL: `/api/v1/products/1`
- Headers:
  - `Authorization: Bearer <token>`

### Response (200)
```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/v1/products/1",
    "timestamp": "2025-03-15T14:35:22Z",
    "details": null
  },
  "data": {
    "product_id": 1,
    "name": "Áo thun",
    "price": 199000
  },
  "meta": null
}
```

---

## 3. Tạo sản phẩm (Create)

**POST** `/api/v1/products`

### Request
- Method: POST
- URL: `/api/v1/products`
- Headers:
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- Body:
```json
{
  "name": "Áo thun",
  "price": 199000
}
```

### Response (201)
```json
{
  "status": {
    "code": 201,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/v1/products",
    "timestamp": "2025-03-15T14:35:22Z",
    "details": null
  },
  "data": {
    "product_id": 2,
    "name": "Áo thun",
    "price": 199000
  },
  "meta": null
}
```

---

## 4. Cập nhật sản phẩm (Update)

**PUT** `/api/v1/products/{id}`

### Request
- Method: PUT
- URL: `/api/v1/products/1`
- Headers:
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- Body:
```json
{
  "name": "Áo thun tay dài",
  "price": 249000
}
```

### Response (200)
```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/v1/products/1",
    "timestamp": "2025-03-15T14:35:22Z",
    "details": null
  },
  "data": {
    "product_id": 1,
    "name": "Áo thun tay dài",
    "price": 249000
  },
  "meta": null
}
```

---

## 5. Xóa sản phẩm (Delete)

**DELETE** `/api/v1/products/{id}`

### Request
- Method: DELETE
- URL: `/api/v1/products/1`
- Headers:
  - `Authorization: Bearer <token>`

### Response (200)
```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/v1/products/1",
    "timestamp": "2025-03-15T14:35:22Z",
    "details": null
  },
  "data": null,
  "meta": null
}
```

---

## 6. Ví dụ lỗi (Error response)

### Response (409)
```json
{
  "status": {
    "code": 409,
    "message": "Product already exists",
    "success": false,
    "error_code": "PRODUCT_ALREADY_EXISTS",
    "path": "/api/v1/products",
    "timestamp": "2025-03-15T14:30:45Z",
    "details": [
      {
        "field": "name",
        "message": "Sản phẩm đã tồn tại"
      }
    ]
  }
}
```
