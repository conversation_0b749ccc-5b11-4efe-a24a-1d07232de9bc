# API Reference WNAPI

## Tổng Quan

WNAPI cung cấp RESTful API với kiến trúc multi-tenant. Tất cả API endpoints đều yêu cầu tenant context và hầu hết cần authentication.

## Base URL
```
http://localhost:8080/api
```

## Authentication

### Headers Required
```
Authorization: Bearer <jwt_token>
X-Tenant-ID: <tenant_id>
Content-Type: application/json
```

### Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "errors": null
}
```

## Auth Module APIs

### Register
```http
POST /auth/register
Content-Type: application/json

{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "secure_password",
  "full_name": "<PERSON>e",
  "tenant_id": 1
}
```

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
    "user": {
      "user_id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "full_name": "John Doe"
    }
  }
}
```

### Refresh Token
```http
POST /auth/refresh
Authorization: Bearer <refresh_token>
```

### Logout
```http
POST /auth/logout
Authorization: Bearer <access_token>
```

### Forgot Password
```http
POST /auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### Reset Password
```http
POST /auth/reset-password
Content-Type: application/json

{
  "token": "reset_token",
  "new_password": "new_secure_password"
}
```

## User Module APIs

### Get Current User
```http
GET /auth/profile
Authorization: Bearer <access_token>
```

### Update Profile
```http
PUT /auth/profile
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "full_name": "John Smith",
  "phone": "+84123456789"
}
```

## RBAC Module APIs

### Roles Management

#### List Roles
```http
GET /rbac/roles?cursor=&limit=10
Authorization: Bearer <access_token>
```

#### Create Role
```http
POST /rbac/roles
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "role_code": "content_editor",
  "role_name": "Content Editor",
  "role_description": "Can create and edit content"
}
```

#### Get Role
```http
GET /rbac/roles/{role_id}
Authorization: Bearer <access_token>
```

#### Update Role
```http
PUT /rbac/roles/{role_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "role_name": "Senior Content Editor",
  "role_description": "Can create, edit and publish content"
}
```

#### Delete Role
```http
DELETE /rbac/roles/{role_id}
Authorization: Bearer <access_token>
```

### Permissions Management

#### List Permissions
```http
GET /rbac/permissions?cursor=&limit=10
Authorization: Bearer <access_token>
```

#### Create Permission
```http
POST /rbac/permissions
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "permission_code": "products.create",
  "permission_name": "Create Products",
  "permission_description": "Ability to create new products",
  "group_id": 1
}
```

### User Role Assignment

#### Assign Role to User
```http
POST /rbac/user-roles
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "user_id": 1,
  "role_id": 2
}
```

#### Get User Roles
```http
GET /rbac/users/{user_id}/roles
Authorization: Bearer <access_token>
```

#### Revoke Role from User
```http
DELETE /rbac/user-roles
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "user_id": 1,
  "role_id": 2
}
```

## Product Module APIs

### List Products
```http
GET /products?cursor=&limit=10&category_id=1
Authorization: Bearer <access_token>
```

### Create Product
```http
POST /products
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "iPhone 15",
  "description": "Latest iPhone model",
  "price": 999.99,
  "category_id": 1,
  "sku": "IPH15-001"
}
```

### Get Product
```http
GET /products/{product_id}
Authorization: Bearer <access_token>
```

### Update Product
```http
PUT /products/{product_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "iPhone 15 Pro",
  "price": 1199.99
}
```

### Delete Product
```http
DELETE /products/{product_id}
Authorization: Bearer <access_token>
```

## Cart Module APIs

### Get Cart
```http
GET /cart
Authorization: Bearer <access_token>
```

### Add to Cart
```http
POST /cart/items
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "product_id": 1,
  "quantity": 2
}
```

### Update Cart Item
```http
PUT /cart/items/{item_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "quantity": 3
}
```

### Remove from Cart
```http
DELETE /cart/items/{item_id}
Authorization: Bearer <access_token>
```

### Clear Cart
```http
DELETE /cart
Authorization: Bearer <access_token>
```

## Media Module APIs

### Upload File
```http
POST /media/upload
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

file: <binary_file>
```

### Get File
```http
GET /media/{file_id}
Authorization: Bearer <access_token>
```

### Delete File
```http
DELETE /media/{file_id}
Authorization: Bearer <access_token>
```

## Notification Module APIs

### Get Notifications
```http
GET /notifications?cursor=&limit=10&read=false
Authorization: Bearer <access_token>
```

### Mark as Read
```http
PUT /notifications/{notification_id}/read
Authorization: Bearer <access_token>
```

### Mark All as Read
```http
PUT /notifications/read-all
Authorization: Bearer <access_token>
```

### Get Unread Count
```http
GET /notifications/unread-count
Authorization: Bearer <access_token>
```

## Error Responses

### 400 Bad Request
```json
{
  "success": false,
  "data": null,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "success": false,
  "data": null,
  "message": "Authentication required",
  "errors": [
    {
      "code": "AUTHENTICATION_REQUIRED",
      "message": "Valid JWT token is required"
    }
  ]
}
```

### 403 Forbidden
```json
{
  "success": false,
  "data": null,
  "message": "Permission denied",
  "errors": [
    {
      "code": "PERMISSION_DENIED",
      "message": "Insufficient permissions to access this resource"
    }
  ]
}
```

### 404 Not Found
```json
{
  "success": false,
  "data": null,
  "message": "Resource not found",
  "errors": [
    {
      "code": "RESOURCE_NOT_FOUND",
      "message": "The requested resource does not exist"
    }
  ]
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "data": null,
  "message": "Internal server error",
  "errors": [
    {
      "code": "INTERNAL_ERROR",
      "message": "An unexpected error occurred"
    }
  ]
}
```
