# Hướng Dẫn Phát Triển WNAPI

Tài liệu này cung cấp hướng dẫn chi tiết về cách phát triển và mở rộng framework WNAPI.

## Cài Đặt Môi Trường Phát Triển

### Yêu <PERSON>

- Go 1.19+ ([Tải Go](https://golang.org/dl/))
- MySQL 8.0+ hoặc MariaDB ([Tải MySQL](https://dev.mysql.com/downloads/))
- Git ([Tải Git](https://git-scm.com/downloads))
- Docker & Docker Compose (tùy chọn, [T<PERSON><PERSON> Docker](https://www.docker.com/get-started))

### Cài Đặt

```bash
# Clone repository
git clone https://github.com/yourusername/wnapi.git
cd wnapi

# Cài đặt dependencies
go mod download

# Tạo thư mục cần thiết
mkdir -p build logs projects
```

## Khởi Động Dự Án

### Sử Dụng Makefile

```bash
# Chạy ứng dụng
make run

# Hoặc chạy với project cụ thể
make run PROJECT=sample
```

### Sử Dụng Docker

```bash
# Khởi động containers
docker-compose up -d

# Xem logs
docker-compose logs -f
```

## Cấu Trúc Code

### Các Nguyên Tắc Cơ Bản

- **Mô-đun hóa**: Mỗi chức năng phải được tách thành module riêng
- **Tách biệt các tầng**: API, domain, repository
- **Cấu hình linh hoạt**: Cấu hình từ file YAML và biến môi trường
- **Dependency Injection**: Inject dependencies qua constructor

### Phân Chia Code Theo Tầng

Mỗi module nên được tổ chức thành các tầng sau:

1. **API Layer** (`api/`): REST handlers, request/response, routing
2. **Domain Layer** (`domain/`): Business logic, entities, services
3. **Repository Layer** (`repository/`): Database access, data persistence
4. **DTO** (`dto/`): Data Transfer Objects, validation

## Phát Triển Module

### Tạo Module Mới

Sử dụng lệnh Makefile để tạo cấu trúc module:

```bash
make create-module name=user
```

Hoặc tạo thủ công:

```bash
mkdir -p modules/user/{api,domain,dto,repository,migrations}
touch modules/user/module.go
```

### Cấu Trúc Module

Một module hoàn chỉnh nên bao gồm:

1. **module.go**: Định nghĩa và đăng ký module
2. **api/handler.go**: Xử lý HTTP requests
3. **api/router.go**: Định nghĩa routes
4. **domain/service.go**: Business logic
5. **service/entity.go**: Entities và service objects
6. **repository/repository.go**: Interface repository
7. **repository/mysql_repository.go**: MySQL implementation
8. **dto/request.go**: Cấu trúc request
9. **dto/response.go**: Cấu trúc response
10. **migrations/**: Các file migration

### Quy Trình Phát Triển Module

1. Định nghĩa service entities và DTOs
2. Tạo repository interfaces và implementations
3. Xây dựng service layer với business logic
4. Tạo API handlers và routes
5. Đăng ký với App trong module.go
6. Tạo migrations để khởi tạo database
7. Cập nhật cấu hình trong project configs

## Phát Triển Plugin

### Tạo Plugin Mới

Sử dụng lệnh Makefile:

```bash
make create-plugin name=cache
```

Hoặc tạo thủ công:

```bash
mkdir -p plugins/cache
touch plugins/cache/plugin.go
```

### Cấu Trúc Plugin

Một plugin hoàn chỉnh nên bao gồm:

1. **plugin.go**: Định nghĩa và đăng ký plugin
2. Các file triển khai chức năng

### Quy Trình Phát Triển Plugin

1. Định nghĩa interface và kiểu dữ liệu
2. Cài đặt logic
3. Đăng ký plugin trong `init()`
4. Cập nhật cấu hình trong project configs

## Tạo và Quản Lý Projects

Mỗi project là một tập hợp cấu hình cho phép chạy ứng dụng với các module và plugin cụ thể.

### Tạo Project Mới

```bash
mkdir -p projects/my_project
cp projects/sample/config.yaml projects/my_project/
cp projects/sample/modules.yaml projects/my_project/
```

### Cấu Trúc Project

- **config.yaml**: Cấu hình chính
- **modules.yaml**: Cấu hình chi tiết về modules

### Cấu hình Project

Chỉnh sửa `config.yaml` để định nghĩa:

1. Thông tin ứng dụng (tên, phiên bản)
2. Cấu hình server (host, port)
3. Danh sách modules được kích hoạt
4. Cấu hình plugins
5. Cấu hình database

## Database Migrations

### Tạo Migration Mới

```bash
make migrate-create name=create_users_table
```

### Chạy Migration

```bash
# Chạy migration mặc định
make migrate

# Chạy migration cho project cụ thể
make migrate PROJECT=sample
```

## Testing

### Unit Tests

```bash
# Chạy tất cả tests
make test

# Chạy test với coverage
make test-coverage
```

### Cách Viết Test

- Tạo file test có tên dạng `xxx_test.go`
- Sử dụng standard testing package hoặc testify
- Mock external dependencies
- Test tất cả các tầng: repository, service, api

## Quy Ước Coding

### Đặt Tên

- **Packages**: Lowercase, một từ (api, domain, repository)
- **Files**: Snake case (user_service.go, mysql_repository.go)
- **Structs/Interfaces**: Pascal case (UserService, Repository)
- **Functions/Methods**: Pascal case (GetByID, CreateUser)
- **Variables**: Camel case (userID, errorMsg)

### Tổ Chức Code

- Một file nên chỉ chứa một type chính
- Giới hạn mỗi package cho một trách nhiệm cụ thể
- Sử dụng interface để tách biệt các tầng
- Comments cho exported functions, types, constants

## Xử Lý Lỗi

- Sử dụng custom errors trong `internal/pkg/errors`
- Wrap errors để giữ context
- Log errors với level phù hợp
- Trả về error codes và messages rõ ràng cho client

## Logging

- Sử dụng interface logger trong `internal/pkg/logger`
- Chọn log level phù hợp (DEBUG, INFO, WARN, ERROR)
- Include context trong log messages
- Cấu hình log format và destination trong config

## Performance Tuning

- Sử dụng connection pooling cho database
- Cache kết quả truy vấn khi thích hợp
- Sử dụng goroutines cho xử lý bất đồng bộ
- Monitoring và profiling với pprof 