# Task 03: Triển khai MiddlewareFactory

## M<PERSON><PERSON> ti<PERSON>u
<PERSON> dựng `MiddlewareFactory` - m<PERSON><PERSON> thành phần trung tâm của hệ thống phân quyền, chị<PERSON> tr<PERSON>ch nhiệm tạo ra các middleware để kiểm tra quyền cho các route.

## C<PERSON><PERSON> bước thực hiện

1. Triển khai đầy đủ file `factory.go` trong package `internal/pkg/permission`
2. Tạo constructor và các phương thức middleware
3. Triển khai logic kiểm tra quyền trong các middleware
4. <PERSON><PERSON><PERSON> hợ<PERSON> với hệ thống lỗi đã được định nghĩa trong Task 02

## Chi tiết triển khai

### 1. Triển khai file factory.go

```go
// internal/pkg/permission/factory.go
package permission

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/auth"   // <PERSON><PERSON><PERSON> package auth cung cấp các hàm GetUserIDFromContext, GetTenantIDFromContext
	"wnapi/internal/pkg/logger" // G<PERSON><PERSON> đ<PERSON>nh có package logger
)

// MiddlewareFactory tạo ra các middleware functions để kiểm tra quyền.
// Nó implement PermissionMiddleware interface.
type MiddlewareFactory struct {
	checker PermissionChecker // Checker này có thể là CachedPermissionChecker hoặc implementation khác
	logger  logger.Logger
}

// NewMiddlewareFactory là constructor cho MiddlewareFactory.
func NewMiddlewareFactory(checker PermissionChecker, log logger.Logger) *MiddlewareFactory {
	return &MiddlewareFactory{
		checker: checker,
		logger:  log,
	}
}

// RequirePermission tạo middleware kiểm tra một quyền cụ thể.
func (mf *MiddlewareFactory) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok {
			return // Lỗi đã được xử lý trong extractAuthContext
		}

		hasPermission, err := mf.checker.UserHasPermission(c.Request.Context(), tenantID, userID, permission)
		if err != nil {
			mf.logger.ErrorContext(c.Request.Context(), "Lỗi kiểm tra quyền (UserHasPermission)",
				"error", err, "user_id", userID, "tenant_id", tenantID, "permission", permission)
			AbortWithPermissionCheckError(c, err)
			return
		}

		if !hasPermission {
			mf.logger.WarnContext(c.Request.Context(), "Từ chối quyền truy cập",
				"user_id", userID, "tenant_id", tenantID, "required_permission", permission, "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, permission)
			return
		}
		c.Next()
	}
}

// RequireAnyPermission tạo middleware kiểm tra người dùng có ít nhất một trong các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAnyPermission(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(permissions) == 0 {
			mf.logger.InfoContext(c.Request.Context(), "Không có quyền nào được yêu cầu trong RequireAnyPermission, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}
		
		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok { 
			return 
		}

		hasAny, err := mf.checker.UserHasAnyPermission(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.ErrorContext(c.Request.Context(), "Lỗi kiểm tra quyền (UserHasAnyPermission)", 
				"error", err, "user_id", userID, "tenant_id", tenantID)
			AbortWithPermissionCheckError(c, err)
			return
		}
		if !hasAny {
			mf.logger.WarnContext(c.Request.Context(), "Từ chối quyền truy cập (UserHasAnyPermission)",
				"user_id", userID, "tenant_id", tenantID, "required_permissions", fmt.Sprintf("%v", permissions), "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, fmt.Sprintf("any of: %v", permissions))
			return
		}
		c.Next()
	}
}

// RequireAllPermissions tạo middleware kiểm tra người dùng có tất cả các quyền được liệt kê.
func (mf *MiddlewareFactory) RequireAllPermissions(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if len(permissions) == 0 {
			mf.logger.InfoContext(c.Request.Context(), "Không có quyền nào được yêu cầu trong RequireAllPermissions, cho qua.",
				"path", c.Request.URL.Path)
			c.Next()
			return
		}
		
		userID, tenantID, ok := mf.extractAuthContext(c)
		if !ok { 
			return 
		}

		hasAll, err := mf.checker.UserHasAllPermissions(c.Request.Context(), tenantID, userID, permissions)
		if err != nil {
			mf.logger.ErrorContext(c.Request.Context(), "Lỗi kiểm tra quyền (UserHasAllPermissions)", 
				"error", err, "user_id", userID, "tenant_id", tenantID)
			AbortWithPermissionCheckError(c, err)
			return
		}
		if !hasAll {
			mf.logger.WarnContext(c.Request.Context(), "Từ chối quyền truy cập (UserHasAllPermissions)",
				"user_id", userID, "tenant_id", tenantID, "required_permissions", fmt.Sprintf("%v", permissions), "path", c.Request.URL.Path)
			AbortWithPermissionDenied(c, fmt.Sprintf("all of: %v", permissions))
			return
		}
		c.Next()
	}
}

// extractAuthContext là helper method để lấy UserID và TenantID từ Gin context.
func (mf *MiddlewareFactory) extractAuthContext(c *gin.Context) (userID uint, tenantID uint, ok bool) {
	uID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		mf.logger.WarnContext(c.Request.Context(), "Không tìm thấy UserID trong context", "path", c.Request.URL.Path)
		AbortWithAuthRequired(c)
		return 0, 0, false
	}
	
	tID, err := auth.GetTenantIDFromContext(c)
	if err != nil {
		mf.logger.WarnContext(c.Request.Context(), "Không tìm thấy TenantID trong context", 
			"error", err, "path", c.Request.URL.Path)
		AbortWithTenantRequired(c)
		return 0, 0, false
	}
	
	return uID, tID, true
}
```

### 2. Chú ý tích hợp với package auth

File này phụ thuộc vào package `auth` để lấy thông tin người dùng từ context. Cần đảm bảo các hàm sau tồn tại hoặc được tạo:

- `auth.GetUserIDFromContext(c *gin.Context) (uint, bool)`: Lấy ID người dùng đã xác thực
- `auth.GetTenantIDFromContext(c *gin.Context) (uint, error)`: Lấy ID tenant từ context

Nếu các hàm này hoạt động khác hoặc chưa tồn tại, cần điều chỉnh code để phù hợp.

### 3. Tạo stub cho `internal/pkg/auth/auth.go` (nếu cần)

Nếu package `auth` chưa có các hàm này, cần tạo stub:

```go
// internal/pkg/auth/auth.go
package auth

import (
	"fmt"
	"github.com/gin-gonic/gin"
)

// Key dùng để lưu userID và tenantID trong context
const (
	UserIDKey   = "user_id"
	TenantIDKey = "tenant_id"
)

// GetUserIDFromContext lấy user ID từ Gin context
func GetUserIDFromContext(c *gin.Context) (uint, bool) {
	id, exists := c.Get(UserIDKey)
	if !exists {
		return 0, false
	}
	
	userID, ok := id.(uint)
	if !ok {
		return 0, false
	}
	
	return userID, true
}

// GetTenantIDFromContext lấy tenant ID từ Gin context
func GetTenantIDFromContext(c *gin.Context) (uint, error) {
	id, exists := c.Get(TenantIDKey)
	if !exists {
		return 0, fmt.Errorf("tenant ID không tồn tại trong context")
	}
	
	tenantID, ok := id.(uint)
	if !ok {
		return 0, fmt.Errorf("tenant ID không phải kiểu uint")
	}
	
	return tenantID, nil
}
```

## Kết quả mong đợi
- `MiddlewareFactory` được triển khai đầy đủ
- Các middleware kiểm tra quyền hoạt động đúng
- Tích hợp tốt với hệ thống lỗi
- Dự án vẫn biên dịch và chạy được sau khi triển khai

## Thời gian dự kiến
- 3-4 giờ làm việc

## Độ ưu tiên
- Cao (Đây là thành phần trung tâm của hệ thống phân quyền)

## Phụ thuộc
- Phải hoàn thành Task 01 và Task 02 trước
- Cần có các package liên quan (auth, logger) hoặc tạo stub cho chúng 