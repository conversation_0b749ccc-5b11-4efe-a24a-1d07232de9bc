# Task 04: Triển khai Cache cho Permission

## M<PERSON><PERSON> tiêu
Xây dựng lớp cache cho hệ thống permission để tối ưu hiệu năng khi kiểm tra quyền, gi<PERSON>m tải cho database trong các trường hợp kiểm tra quyền lặp lại.

## <PERSON><PERSON><PERSON> bước thực hiện

1. Triển khai đầy đủ file `cache.go` trong package `internal/pkg/permission`
2. Xây dựng `CachedPermissionChecker` decorator
3. T<PERSON>ch hợp với hệ thống cache của ứng dụng
4. <PERSON><PERSON> cấp phương thức invalidate cache khi quyền thay đổi

## Chi tiết triển khai

### 1. Triển khai file cache.go

```go
// internal/pkg/permission/cache.go
package permission

import (
	"context"
	"fmt"
	"strings"
	"time"

	"wnapi/internal/pkg/cache"   // <PERSON><PERSON><PERSON> định có interface cache.Cache
	"wnapi/internal/pkg/logger"
)

// CachedPermissionChecker implement PermissionChecker và thêm lớp caching.
type CachedPermissionChecker struct {
	delegate PermissionChecker // PermissionChecker thực tế (ví dụ: truy vấn DB)
	cache    cache.Cache
	ttl      time.Duration
	logger   logger.Logger
}

// NewCachedPermissionChecker là constructor cho CachedPermissionChecker.
// `checker` ở đây là un-cached version (ví dụ: RBACPermissionService).
func NewCachedPermissionChecker(
	checker PermissionChecker,
	c cache.Cache,
	ttl time.Duration,
	log logger.Logger,
) *CachedPermissionChecker {
	return &CachedPermissionChecker{
		delegate: checker,
		cache:    c,
		ttl:      ttl,
		logger:   log,
	}
}

func (cpc *CachedPermissionChecker) buildCacheKey(tenantID uint, userID uint, permissionCode string) string {
	normalizedPermissionCode := strings.ReplaceAll(strings.ToLower(permissionCode), " ", "_")
	return fmt.Sprintf("perm_cache:%d:%d:%s", tenantID, userID, normalizedPermissionCode)
}

// UserHasPermission kiểm tra quyền, ưu tiên lấy từ cache.
func (cpc *CachedPermissionChecker) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	cacheKey := cpc.buildCacheKey(tenantID, userID, permissionCode)
	var cachedResult bool
	
	// Thử lấy kết quả từ cache
	if val, err := cpc.cache.Get(ctx, cacheKey); err == nil {
		if bVal, ok := val.(bool); ok {
			cpc.logger.DebugContext(ctx, "Cache hit for permission", "key", cacheKey)
			return bVal, nil
		}
		cpc.logger.WarnContext(ctx, "Cache data type mismatch", "key", cacheKey)
	} else if !cpc.cache.IsErrCacheMiss(err) { // Giả định cache interface có hàm này
        cpc.logger.ErrorContext(ctx, "Lỗi khi lấy permission từ cache", "key", cacheKey, "error", err)
    } else {
         cpc.logger.DebugContext(ctx, "Cache miss for permission", "key", cacheKey)
    }

	// Gọi delegate để lấy kết quả thực tế
	hasPermission, err := cpc.delegate.UserHasPermission(ctx, tenantID, userID, permissionCode)
	if err != nil {
		return false, err
	}
	
	// Lưu kết quả vào cache để lần sau sử dụng
	if errSet := cpc.cache.Set(ctx, cacheKey, hasPermission, cpc.ttl); errSet != nil {
		cpc.logger.ErrorContext(ctx, "Lỗi khi lưu permission vào cache", "key", cacheKey, "error", errSet)
	}
	
	return hasPermission, nil
}

// UserHasAnyPermission sử dụng UserHasPermission (đã được cache).
func (cpc *CachedPermissionChecker) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 { 
		return true, nil 
	}
	
	for _, p := range permissions {
		has, err := cpc.UserHasPermission(ctx, tenantID, userID, p)
		if err != nil { 
			return false, fmt.Errorf("lỗi kiểm tra quyền '%s': %w", p, err) 
		}
		if has { 
			return true, nil 
		}
	}
	
	return false, nil
}

// UserHasAllPermissions sử dụng UserHasPermission (đã được cache).
func (cpc *CachedPermissionChecker) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 { 
		return true, nil 
	}
	
	for _, p := range permissions {
		has, err := cpc.UserHasPermission(ctx, tenantID, userID, p)
		if err != nil { 
			return false, fmt.Errorf("lỗi kiểm tra quyền '%s': %w", p, err) 
		}
		if !has { 
			return false, nil 
		}
	}
	
	return true, nil
}

// InvalidateUserPermissions xóa cache permission của user.
func (cpc *CachedPermissionChecker) InvalidateUserPermissions(ctx context.Context, tenantID uint, userID uint) error {
	pattern := fmt.Sprintf("perm_cache:%d:%d:*", tenantID, userID)
	cpc.logger.InfoContext(ctx, "Invalidating user permissions cache", "pattern", pattern)
	return cpc.cache.DeletePattern(ctx, pattern) // Giả sử cache interface có hàm này
}

// InvalidateAllPermissions xóa tất cả cache permission.
func (cpc *CachedPermissionChecker) InvalidateAllPermissions(ctx context.Context) error {
	pattern := "perm_cache:*"
	cpc.logger.InfoContext(ctx, "Invalidating all permissions cache", "pattern", pattern)
	return cpc.cache.DeletePattern(ctx, pattern)
}
```

### 2. Chú ý tích hợp với cache interface

Code này giả định có một `cache.Cache` interface với các phương thức như sau:

```go
// Giả định interface cho cache.Cache
type Cache interface {
	Get(ctx context.Context, key string) (interface{}, error)
	Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error
	Delete(ctx context.Context, key string) error
	DeletePattern(ctx context.Context, pattern string) error
	IsErrCacheMiss(err error) bool
}
```

Nếu interface cache của ứng dụng khác, cần điều chỉnh code để phù hợp.

### 3. Tạo stub cho cache interface (nếu cần)

Nếu ứng dụng chưa có cache interface hoặc cần một bản đơn giản cho development, tạo một in-memory cache stub:

```go
// internal/pkg/cache/memory_cache.go
package cache

import (
	"context"
	"errors"
	"sync"
	"time"
)

var ErrCacheMiss = errors.New("cache miss")

type cacheItem struct {
	value      interface{}
	expiration time.Time
}

// MemoryCache là implementation đơn giản cho Cache sử dụng memory
type MemoryCache struct {
	items map[string]cacheItem
	mu    sync.RWMutex
}

// NewMemoryCache tạo một in-memory cache mới
func NewMemoryCache() *MemoryCache {
	return &MemoryCache{
		items: make(map[string]cacheItem),
	}
}

// Get lấy giá trị từ cache
func (c *MemoryCache) Get(ctx context.Context, key string) (interface{}, error) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	item, found := c.items[key]
	if !found {
		return nil, ErrCacheMiss
	}
	
	if item.expiration.Before(time.Now()) {
		delete(c.items, key)
		return nil, ErrCacheMiss
	}
	
	return item.value, nil
}

// Set lưu giá trị vào cache
func (c *MemoryCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.items[key] = cacheItem{
		value:      value,
		expiration: time.Now().Add(ttl),
	}
	
	return nil
}

// Delete xóa một key từ cache
func (c *MemoryCache) Delete(ctx context.Context, key string) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	delete(c.items, key)
	return nil
}

// DeletePattern xóa tất cả các key khớp với pattern (đơn giản hóa, chỉ hỗ trợ suffix wildcard)
func (c *MemoryCache) DeletePattern(ctx context.Context, pattern string) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// Giả sử pattern chỉ có dạng "prefix:*"
	if pattern == "*" {
		c.items = make(map[string]cacheItem)
		return nil
	}
	
	prefix := ""
	if len(pattern) > 0 && pattern[len(pattern)-1] == '*' {
		prefix = pattern[:len(pattern)-1]
	} else {
		// Nếu không có wildcard, xử lý như key thông thường
		delete(c.items, pattern)
		return nil
	}
	
	// Xóa tất cả key bắt đầu bằng prefix
	for k := range c.items {
		if len(k) >= len(prefix) && k[:len(prefix)] == prefix {
			delete(c.items, k)
		}
	}
	
	return nil
}

// IsErrCacheMiss kiểm tra xem lỗi có phải là cache miss không
func (c *MemoryCache) IsErrCacheMiss(err error) bool {
	return errors.Is(err, ErrCacheMiss)
}
```

## Kết quả mong đợi
- `CachedPermissionChecker` được triển khai đầy đủ
- Hệ thống cache hoạt động hiệu quả
- Cơ chế invalidate cache được triển khai
- Dự án vẫn biên dịch và chạy được sau khi triển khai

## Thời gian dự kiến
- 3-4 giờ làm việc

## Độ ưu tiên
- Trung bình (Quan trọng cho hiệu năng nhưng có thể triển khai sau khi các chức năng cơ bản hoạt động)

## Phụ thuộc
- Phải hoàn thành Task 01, 02, 03 trước
- Cần có hệ thống cache của ứng dụng hoặc tạo stub cho nó 