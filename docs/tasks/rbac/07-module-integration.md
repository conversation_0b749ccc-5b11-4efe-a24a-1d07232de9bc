# Task 07: <PERSON><PERSON><PERSON> hợ<PERSON> quyền vào <PERSON>le Chức năng

## M<PERSON><PERSON> tiêu
Tích hợp hệ thống phân quyền vào các module chức năng của <PERSON>ng dụng, đ<PERSON><PERSON> bảo các API được bảo vệ phù hợp với quyền hạn của người dùng.

## <PERSON><PERSON><PERSON> bước thực hiện

1. C<PERSON>p nhật handlers của các module để nhận `MiddlewareFactory`
2. Áp dụng middleware phân quyền cho các routes cần bảo vệ
3. <PERSON><PERSON><PERSON> nghĩa các permission chuẩn cho từng module
4. <PERSON><PERSON><PERSON> bảo thứ tự đúng của middleware: Tenant → Auth → Permission

## Chi tiết triển khai

### 1. Cập nhật Handler của Module (Ví dụ: Product)

```go
// modules/product/api/handler.go
package api

import (
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/product/service"

	"github.com/gin-gonic/gin"
)

// ProductHandler xử lý các API của module product
type ProductHandler struct {
	service           service.ProductService
	middlewareFactory *permission.MiddlewareFactory // Thêm MiddlewareFactory
	logger            logger.Logger
}

// NewProductHandler tạo một handler mới
func NewProductHandler(
	svc service.ProductService,
	mwFactory *permission.MiddlewareFactory, // Thêm tham số MiddlewareFactory
	log logger.Logger,
) *ProductHandler {
	return &ProductHandler{
		service:           svc,
		middlewareFactory: mwFactory, // Lưu MiddlewareFactory
		logger:            log,
	}
}

// RegisterRoutes đăng ký routes cho module product
func (h *ProductHandler) RegisterRoutes(router *gin.RouterGroup) {
	// Group cho module product
	productGroup := router.Group("/products")
	
	// Các public endpoints (không cần xác thực và phân quyền)
	productGroup.GET("/public", h.ListPublicProducts)
	
	// Protected routes - yêu cầu xác thực và phân quyền
	// Lưu ý: Thứ tự middleware rất quan trọng - Tenant → Auth → Permission
	// Giả sử tenantMiddleware và authMiddleware đã được áp dụng ở một cấp cao hơn
	
	// API liệt kê sản phẩm - yêu cầu quyền "products.list"
	productGroup.GET("",
		h.middlewareFactory.RequirePermission("products.list"),
		h.ListProducts,
	)
	
	// API lấy chi tiết sản phẩm - yêu cầu quyền "products.read"
	productGroup.GET("/:id", 
		h.middlewareFactory.RequirePermission("products.read"),
		h.GetProduct,
	)
	
	// API tạo sản phẩm - yêu cầu quyền "products.create"
	productGroup.POST("", 
		h.middlewareFactory.RequirePermission("products.create"),
		h.CreateProduct,
	)
	
	// API cập nhật sản phẩm - yêu cầu quyền "products.update"
	productGroup.PUT("/:id", 
		h.middlewareFactory.RequirePermission("products.update"),
		h.UpdateProduct,
	)
	
	// API xóa sản phẩm - yêu cầu quyền "products.delete"
	productGroup.DELETE("/:id", 
		h.middlewareFactory.RequirePermission("products.delete"),
		h.DeleteProduct,
	)
	
	// API đặc biệt - yêu cầu nhiều quyền cùng lúc
	productGroup.PUT("/:id/publish", 
		h.middlewareFactory.RequireAllPermissions("products.update", "products.publish"),
		h.PublishProduct,
	)
	
	// API chỉ yêu cầu một trong các quyền
	productGroup.GET("/stats", 
		h.middlewareFactory.RequireAnyPermission("products.read", "reports.view"),
		h.GetProductStats,
	)
}

// Các handler methods
func (h *ProductHandler) ListProducts(c *gin.Context) {
	// Logic xử lý
}

func (h *ProductHandler) GetProduct(c *gin.Context) {
	// Logic xử lý
}

// ... các handler methods khác
```

### 2. Định nghĩa Permission cho Module (Quy ước)

Tạo file định nghĩa các permission chuẩn của module, giúp tránh "magic strings" và đảm bảo nhất quán:

```go
// modules/product/permission_defs.go
package product

import "wnapi/internal/pkg/permission"

// Module name
const ModuleName = "products"

// Product permissions
const (
	// Standard CRUD permissions
	CreatePermission = ModuleName + permission.PermissionSeparator + permission.ActionCreate
	ReadPermission   = ModuleName + permission.PermissionSeparator + permission.ActionRead
	UpdatePermission = ModuleName + permission.PermissionSeparator + permission.ActionUpdate
	DeletePermission = ModuleName + permission.PermissionSeparator + permission.ActionDelete
	ListPermission   = ModuleName + permission.PermissionSeparator + permission.ActionList
	
	// Special permissions
	PublishPermission = ModuleName + permission.PermissionSeparator + "publish"
	ExportPermission  = ModuleName + permission.PermissionSeparator + "export"
	ImportPermission  = ModuleName + permission.PermissionSeparator + "import"
	
	// Full management permission
	ManagePermission = ModuleName + permission.PermissionSeparator + permission.ActionManage
)

// Helper để lấy tất cả CRUD permissions của module
func GetCRUDPermissions() []string {
	return []string{
		CreatePermission,
		ReadPermission,
		UpdatePermission,
		DeletePermission,
		ListPermission,
	}
}

// Helper để lấy tất cả permissions của module (bao gồm cả special permissions)
func GetAllPermissions() []string {
	return append(
		GetCRUDPermissions(),
		PublishPermission,
		ExportPermission,
		ImportPermission,
		ManagePermission,
	)
}
```

### 3. Sử dụng Permission Definitions trong Handler

Cập nhật handler để sử dụng các hằng số permission:

```go
// modules/product/api/handler.go
import (
	// ... imports khác
	productPerms "wnapi/modules/product" // Import permission definitions
)

// Trong RegisterRoutes:
productGroup.GET("", 
    h.middlewareFactory.RequirePermission(productPerms.ListPermission),
    h.ListProducts,
)

productGroup.POST("", 
    h.middlewareFactory.RequirePermission(productPerms.CreatePermission),
    h.CreateProduct,
)

// ... và tương tự cho các routes khác
```

### 4. Đảm bảo thứ tự middleware

Triển khai thứ tự middleware phù hợp cho router chính của ứng dụng:

```go
// internal/core/server.go hoặc setup routes tương tự
func (s *Server) SetupRoutes() {
	// Root API group
	apiV1 := s.engine.Group("/api/v1")
	
	// Protected routes - yêu cầu xác thực và phân quyền
	protectedRoutes := apiV1.Group("")
	
	// 1. Áp dụng Tenant Middleware
	protectedRoutes.Use(s.tenantMiddleware())
	
	// 2. Áp dụng Authentication Middleware
	protectedRoutes.Use(s.authMiddleware())
	
	// Từ đây, các module sẽ đăng ký routes của chúng và áp dụng permission middleware
	// riêng cho từng endpoint
	s.registerModuleRoutes(protectedRoutes)
}

func (s *Server) registerModuleRoutes(parentRouter *gin.RouterGroup) {
	// Lấy middleware factory từ app instance
	mwFactory := s.app.GetMiddlewareFactory()
	
	// Register Product module routes
	productHandler := s.productHandler // Giả sử đã được khởi tạo trước
	productHandler.RegisterRoutes(parentRouter)
	
	// Register User module routes
	// ... tương tự cho các module khác
}
```

## Kết quả mong đợi
- Các handlers của module được cập nhật để nhận và sử dụng `MiddlewareFactory`
- Routes được bảo vệ phù hợp với permissions
- Permission definitions được tạo và sử dụng nhất quán
- Thứ tự middleware được đảm bảo chính xác
- Dự án vẫn biên dịch và chạy được sau khi triển khai

## Thời gian dự kiến
- 4-5 giờ làm việc (phụ thuộc vào số lượng module cần tích hợp)

## Độ ưu tiên
- Cao (Đây là phần cuối cùng để hoàn thiện hệ thống phân quyền)

## Phụ thuộc
- Phải hoàn thành Task 01, 02, 03, 04, 05, 06 trước
- Phụ thuộc vào cấu trúc hiện tại của các module trong ứng dụng 