# Task 05: Triển khai RBAC Permission Service

## M<PERSON><PERSON> tiêu
<PERSON>y dựng `RBACPermissionService` trong module RBAC để kiểm tra quyền người dùng thông qua database. Service này sẽ implement interface `PermissionChecker` từ package `internal/pkg/permission`.

## C<PERSON><PERSON> bước thực hiện

1. Thiết kế các repository cần thiết trong module RBAC
2. Triển khai `PermissionService` trong module RBAC
3. Đảm bảo service implement đúng interface `PermissionChecker`
4. Triển khai logic kiểm tra quyền qua database

## Chi tiết triển khai

### 1. Định nghĩa các repository cần thiết

```go
// modules/rbac/internal/repository/repository.go
package repository

import (
	"context"

	"wnapi/modules/rbac/models"
)

// UserRoleRepository là interface cho các thao tác liên quan đến vai trò người dùng
type UserRoleRepository interface {
	// GetUserRoles lấy tất cả vai trò của một người dùng trong tenant
	GetUserRoles(ctx context.Context, tenantID uint, userID uint) ([]models.Role, error)
	
	// CheckUserHasRole kiểm tra xem người dùng có vai trò cụ thể không
	CheckUserHasRole(ctx context.Context, tenantID uint, userID uint, roleID uint) (bool, error)
	
	// GetRoleIDs lấy danh sách ID vai trò của người dùng
	GetRoleIDs(ctx context.Context, tenantID uint, userID uint) ([]uint, error)
}

// RolePermissionRepository là interface cho các thao tác liên quan đến quyền của vai trò
type RolePermissionRepository interface {
	// GetRolePermissions lấy tất cả quyền của một vai trò
	GetRolePermissions(ctx context.Context, roleID uint) ([]models.Permission, error)
	
	// CheckRoleHasPermission kiểm tra xem vai trò có quyền cụ thể không
	CheckRoleHasPermission(ctx context.Context, roleID uint, permissionCode string) (bool, error)
	
	// GetPermissionsByRoleIDs lấy tất cả quyền từ danh sách vai trò
	GetPermissionsByRoleIDs(ctx context.Context, roleIDs []uint) ([]models.Permission, error)
}
```

### 2. Triển khai PermissionService

```go
// modules/rbac/internal/service/permission_service.go
package service

import (
	"context"
	"fmt"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/internal/repository"
	"wnapi/modules/rbac/models"
)

// PermissionService triển khai logic kiểm tra quyền
type PermissionService struct {
	userRoleRepo repository.UserRoleRepository
	rolePermRepo repository.RolePermissionRepository
	logger       logger.Logger
}

// NewRBACPermissionService tạo một PermissionService mới
func NewRBACPermissionService(
	userRoleRepo repository.UserRoleRepository,
	rolePermRepo repository.RolePermissionRepository,
	log logger.Logger,
) *PermissionService {
	return &PermissionService{
		userRoleRepo: userRoleRepo,
		rolePermRepo: rolePermRepo,
		logger:       log,
	}
}

// UserHasPermission kiểm tra xem người dùng có quyền cụ thể không
func (s *PermissionService) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	// Lấy tất cả roleIDs của user
	roleIDs, err := s.userRoleRepo.GetRoleIDs(ctx, tenantID, userID)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy vai trò của người dùng: %w", err)
	}
	
	if len(roleIDs) == 0 {
		s.logger.DebugContext(ctx, "Người dùng không có vai trò nào", 
			"user_id", userID, "tenant_id", tenantID)
		return false, nil
	}
	
	// Lấy tất cả permissions của các role
	permissions, err := s.rolePermRepo.GetPermissionsByRoleIDs(ctx, roleIDs)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy quyền của các vai trò: %w", err)
	}
	
	// Kiểm tra xem permissionCode có trong danh sách không
	for _, perm := range permissions {
		if perm.Code == permissionCode {
			return true, nil
		}
	}
	
	s.logger.DebugContext(ctx, "Người dùng không có quyền", 
		"user_id", userID, "tenant_id", tenantID, "permission", permissionCode)
	return false, nil
}

// UserHasAnyPermission kiểm tra xem người dùng có ít nhất một trong các quyền không
func (s *PermissionService) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 {
		return true, nil
	}
	
	// Lấy tất cả roleIDs của user
	roleIDs, err := s.userRoleRepo.GetRoleIDs(ctx, tenantID, userID)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy vai trò của người dùng: %w", err)
	}
	
	if len(roleIDs) == 0 {
		return false, nil
	}
	
	// Lấy tất cả permissions của các role
	userPerms, err := s.rolePermRepo.GetPermissionsByRoleIDs(ctx, roleIDs)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy quyền của các vai trò: %w", err)
	}
	
	// Tạo map cho tìm kiếm nhanh
	permMap := make(map[string]bool)
	for _, perm := range userPerms {
		permMap[perm.Code] = true
	}
	
	// Kiểm tra từng permission trong danh sách
	for _, permCode := range permissions {
		if permMap[permCode] {
			return true, nil
		}
	}
	
	return false, nil
}

// UserHasAllPermissions kiểm tra xem người dùng có tất cả các quyền không
func (s *PermissionService) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	if len(permissions) == 0 {
		return true, nil
	}
	
	// Lấy tất cả roleIDs của user
	roleIDs, err := s.userRoleRepo.GetRoleIDs(ctx, tenantID, userID)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy vai trò của người dùng: %w", err)
	}
	
	if len(roleIDs) == 0 {
		return false, nil
	}
	
	// Lấy tất cả permissions của các role
	userPerms, err := s.rolePermRepo.GetPermissionsByRoleIDs(ctx, roleIDs)
	if err != nil {
		return false, fmt.Errorf("lỗi khi lấy quyền của các vai trò: %w", err)
	}
	
	// Tạo map cho tìm kiếm nhanh
	permMap := make(map[string]bool)
	for _, perm := range userPerms {
		permMap[perm.Code] = true
	}
	
	// Kiểm tra từng permission trong danh sách
	for _, permCode := range permissions {
		if !permMap[permCode] {
			return false, nil
		}
	}
	
	return true, nil
}
```

### 3. Triển khai MySQL Repository (Ví dụ)

```go
// modules/rbac/internal/repository/mysql/user_role_repo.go
package mysql

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/models"

	"gorm.io/gorm"
)

type MySQLUserRoleRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

func NewMySQLUserRoleRepository(db *gorm.DB, log logger.Logger) *MySQLUserRoleRepository {
	return &MySQLUserRoleRepository{
		db:     db,
		logger: log,
	}
}

// GetUserRoles lấy tất cả vai trò của một người dùng trong tenant
func (r *MySQLUserRoleRepository) GetUserRoles(ctx context.Context, tenantID uint, userID uint) ([]models.Role, error) {
	var roles []models.Role
	
	err := r.db.WithContext(ctx).
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND user_roles.tenant_id = ?", userID, tenantID).
		Find(&roles).Error
		
	if err != nil {
		return nil, err
	}
	
	return roles, nil
}

// CheckUserHasRole kiểm tra xem người dùng có vai trò cụ thể không
func (r *MySQLUserRoleRepository) CheckUserHasRole(ctx context.Context, tenantID uint, userID uint, roleID uint) (bool, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&models.UserRole{}).
		Where("user_id = ? AND tenant_id = ? AND role_id = ?", userID, tenantID, roleID).
		Count(&count).Error
		
	if err != nil {
		return false, err
	}
	
	return count > 0, nil
}

// GetRoleIDs lấy danh sách ID vai trò của người dùng
func (r *MySQLUserRoleRepository) GetRoleIDs(ctx context.Context, tenantID uint, userID uint) ([]uint, error) {
	var userRoles []models.UserRole
	
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND tenant_id = ?", userID, tenantID).
		Find(&userRoles).Error
		
	if err != nil {
		return nil, err
	}
	
	roleIDs := make([]uint, len(userRoles))
	for i, ur := range userRoles {
		roleIDs[i] = ur.RoleID
	}
	
	return roleIDs, nil
}
```

```go
// modules/rbac/internal/repository/mysql/role_permission_repo.go
package mysql

import (
	"context"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/rbac/models"

	"gorm.io/gorm"
)

type MySQLRolePermissionRepository struct {
	db     *gorm.DB
	logger logger.Logger
}

func NewMySQLRolePermissionRepository(db *gorm.DB, log logger.Logger) *MySQLRolePermissionRepository {
	return &MySQLRolePermissionRepository{
		db:     db,
		logger: log,
	}
}

// GetRolePermissions lấy tất cả quyền của một vai trò
func (r *MySQLRolePermissionRepository) GetRolePermissions(ctx context.Context, roleID uint) ([]models.Permission, error) {
	var permissions []models.Permission
	
	err := r.db.WithContext(ctx).
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", roleID).
		Find(&permissions).Error
		
	if err != nil {
		return nil, err
	}
	
	return permissions, nil
}

// CheckRoleHasPermission kiểm tra xem vai trò có quyền cụ thể không
func (r *MySQLRolePermissionRepository) CheckRoleHasPermission(ctx context.Context, roleID uint, permissionCode string) (bool, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&models.RolePermission{}).
		Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
		Where("role_permissions.role_id = ? AND permissions.code = ?", roleID, permissionCode).
		Count(&count).Error
		
	if err != nil {
		return false, err
	}
	
	return count > 0, nil
}

// GetPermissionsByRoleIDs lấy tất cả quyền từ danh sách vai trò
func (r *MySQLRolePermissionRepository) GetPermissionsByRoleIDs(ctx context.Context, roleIDs []uint) ([]models.Permission, error) {
	if len(roleIDs) == 0 {
		return []models.Permission{}, nil
	}
	
	var permissions []models.Permission
	
	err := r.db.WithContext(ctx).
		Distinct().
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id IN ?", roleIDs).
		Find(&permissions).Error
		
	if err != nil {
		return nil, err
	}
	
	return permissions, nil
}
```

### 4. Export Service trong Module RBAC

```go
// modules/rbac/service/permission_service.go
package service

import (
	"context"
	
	"wnapi/internal/pkg/permission"
	internalService "wnapi/modules/rbac/internal/service"
)

// Ensure PermissionService implements permission.PermissionChecker
var _ permission.PermissionChecker = (*PermissionService)(nil)

// PermissionService là service được export ra bên ngoài module
type PermissionService struct {
	internal *internalService.PermissionService
}

// NewPermissionService tạo một PermissionService mới
func NewPermissionService(internal *internalService.PermissionService) *PermissionService {
	return &PermissionService{
		internal: internal,
	}
}

// UserHasPermission kiểm tra xem người dùng có quyền cụ thể không
func (s *PermissionService) UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error) {
	return s.internal.UserHasPermission(ctx, tenantID, userID, permissionCode)
}

// UserHasAnyPermission kiểm tra xem người dùng có ít nhất một trong các quyền không
func (s *PermissionService) UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	return s.internal.UserHasAnyPermission(ctx, tenantID, userID, permissions)
}

// UserHasAllPermissions kiểm tra xem người dùng có tất cả các quyền không
func (s *PermissionService) UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error) {
	return s.internal.UserHasAllPermissions(ctx, tenantID, userID, permissions)
}
```

## Kết quả mong đợi
- `RBACPermissionService` được triển khai đầy đủ và implement interface `PermissionChecker`
- Repository cho UserRole và RolePermission hoạt động đúng
- Logic kiểm tra quyền người dùng thông qua database được triển khai
- Dự án vẫn biên dịch và chạy được sau khi triển khai

## Thời gian dự kiến
- 4-5 giờ làm việc

## Độ ưu tiên
- Cao (Đây là thành phần cốt lõi cung cấp logic kiểm tra quyền thực tế)

## Phụ thuộc
- Phải hoàn thành Task 01 và có interface `PermissionChecker`
- Cần có database schema và models cho các bảng RBAC
- Phụ thuộc vào infrastructure của ứng dụng (gorm, logger) 