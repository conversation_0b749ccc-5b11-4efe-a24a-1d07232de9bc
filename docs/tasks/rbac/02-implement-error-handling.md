# Task 02: Triển khai Xử lý Lỗi cho Hệ thống Phân quyền

## M<PERSON><PERSON> tiêu
Xây dựng hệ thống xử lý lỗi chuẩn hóa cho package `internal/pkg/permission` giúp trả về các thông báo lỗi nhất quán khi việc kiểm tra quyền thất bại.

## <PERSON><PERSON><PERSON> bước thực hiện

1. Cập nhật file `errors.go` trong package `internal/pkg/permission`
2. Định nghĩa các loại lỗi domain cụ thể cho hệ thống phân quyền
3. Triển khai các helper function để xử lý lỗi trong middleware
4. <PERSON><PERSON><PERSON> bảo tích hợp với hệ thống response formatter của ứng dụng

## Chi tiết triển khai

### 1. Cập nhật file errors.go

```go
// internal/pkg/permission/errors.go
package permission

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"wnapi/internal/pkg/errors"
	"wnapi/internal/pkg/response"
)

// Domain error types định nghĩa các lỗi domain-specific
var (
	ErrAuthRequired   = errors.NewUnauthorizedError(ErrorCodeAuthRequired, "Yêu cầu xác thực")
	ErrTenantRequired = errors.NewBadRequestError(ErrorCodeTenantRequired, "Không tìm thấy thông tin tenant hoặc tenant không hợp lệ")
	ErrPermissionDenied = errors.NewForbiddenError(ErrorCodePermissionDenied, "Không có quyền truy cập")
	ErrPermissionCheck  = errors.NewInternalError(ErrorCodePermissionCheck, "Lỗi hệ thống khi kiểm tra quyền truy cập")
)

// AbortWithAuthRequired dừng request và trả về lỗi 401 yêu cầu xác thực
func AbortWithAuthRequired(c *gin.Context) {
	response.AbortWithError(c, ErrAuthRequired)
}

// AbortWithTenantRequired dừng request và trả về lỗi yêu cầu tenant
func AbortWithTenantRequired(c *gin.Context) {
	response.AbortWithError(c, ErrTenantRequired)
}

// AbortWithPermissionDenied dừng request và trả về lỗi 403 từ chối quyền truy cập
func AbortWithPermissionDenied(c *gin.Context, permission string) {
	// Tạo lỗi cụ thể với thông tin permission
	permErr := errors.NewForbiddenError(
		ErrorCodePermissionDenied,
		"Không có quyền truy cập",
	)
	
	// Thêm thông tin chi tiết về permission bị từ chối
	permErr = permErr.WithDetails(errors.Detail{
		Field: "required_permission",
		Message: permission,
	})
	
	response.AbortWithError(c, permErr)
}

// AbortWithPermissionCheckError dừng request và trả về lỗi 500 do lỗi hệ thống khi kiểm tra quyền
func AbortWithPermissionCheckError(c *gin.Context, err error) {
	// Bọc lỗi gốc vào domain error
	wrappedErr := errors.WrapError(ErrPermissionCheck, err)
	response.AbortWithError(c, wrappedErr)
}
```

### 2. Tích hợp với hệ thống lỗi hiện có

Trong file này, chúng ta dựa vào giả định có các package sau:

1. `internal/pkg/errors` cung cấp các hàm tạo lỗi domain:
   - `NewUnauthorizedError`: Tạo lỗi 401
   - `NewBadRequestError`: Tạo lỗi 400
   - `NewForbiddenError`: Tạo lỗi 403
   - `NewInternalError`: Tạo lỗi 500
   - `WrapError`: Bọc một lỗi gốc vào domain error
   - `Detail`: Struct cho phép thêm thông tin chi tiết vào lỗi

2. `internal/pkg/response` cung cấp `AbortWithError(c *gin.Context, err error)` để trả về HTTP response lỗi một cách chuẩn hóa.

Nếu các package này chưa tồn tại hoặc hoạt động khác, cần điều chỉnh code để phù hợp với cấu trúc hiện tại của ứng dụng.

### 3. Cập nhật các error codes trong constants.go (nếu cần)

Đảm bảo các error codes đã được định nghĩa trong `constants.go` phù hợp và nhất quán với hệ thống lỗi của ứng dụng.

## Kết quả mong đợi
- File `errors.go` được triển khai đầy đủ
- Hệ thống xử lý lỗi phân quyền nhất quán và tích hợp tốt với hệ thống lỗi hiện có
- Các response lỗi cung cấp thông tin hữu ích cho client
- Dự án vẫn biên dịch và chạy được sau khi cập nhật

## Thời gian dự kiến
- 2-3 giờ làm việc

## Độ ưu tiên
- Cao (Task quan trọng để đảm bảo trải nghiệm người dùng nhất quán)

## Phụ thuộc
- Phải hoàn thành Task 01 trước
- Có thể cần điều chỉnh tùy thuộc vào hệ thống xử lý lỗi hiện có của ứng dụng
