# Task 06: <PERSON><PERSON><PERSON> khai Bootstrap và Kết nối Components

## <PERSON><PERSON><PERSON> tiêu
Xây dựng hệ thống bootstrap để khởi tạo và kết nối tất cả các thành phần của hệ thống phân quyền, từ package permission đến module RBAC.

## C<PERSON><PERSON> bước thực hiện

1. Tạo function bootstrap tổng quan cho việc khởi tạo thủ công
2. K<PERSON><PERSON> nối `RBACPermissionService` với `CachedPermissionChecker`
3. <PERSON><PERSON><PERSON> nối `CachedPermissionChecker` với `MiddlewareFactory`
4. <PERSON><PERSON><PERSON> hợp `MiddlewareFactory` vào `core.App` để truyền cho các module khác

## Chi tiết triển khai

### 1. Tạo Bootstrap Function (Ví dụ trong bootstrap.go)

```go
// internal/bootstrap/rbac_bootstrap.go
package bootstrap

import (
	"time"

	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/modules/rbac/internal/repository/mysql"
	rbacInternalSvc "wnapi/modules/rbac/internal/service"
	rbacSvc "wnapi/modules/rbac/service"

	"gorm.io/gorm"
)

// BootstrapRBAC khởi tạo và kết nối toàn bộ thành phần của hệ thống phân quyền RBAC
func BootstrapRBAC(
	db *gorm.DB,
	appCache cache.Cache,
	appLogger logger.Logger,
	cfg config.Config,
) (*permission.MiddlewareFactory, error) {
	// 1. Khởi tạo các repositories
	userRoleRepo := mysql.NewMySQLUserRoleRepository(db, appLogger)
	rolePermRepo := mysql.NewMySQLRolePermissionRepository(db, appLogger)
	
	// 2. Khởi tạo RBAC Permission Service (internal)
	rbacPermissionInternal := rbacInternalSvc.NewRBACPermissionService(
		userRoleRepo,
		rolePermRepo,
		appLogger,
	)
	
	// 3. Tạo PermissionService public API (đây là PermissionChecker thực tế)
	rbacPermissionService := rbacSvc.NewPermissionService(rbacPermissionInternal)
	
	// 4. Đọc config TTL cho cache
	permissionCacheTTL := cfg.GetDurationWithDefault("cache.permission.ttl", 5*time.Minute)
	
	// 5. Khởi tạo CachedPermissionChecker
	cachedPermChecker := permission.NewCachedPermissionChecker(
		rbacPermissionService, // delegate
		appCache,
		permissionCacheTTL,
		appLogger,
	)
	
	// 6. Khởi tạo MiddlewareFactory
	middlewareFactory := permission.NewMiddlewareFactory(
		cachedPermChecker, // Sử dụng cached checker
		appLogger,
	)
	
	// 7. Trả về MiddlewareFactory để sử dụng ở main.go hoặc core.App
	return middlewareFactory, nil
}
```

### 2. Tích hợp với core.App (nếu có)

```go
// internal/core/app.go
package core

import (
	"wnapi/internal/pkg/cache"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
)

// App là đối tượng chính của ứng dụng
type App struct {
	Config            config.Config
	Logger            logger.Logger
	DBManager         *database.DBManager
	Cache             cache.Cache
	MiddlewareFactory *permission.MiddlewareFactory // Quan trọng cho phân quyền
	// ... các fields khác
}

// NewApp là constructor thủ công
func NewApp(
	cfg config.Config,
	log logger.Logger,
	dbm *database.DBManager,
	appCache cache.Cache,
	mwFactory *permission.MiddlewareFactory,
) *App {
	return &App{
		Config:            cfg,
		Logger:            log,
		DBManager:         dbm,
		Cache:             appCache,
		MiddlewareFactory: mwFactory,
	}
}

// GetMiddlewareFactory trả về MiddlewareFactory để sử dụng
func (a *App) GetMiddlewareFactory() *permission.MiddlewareFactory {
	return a.MiddlewareFactory
}
```

### 3. Tích hợp vào main.go

```go
// cmd/server/main.go
package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"

	"wnapi/internal/bootstrap"
	"wnapi/internal/core"
	"wnapi/internal/pkg/cache/memorycache"
	"wnapi/internal/pkg/config/viperconfig"
	"wnapi/internal/pkg/database/gormdb"
	"wnapi/internal/pkg/logger/zaplogger"
)

func main() {
	// 1. Khởi tạo config
	cfg, err := viperconfig.NewConfigLoader().Load("./config/app.yaml")
	if err != nil {
		log.Fatalf("Lỗi khởi tạo config: %v", err)
	}

	// 2. Khởi tạo logger
	appLogger, err := zaplogger.NewLogger(
		cfg.GetString("logger.level"),
		cfg.GetString("logger.format"),
	)
	if err != nil {
		log.Fatalf("Lỗi khởi tạo logger: %v", err)
	}

	// 3. Khởi tạo database
	dbManager, err := gormdb.NewGORMDBManager(
		cfg.GetString("database.dsn_write"),
		cfg.GetString("database.dsn_read"),
		appLogger,
	)
	if err != nil {
		appLogger.Error("Lỗi khởi tạo DB manager", "error", err)
		os.Exit(1)
	}

	// 4. Khởi tạo cache
	appCache := memorycache.NewMemoryCache()
	appLogger.Info("Cache initialized", "type", "in-memory")

	// 5. Bootstrap RBAC và Permission
	mwFactory, err := bootstrap.BootstrapRBAC(
		dbManager.DB(),
		appCache,
		appLogger,
		cfg,
	)
	if err != nil {
		appLogger.Error("Lỗi khởi tạo RBAC components", "error", err)
		os.Exit(1)
	}
	appLogger.Info("RBAC Permission system initialized")

	// 6. Khởi tạo App
	appInstance := core.NewApp(
		cfg,
		appLogger,
		dbManager,
		appCache,
		mwFactory,
	)

	// 7. Khởi tạo Server
	server := core.NewServer(appInstance)
	appLogger.Info("HTTP Server initialized")

	// 8. Khởi động server trong goroutine
	go func() {
		if err := server.Start(); err != nil {
			appLogger.Error("Lỗi chạy server", "error", err)
			os.Exit(1)
		}
	}()

	// 9. Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	appLogger.Info("Shutting down server...")
	ctx, cancel := context.WithTimeout(context.Background(), cfg.GetDuration("server.shutdown_timeout"))
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		appLogger.Error("Lỗi shutdown server", "error", err)
	}

	appLogger.Info("Server shutdown complete")
}
```

### 4. Truyền MiddlewareFactory vào Handler của module (ví dụ: Product)

```go
// Trong module bootstrap hoặc đăng ký route
func RegisterProductModule(appInstance *core.App) {
    // Lấy MiddlewareFactory từ App
    mwFactory := appInstance.GetMiddlewareFactory()
    
    // Khởi tạo ProductHandler với MiddlewareFactory
    productHandler := NewProductHandler(
        productService,
        mwFactory,
        appInstance.Logger,
    )
    
    // Đăng ký route
    productHandler.RegisterRoutes(router)
}
```

## Kết quả mong đợi
- Cơ chế bootstrap để khởi tạo thủ công đầy đủ hệ thống phân quyền
- Các thành phần được kết nối đúng với nhau
- MiddlewareFactory được truyền vào App core và có thể được sử dụng bởi các module
- Dự án vẫn biên dịch và chạy được sau khi triển khai

## Thời gian dự kiến
- 3-4 giờ làm việc

## Độ ưu tiên
- Cao (Đây là phần quan trọng để kết nối các thành phần lại với nhau)

## Phụ thuộc
- Phải hoàn thành Task 01, 02, 03, 04, 05 trước
- Phụ thuộc vào cấu trúc hiện tại của ứng dụng (core.App, logger, cache, database) 