# Task 01: Thi<PERSON><PERSON> lập Package Permission Cơ bản

## Mục tiêu
Tạo cấu trúc thư mục và các file cơ bản cho package `internal/pkg/permission` làm nền tảng cho hệ thống phân quyền RBAC.

## C<PERSON><PERSON> bước thực hiện

1. T<PERSON><PERSON> cấu trúc thư mục `internal/pkg/permission/`
2. Tạo file `interfaces.go` với các interface cơ bản
3. Tạo file `constants.go` chứa các hằng số và PermissionBuilder
4. Tạo file `errors.go` để xử lý lỗi
5. Tạo file `factory.go` cho MiddlewareFactory (phiên bản cơ bản)
6. Tạo file `cache.go` (phiên bản stub)

## Chi tiết triển khai

### 1. Cấu trúc thư mục
```
internal/pkg/permission/
├── interfaces.go    # Đ<PERSON><PERSON> nghĩa interfaces chung
├── constants.go     # Hằng số, action chuẩn, patterns đặt tên
├── errors.go        # X<PERSON> lý lỗi chuẩn hóa
├── factory.go       # Factory tạo middleware 
└── cache.go         # Stub cho caching
```

### 2. Triển khai interfaces.go
```go
// internal/pkg/permission/interfaces.go
package permission

import (
	"context"

	"github.com/gin-gonic/gin"
)

// PermissionChecker là interface cho việc kiểm tra quyền của người dùng
type PermissionChecker interface {
	// UserHasPermission kiểm tra xem người dùng có một quyền cụ thể không
	UserHasPermission(ctx context.Context, tenantID uint, userID uint, permissionCode string) (bool, error)

	// UserHasAnyPermission kiểm tra xem người dùng có ít nhất một trong các quyền không
	UserHasAnyPermission(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error)

	// UserHasAllPermissions kiểm tra xem người dùng có tất cả các quyền không
	UserHasAllPermissions(ctx context.Context, tenantID uint, userID uint, permissions []string) (bool, error)
}

// PermissionMiddleware là interface cho việc tạo ra các middleware functions
type PermissionMiddleware interface {
	RequirePermission(permission string) gin.HandlerFunc
	RequireAnyPermission(permissions ...string) gin.HandlerFunc
	RequireAllPermissions(permissions ...string) gin.HandlerFunc
}
```

### 3. Triển khai constants.go
```go
// internal/pkg/permission/constants.go
package permission

import "fmt"

const (
	// Standard Actions
	ActionCreate = "create"
	ActionRead   = "read"
	ActionUpdate = "update"
	ActionDelete = "delete"
	ActionList   = "list"
	ActionManage = "manage"

	// PermissionSeparator là ký tự phân tách các phần trong chuỗi permission
	PermissionSeparator = "."

	// Error Codes
	ErrorCodeAuthRequired     = "AUTHENTICATION_REQUIRED"
	ErrorCodeTenantRequired   = "TENANT_REQUIRED"
	ErrorCodePermissionDenied = "PERMISSION_DENIED"
	ErrorCodePermissionCheck  = "PERMISSION_CHECK_ERROR"
)

// PermissionBuilder là một helper struct để xây dựng chuỗi permission một cách an toàn và nhất quán
type PermissionBuilder struct {
	module   string
	action   string
	resource string // Optional
}

// NewPermissionBuilder khởi tạo một PermissionBuilder với tên module
func NewPermissionBuilder(module string) *PermissionBuilder {
	return &PermissionBuilder{module: module}
}

// Action thiết lập action cho permission
func (pb *PermissionBuilder) Action(action string) *PermissionBuilder {
	pb.action = action
	return pb
}

// Resource thiết lập resource (tùy chọn) cho permission
func (pb *PermissionBuilder) Resource(resource string) *PermissionBuilder {
	pb.resource = resource
	return pb
}

// Build tạo ra chuỗi permission hoàn chỉnh
func (pb *PermissionBuilder) Build() string {
	if pb.module == "" || pb.action == "" {
		return ""
	}
	if pb.resource != "" {
		return fmt.Sprintf("%s%s%s%s%s", pb.module, PermissionSeparator, pb.action, PermissionSeparator, pb.resource)
	}
	return fmt.Sprintf("%s%s%s", pb.module, PermissionSeparator, pb.action)
}
```

### 4. Triển khai stub cho các file còn lại
Tạo các file còn lại với nội dung placeholder để dự án có thể biên dịch được. Các file này sẽ được hoàn thiện trong các task tiếp theo.

## Kết quả mong đợi
- Package `internal/pkg/permission` được tạo với cấu trúc cơ bản
- Các interface và constants được định nghĩa đầy đủ
- Stub của các thành phần còn lại được tạo
- Dự án vẫn biên dịch và chạy được sau khi thêm package này

## Thời gian dự kiến
- 2-3 giờ làm việc

## Độ ưu tiên
- Cao (Đây là task đầu tiên của dự án RBAC) 