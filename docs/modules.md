# Tài Liệu Modules WNAPI

## Tổng Quan

WNAPI được xây dựng theo kiến trúc modular với các module độc lập. Mỗi module có thể được kích hoạt/tắt thông qua cấu hình và có cấu trúc thư mục chu<PERSON>n.

## Cấu Trúc <PERSON>

```
modules/{module_name}/
├── module.go              # Module definition và registration
├── api/                   # HTTP handlers và routes
│   ├── handler.go
│   ├── routes.go
│   └── middleware.go
├── service/               # Business logic
│   └── service.go
├── repository/            # Data access layer
│   └── mysql/
│       └── repository.go
├── models/                # GORM models
│   └── model.go
├── dto/                   # Data Transfer Objects
│   ├── request.go
│   └── response.go
├── migrations/            # Database migrations
│   ├── 001_create_table.up.sql
│   └── 001_create_table.down.sql
├── seeds/                 # Seed data
│   ├── common/
│   └── test/
└── internal/              # Module-specific internal code
    └── config.go
```

## Modules Hiện Tại

### Core Modules

#### 1. Auth Module
**Mụ<PERSON> đích**: <PERSON><PERSON><PERSON> thực và quản lý session người dùng

**<PERSON><PERSON><PERSON> năng chính**:
- Đăng ký, đăng nhập, đăng xuất
- JWT token management
- Password reset
- Email verification

**API Endpoints**:
- `POST /auth/register` - Đăng ký tài khoản
- `POST /auth/login` - Đăng nhập
- `POST /auth/logout` - Đăng xuất
- `POST /auth/refresh` - Refresh token
- `POST /auth/forgot-password` - Quên mật khẩu
- `POST /auth/reset-password` - Reset mật khẩu
- `POST /auth/verify-email` - Xác thực email

**Database Tables**: `users`, `user_profiles`

#### 2. RBAC Module
**Mục đích**: Quản lý vai trò và phân quyền

**Chức năng chính**:
- Quản lý roles và permissions
- User-role assignments
- Permission checking (hiện tại đang trong giai đoạn phát triển)

**API Endpoints**:
- `GET /rbac/roles` - Danh sách roles
- `POST /rbac/roles` - Tạo role mới
- `GET /rbac/permissions` - Danh sách permissions
- `POST /rbac/user-roles` - Gán role cho user

**Database Tables**: `rbac_roles`, `rbac_permissions`, `rbac_role_permissions`, `rbac_user_roles`

#### 3. Tenant Module
**Mục đích**: Quản lý multi-tenancy

**Chức năng chính**:
- Tenant management
- Tenant-specific configurations
- Subscription management

**API Endpoints**:
- `GET /tenants` - Danh sách tenants
- `POST /tenants` - Tạo tenant mới
- `GET /tenants/{id}` - Chi tiết tenant
- `PUT /tenants/{id}` - Cập nhật tenant

**Database Tables**: `tenants`

### Business Modules

#### 4. Product Module
**Mục đích**: Quản lý sản phẩm cho e-commerce

**Chức năng chính**:
- Product CRUD operations
- Category management
- Product variants và attributes
- Bulk operations

**API Endpoints**:
- `GET /products` - Danh sách sản phẩm
- `POST /products` - Tạo sản phẩm
- `GET /products/{id}` - Chi tiết sản phẩm
- `PUT /products/{id}` - Cập nhật sản phẩm
- `DELETE /products/{id}` - Xóa sản phẩm

#### 5. Cart Module
**Mục đích**: Quản lý giỏ hàng

**Chức năng chính**:
- Add/remove items from cart
- Update quantities
- Cart persistence
- Cart calculations

**API Endpoints**:
- `GET /cart` - Xem giỏ hàng
- `POST /cart/items` - Thêm sản phẩm vào giỏ
- `PUT /cart/items/{id}` - Cập nhật số lượng
- `DELETE /cart/items/{id}` - Xóa khỏi giỏ
- `DELETE /cart` - Xóa toàn bộ giỏ hàng

#### 6. Blog Module
**Mục đích**: Quản lý nội dung blog

**Chức năng chính**:
- Post management
- Category và tag management
- Author management
- Content publishing workflow

**API Endpoints**:
- `GET /blog/posts` - Danh sách bài viết
- `POST /blog/posts` - Tạo bài viết
- `GET /blog/posts/{id}` - Chi tiết bài viết
- `PUT /blog/posts/{id}` - Cập nhật bài viết

#### 7. Media Module
**Mục đích**: Quản lý file và media

**Chức năng chính**:
- File upload/download
- Image processing
- File organization
- CDN integration

**API Endpoints**:
- `POST /media/upload` - Upload file
- `GET /media/{id}` - Download file
- `DELETE /media/{id}` - Xóa file

#### 8. Notification Module
**Mục đích**: Hệ thống thông báo

**Chức năng chính**:
- Send notifications
- Notification templates
- Delivery tracking
- User preferences

**API Endpoints**:
- `GET /notifications` - Danh sách thông báo
- `POST /notifications` - Gửi thông báo
- `PUT /notifications/{id}/read` - Đánh dấu đã đọc

### Additional Modules

#### 9. Website Module
**Mục đích**: Quản lý website settings và content

#### 10. CRM Module
**Mục đích**: Customer Relationship Management

#### 11. SEO Module
**Mục đích**: SEO optimization tools

## Module Registration

Mỗi module tự đăng ký thông qua `init()` function:

```go
func init() {
    core.RegisterModuleFactory("module_name", NewModule)
}
```

## Module Configuration

Modules được kích hoạt thông qua biến môi trường:

```yaml
MODULES_ENABLED: "auth,rbac,tenant,product,cart,blog,media,notification"
```

## Module Dependencies

### Dependency Order
1. **Tenant** - Phải được khởi tạo trước
2. **Auth** - Cần tenant để hoạt động
3. **RBAC** - Cần auth để phân quyền
4. **Business Modules** - Có thể khởi tạo song song

### Inter-module Communication
- Modules giao tiếp thông qua shared interfaces
- Event-driven architecture cho loose coupling
- Shared packages trong `internal/pkg/`

## Best Practices

### Module Development
1. Tuân thủ cấu trúc thư mục chuẩn
2. Implement đầy đủ Module interface
3. Sử dụng dependency injection
4. Viết unit tests cho mỗi layer
5. Document API endpoints

### Database Design
1. Tất cả bảng phải có `tenant_id`
2. Sử dụng INT UNSIGNED cho IDs
3. Consistent naming conventions
4. Proper indexing strategy

### API Design
1. RESTful endpoints
2. Consistent response format
3. Proper HTTP status codes
4. Input validation
5. Error handling

## Testing Strategy

### Unit Tests
- Service layer logic
- Repository operations
- DTO validations

### Integration Tests
- API endpoint testing
- Database operations
- Module interactions

### E2E Tests
- Complete user workflows
- Multi-module scenarios
- Performance testing
