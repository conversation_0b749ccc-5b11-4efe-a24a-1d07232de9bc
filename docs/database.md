# Tài Liệu Database WNAPI

## Tổng Quan

WNAPI sử dụng kiến trúc multi-tenant với MySQL làm cơ sở dữ liệu chính. Tất cả các bảng đều hỗ trợ multi-tenancy thông qua trường `tenant_id`.

## Kiến Trúc Multi-Tenant

### Tenant Isolation
- Mỗi tenant có `tenant_id` riêng biệt
- Dữ liệu được phân tách hoàn toàn giữa các tenant
- Hỗ trợ tenant-specific configurations và settings

### ID Types
- Tất cả primary keys và foreign keys sử dụng `INT UNSIGNED`
- Auto-increment cho primary keys
- Consistent naming: `table_name_id` (ví dụ: `user_id`, `tenant_id`)

## Core Tables

### Tenants Table
```sql
CREATE TABLE tenants (
  tenant_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_name VARCHAR(255) NOT NULL,
  tenant_code VARCHAR(50) NOT NULL UNIQUE,
  status ENUM('active', 'inactive', 'suspended', 'trial') DEFAULT 'active',
  plan_type VARCHAR(50) DEFAULT 'standard',
  subscription_expires_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Users Table
```sql
CREATE TABLE users (
  user_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  full_name VARCHAR(100),
  user_type ENUM('admin', 'tenant', 'customer') NOT NULL,
  status ENUM('active', 'inactive', 'suspended', 'pending_verification', 
              'email_verification_required', 'banned', 'locked', 'deleted') 
              DEFAULT 'pending_verification',
  is_email_verified TINYINT(1) NOT NULL DEFAULT 0,
  last_login TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## RBAC Tables

### Roles Table
```sql
CREATE TABLE rbac_roles (
  role_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  role_code VARCHAR(255) NOT NULL UNIQUE,
  tenant_id INT UNSIGNED REFERENCES tenants(tenant_id),
  role_name VARCHAR(255) NOT NULL,
  role_description TEXT,
  created_by INT UNSIGNED,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Permissions Table
```sql
CREATE TABLE rbac_permissions (
  permission_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  permission_code VARCHAR(255) NOT NULL UNIQUE,
  tenant_id INT UNSIGNED REFERENCES tenants(tenant_id),
  group_id INT UNSIGNED REFERENCES rbac_permission_groups(group_id),
  permission_name VARCHAR(255) NOT NULL,
  permission_description TEXT,
  created_by INT UNSIGNED,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Role Permissions Mapping
```sql
CREATE TABLE rbac_role_permissions (
  role_permission_id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
  tenant_id INT UNSIGNED NOT NULL,
  role_id INT UNSIGNED NOT NULL REFERENCES rbac_roles(role_id),
  permission_id INT UNSIGNED NOT NULL REFERENCES rbac_permissions(permission_id),
  created_by INT UNSIGNED,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### User Roles Mapping
```sql
CREATE TABLE rbac_user_roles (
  tenant_id INT UNSIGNED NOT NULL,
  user_id INT UNSIGNED NOT NULL REFERENCES users(user_id),
  role_id INT UNSIGNED NOT NULL REFERENCES rbac_roles(role_id),
  created_by INT UNSIGNED,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (tenant_id, user_id, role_id)
);
```

## Migration System

### Module-based Migrations
- Mỗi module có thư mục `migrations/` riêng
- Migrations được chạy theo thứ tự priority của module
- Hỗ trợ cả up và down migrations

### Migration Order
1. **Tenant Module** (priority: 0) - Tạo bảng tenants trước
2. **Auth Module** (priority: 1) - Tạo bảng users
3. **RBAC Module** (priority: 2) - Tạo bảng roles và permissions
4. **Other Modules** (priority: 10+) - Các module khác

### Migration Commands
```bash
# Chạy tất cả migrations
make migrate

# Chạy migrations cho module cụ thể
./scripts/migrate.sh auth

# Rollback migrations
./scripts/migrate.sh down
```

## Indexing Strategy

### Primary Indexes
- Tất cả primary keys đều có index tự động
- Composite primary keys cho mapping tables

### Secondary Indexes
- `tenant_id` trên tất cả các bảng multi-tenant
- `email` và `username` trên bảng users
- `status` fields cho filtering
- Foreign key relationships

### Performance Considerations
- Connection pooling với GORM
- Read/Write splitting support
- Query optimization với proper indexing
- Cursor-based pagination cho large datasets

## Data Types và Conventions

### Naming Conventions
- Table names: snake_case
- Column names: snake_case  
- Primary keys: `table_name_id`
- Foreign keys: `referenced_table_id`
- Boolean fields: `is_` prefix

### Standard Columns
- `created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`
- `updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP`
- `created_by INT UNSIGNED` (user who created the record)
- `updated_by INT UNSIGNED` (user who last updated the record)

### Status Fields
- Sử dụng ENUM cho status fields
- Consistent status values across modules
- Default values được định nghĩa rõ ràng

## Backup và Recovery

### Backup Strategy
- Daily automated backups
- Point-in-time recovery capability
- Tenant-specific backup options

### Data Retention
- Soft delete cho user data
- Audit trail cho sensitive operations
- Configurable retention policies per tenant
