# Cấu trúc Dự án Golang Microservices Mô-đun hóa

Dưới đây là cấu trúc tổng hợp cho dự án Golang microservices mô-đun hóa với khả năng mở rộng qua plugin:

```
├── cmd/
│   └── server/
│       └── main.go                         # Điểm khởi đầu ch<PERSON>, khởi tạo và chạy ứng dụng
│
├── config/
│   ├── app.yaml                            # Cấu hình chung của ứng dụng
│   ├── db.yaml                             # Cấu hình cơ sở dữ liệu
│   └── modules.yaml                        # Cấu hình kích hoạt module
│
├── modules/                                # Các module chức năng core
│   ├── auth/                               # Module xác thực
│   │   ├── module.go                       # Khai báo & khởi tạo module
│   │   ├── api/                            # API handlers
│   │   │   ├── handler.go
│   │   │   ├── middleware.go
│   │   │   └── router.go                   # Định nghĩa routes
│   │   ├── service/                         # Business logic
│   │   │   ├── entity.go                    # Service entities
│   │   │   └── service.go                  # Business services
│   │   ├── repository/                     # Data access layer
│   │   │   ├── repository.go
│   │   │   └── mysql_repository.go
│   │   ├── dto/                            # Data Transfer Objects
│   │   │   └── auth_dto.go
│   │   └── migrations/                     # Database migrations
│   │       └── 001_create_tables.sql
│   │
│   ├── user/                               # Module quản lý người dùng  
│   │   ├── module.go
│   │   ├── api/
│   │   ├── domain/
│   │   ├── repository/
│   │   ├── dto/
│   │   └── migrations/
│   │
│   ├── blog/                               # Module blog
│   │   └── ...
│   │
│   ├── media/                              # Module quản lý media
│   │   └── ...
│   │
│   └── [module khác]/                      # Các module core khác
│
├── plugins/                                # Hệ thống plugin mở rộng
│   ├── registry.go                         # Registry quản lý plugins
│   ├── interface.go                        # Interface định nghĩa plugin
│   │
│   ├── payment/                            # Plugin nhóm thanh toán
│   │   ├── stripe/                         # Plugin Stripe
│   │   │   ├── plugin.go                   # Khai báo plugin
│   │   │   ├── handler.go                  # API handlers
│   │   │   └── service.go                  # Xử lý nghiệp vụ
│   │   │
│   │   └── paypal/                         # Plugin PayPal
│   │       └── ...
│   │
│   ├── social/                             # Plugin nhóm social login
│   │   ├── google/
│   │   └── facebook/
│   │
│   └── [nhóm plugin khác]/
│
├── internal/                               # Package dùng chung nội bộ
│   ├── core/                               # Core framework
│   │   ├── app.go                          # App initialization
│   │   ├── module.go                       # Module interface & registry
│   │   ├── plugin.go                       # Plugin loader
│   │   ├── config.go                       # Config loader
│   │   └── server.go                       # HTTP server
│   │
│   ├── database/                           # Database utilities
│   │   ├── connection.go                   # DB connection manager
│   │   └── migration.go                    # Migration utilities
│   │
│   ├── middleware/                         # Shared middleware
│   │   ├── auth.go                         # Authentication middleware
│   │   ├── logger.go                       # Logging middleware
│   │   └── cors.go                         # CORS middleware
│   │
│   └── pkg/                                # Shared utilities
│       ├── logger/                         # Logging utilities
│       ├── errors/                         # Error handling
│       ├── validator/                      # Validation utilities
│       ├── context/                        # Context utilities
│       ├── response/                       # Response formatting
│       └── utils/                          # Misc utilities
│
├── projects/                               # Định nghĩa dự án cụ thể
│   ├── blog-site/                          # Dự án blog
│   │   ├── config.yaml                     # Cấu hình ứng dụng
│   │   └── modules.yaml                    # Kích hoạt auth, user, blog
│   │
│   ├── ecommerce/                          # Dự án thương mại điện tử
│   │   ├── config.yaml
│   │   └── modules.yaml                    # Kích hoạt auth, user, product, cart, payment
│   │
│   └── [dự án khác]/
│
├── migrations/                             # Migration chung (system-wide)
│   └── init_schema.sql                     # Initial schema setup
│
├── scripts/                                # Utility scripts
│   ├── start.sh                            # Script start ứng dụng
│   ├── build.sh                            # Script build
│   ├── test.sh                             # Script test
│   ├── migrate.sh                          # Script migration
│   └── create-module.sh                    # Script tạo module mới
│
├── tests/                                  # Tests
│   ├── unit/                               # Unit tests
│   ├── integration/                        # Integration tests
│   └── e2e/                                # End-to-end tests
│
├── docs/                                   # Tài liệu
│   ├── architecture.md                     # Tài liệu kiến trúc
│   ├── modules.md                          # Tài liệu module
│   ├── api/                                # API docs
│   │   └── swagger.yaml                    # Swagger API specs
│   └── guides/                             # Developer guides
│
├── go.mod                                  # Go modules
├── go.sum                                  # Go modules checksums
├── Makefile                                # Build automation
├── Dockerfile                              # Docker configuration
├── docker-compose.yml                      # Docker Compose dev
├── docker-compose.prod.yml                 # Docker Compose prod
└── README.md                               # Tài liệu dự án
```

## Cách Hoạt Động

### 1. Hệ thống Module Core

Mỗi module trong thư mục `modules/` là một thành phần chức năng hoàn chỉnh, độc lập:

```go
// modules/auth/module.go
package auth

import (
    "myapp/internal/core"
    "myapp/modules/auth/api"
    "myapp/modules/auth/domain"
    "myapp/modules/auth/repository"
)

type AuthModule struct {
    service    *domain.AuthService
    repository *repository.AuthRepository
    handler    *api.AuthHandler
}

func NewAuthModule(app *core.App, config map[string]interface{}) (core.Module, error) {
    // Khởi tạo repository
    repo := repository.NewAuthRepository(app.DB())
    
    // Khởi tạo service
    svc := domain.NewAuthService(repo)
    
    // Khởi tạo handler
    handler := api.NewAuthHandler(svc)
    
    return &AuthModule{
        service:    svc,
        repository: repo,
        handler:    handler,
    }, nil
}

func (m *AuthModule) Name() string {
    return "auth"
}

func (m *AuthModule) RegisterRoutes(router core.Router) {
    api.RegisterRoutes(router, m.handler)
}

func init() {
    core.RegisterModuleFactory("auth", NewAuthModule)
}
```

### 2. Hệ thống Plugin

Plugins mở rộng chức năng của ứng dụng mà không cần sửa đổi core:

```go
// plugins/interface.go
package plugins

import "myapp/internal/core"

type Plugin interface {
    Name() string
    Version() string
    Init(config map[string]interface{}) error
    RegisterRoutes(router core.Router)
}

// plugins/registry.go
type Registry struct {
    plugins map[string]Plugin
}

func NewRegistry() *Registry {
    return &Registry{
        plugins: make(map[string]Plugin),
    }
}

func (r *Registry) Register(p Plugin) {
    r.plugins[p.Name()] = p
}

func (r *Registry) Get(name string) (Plugin, bool) {
    p, ok := r.plugins[name]
    return p, ok
}
```

### 3. Khởi tạo ứng dụng với các module/plugin theo dự án

```go
// cmd/server/main.go
package main

import (
    "flag"
    "fmt"
    "log"
    
    "myapp/internal/core"
    
    // Import tất cả module (để có thể đăng ký qua init())
    _ "myapp/modules/auth"
    _ "myapp/modules/user"
    _ "myapp/modules/blog"
    // ...
    
    // Import plugins
    _ "myapp/plugins/payment/stripe"
    _ "myapp/plugins/social/google"
    // ...
)

func main() {
    // Xác định dự án cần chạy
    projectName := flag.String("project", "", "Tên dự án (thư mục trong projects/)")
    flag.Parse()
    
    if *projectName == "" {
        log.Fatal("Vui lòng chỉ định tên dự án với flag --project")
    }
    
    // Đường dẫn tới thư mục dự án
    projectPath := fmt.Sprintf("projects/%s", *projectName)
    
    // Khởi tạo ứng dụng
    app, err := core.NewApp(&core.AppConfig{
        ConfigPath: fmt.Sprintf("%s/config.yaml", projectPath),
        ModulesConfigPath: fmt.Sprintf("%s/modules.yaml", projectPath),
    })
    if err != nil {
        log.Fatalf("Lỗi khởi tạo ứng dụng: %v", err)
    }
    
    // Khởi tạo core modules từ cấu hình
    if err := app.InitModules(); err != nil {
        log.Fatalf("Lỗi khởi tạo modules: %v", err)
    }
    
    // Khởi tạo plugins từ cấu hình (nếu có)
    if err := app.InitPlugins(); err != nil {
        log.Fatalf("Lỗi khởi tạo plugins: %v", err)
    }
    
    // Chạy ứng dụng
    if err := app.Run(); err != nil {
        log.Fatalf("Lỗi chạy ứng dụng: %v", err)
    }
}
```

### 4. Cấu hình module theo dự án (projects/blog-site/modules.yaml)

```yaml
# Danh sách module được kích hoạt
modules:
  - name: auth
    enabled: true
    config:
      jwt_secret: "your-secret-key"
      token_expiry: 3600
  
  - name: user
    enabled: true
    
  - name: blog
    enabled: true
    config:
      posts_per_page: 10
      
  - name: media
    enabled: true
    config:
      upload_dir: "/uploads"
      max_file_size: 5242880  # 5MB
      
  # Các module không được liệt kê hoặc enabled=false sẽ không được kích hoạt

# Danh sách plugin được kích hoạt (nếu cần)
plugins:
  - name: payment-stripe
    enabled: false  # Không cần trong dự án blog
    
  - name: social-google
    enabled: true
    config:
      client_id: "google-client-id"
      client_secret: "google-client-secret"
```

## Ưu điểm của Cấu trúc Này

1. **Mô-đun hóa cao** - Mỗi module hoàn toàn độc lập, có thể phát triển và kiểm thử riêng biệt

2. **Linh hoạt theo dự án** - Dễ dàng tạo các dự án khác nhau từ cùng một codebase bằng cách chỉ kích hoạt các module cần thiết

3. **Khả năng mở rộng** - Hệ thống plugin cho phép thêm chức năng mới mà không cần sửa đổi code core

4. **Tổ chức tốt** - Mỗi thành phần có vị trí rõ ràng, dễ tìm và dễ bảo trì

5. **Scalable** - Cấu trúc phù hợp cho cả dự án nhỏ và lớn, dễ dàng thêm module mới khi cần

6. **Tái sử dụng code** - Các package dùng chung được tổ chức trong thư mục `internal/pkg` để tái sử dụng

7. **Tách biệt mối quan tâm** - Presentation layer (API), business logic (domain) và data access (repository) được tách biệt rõ ràng trong mỗi module

8. **Dễ dàng deploy** - Có thể deploy toàn bộ ứng dụng hoặc từng module độc lập (nếu cấu hình microservices)

Cấu trúc này kết hợp những ưu điểm của thiết kế module hóa và microservices, đồng thời vẫn duy trì sự đơn giản trong phát triển và triển khai.