version: '3.8'

services:
  traefik:
    image: traefik:v2.10
    container_name: wn-traefik
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080" # Traefik dashboard
    volumes:
      - ./traefik.yaml:/etc/traefik/traefik.yaml
      - ./providers:/etc/traefik/providers
      - ./logs:/var/log/traefik
    networks:
      - wn-network
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.wn-api.local`)"
      - "traefik.http.routers.dashboard.service=api@internal"
      - "traefik.http.routers.dashboard.entrypoints=web"

networks:
  wn-network:
    name: wn-network
    driver: bridge 