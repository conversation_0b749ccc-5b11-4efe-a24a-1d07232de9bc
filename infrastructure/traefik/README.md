# Traefik Configuration cho WebNew Backend

Cấu hình Traefik để thay thế hoặc sử dụng song song với Nginx.

## Cách chạy với Traefik hoặc Nginx

Dự án hiện đã tích hợp cả Nginx và Traefik vào file docker-compose.yml chính. <PERSON><PERSON> chọn sử dụng một trong hai:

### Chạy với <PERSON> (mặc định)
```bash
docker compose --profile nginx up -d
```

### Chạy với <PERSON>raefik
```bash
docker compose --profile traefik up -d
```

### Dừng cả hai
```bash
docker compose down
```

## Cấu trúc thư mục

```
infrastructure/traefik/
├── traefik.yaml            # Cấu hình chính của Traefik
├── logs/                   # Thư mục chứa log
│   ├── traefik.log         # Log của Traefik
│   └── access.log          # Access log
└── providers/              # Thư mục chứa cấu hình động
    └── file/               # Provider kiểu file
        └── dynamic.yaml    # Cấu hình tổng hợp (services, routers và middlewares)
```

## Cấu hình host

Cần thêm vào file `/etc/hosts`:

```
127.0.0.1 wn-api.local cdn.wn-api.local traefik.wn-api.local
```

## Truy cập Dashboard

Traefik Dashboard có thể truy cập tại:

```
http://traefik.wn-api.local
```

## Lưu ý quan trọng về cấu hình

1. **File cấu hình động**: Tất cả cấu hình của routers, services, middlewares nên được đặt trong một file duy nhất (`dynamic.yaml`) để tránh vấn đề không load được cấu hình.

2. **Đường dẫn**: Phải sử dụng đường dẫn tuyệt đối trong file traefik.yaml, ví dụ:
   ```yaml
   providers:
     file:
       directory: "/etc/traefik/providers/file"
   ```

3. **Regex**: Traefik không hỗ trợ negative lookahead `(?!)` trong regex. Thay vào đó, hãy sử dụng các phương pháp khác như phân chia router với các priority khác nhau.

## Khắc phục lỗi

Nếu bạn thấy Traefik chạy nhưng không load được cấu hình:

1. Kiểm tra log: `docker logs wn-traefik`
2. Kiểm tra API của Traefik: `curl -s http://localhost:8080/api/rawdata`
3. Đảm bảo đường dẫn file cấu hình là chính xác
4. Xác nhận rằng file YAML có cú pháp hợp lệ
5. Khởi động lại container: `docker restart wn-traefik` 