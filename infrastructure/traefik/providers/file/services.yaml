http:
  services:
    # User module service
    user-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9044"

    # Auth module service
    auth-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9042"

    # Blog module service
    blog-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9043"

    # Tenant module service
    tenant-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9041"

    # RBAC module service
    rbac-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9040"

    # Site module service
    site-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9047"

    # Media module service
    media-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9046"

    # Ecom module service
    ecom-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9051"

    # Payment module service
    payment-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9050"
          
    # Minio service
    minio-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9071" 