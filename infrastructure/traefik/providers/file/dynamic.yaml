http:
  # SERVICES
  services:
    # User module service
    user-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9044"

    # Auth module service
    auth-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9042"

    # Blog module service
    blog-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9043"

    # Tenant module service
    tenant-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9041"

    # RBAC module service
    rbac-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9040"

    # Site module service
    site-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9047"

    # Media module service
    media-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9046"

    # Ecom module service
    ecom-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9051"

    # Payment module service
    payment-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9050"
          
    # Minio service
    minio-service:
      loadBalancer:
        servers:
          - url: "http://localhost:9071"
  
  # ROUTERS
  routers:
    # User module routes
    user-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/users`)"
      service: "user-service"
      middlewares:
        - "cors-headers"

    # Auth module routes
    auth-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/auth`)"
      service: "auth-service"
      middlewares:
        - "cors-headers"

    # Blog module routes
    blog-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/blog`)"
      service: "blog-service"
      middlewares:
        - "cors-headers"

    # Tenant module routes
    tenant-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/tenants`)"
      service: "tenant-service"
      middlewares:
        - "cors-headers"

    # RBAC module routes
    rbac-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/rbac`)"
      service: "rbac-service"
      middlewares:
        - "cors-headers"

    # Site module routes
    site-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/site`)"
      service: "site-service"
      middlewares:
        - "cors-headers"

    # Media module routes
    media-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/media`)"
      service: "media-service"
      middlewares:
        - "cors-headers"

    # Ecom module routes
    ecom-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/ecom`)"
      service: "ecom-service"
      middlewares:
        - "cors-headers"

    # Payment module routes
    payment-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/payment`)"
      service: "payment-service"
      middlewares:
        - "cors-headers"

    # Health check route
    health-router:
      rule: "Host(`wn-api.local`) && Path(`/health`)"
      service: "user-service"
      middlewares:
        - "health-check"

    # CDN/Minio routes for content already with media prefix
    cdn-media-router:
      rule: "Host(`cdn.wn-api.local`) && PathPrefix(`/media/`)"
      service: "minio-service"
      middlewares:
        - "cdn-headers"
      priority: 20

    # CDN/Minio routes for content without media prefix
    cdn-router:
      rule: "Host(`cdn.wn-api.local`)"
      service: "minio-service"
      middlewares:
        - "cdn-headers"
        - "add-media-prefix"
      priority: 10

  middlewares:
    # Global CORS headers
    cors-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
        accessControlAllowOriginList:
          - "*"
        accessControlAllowHeaders:
          - "Authorization"
          - "Content-Type"
          - "Accept"
        accessControlMaxAge: 1728000
        
    # Health Check middleware
    health-check:
      replacePath:
        path: "/health"
        
    # CDN specific headers
    cdn-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
        accessControlAllowOriginList:
          - "*"
        accessControlAllowHeaders:
          - "Authorization"
          - "Content-Type"
          - "Accept"
        accessControlMaxAge: 1728000
        customResponseHeaders:
          Cache-Control: "public, max-age=86400"
          Expires: "86400"
    
    # Add media prefix
    add-media-prefix:
      addPrefix:
        prefix: "/media" 