http:
  routers:
    # User module routes
    user-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/users`)"
      service: "user-service"
      middlewares:
        - "cors-headers"

    # Auth module routes
    auth-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/auth`)"
      service: "auth-service"
      middlewares:
        - "cors-headers"

    # Blog module routes
    blog-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/blog`)"
      service: "blog-service"
      middlewares:
        - "cors-headers"

    # Tenant module routes
    tenant-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/tenants`)"
      service: "tenant-service"
      middlewares:
        - "cors-headers"

    # RBAC module routes
    rbac-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/rbac`)"
      service: "rbac-service"
      middlewares:
        - "cors-headers"

    # Site module routes
    site-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/site`)"
      service: "site-service"
      middlewares:
        - "cors-headers"

    # Media module routes
    media-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/media`)"
      service: "media-service"
      middlewares:
        - "cors-headers"

    # Ecom module routes
    ecom-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/ecom`)"
      service: "ecom-service"
      middlewares:
        - "cors-headers"

    # Payment module routes
    payment-router:
      rule: "Host(`wn-api.local`) && PathPrefix(`/api/v1/payment`)"
      service: "payment-service"
      middlewares:
        - "cors-headers"

    # Health check route
    health-router:
      rule: "Host(`wn-api.local`) && Path(`/health`)"
      service: "user-service"
      middlewares:
        - "health-check"

    # CDN/Minio routes
    cdn-router:
      rule: "Host(`cdn.wn-api.local`)"
      service: "minio-service"
      middlewares:
        - "cdn-headers"
        - "minio-path-modifier"

  middlewares:
    # Global CORS headers
    cors-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
        accessControlAllowOriginList:
          - "*"
        accessControlAllowHeaders:
          - "Authorization"
          - "Content-Type"
          - "Accept"
        accessControlMaxAge: 1728000
        
    # Health Check middleware
    health-check:
      replacePath:
        path: "/health"
        
    # CDN specific headers
    cdn-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - OPTIONS
        accessControlAllowOriginList:
          - "*"
        accessControlAllowHeaders:
          - "Authorization"
          - "Content-Type"
          - "Accept"
        accessControlMaxAge: 1728000
        customResponseHeaders:
          Cache-Control: "public, max-age=86400"
          Expires: "86400"
    
    # Minio path modifier
    minio-path-modifier:
      replacePathRegex:
        regex: "^/(?!media/)(.*)$"
        replacement: "/media/$1" 