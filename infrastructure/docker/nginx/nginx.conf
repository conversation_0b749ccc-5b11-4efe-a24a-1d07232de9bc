worker_processes auto;

events {
    worker_connections 1024;
}

http {
    include mime.types;
    default_type application/octet-stream;

    sendfile on;
    keepalive_timeout 65;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # Enable compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Define upstream servers for each module
    upstream user-module {
        server user-module:9044 max_fails=3 fail_timeout=30s;
    }

    upstream auth-module {
        server auth-module:9042 max_fails=3 fail_timeout=30s;
    }

    upstream blog-module {
        server blog-module:9043 max_fails=3 fail_timeout=30s;
    }

    upstream tenant-module {
        server tenant-module:9041 max_fails=3 fail_timeout=30s;
    }

    upstream rbac-module {
        server rbac-module:9040 max_fails=3 fail_timeout=30s;
    }

    upstream site-module {
        server site-module:9047 max_fails=3 fail_timeout=30s;
    }

    upstream media-module {
        server media-module:9046 max_fails=3 fail_timeout=30s;
    }

    upstream payment-module {
        server payment-module:9050 max_fails=3 fail_timeout=30s;
    }

    upstream notification-module {
        server notification-module:9048 max_fails=3 fail_timeout=30s;
    }

    upstream ecom-module {
        server ecom-module:9051 max_fails=3 fail_timeout=30s;
    }
    
    upstream crm-module {
        server crm-module:9053 max_fails=3 fail_timeout=30s;
    }

    # Define upstream for minio
    upstream minio-server {
        server minio:9000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # API Gateway configuration
    server {
        listen 80;
        server_name wn-api.local;

        access_log /var/log/nginx/wn-api.local.log main;

        # User Module
        location /api/v1/users {
            proxy_pass http://user-module:9044;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Auth Module
        location /api/v1/auth {
            proxy_pass http://auth-module:9042;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Blog Module
        location /api/v1/blog {
            proxy_pass http://blog-module:9043;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Tenant Module
        location /api/v1/tenants {
            proxy_pass http://tenant-module:9041;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # RBAC Module
        location /api/v1/rbac {
            proxy_pass http://rbac-module:9040;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Site Module
        location /api/v1/site {
            proxy_pass http://site-module:9047;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Notification Module
        location /api/v1/notifications {
            proxy_pass http://notification-module:9048;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Media Module
        location /api/v1/media {
            proxy_pass http://media-module:9046;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Payment Module
        location /api/v1/payment {
            proxy_pass http://payment-module:9050;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Ecom Module
        location /api/v1/ecom {
            proxy_pass http://ecom-module:9051;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # CRM Module
        location /api/v1/crm {
            proxy_pass http://crm-module:9053;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Fallback for other routes
        location / {
            return 404;
        }
    }

    # CDN server block for minio
    server {
        listen 80;
        server_name cdn.wn-api.local;

        access_log /var/log/nginx/cdn.wn-api.local.log main;

        # CORS configuration
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type, Accept' always;

        # Handle preflight requests
        location / {
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
                add_header 'Access-Control-Allow-Headers' 'Authorization, Content-Type, Accept';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }

            # Rewrite to match Minio's expected path format: /{bucket}/{object}
            # The URL is coming as /tenant_id/type/date/filename.ext
            # We need to modify it to /media/tenant_id/type/date/filename.ext
            rewrite ^/(.*)$ /media/$1 break;

            proxy_pass http://minio-server;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Increase timeouts for large file transfers
            proxy_connect_timeout 300;
            proxy_send_timeout 300;
            proxy_read_timeout 300;
            send_timeout 300;

            # Enable WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # Remove Minio headers
            proxy_hide_header 'x-amz-bucket-region';
            proxy_hide_header 'x-amz-request-id';
            proxy_hide_header 'x-amz-id-2';

            # Add caching headers for better performance
            expires 1d;
            add_header Cache-Control "public, max-age=86400";

            # Debug header to see what's being requested
            add_header X-Debug-Original-URI $uri;
            add_header X-Debug-Rewritten-URI $request_uri;
        }
    }

    # Minio health check endpoint
    server {
        listen 80;
        server_name minio-health.local;

        location /health {
            proxy_pass http://minio-server/minio/health/live;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;

            # Return success only on 200 OK
            proxy_intercept_errors on;
            error_page 301 302 307 = 200;
            error_page 404 =503;

            # Don't show the body
            proxy_pass_request_body off;
            proxy_set_header Content-Length "";
        }
    }
}