user nginx;
worker_processes auto;
pid /var/run/nginx.pid;

# S<PERSON> lượng kết nối mở tối đa
worker_rlimit_nofile 65535;

events {
    # S<PERSON> lượng kết nối đồng thời tối đa mỗi worker
    worker_connections 4096;
    # Chấ<PERSON> nhận nhiều kết nối cùng lúc
    multi_accept on;
    # Sử dụng epoll cho hiệu suất tốt hơn trên Linux
    use epoll;
}

http {
    # Cấu hình cơ bản
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Cấu hình SSL
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Cấu hình DH parameters cho DHE ciphersuites
    ssl_dhparam /etc/nginx/ssl/dhparam.pem;

    # Cấu hình HSTS
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    
    # Các header bảo mật khác
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; font-src 'self'; connect-src 'self'";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Feature-Policy "camera 'none'; microphone 'none'; geolocation 'none'";

    # Cấu hình gzip
    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_http_version 1.1;
    gzip_min_length 256;
    gzip_types
        application/atom+xml
        application/javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        application/xhtml+xml
        application/xml
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon
        text/cache-manifest
        text/css
        text/plain
        text/vcard
        text/vnd.rim.location.xloc
        text/vtt
        text/x-component
        text/x-cross-domain-policy;

    # Cấu hình logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time $pipe';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Cấu hình buffer
    client_body_buffer_size 10K;
    client_header_buffer_size 1k;
    client_max_body_size 10m;
    large_client_header_buffers 4 4k;

    # Cấu hình timeout
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # Cấu hình rate limiting
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;

    # Define upstream for minio
    upstream minio-server {
        server minio:9000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # API Gateway configuration
    server {
        listen 80;
        server_name api.production-domain.com;
        
        # Redirect all HTTP requests to HTTPS
        location / {
            return 301 https://$host$request_uri;
        }
    }

    server {
        listen 443 ssl http2;
        server_name api.production-domain.com;

        # SSL certificates
        ssl_certificate /etc/nginx/ssl/fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/privkey.pem;

        # Logging
        access_log /var/log/nginx/api.production-domain.com.access.log main;
        error_log /var/log/nginx/api.production-domain.com.error.log warn;

        # Root location
        location / {
            return 404;
        }

        # Health check
        location /health {
            access_log off;
            add_header Content-Type text/plain;
            return 200 'OK';
        }

        # User Module
        location /api/v1/users {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://user-module:9044;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # Auth Module
        location /api/v1/auth {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://auth-module:9042;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # Blog Module
        location /api/v1/blogs {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://blog-module:9043;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # Tenant Module
        location /api/v1/tenants {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://tenant-module:9041;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # RBAC Module
        location /api/v1/rbac {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://rbac-module:9040;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # Site Module
        location /api/v1/sites {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://site-module:9047;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # Notification Module
        location /api/v1/notifications {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://notification-module:9048;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # Media Module
        location /api/v1/media {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://media-module:9046;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 60s; # Longer timeout for media uploads
            
            client_max_body_size 50M; # Allow larger file uploads
            
            proxy_buffering on;
            proxy_buffer_size 16k;
            proxy_buffers 8 16k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # Payment Module
        location /api/v1/payments {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://payment-module:9050;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # Ecom Module
        location /api/v1/ecom {
            limit_req zone=api_limit burst=20 nodelay;
            
            proxy_pass http://ecom-module:9051;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 30s;
            
            proxy_buffering on;
            proxy_buffer_size 8k;
            proxy_buffers 8 8k;
            
            proxy_intercept_errors on;
            error_page 500 502 503 504 /50x.html;
        }

        # Minio proxy for media files
        location /media/ {
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 300;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;

            proxy_pass http://minio-server/media/;
        }

        # Error pages
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # CDN server for media files
    server {
        listen 80;
        server_name cdn.production-domain.com;
        
        # Redirect all HTTP requests to HTTPS
        location / {
            return 301 https://$host$request_uri;
        }
    }

    server {
        listen 443 ssl http2;
        server_name cdn.production-domain.com;

        # SSL certificates
        ssl_certificate /etc/nginx/ssl/cdn_fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/cdn_privkey.pem;

        # Logging
        access_log /var/log/nginx/cdn.production-domain.com.access.log main;
        error_log /var/log/nginx/cdn.production-domain.com.error.log warn;

        # Cache settings for static files
        location / {
            proxy_pass http://minio-server;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            proxy_connect_timeout 300;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            chunked_transfer_encoding off;

            # Cache settings
            proxy_cache_valid 200 302 24h;
            proxy_cache_valid 404 1m;
            expires 1d;
            add_header Cache-Control "public, max-age=86400";
        }
    }

    # Monitoring server (Grafana)
    server {
        listen 80;
        server_name monitoring.production-domain.com;
        
        # Redirect all HTTP requests to HTTPS
        location / {
            return 301 https://$host$request_uri;
        }
    }

    server {
        listen 443 ssl http2;
        server_name monitoring.production-domain.com;

        # SSL certificates
        ssl_certificate /etc/nginx/ssl/monitoring_fullchain.pem;
        ssl_certificate_key /etc/nginx/ssl/monitoring_privkey.pem;

        # Logging
        access_log /var/log/nginx/monitoring.production-domain.com.access.log main;
        error_log /var/log/nginx/monitoring.production-domain.com.error.log warn;

        # Basic auth for additional security
        auth_basic "Restricted Access";
        auth_basic_user_file /etc/nginx/auth/.htpasswd;

        # Grafana
        location / {
            proxy_pass http://grafana:3000;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Prometheus (optional, if you want to expose it)
        location /prometheus/ {
            proxy_pass http://prometheus:9090/;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
