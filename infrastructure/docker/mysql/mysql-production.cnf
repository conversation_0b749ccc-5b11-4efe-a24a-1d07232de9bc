[mysqld]
# C<PERSON><PERSON> hình cơ bản
pid-file                = /var/run/mysqld/mysqld.pid
socket                  = /var/run/mysqld/mysqld.sock
datadir                 = /var/lib/mysql
secure-file-priv        = NULL

# C<PERSON><PERSON> hình character set và collation
character-set-server    = utf8mb4
collation-server        = utf8mb4_unicode_ci

# Cấu hình InnoDB
innodb_buffer_pool_size = 1G
innodb_log_file_size    = 256M
innodb_flush_log_at_trx_commit = 1
innodb_flush_method     = O_DIRECT
innodb_file_per_table   = 1
innodb_io_capacity      = 200
innodb_io_capacity_max  = 400

# Cấu hình query cache (tắt trong MySQL 8.0)
query_cache_type        = 0
query_cache_size        = 0

# Cấu hình connection
max_connections         = 500
max_connect_errors      = 10000
wait_timeout            = 600
interactive_timeout     = 600
connect_timeout         = 10

# C<PERSON>u hình log
log_error               = /var/log/mysql/error.log
slow_query_log          = 1
slow_query_log_file     = /var/log/mysql/slow.log
long_query_time         = 2
log_queries_not_using_indexes = 0

# Cấu hình bảo mật
local_infile            = 0
symbolic-links          = 0

# Cấu hình performance
join_buffer_size        = 2M
sort_buffer_size        = 4M
read_buffer_size        = 2M
read_rnd_buffer_size    = 2M
tmp_table_size          = 32M
max_heap_table_size     = 32M

# Cấu hình binary log
server-id               = 1
log_bin                 = mysql-bin
binlog_format           = ROW
binlog_expire_logs_seconds = 604800
max_binlog_size         = 100M
sync_binlog             = 1

# Cấu hình timezone
default-time-zone       = '+00:00'

[client]
default-character-set   = utf8mb4

[mysql]
default-character-set   = utf8mb4
