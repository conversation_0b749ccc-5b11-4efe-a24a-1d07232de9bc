global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

  # Attach these labels to any time series or alerts when communicating with
  # external systems (federation, remote storage, Alertmanager).
  external_labels:
    monitor: 'wn-backend-monitor'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Scrape configuration for service modules
  - job_name: 'user-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['user-module:9044']

  - job_name: 'auth-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['auth-module:9042']

  - job_name: 'blog-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['blog-module:9043']

  - job_name: 'tenant-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['tenant-module:9041']

  - job_name: 'rbac-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['rbac-module:9040']

  - job_name: 'site-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['site-module:9047']

  - job_name: 'notification-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['notification-module:9048']

  - job_name: 'media-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['media-module:9046']

  - job_name: 'payment-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['payment-module:9050']

  - job_name: 'ecom-module'
    metrics_path: '/metrics'
    static_configs:
      - targets: ['ecom-module:9051']

  # Scrape configuration for infrastructure services
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']

  - job_name: 'mysql'
    static_configs:
      - targets: ['mysqld-exporter:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
