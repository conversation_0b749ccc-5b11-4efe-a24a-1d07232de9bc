---
description: 
globs: 
alwaysApply: true
---
# Database rule
- DB local: blog_v4
- Luôn đặt tên field giống với tên cột trong database
- V<PERSON> dụ cột trong db là website_id thì json phải là website_id
# Models files:
- Tên model phải giống tên bảng
- Tên class model phải giống tên bảng
- Tạo file `up.sql` và `down.sql` trong thư mục `migrations/mysql/`
- Đặt tên theo format: `XXX_create_table_name.up.sql`, trong đó XXX là 3 số
# Gorm
- Thêm gorm:"primaryKey"
# PROCEDURE
- Tên : tên bảng _ tên procedure
Ví dụ : blog_categories_move_node
- Tên file migration : version_ create_ tên bảng _ tên procedure
Ví dụ : 006_create_procedure_blog_categories_move_node.up.sql, 006_create_procedure_blog_categories_move_node.down.sql
- Nội dung file procedure: 
+ <PERSON><PERSON>a dòng "DELIMITER //" ở đầu file nếu có
+ Xóa dòng " //" và "DELIMITER ;" ở cuối nếu có
# Kiểu dữ liệu

---

# ERD cho Location (Ví dụ)

```mermaid
erDiagram
    core_country ||--o{ core_city : ""
    core_city ||--o{ core_district : ""
    core_district ||--o{ core_address : ""

    core_country {
      int id UNSIGNED PK
      varchar name
      char(2) code // ISO 3166-1 alpha-2
    }
    core_city {
      int id UNSIGNED PK
      varchar name
      int country_id UNSIGNED FK
    }
    core_district {
      int id UNSIGNED PK
      varchar name
      int city_id UNSIGNED FK
    }
    core_address {
      int id UNSIGNED PK
      varchar detail
      int district_id UNSIGNED FK
    }
```

---

# MySQL Table Example

```sql
CREATE TABLE core_country (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  code CHAR(2) NOT NULL
);

CREATE TABLE core_city (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  country_id INT UNSIGNED NOT NULL,
  FOREIGN KEY (country_id) REFERENCES core_country(id)
);

CREATE TABLE core_district (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  city_id INT UNSIGNED NOT NULL,
  FOREIGN KEY (city_id) REFERENCES core_city(id)
);

CREATE TABLE core_address (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  detail VARCHAR(255) NOT NULL,
  district_id INT UNSIGNED NOT NULL,
  FOREIGN KEY (district_id) REFERENCES core_district(id)
);
```

---

# Rule tổng quát cho DB
- Ưu tiên chuẩn hóa dữ liệu, đặt tên theo tiếng Anh, viết thường, snake_case.
- Tên bảng location phải có prefix: core_ (vd: core_country, core_city, core_district, core_address).
- Tên bảng số nhiều cho các bảng khác (theo chuẩn project).
- Tên trường giống cột DB, dùng snake_case, trùng với json.
- Luôn có id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY.
- Có thể bổ sung trường code (chuẩn quốc tế) nếu cần.
- Nếu có trường liên kết (FK), phải index rõ ràng và đặt tên đúng chuẩn (vd: city_id, country_id).
- Mọi migration phải có up.sql và down.sql, đặt tên đúng format.
- Khi dùng Gorm: luôn thêm tag gorm:"primaryKey" cho id.
- Nếu dùng procedure: đặt tên theo quy tắc, migration procedure như hướng dẫn trên.
- Ưu tiên dùng chuẩn ISO cho các trường mã quốc gia, mã vùng.
- Khi mở rộng quốc tế: cân nhắc thêm các trường như state, province, postcode, v.v.
- Khi mapping với API hoặc model Go: giữ nguyên tên cột DB cho json, không dịch.
- Đối với ID thì luôn là INT UNSIGNED
- price luôn là DECIMAL(18, 2) DEFAULT 0.00