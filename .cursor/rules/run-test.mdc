---
description: 
globs: ["**/*.go"]
alwaysApply: false
---

# Project Overview

This is a microservices-based backend project with multiple modules (auth, user, blog, etc.). Each module can be tested and run independently.

## Project Structure

- Modules directory: `./modules`
- Each module has its own directory: `auth`, `user`, `blog`, etc.
- Each module has a `cmd/main.go` file that serves as the entry point

## Testing and Running Modules

### General Commands

- To run a specific module (e.g., auth):
  ```bash
  cd /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/modules/auth && go run cmd/main.go
  ```

- To run tests for a specific module:
  ```bash
  cd /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/modules/auth && go test ./...
  ```

- To build a specific module:
  ```bash
  cd /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/modules/auth && go build -o auth-service cmd/main.go
  ```

## API Endpoints

- Base URL: `http://wn-api.local`
- Auth module endpoints:
  - Register: `POST /api/v1/auth/register`
  - Login: `POST /api/v1/auth/login`
  - Verify Email: `GET /api/v1/auth/verify-email?token=<token>`

## Common Issues and Solutions

- If you encounter dependency issues, run:
  ```bash
  cd /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2/modules/auth && go mod tidy
  ```

- If you need to update OpenTelemetry dependencies:
  ```bash
  cd /Users/<USER>/Desktop/Workspace/Webnew/wn-backend-v2 && go get go.opentelemetry.io/otel go.opentelemetry.io/otel/trace go.opentelemetry.io/otel/sdk
  ```