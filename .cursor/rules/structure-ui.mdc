---
description: 
globs: 
alwaysApply: true
---
# Module sample:

## assets
web/cms/src/pages/module/assets/styles.scss
## components
web/cms/src/pages/module/components/index.tsx
web/cms/src/pages/module/components/search-module.tsx
web/cms/src/pages/module/components/select-module.tsx
## detail
web/cms/src/pages/module/detail/index.tsx
## form
web/cms/src/pages/module/form/form.tsx
web/cms/src/pages/module/form/index.tsx
web/cms/src/pages/module/form/modal.tsx
## Multi lang
web/cms/src/pages/module/i18n/en.json
web/cms/src/pages/module/i18n/vi.json
## export 
web/cms/src/pages/module/index.tsx
## list
web/cms/src/pages/module/list/index.tsx
web/cms/src/pages/module/list/search.tsx
## store, type, api, config
web/cms/src/pages/module/store.ts
web/cms/src/pages/module/type.ts
web/cms/src/pages/module/api.ts
web/cms/src/pages/module/config.ts

