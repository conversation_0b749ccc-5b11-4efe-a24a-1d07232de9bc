---
description: 
globs: 
alwaysApply: true
---
# API protect
- Mọi API cần được protect th<PERSON> nên apply jwt
- Sử dụng [jwt.go](mdc:pkg/auth/jwt.go)
- Config :
jwt:
  access_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  refresh_signing_key: "AuH7JJzXKjnqSWNQByjc2uYX6Q3KP9Uj"
  access_token_expiration: "168h"
  refresh_token_expiration: "168h"
  issuer: wn-backend

# Permission Control
- Sử dụng [permission.go](mdc:pkg/middleware/permission.go) cho việc kiểm tra quyền
- Ví dụ sử dụng:
```go
router.GET("/products", RequirePermission(permService, "products.read"), productHandler.List)
router.POST("/products", RequirePermission(permService, "products.create"), productHandler.Create)
```

# Tenant
- sử dụng [auth.go](mdc:pkg/auth/auth.go)
// getTenantIDFromContext lấy tenant ID từ context request
func getTenantIDFromContext(c *gin.Context) (string, error) {
	// Lấy claims từ context
	claims, exists := auth.GetClaimsFromContext(c)
	if !exists {
		return "", fmt.Errorf("không tìm thấy thông tin xác thực")
	}

	return strconv.Itoa(claims.TenantID), nil
}

// getUserIDFromContext lấy user ID từ context request
func getUserIDFromContext(c *gin.Context) (string, error) {
	// Lấy user ID từ context
	userID, exists := auth.GetUserIDFromContext(c)
	if !exists {
		return "", fmt.Errorf("không tìm thấy thông tin người dùng")
	}

	return strconv.Itoa(userID), nil
}