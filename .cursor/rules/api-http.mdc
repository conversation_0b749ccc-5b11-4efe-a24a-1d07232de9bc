---
description: 
globs: 
alwaysApply: true
---
# HTTP rule
- Mã lỗi phải được define ở module
# Response success
```json
{
  "status": {
    "code": 200,
    "message": "Operation completed successfully",
    "success": true,
    "error_code": null,
    "path": "/api/users",
    "timestamp": "2025-03-15T14:35:22Z",
    "details": null
  },
  "data": [] or {},
  "meta": {
    "next_cursor": string,
    "has_more": true
  }
}

Note: 
"data": {
     "roles": []
} nên là  "data": []

# Response error
```json
{
  "status": {
    "code": 409,
    "message": "<PERSON><PERSON> already exists",
    "success": false,
    "error_code": "EMAIL_ALREADY_EXISTS",
    "path": "/api/users",
    "timestamp": "2025-03-15T14:30:45Z",
    "details": [
      {
        "field": "email",
        "message": "<EMAIL> đã được sử dụng"
      }
    ]
  }
}

# CRUD API
- List: Thay vì wrap mảng trong một struct, gi<PERSON> đây chúng sẽ trả về mảng trực tiếp.
- <PERSON><PERSON>, <PERSON><PERSON>, Update: Thay vì wrap mảng trong một struct, giờ đây chúng sẽ trả về object trực tiếp.

# Create api
- Nên trả về đối tượng đầy đủ 

# Cấu trúc phản hồi phải nhất quán
- Sử dụng [http_response.go](mdc:pkg/response/http_response.go)
// Thay thế
c.JSON(http.StatusBadRequest, gin.H{
    "status": gin.H{
        "code":       http.StatusBadRequest,
        "message":    "Invalid request data",
        "success":    false,
        "error_code": common.ErrorCodeInvalidTenantData,
        "path":       c.Request.URL.Path,
        "timestamp":  time.Now().Format(time.RFC3339),
        "details": []common.Detail{
            {
                Message: err.Error(),
            },
        },
    },
})

// Bằng
details := []interface{}{map[string]string{"message": err.Error()}}
response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", common.ErrorCodeInvalidTenantData, details)
